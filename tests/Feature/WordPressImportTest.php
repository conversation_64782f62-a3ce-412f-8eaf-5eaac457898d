<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\News;
use App\Models\User;
use App\Services\CategoryMappingService;
use App\Services\WordPressImportService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class WordPressImportTest extends TestCase
{
    use RefreshDatabase;

    protected WordPressImportService $importService;
    protected CategoryMappingService $categoryMappingService;
    protected User $superAdmin;
    protected Category $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->importService = app(WordPressImportService::class);
        $this->categoryMappingService = app(CategoryMappingService::class);

        // Create roles
        Role::create(['name' => 'Super Admin']);
        Role::create(['name' => 'Writer']);
        
        // Create test user
        $this->superAdmin = User::factory()->create();
        $this->superAdmin->assignRole('Super Admin');
        
        // Create test category
        $this->category = Category::factory()->create([
            'name' => 'Test Category'
        ]);
    }

    /** @test */
    public function it_can_preview_wordpress_import()
    {
        $sqlContent = $this->getSampleSqlContent();
        $tempFile = $this->createTempSqlFile($sqlContent);
        
        $options = [
            'fallback_category_id' => $this->category->id,
            'skip_duplicates' => true,
            'create_missing_categories' => true,
            'auto_assign_authors' => true
        ];
        
        $preview = $this->importService->previewImport($tempFile, $options);
        
        $this->assertArrayHasKey('total_posts', $preview);
        $this->assertArrayHasKey('sample_posts', $preview);
        $this->assertArrayHasKey('post_statuses', $preview);
        $this->assertArrayHasKey('date_range', $preview);
        
        $this->assertEquals(2, $preview['total_posts']);
        $this->assertCount(2, $preview['sample_posts']);
        
        unlink($tempFile);
    }

    /** @test */
    public function it_can_import_wordpress_posts()
    {
        $sqlContent = $this->getSampleSqlContent();
        $tempFile = $this->createTempSqlFile($sqlContent);
        
        $options = [
            'fallback_category_id' => $this->category->id,
            'skip_duplicates' => false,
            'create_missing_categories' => true,
            'auto_assign_authors' => true,
            'post_status_mapping' => [
                'publish' => 'Accept',
                'draft' => 'Pending'
            ]
        ];
        
        $result = $this->importService->importFromSql($tempFile, $options);
        
        $this->assertEquals(2, $result['imported']);
        $this->assertEquals(0, $result['skipped']);
        $this->assertEquals(2, $result['total_processed']);
        $this->assertEmpty($result['errors']);
        
        // Verify data in database
        $this->assertEquals(2, News::count());

        $firstNews = News::where('title', 'Test Article 1')->first();
        $this->assertNotNull($firstNews);
        $this->assertEquals('Accept', $firstNews->status);

        // Should be assigned to placeholder author, not the super admin
        $placeholderAuthor = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($placeholderAuthor);
        $this->assertEquals($placeholderAuthor->id, $firstNews->user_id);
        
        unlink($tempFile);
    }

    /** @test */
    public function it_can_skip_duplicates()
    {
        // Create existing news
        News::factory()->create([
            'title' => 'Test Article 1',
            'created_at' => '2024-01-01 10:00:00'
        ]);
        
        $sqlContent = $this->getSampleSqlContent();
        $tempFile = $this->createTempSqlFile($sqlContent);
        
        $options = [
            'fallback_category_id' => $this->category->id,
            'skip_duplicates' => true,
            'create_missing_categories' => true,
            'auto_assign_authors' => true
        ];
        
        $result = $this->importService->importFromSql($tempFile, $options);
        
        $this->assertEquals(1, $result['imported']);
        $this->assertEquals(1, $result['skipped']);
        $this->assertEquals(2, $result['total_processed']);
        
        unlink($tempFile);
    }

    /** @test */
    public function super_admin_can_access_import_page()
    {
        $this->actingAs($this->superAdmin);
        
        $response = $this->get(route('admin.import.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.import.index');
        $response->assertViewHas('categories');
        $response->assertViewHas('users');
    }

    /** @test */
    public function non_super_admin_cannot_access_import_page()
    {
        $writer = User::factory()->create();
        $writer->assignRole('Writer');
        
        $this->actingAs($writer);
        
        $response = $this->get(route('admin.import.index'));
        
        $response->assertStatus(403);
    }

    /** @test */
    public function it_validates_import_request()
    {
        $this->actingAs($this->superAdmin);
        
        $response = $this->post(route('admin.import.wordpress'), [
            // Missing required fields
        ]);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'sql_file'
        ]);
    }

    /** @test */
    public function it_can_detect_missing_categories()
    {
        $sqlContent = $this->getSampleSqlWithCategories();
        $tempFile = $this->createTempSqlFile($sqlContent);

        $options = [
            'fallback_category_id' => $this->category->id,
            'skip_duplicates' => true,
            'create_missing_categories' => true,
            'auto_assign_authors' => true
        ];

        $preview = $this->importService->previewImport($tempFile, $options);

        $this->assertArrayHasKey('category_analysis', $preview);
        $this->assertArrayHasKey('missing_categories', $preview['category_analysis']);
        $this->assertGreaterThan(0, count($preview['category_analysis']['missing_categories']));

        unlink($tempFile);
    }

    /** @test */
    public function it_caches_missing_categories_during_import()
    {
        // Clear any existing cache
        Cache::forget('wordpress_import_cache_keys');

        $sqlContent = $this->getSampleSqlWithCategories();
        $tempFile = $this->createTempSqlFile($sqlContent);

        $options = [
            'fallback_category_id' => $this->category->id,
            'skip_duplicates' => false,
            'create_missing_categories' => true,
            'auto_assign_authors' => true
        ];

        $this->importService->importFromSql($tempFile, $options);

        // Check if cache keys were created
        $cacheKeys = Cache::get('wordpress_import_cache_keys', []);
        $this->assertNotEmpty($cacheKeys);

        // Check if cached data exists
        $cachedData = Cache::get($cacheKeys[0]);
        $this->assertNotNull($cachedData);
        $this->assertArrayHasKey('missing_categories', $cachedData);

        unlink($tempFile);
    }

    /** @test */
    public function it_can_get_missing_categories_summary()
    {
        // Create some cached missing categories
        $cacheData = [
            'timestamp' => now()->toISOString(),
            'missing_categories' => [
                ['name' => 'Technology', 'id' => 1, 'slug' => 'technology'],
                ['name' => 'Sports', 'id' => 2, 'slug' => 'sports']
            ],
            'suggested_mappings' => [],
            'total_missing' => 2,
            'total_suggestions' => 0
        ];

        $cacheKey = 'wordpress_import_missing_categories_test';
        Cache::put($cacheKey, $cacheData, now()->addHours(24));
        Cache::put('wordpress_import_cache_keys', [$cacheKey], now()->addHours(24));

        $summary = $this->categoryMappingService->getMissingCategoriesSummary();

        $this->assertTrue($summary['has_missing_categories']);
        $this->assertEquals(2, $summary['unique_count']);
        $this->assertCount(2, $summary['unique_missing_categories']);
    }

    /** @test */
    public function it_can_create_categories_from_suggestions()
    {
        $categoryNames = ['Technology News', 'Sports Update'];

        $result = $this->categoryMappingService->createCategoriesFromSuggestions($categoryNames);

        $this->assertEquals(2, $result['created_count']);
        $this->assertEquals(0, $result['error_count']);

        // Verify categories were created in database
        $this->assertDatabaseHas('category', ['name' => 'Technology News']);
        $this->assertDatabaseHas('category', ['name' => 'Sports Update']);
    }

    /** @test */
    public function super_admin_can_access_missing_categories_endpoints()
    {
        $this->actingAs($this->superAdmin);

        // Test get missing categories
        $response = $this->get(route('admin.import.missing-categories'));
        $response->assertStatus(200);

        // Test create categories
        $response = $this->post(route('admin.import.create-categories'), [
            'category_names' => ['Test Category']
        ]);
        $response->assertStatus(200);

        // Test clear cache
        $response = $this->delete(route('admin.import.clear-cache'));
        $response->assertStatus(200);
    }

    /** @test */
    public function it_creates_placeholder_author_for_imports()
    {
        $placeholderAuthor = $this->authorMappingService->getPlaceholderAuthor();

        $this->assertNotNull($placeholderAuthor);
        $this->assertEquals('WordPress Import Placeholder', $placeholderAuthor->name);
        $this->assertEquals('<EMAIL>', $placeholderAuthor->email);
        $this->assertTrue($placeholderAuthor->hasRole('Writer'));
    }

    /** @test */
    public function it_can_detect_missing_authors()
    {
        $sqlContent = $this->getSampleSqlWithAuthors();

        $wpAuthors = $this->authorMappingService->extractWordPressAuthors($sqlContent);
        $analysis = $this->authorMappingService->analyzeAuthorMapping($wpAuthors);

        $this->assertGreaterThan(0, count($wpAuthors));
        $this->assertArrayHasKey('missing_authors', $analysis);
        $this->assertArrayHasKey('suggested_mappings', $analysis);
    }

    /** @test */
    public function it_assigns_placeholder_author_to_imported_articles()
    {
        $sqlContent = $this->getSampleSqlContent();
        $tempFile = $this->createTempSqlFile($sqlContent);

        $options = [
            'fallback_category_id' => $this->category->id,
            'skip_duplicates' => false,
            'auto_assign_authors' => true
        ];

        $this->importService->importFromSql($tempFile, $options);

        $placeholderAuthor = $this->authorMappingService->getPlaceholderAuthor();
        $importedNews = News::where('user_id', $placeholderAuthor->id)->get();

        $this->assertGreaterThan(0, $importedNews->count());

        unlink($tempFile);
    }

    protected function getSampleSqlContent(): string
    {
        return "
INSERT INTO `wp_posts` (`ID`, `post_author`, `post_date`, `post_date_gmt`, `post_content`, `post_title`, `post_excerpt`, `post_status`, `comment_status`, `ping_status`, `post_password`, `post_name`, `to_ping`, `pinged`, `post_modified`, `post_modified_gmt`, `post_content_filtered`, `post_parent`, `guid`, `menu_order`, `post_type`, `post_mime_type`, `comment_count`) VALUES
(1, 1, '2024-01-01 10:00:00', '2024-01-01 10:00:00', 'Content of test article 1', 'Test Article 1', '', 'publish', 'open', 'open', '', 'test-article-1', '', '', '2024-01-01 10:00:00', '2024-01-01 10:00:00', '', 0, 'http://example.com/?p=1', 0, 'post', '', 0),
(2, 1, '2024-01-02 10:00:00', '2024-01-02 10:00:00', 'Content of test article 2', 'Test Article 2', '', 'draft', 'open', 'open', '', 'test-article-2', '', '', '2024-01-02 10:00:00', '2024-01-02 10:00:00', '', 0, 'http://example.com/?p=2', 0, 'post', '', 0);
        ";
    }

    protected function getSampleSqlWithCategories(): string
    {
        return "
INSERT INTO `wp_terms` (`term_id`, `name`, `slug`, `term_group`) VALUES
(1, 'Technology', 'technology', 0),
(2, 'Sports', 'sports', 0),
(3, 'Entertainment', 'entertainment', 0);

INSERT INTO `wp_term_taxonomy` (`term_taxonomy_id`, `term_id`, `taxonomy`, `description`, `parent`, `count`) VALUES
(1, 1, 'category', '', 0, 1),
(2, 2, 'category', '', 0, 1),
(3, 3, 'category', '', 0, 1);

INSERT INTO `wp_posts` (`ID`, `post_author`, `post_date`, `post_date_gmt`, `post_content`, `post_title`, `post_excerpt`, `post_status`, `comment_status`, `ping_status`, `post_password`, `post_name`, `to_ping`, `pinged`, `post_modified`, `post_modified_gmt`, `post_content_filtered`, `post_parent`, `guid`, `menu_order`, `post_type`, `post_mime_type`, `comment_count`) VALUES
(1, 1, '2024-01-01 10:00:00', '2024-01-01 10:00:00', 'Content of test article 1', 'Test Article 1', '', 'publish', 'open', 'open', '', 'test-article-1', '', '', '2024-01-01 10:00:00', '2024-01-01 10:00:00', '', 0, 'http://example.com/?p=1', 0, 'post', '', 0),
(2, 1, '2024-01-02 10:00:00', '2024-01-02 10:00:00', 'Content of test article 2', 'Test Article 2', '', 'draft', 'open', 'open', '', 'test-article-2', '', '', '2024-01-02 10:00:00', '2024-01-02 10:00:00', '', 0, 'http://example.com/?p=2', 0, 'post', '', 0);
        ";
    }

    protected function getSampleSqlWithAuthors(): string
    {
        return "
INSERT INTO `wp_users` (`ID`, `user_login`, `user_pass`, `user_nicename`, `user_email`, `user_url`, `user_registered`, `user_activation_key`, `user_status`, `display_name`) VALUES
(1, 'admin', '\$P\$BZlPX7NIx8MYpXokBW2AGsN7i.aUOt0', 'admin', '<EMAIL>', '', '2024-01-01 00:00:00', '', 0, 'Administrator'),
(2, 'editor', '\$P\$BZlPX7NIx8MYpXokBW2AGsN7i.aUOt0', 'editor', '<EMAIL>', '', '2024-01-01 00:00:00', '', 0, 'Editor User'),
(3, 'author', '\$P\$BZlPX7NIx8MYpXokBW2AGsN7i.aUOt0', 'author', '<EMAIL>', '', '2024-01-01 00:00:00', '', 0, 'Author User');

INSERT INTO `wp_posts` (`ID`, `post_author`, `post_date`, `post_date_gmt`, `post_content`, `post_title`, `post_excerpt`, `post_status`, `comment_status`, `ping_status`, `post_password`, `post_name`, `to_ping`, `pinged`, `post_modified`, `post_modified_gmt`, `post_content_filtered`, `post_parent`, `guid`, `menu_order`, `post_type`, `post_mime_type`, `comment_count`) VALUES
(1, 1, '2024-01-01 10:00:00', '2024-01-01 10:00:00', 'Content of test article 1', 'Test Article 1', '', 'publish', 'open', 'open', '', 'test-article-1', '', '', '2024-01-01 10:00:00', '2024-01-01 10:00:00', '', 0, 'http://example.com/?p=1', 0, 'post', '', 0),
(2, 2, '2024-01-02 10:00:00', '2024-01-02 10:00:00', 'Content of test article 2', 'Test Article 2', '', 'draft', 'open', 'open', '', 'test-article-2', '', '', '2024-01-02 10:00:00', '2024-01-02 10:00:00', '', 0, 'http://example.com/?p=2', 0, 'post', '', 0);
        ";
    }

    protected function createTempSqlFile(string $content): string
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'wp_import_test_');
        file_put_contents($tempFile, $content);
        return $tempFile;
    }
}
