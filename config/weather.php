<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Weather Provider
    |--------------------------------------------------------------------------
    |
    | This option controls the default weather provider that will be used
    | to fetch weather data. You may change this to any of the providers
    | defined in the "providers" configuration array below.
    |
    */

    'default' => env('WEATHER_PROVIDER', 'openweathermap'),

    /*
    |--------------------------------------------------------------------------
    | Weather Providers
    |--------------------------------------------------------------------------
    |
    | Here you may configure the weather providers for your application.
    | Each provider may have different configuration options.
    |
    */

    'providers' => [
        'openweathermap' => [
            'api_key' => env('OPENWEATHER_API_KEY'),
            'base_url' => env('OPENWEATHER_BASE_URL', 'https://api.openweathermap.org/data/2.5'),
            'timeout' => env('OPENWEATHER_TIMEOUT', 30),
            'units' => env('OPENWEATHER_UNITS', 'metric'), // metric, imperial, kelvin
            'lang' => env('OPENWEATHER_LANG', 'id'), // language code
        ],

        'weatherapi' => [
            'api_key' => env('WEATHERAPI_KEY'),
            'base_url' => env('WEATHERAPI_BASE_URL', 'https://api.weatherapi.com/v1'),
            'timeout' => env('WEATHERAPI_TIMEOUT', 30),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Configure how long weather data should be cached to avoid unnecessary
    | API calls. Time is in minutes.
    |
    */

    'cache' => [
        'enabled' => env('WEATHER_CACHE_ENABLED', true),
        'ttl' => env('WEATHER_CACHE_TTL', 10), // minutes
        'prefix' => env('WEATHER_CACHE_PREFIX', 'weather'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Location
    |--------------------------------------------------------------------------
    |
    | Default location for weather data when no specific location is provided.
    |
    */

    'default_location' => [
        'city' => env('WEATHER_DEFAULT_CITY', 'Jakarta'),
        'country' => env('WEATHER_DEFAULT_COUNTRY', 'ID'),
        'lat' => env('WEATHER_DEFAULT_LAT', -6.2088),
        'lon' => env('WEATHER_DEFAULT_LON', 106.8456),
    ],

];