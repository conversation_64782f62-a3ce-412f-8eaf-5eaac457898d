# WordPress Import Feature

## Overview
Fitur import WordPress memungkinkan Super Admin untuk mengimpor artikel dari database WordPress ke dalam sistem Lambe Turah News dengan mudah dan aman.

## Features
- ✅ Import artikel dari file SQL WordPress
- ✅ Preview data sebelum import
- ✅ Mapping status WordPress ke status lokal
- ✅ Skip artikel duplikat
- ✅ **Deteksi kategori WordPress yang hilang**
- ✅ **Cache kategori yang hilang untuk review admin**
- ✅ **Notifikasi otomatis ke Super Admin**
- ✅ **Saran mapping kategori dengan similarity scoring**
- ✅ **Bulk creation kategori yang hilang**
- ✅ Validasi file dan data
- ✅ Progress tracking
- ✅ Error handling dan rollback
- ✅ Command line interface
- ✅ Unit testing

## Installation & Setup

### 1. File Structure
```
app/
├── Http/Controllers/FE/ImportController.php
├── Http/Requests/ImportWordPressRequest.php
├── Services/WordPressImportService.php
└── Console/Commands/ImportWordPressCommand.php

resources/views/admin/import/
└── index.blade.php

tests/Feature/
└── WordPressImportTest.php

storage/app/examples/
└── wordpress-sample.sql

docs/
└── wordpress-import-guide.md
```

### 2. Routes
Routes sudah ditambahkan di `routes/web.php`:
```php
// Super Admin only
Route::get("/admin/import", [ImportController::class, "index"])->name("admin.import.index");
Route::post("/admin/import/wordpress", [ImportController::class, "importWordPress"])->name("admin.import.wordpress");
Route::post("/admin/import/preview", [ImportController::class, "previewImport"])->name("admin.import.preview");
```

### 3. Menu Navigation
Menu "Import WordPress" sudah ditambahkan di sidebar admin untuk Super Admin.

## Usage

### Web Interface
1. Login sebagai Super Admin
2. Klik menu "Import WordPress" di sidebar
3. Upload file SQL WordPress
4. Domain akan auto-detect dari SQL (bisa diedit untuk download gambar)
5. Konfigurasi pengaturan import
6. Preview data (opsional)
7. Mulai import

### Command Line
```bash
# Preview import
php artisan import:wordpress /path/to/wordpress.sql --dry-run

# Import dengan opsi
php artisan import:wordpress /path/to/wordpress.sql \
  --fallback-category=1 \
  --skip-duplicates \
  --no-auto-create-categories

# Interactive mode (recommended)
php artisan import:wordpress /path/to/wordpress.sql

# Manage missing categories
php artisan import:missing-categories list
php artisan import:missing-categories create --all
php artisan import:missing-categories create --categories="Technology,Sports"
php artisan import:missing-categories clear
```

## Configuration Options

### Status Mapping
```php
'post_status_mapping' => [
    'publish' => 'Accept',    // Published posts
    'draft' => 'Pending',     // Draft posts  
    'private' => 'Pending'    // Private posts
]
```

### Import Options
- **WordPress Domain**: Auto-detected dari SQL, bisa diedit untuk download gambar
- **Skip Duplicates**: Lewati artikel yang sudah ada (berdasarkan title + date)
- **Auto-create Missing Categories**: Otomatis buat kategori yang hilang (default: enabled)
- **Fallback Category**: Kategori cadangan jika auto-create disabled (optional)
- **Placeholder Author**: Semua artikel menggunakan placeholder author untuk review manual
- **Import Images**: (Experimental) Download gambar dari domain yang ditentukan

## Missing Categories Management

### Automatic Detection
Sistem secara otomatis mendeteksi kategori WordPress yang tidak ada di sistem lokal:
- **Parsing wp_terms & wp_term_taxonomy**: Extract kategori dari database WordPress
- **Similarity Matching**: Cari kategori lokal yang mirip (threshold 70%)
- **Cache Storage**: Simpan kategori yang hilang dalam cache untuk review
- **Admin Notification**: Kirim notifikasi ke Super Admin

### Category Mapping Process
1. **Exact Match**: Cari kategori dengan nama yang sama persis
2. **Similarity Scoring**: Gunakan Levenshtein distance untuk similarity
3. **High-Confidence Suggestions**: Kategori dengan similarity ≥80%
4. **Creation Recommendations**: Kategori yang perlu dibuat baru

### Web Interface Features
- **Missing Categories Alert**: Banner di halaman import
- **Category Analysis**: Detail kategori yang hilang dalam preview
- **Bulk Creation**: Buat multiple kategori sekaligus
- **Mapping Suggestions**: Lihat saran mapping dengan confidence score
- **Cache Management**: Clear cache kategori yang hilang

### Cache Structure
```php
'wordpress_import_missing_categories_TIMESTAMP' => [
    'timestamp' => '2024-01-01T10:00:00Z',
    'missing_categories' => [
        ['name' => 'Technology', 'id' => 1, 'slug' => 'technology'],
        // ...
    ],
    'suggested_mappings' => [
        [
            'wp_category' => 'Tech News',
            'suggested_local' => ['id' => 5, 'name' => 'Teknologi'],
            'similarity' => 0.85
        ],
        // ...
    ],
    'total_missing' => 5,
    'total_suggestions' => 2
]
```

## Image Import (Experimental)

### How It Works
1. **Domain Detection**: Sistem auto-detect domain dari SQL file (wp_options atau guid)
2. **Editable Domain**: Admin bisa edit domain di form jika berbeda
3. **Image Download**: Download featured image dari `https://{domain}/wp-content/uploads/...`
4. **Placeholder Fallback**: Jika gagal download, gunakan placeholder image
5. **Admin Notification**: Notifikasi jumlah artikel dengan placeholder image

### Domain Detection Process
```php
// Priority 1: wp_options table
SELECT option_value FROM wp_options WHERE option_name IN ('siteurl', 'home')

// Priority 2: post guid URLs
SELECT guid FROM wp_posts WHERE guid LIKE 'http%' LIMIT 1

// Fallback: lambeturah.com
```

### Image Download Process
```php
// Original URL dari SQL: http://old-domain.com/wp-content/uploads/2024/01/image.jpg
// Modified URL: https://lambeturah.com/wp-content/uploads/2024/01/image.jpg

if (download_success) {
    $news->image = 'news-images/article-slug-timestamp.jpg';
} else {
    $news->image = 'img/noimg.jpg'; // Menggunakan placeholder yang sudah ada
    $images_not_found++;
}
```

### Placeholder Image Features
- **Static placeholder**: `public/img/noimg.jpg` (menggunakan yang sudah ada)
- **Admin notification**: Alert untuk artikel yang perlu gambar manual
- **Alt text**: "Image not available - Please upload manually"
- **Seamless integration**: Menggunakan placeholder yang sudah terintegrasi dengan sistem

## Data Mapping

| WordPress Field | Lambe Turah Field | Notes |
|----------------|-------------------|-------|
| post_title | title | Required |
| post_content | content | Required |
| post_date | created_at | Timestamp |
| post_modified | updated_at | Timestamp |
| post_status | status | Via mapping |
| post_author | user_id | Placeholder author |
| wp_terms.name | category_id | Auto-detected/mapped |
| featured_media | image | Downloaded or placeholder |
| - | views | Default: 0 |

## Security Features

### Access Control
- Hanya Super Admin yang dapat mengakses
- Form Request validation
- CSRF protection

### File Validation
- Ekstensi file: .sql, .txt
- Ukuran maksimal: 50MB
- Validasi struktur SQL

### Data Safety
- Database transactions
- Automatic rollback on error
- Duplicate detection
- Input sanitization

## Error Handling

### Common Errors
1. **File tidak valid**: Periksa format dan ekstensi
2. **Kategori tidak ditemukan**: Pilih kategori yang valid
3. **User tidak ditemukan**: Pilih user yang valid
4. **SQL parsing error**: Periksa struktur file SQL

### Logging
Semua error dicatat di `storage/logs/laravel.log`:
```
[2024-01-01 10:00:00] local.ERROR: WordPress import failed: ...
[2024-01-01 10:00:00] local.ERROR: Import preview failed: ...
```

## Testing

### Run Tests
```bash
# Run all import tests
php artisan test --filter=WordPressImportTest

# Run specific test
php artisan test --filter=it_can_import_wordpress_posts
```

### Test Coverage
- ✅ Preview functionality
- ✅ Import process
- ✅ Duplicate detection
- ✅ Access control
- ✅ Validation
- ✅ Error handling

## Performance Considerations

### Large Files
- File besar (>10MB) akan memakan waktu lama
- Gunakan command line untuk file besar
- Monitor memory usage

### Optimization Tips
- Import di luar jam sibuk
- Gunakan skip duplicates untuk import ulang
- Split file besar jadi beberapa bagian
- Monitor database performance

## Sample Data
File contoh tersedia di `storage/app/examples/wordpress-sample.sql` untuk testing.

## Troubleshooting

### Memory Issues
```php
// Increase memory limit in php.ini
memory_limit = 512M

// Or in code
ini_set('memory_limit', '512M');
```

### Timeout Issues
```php
// Increase execution time
set_time_limit(300); // 5 minutes
```

### Database Issues
```bash
# Check database connection
php artisan tinker
>>> DB::connection()->getPdo();

# Check table structure
>>> Schema::hasTable('news');
```

## Future Enhancements
- [ ] Import kategori WordPress
- [ ] Import gambar featured
- [ ] Import komentar
- [ ] Import meta data
- [ ] Batch processing untuk file besar
- [ ] Progress bar real-time
- [ ] Export mapping configuration

## Support
Untuk bantuan lebih lanjut:
1. Periksa log aplikasi
2. Review dokumentasi di `docs/wordpress-import-guide.md`
3. Jalankan test untuk memastikan functionality
4. Konsultasi dengan developer team
