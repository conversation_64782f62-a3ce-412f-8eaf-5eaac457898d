<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ImportCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            'WordPress Import',
            'Berita Umum',
            'Artikel',
            'Tutorial',
            'Review',
            'Opini',
            '<PERSON><PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            'Breaking News',
            'Update'
        ];

        foreach ($categories as $categoryName) {
            Category::firstOrCreate(
                ['name' => $categoryName],
                [
                    'name' => $categoryName,
                    'slug' => Str::slug($categoryName),
                    'views' => 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }
    }
}
