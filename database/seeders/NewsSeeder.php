<?php

namespace Database\Seeders;

use App\Models\News;
use App\Models\User;
use App\Models\Like;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class NewsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles and permissions first
        $this->createRolesAndPermissions();
        
        // Create categories
        $this->createCategories();
        
        // Create tags
        $this->createTags();
        
        // Create users
        $this->createUsers();
        
        // Create news articles
        $this->createNewsArticles();
        
        // <PERSON>reate likes
        $this->createLikes();
    }

    private function createRolesAndPermissions(): void
    {
        // Create roles if they don't exist
        $roles = ['Super Admin', 'Editor', 'Writer'];
        foreach ($roles as $roleName) {
            Role::firstOrCreate(['name' => $roleName], ['name' => $roleName]);
        }

        // Create permissions if they don't exist
        $permissions = [
            'Create News', 'Store News', 'Edit News', 'Update News', 'Draft',
            'Status News', 'Update Status News'
        ];
        foreach ($permissions as $permissionName) {
            Permission::firstOrCreate(['name' => $permissionName], ['name' => $permissionName]);
        }

        // Assign permissions to roles
        $writerRole = Role::query()->where('name', 'Writer')->first();
        $editorRole = Role::query()->where('name', 'Editor')->first();
        $adminRole = Role::query()->where('name', 'Super Admin')->first();

        if ($writerRole) {
            $writerRole->givePermissionTo(['Create News', 'Store News', 'Edit News', 'Update News', 'Draft']);
        }

        if ($editorRole) {
            $editorRole->givePermissionTo(['Status News', 'Update Status News']);
        }
    }

    private function createCategories(): void
    {
        $categories = [
            ['name' => 'Politik', 'views' => rand(1000, 5000)],
            ['name' => 'Ekonomi', 'views' => rand(800, 4000)],
            ['name' => 'Selebriti', 'views' => rand(2000, 8000)],
            ['name' => 'Olahraga', 'views' => rand(1500, 6000)],
            ['name' => 'Teknologi', 'views' => rand(1200, 5000)],
            ['name' => 'Kesehatan', 'views' => rand(900, 4500)],
            ['name' => 'Pendidikan', 'views' => rand(700, 3500)],
            ['name' => 'Hiburan', 'views' => rand(1800, 7000)],
            ['name' => 'Lifestyle', 'views' => rand(1300, 5500)],
            ['name' => 'Otomotif', 'views' => rand(1000, 4000)],
            ['name' => 'Kuliner', 'views' => rand(1100, 4500)],
            ['name' => 'Travel', 'views' => rand(900, 4000)],
            ['name' => 'Fashion', 'views' => rand(1200, 5000)],
            ['name' => 'Music', 'views' => rand(1400, 5500)],
            ['name' => 'Film & Series', 'views' => rand(1600, 6000)],
            ['name' => 'Viral', 'views' => rand(3000, 10000)],
            ['name' => 'Breaking News', 'views' => rand(2500, 9000)],
            ['name' => 'Internasional', 'views' => rand(1800, 7000)],
        ];

        foreach ($categories as $category) {
            Category::updateOrCreate(['name' => $category['name']], $category);
        }
    }

    private function createUsers(): void
    {
        // Create admin user
        $admin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('password'),
                'bio' => 'Administrator of Lambe Turah News',
                'email_verified_at' => now(),
            ]
        );
        $adminRole = Role::query()->where('name', 'Super Admin')->first();
        if ($adminRole) {
            $admin->assignRole($adminRole);
        }

        // Create editor user
        $editor = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'News Editor',
                'password' => Hash::make('password'),
                'bio' => 'Senior News Editor',
                'email_verified_at' => now(),
            ]
        );
        $editorRole = Role::query()->where('name', 'Editor')->first();
        if ($editorRole) {
            $editor->assignRole($editorRole);
        }

        // Create writers
        $writers = [
            ['name' => 'Ahmad Santoso', 'email' => '<EMAIL>', 'bio' => 'Political News Writer'],
            ['name' => 'Siti Rahayu', 'email' => '<EMAIL>', 'bio' => 'Entertainment News Writer'],
            ['name' => 'Budi Prakoso', 'email' => '<EMAIL>', 'bio' => 'Sports News Writer'],
            ['name' => 'Maya Sari', 'email' => '<EMAIL>', 'bio' => 'Technology News Writer'],
            ['name' => 'Rizki Fadli', 'email' => '<EMAIL>', 'bio' => 'Celebrity News Writer'],
        ];

        $writerRole = Role::query()->where('name', 'Writer')->first();
        foreach ($writers as $writerData) {
            $writer = User::updateOrCreate(
                ['email' => $writerData['email']],
                [
                    'name' => $writerData['name'],
                    'password' => Hash::make('password'),
                    'bio' => $writerData['bio'],
                    'email_verified_at' => now(),
                ]
            );
            if ($writerRole) {
                $writer->assignRole($writerRole);
            }
        }

        // Create additional random users
        User::factory(15)->create();
    }

    private function createNewsArticles(): void
    {
        $users = User::all();
        $categories = Category::all();

        $newsData = [
            [
                'title' => 'Viral Presiden Francis Macron Ditegur Istri di Pintu Pesawat saat Tiba di Vietnam',
                'content' => 'Paris - Sebuah video viral menunjukkan momen ketika Presiden Prancis Emmanuel Macron ditegur oleh istrinya, Brigitte Macron, di pintu pesawat saat tiba di Vietnam untuk kunjungan kenegaraan. Video tersebut dengan cepat menyebar di media sosial dan menjadi pembicaraan hangat di kalangan netizen.\n\nDalam video yang berdurasi beberapa detik tersebut, terlihat Brigitte Macron berbisik sesuatu kepada sang suami sebelum mereka turun dari pesawat. Gestur dan ekspresi wajah Brigitte terlihat seolah-olah sedang mengingatkan atau menegur Macron tentang sesuatu.\n\nMeski tidak jelas apa yang dibicarakan pasangan ini, netizen di berbagai platform media sosial memberikan interpretasi beragam. Banyak yang menganggap momen tersebut lucu dan menunjukkan sisi humanis dari pasangan pemimpin dunia.\n\nIstana Élysée belum memberikan komentar resmi terkait video viral ini. Kunjungan Macron ke Vietnam dijadwalkan berlangsung selama tiga hari untuk membahas kerja sama bilateral di berbagai bidang.',
                'category' => 'Viral',
                'views' => rand(5000, 15000),
                'status' => 'Accept'
            ],
            [
                'title' => 'Rupiah Kembali Menguat, Dollar AS Makin Terpuruk',
                'content' => 'Jakarta - Nilai tukar rupiah terhadap dollar Amerika Serikat (AS) kembali mengalami penguatan signifikan pada perdagangan hari ini. Rupiah ditutup menguat 0,8% ke level Rp 15.250 per dollar AS, melanjutkan tren positif yang telah berlangsung selama beberapa pekan terakhir.\n\nMenguatnya rupiah didorong oleh beberapa faktor fundamental yang positif, antara lain membaiknya neraca perdagangan Indonesia, inflasi yang terkendali, dan stabilitas politik domestik. Bank Indonesia juga berhasil menjaga stabilitas nilai tukar melalui intervensi yang tepat sasaran.\n\nAnalis ekonomi dari berbagai lembaga keuangan memprediksi bahwa tren penguatan rupiah masih akan berlanjut hingga akhir tahun. Hal ini didukung oleh proyeksi pertumbuhan ekonomi Indonesia yang optimis dan meningkatnya kepercayaan investor asing.\n\nDi sisi lain, dollar AS mengalami tekanan akibat ketidakpastian kebijakan Federal Reserve dan kondisi ekonomi global yang masih volatile. Investor cenderung mencari safe haven di mata uang emerging market yang fundamental ekonominya kuat.',
                'category' => 'Ekonomi',
                'views' => rand(3000, 8000),
                'status' => 'Accept'
            ],
            [
                'title' => 'CEO Netflix Kaget Lihat Hasil Survey Ini, Ternyata Acara Asli Indonesia Yang Paling Populer',
                'content' => 'Los Angeles - CEO Netflix, Reed Hastings, mengungkapkan rasa terkejutnya melihat hasil survei terbaru yang menunjukkan bahwa konten asli Indonesia mendominasi daftar tayangan paling populer di platform streaming tersebut, bahkan mengalahkan serial asal Korea Selatan dan Amerika.\n\nDalam sebuah wawancara eksklusif, Hastings menyebutkan bahwa serial "Gadis Kretek" dan "The Big 4" berhasil meraih posisi teratas dalam kategori most-watched content di wilayah Asia Tenggara. "Ini benar-benar di luar ekspektasi kami," ungkap Hastings.\n\nData internal Netflix menunjukkan bahwa konten lokal Indonesia telah ditonton lebih dari 50 juta jam tayang dalam sebulan terakhir. Kesuksesan ini mendorong Netflix untuk meningkatkan investasi dalam produksi konten original Indonesia.\n\n"Kami akan menggandakan anggaran untuk konten Indonesia tahun depan," tambah Hastings. Netflix berencana memproduksi 15 judul baru dari Indonesia, termasuk serial drama, komedi, dan dokumenter yang akan dirilis sepanjang 2024.',
                'category' => 'Hiburan',
                'views' => rand(4000, 12000),
                'status' => 'Accept'
            ],
            [
                'title' => 'Viral Video Ibu-Ibu Borong Minyak Goreng di Minimarket, Sampai Antre Panjang',
                'content' => 'Surabaya - Sebuah video yang memperlihatkan puluhan ibu-ibu berbondong-bondong membeli minyak goreng di sebuah minimarket viral di media sosial. Video tersebut menunjukkan antrean panjang yang mengular hingga ke luar toko, dengan setiap pembeli membawa beberapa botol minyak goreng.\n\nKejadian ini bermula dari beredarnya informasi bahwa minimarket tersebut menjual minyak goreng dengan harga jauh lebih murah dari harga pasar. Dalam waktu singkat, ratusan pembeli berbondong-bondong datang untuk mendapatkan minyak goreng dengan harga terjangkau.\n\nPemilik minimarket, Pak Sutrisno, menjelaskan bahwa mereka memang sedang mengadakan program diskon besar-besaran untuk minyak goreng sebagai bentuk apresiasi kepada pelanggan setia. "Kami tidak menyangka responnya akan sebesar ini," ujarnya.\n\nVideo viral tersebut memicu beragam reaksi netizen. Sebagian memuji inisiatif minimarket yang peduli dengan kondisi ekonomi masyarakat, namun ada juga yang mengkritik perilaku panic buying yang terlihat dalam video.',
                'category' => 'Viral',
                'views' => rand(6000, 18000),
                'status' => 'Accept'
            ],
            [
                'title' => 'Jokowi Resmi Lantik 17 Duta Besar RI untuk Berbagai Negara',
                'content' => 'Jakarta - Presiden Joko Widodo resmi melantik 17 Duta Besar Republik Indonesia yang akan bertugas di berbagai negara. Upacara pelantikan berlangsung di Istana Negara dengan protokol kesehatan yang ketat dan dihadiri oleh sejumlah pejabat tinggi negara.\n\nKe-17 duta besar yang dilantik akan bertugas di negara-negara strategis seperti Amerika Serikat, China, Jepang, Australia, Inggris, Jerman, dan beberapa negara di Afrika dan Amerika Latin. Mereka dipilih berdasarkan kompetensi, pengalaman diplomatik, dan pemahaman mendalam tentang negara tujuan.\n\nDalam sambutan pelantikan, Presiden Jokowi menekankan pentingnya diplomasi ekonomi dan peningkatan kerja sama bilateral. "Para duta besar tidak hanya bertugas sebagai wakil pemerintah, tetapi juga sebagai agen promosi ekonomi Indonesia," tegas Presiden.\n\nMenteri Luar Negeri Retno Marsudi menyampaikan bahwa pelantikan ini merupakan bagian dari upaya revitalisasi diplomasi Indonesia di tengah dinamika geopolitik global yang semakin kompleks.',
                'category' => 'Politik',
                'views' => rand(2500, 7000),
                'status' => 'Accept'
            ],
            [
                'title' => 'Ganjar Pranowo Janji Akan Fokus pada Pendidikan dan Kesehatan Jika Terpilih',
                'content' => 'Semarang - Calon Presiden Ganjar Pranowo menegaskan komitmennya untuk memprioritaskan sektor pendidikan dan kesehatan jika terpilih menjadi Presiden RI periode 2024-2029. Pernyataan ini disampaikan dalam kampanye terbuka di Lapangan Simpang Lima, Semarang, yang dihadiri ribuan pendukung.\n\nGanjar memaparkan visi dan misinya untuk meningkatkan kualitas pendidikan Indonesia melalui digitalisasi pembelajaran, peningkatan kesejahteraan guru, dan pemerataan akses pendidikan berkualitas hingga ke daerah terpencil. "Pendidikan adalah investasi terbaik untuk masa depan bangsa," ungkap mantan Gubernur Jawa Tengah ini.\n\nDi bidang kesehatan, Ganjar berjanji akan memperkuat sistem kesehatan nasional dengan menambah jumlah tenaga medis, memodernisasi fasilitas kesehatan, dan memperluas jangkauan layanan kesehatan gratis. Program Kartu Indonesia Sehat akan diperluas dengan layanan yang lebih komprehensif.\n\nRencana konkret lainnya meliputi pembangunan 1000 sekolah baru, penambahan 50.000 guru, dan pembangunan 500 puskesmas di daerah terpencil dalam lima tahun pertama masa kepemimpinannya.',
                'category' => 'Politik',
                'views' => rand(3000, 9000),
                'status' => 'Accept'
            ],
            [
                'title' => 'Prabowo Subianto Tegaskan Komitmen untuk Melanjutkan Program Jokowi',
                'content' => 'Jakarta - Calon Presiden Prabowo Subianto menegaskan komitmennya untuk melanjutkan dan mengembangkan program-program strategis yang telah diinisiasi oleh Presiden Joko Widodo. Pernyataan ini disampaikan dalam pertemuan dengan para pengusaha dan investor di Jakarta.\n\nPrabowo menyebutkan bahwa program infrastruktur, IKN (Ibu Kota Nusantara), dan downstreaming industri akan terus dilanjutkan dengan penyempurnaan-penyempurnaan yang diperlukan. "Kita tidak akan membuang-buang pencapaian yang sudah ada, tetapi akan mengoptimalkannya," tegas Prabowo.\n\nMantan Menteri Pertahanan ini juga memaparkan rencana untuk memperkuat pertahanan dan keamanan nasional, serta meningkatkan swasembada pangan melalui modernisasi sektor pertanian. Program ketahanan pangan akan menjadi prioritas utama untuk menjamin stabilitas sosial dan ekonomi.\n\nDalam kesempatan yang sama, Prabowo juga menyampaikan visinya untuk menjadikan Indonesia sebagai kekuatan ekonomi besar dunia melalui pemanfaatan sumber daya alam yang berkelanjutan dan pengembangan industri berbasis teknologi tinggi.',
                'category' => 'Politik',
                'views' => rand(2800, 8500),
                'status' => 'Accept'
            ]
        ];

        foreach ($newsData as $article) {
            $category = $categories->where('name', $article['category'])->first();
            $author = $users->random();
            
            News::query()->create([
                'title' => $article['title'],
                'content' => $article['content'],
                'user_id' => $author->id,
                'category_id' => $category->id,
                'views' => $article['views'],
                'status' => $article['status'],
                'created_at' => now()->subDays(rand(1, 30)),
                'updated_at' => now()->subDays(rand(1, 30)),
            ]);
        }

        // Create additional random news
        News::factory(50)->create([
            'user_id' => fn() => $users->random()->id,
            'category_id' => fn() => $categories->random()->id,
        ]);
    }

    private function createTags(): void
    {
        $tags = [
            ['name' => 'Breaking News', 'count' => rand(500, 2000)],
            ['name' => 'Viral', 'count' => rand(800, 1500)],
            ['name' => 'Trending', 'count' => rand(600, 1200)],
            ['name' => 'Politik', 'count' => rand(400, 1000)],
            ['name' => 'Ekonomi', 'count' => rand(300, 800)],
            ['name' => 'Selebriti', 'count' => rand(700, 1600)],
            ['name' => 'Olahraga', 'count' => rand(500, 1200)],
            ['name' => 'Teknologi', 'count' => rand(400, 1000)],
            ['name' => 'Kesehatan', 'count' => rand(300, 700)],
            ['name' => 'Pendidikan', 'count' => rand(200, 600)],
            ['name' => 'Hiburan', 'count' => rand(600, 1400)],
            ['name' => 'Lifestyle', 'count' => rand(400, 900)],
            ['name' => 'Kuliner', 'count' => rand(350, 800)],
            ['name' => 'Travel', 'count' => rand(300, 700)],
            ['name' => 'Fashion', 'count' => rand(400, 800)],
            ['name' => 'Music', 'count' => rand(450, 900)],
            ['name' => 'Film', 'count' => rand(500, 1000)],
            ['name' => 'Drama Korea', 'count' => rand(600, 1200)],
            ['name' => 'K-Pop', 'count' => rand(700, 1400)],
            ['name' => 'Hollywood', 'count' => rand(400, 800)],
            ['name' => 'Sepak Bola', 'count' => rand(800, 1600)],
            ['name' => 'Basket', 'count' => rand(300, 700)],
            ['name' => 'Badminton', 'count' => rand(400, 800)],
            ['name' => 'MotoGP', 'count' => rand(500, 1000)],
            ['name' => 'Liga Indonesia', 'count' => rand(600, 1200)],
            ['name' => 'Pemilu 2024', 'count' => rand(800, 1500)],
            ['name' => 'Jokowi', 'count' => rand(600, 1200)],
            ['name' => 'Jakarta', 'count' => rand(500, 1000)],
            ['name' => 'Bali', 'count' => rand(400, 800)],
            ['name' => 'Corona', 'count' => rand(300, 600)],
            ['name' => 'Vaksin', 'count' => rand(250, 500)],
            ['name' => 'Startup', 'count' => rand(300, 700)],
            ['name' => 'AI', 'count' => rand(400, 800)],
            ['name' => 'Cryptocurrency', 'count' => rand(350, 700)],
            ['name' => 'iPhone', 'count' => rand(500, 1000)],
            ['name' => 'Gaming', 'count' => rand(600, 1200)],
            ['name' => 'TikTok', 'count' => rand(800, 1600)],
            ['name' => 'Instagram', 'count' => rand(700, 1400)],
            ['name' => 'YouTube', 'count' => rand(600, 1200)],
            ['name' => 'WhatsApp', 'count' => rand(400, 800)]
        ];

        foreach ($tags as $tag) {
            Tag::updateOrCreate(['name' => $tag['name']], $tag);
        }
    }

    private function createLikes(): void
    {
        $news = News::query()->where('status', 'Accept')->get();
        
        foreach ($news as $article) {
            // Create random number of likes for each article
            $likeCount = rand(5, 100);
            
            for ($i = 0; $i < $likeCount; $i++) {
                Like::query()->create([
                    'news_id' => $article->id,
                    'device_id' => fake()->uuid(),
                    'created_at' => fake()->dateTimeBetween($article->created_at, 'now'),
                ]);
            }
        }
    }
}