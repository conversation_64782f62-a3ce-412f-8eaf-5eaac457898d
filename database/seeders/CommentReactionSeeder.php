<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\News;
use App\Models\User;
use App\Models\Comment;
use App\Models\Reaction;
use Illuminate\Support\Str;

class CommentReactionSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $news = News::where('status', 'Accept')->get();
        $users = User::all();
        
        if ($news->isEmpty() || $users->isEmpty()) {
            $this->command->warn('No news or users found. Please run NewsSeeder and UserSeeder first.');
            return;
        }

        $reactionTypes = ['like', 'love', 'wow', 'haha', 'sad', 'angry'];
        
        $sampleComments = [
            'Berita yang sangat menarik! Terima kasih atas informasinya.',
            'Wah, ini benar-benar mengejutkan. Tidak menyangka hal ini bisa terjadi.',
            'Semoga situasi ini segera membaik dan ada solusi yang tepat.',
            'Informasi yang sangat bermanfaat. Saya menung<PERSON> update selanjutnya.',
            'Artikel yang ditulis dengan sangat baik dan mudah dipahami.',
            'Ini sangat penting untuk diketahui banyak orang. Terima kasih sudah berbagi.',
            'Saya setuju dengan pendapat ini. Hal serupa juga terjadi di daerah saya.',
            'Data dan faktanya sangat akurat. Jurnalisme yang berkualitas!',
            'Mudah-mudahan pemerintah segera mengambil tindakan yang tepat.',
            'Artikel ini membuka mata saya tentang isu yang sedang berkembang.',
            'Penjelasannya sangat detail dan komprehensif. Excellent!',
            'Ini bisa menjadi pelajaran berharga untuk kita semua.',
            'Saya berharap ada tindak lanjut dari berita ini.',
            'Terima kasih telah menyajikan berita yang berimbang.',
            'Situasi yang memang perlu mendapat perhatian serius.',
        ];

        $sampleReplies = [
            'Saya sepakat dengan pendapat Anda.',
            'Terima kasih atas tanggapannya yang konstruktif.',
            'Poin yang sangat bagus! Saya tidak memikirkan itu sebelumnya.',
            'Benar sekali, hal ini memang perlu diperhatikan.',
            'Saya memiliki pengalaman serupa dengan yang Anda ceritakan.',
            'Mungkin kita bisa diskusi lebih lanjut tentang hal ini.',
            'Informasi yang Anda berikan sangat membantu.',
            'Saya kurang setuju dengan pendapat ini, tapi menghargai perspektif Anda.',
            'Data yang Anda sebutkan menarik, boleh tahu sumbernya?',
            'Semoga kedepannya ada solusi yang lebih baik.',
        ];

        $guestNames = [
            'Ahmad Rizki', 'Siti Nurhaliza', 'Budi Santoso', 'Dewi Lestari', 'Eko Prasetyo',
            'Fitri Handayani', 'Gunawan Wijaya', 'Hesti Purwanti', 'Indra Kusuma', 'Joko Widodo',
            'Kartika Sari', 'Lukman Hakim', 'Maya Sari', 'Nanda Pratama', 'Oka Mahendra'
        ];

        $this->command->info('Creating comments and reactions...');

        // Create comments for each news article
        foreach ($news as $newsItem) {
            $commentCount = rand(5, 15);
            $parentComments = [];

            // Create parent comments
            for ($i = 0; $i < $commentCount; $i++) {
                $isGuest = rand(1, 100) <= 30; // 30% chance of guest comment
                
                $commentData = [
                    'news_id' => $newsItem->id,
                    'content' => $sampleComments[array_rand($sampleComments)],
                    'is_approved' => rand(1, 100) <= 95, // 95% approved
                    'created_at' => now()->subDays(rand(0, 30))->subHours(rand(0, 23)),
                ];

                if ($isGuest) {
                    $commentData['device_id'] = Str::uuid()->toString();
                    $commentData['guest_name'] = $guestNames[array_rand($guestNames)];
                    $commentData['guest_email'] = strtolower(str_replace(' ', '.', $commentData['guest_name'])) . '@example.com';
                } else {
                    $commentData['user_id'] = $users->random()->id;
                }

                $comment = Comment::create($commentData);
                $parentComments[] = $comment;

                // Add reactions to this comment
                $reactionCount = rand(0, 8);
                $reactedUsers = [];
                
                for ($j = 0; $j < $reactionCount; $j++) {
                    $reactionIsGuest = rand(1, 100) <= 40; // 40% chance of guest reaction
                    
                    $reactionData = [
                        'reactable_type' => Comment::class,
                        'reactable_id' => $comment->id,
                        'reaction_type' => $reactionTypes[array_rand($reactionTypes)],
                    ];

                    if ($reactionIsGuest) {
                        $deviceId = Str::uuid()->toString();
                        if (!in_array('guest_' . $deviceId, $reactedUsers)) {
                            $reactionData['device_id'] = $deviceId;
                            $reactedUsers[] = 'guest_' . $deviceId;
                            Reaction::create($reactionData);
                        }
                    } else {
                        $userId = $users->random()->id;
                        if (!in_array('user_' . $userId, $reactedUsers)) {
                            $reactionData['user_id'] = $userId;
                            $reactedUsers[] = 'user_' . $userId;
                            Reaction::create($reactionData);
                        }
                    }
                }
            }

            // Create replies for some parent comments
            $replyCount = rand(2, 6);
            for ($i = 0; $i < $replyCount && !empty($parentComments); $i++) {
                $parentComment = $parentComments[array_rand($parentComments)];
                $isGuest = rand(1, 100) <= 25; // 25% chance of guest reply

                $replyData = [
                    'news_id' => $newsItem->id,
                    'parent_id' => $parentComment->id,
                    'content' => $sampleReplies[array_rand($sampleReplies)],
                    'is_approved' => rand(1, 100) <= 98, // 98% approved for replies
                    'created_at' => $parentComment->created_at->addMinutes(rand(10, 1440)),
                ];

                if ($isGuest) {
                    $replyData['device_id'] = Str::uuid()->toString();
                    $replyData['guest_name'] = $guestNames[array_rand($guestNames)];
                    $replyData['guest_email'] = strtolower(str_replace(' ', '.', $replyData['guest_name'])) . '@example.com';
                } else {
                    $replyData['user_id'] = $users->random()->id;
                }

                $reply = Comment::create($replyData);

                // Add reactions to this reply
                $reactionCount = rand(0, 5);
                $reactedUsers = [];
                
                for ($j = 0; $j < $reactionCount; $j++) {
                    $reactionIsGuest = rand(1, 100) <= 40;
                    
                    $reactionData = [
                        'reactable_type' => Comment::class,
                        'reactable_id' => $reply->id,
                        'reaction_type' => $reactionTypes[array_rand($reactionTypes)],
                    ];

                    if ($reactionIsGuest) {
                        $deviceId = Str::uuid()->toString();
                        if (!in_array('guest_' . $deviceId, $reactedUsers)) {
                            $reactionData['device_id'] = $deviceId;
                            $reactedUsers[] = 'guest_' . $deviceId;
                            Reaction::create($reactionData);
                        }
                    } else {
                        $userId = $users->random()->id;
                        if (!in_array('user_' . $userId, $reactedUsers)) {
                            $reactionData['user_id'] = $userId;
                            $reactedUsers[] = 'user_' . $userId;
                            Reaction::create($reactionData);
                        }
                    }
                }
            }

            // Add reactions to the news article itself
            $newsReactionCount = rand(10, 50);
            $reactedUsers = [];
            
            for ($i = 0; $i < $newsReactionCount; $i++) {
                $reactionIsGuest = rand(1, 100) <= 50; // 50% chance of guest reaction for news
                
                $reactionData = [
                    'reactable_type' => News::class,
                    'reactable_id' => $newsItem->id,
                    'reaction_type' => $reactionTypes[array_rand($reactionTypes)],
                ];

                if ($reactionIsGuest) {
                    $deviceId = Str::uuid()->toString();
                    if (!in_array('guest_' . $deviceId, $reactedUsers)) {
                        $reactionData['device_id'] = $deviceId;
                        $reactedUsers[] = 'guest_' . $deviceId;
                        Reaction::create($reactionData);
                    }
                } else {
                    $userId = $users->random()->id;
                    if (!in_array('user_' . $userId, $reactedUsers)) {
                        $reactionData['user_id'] = $userId;
                        $reactedUsers[] = 'user_' . $userId;
                        Reaction::create($reactionData);
                    }
                }
            }

            // Update comments count for this news
            $commentsCount = Comment::where('news_id', $newsItem->id)
                ->where('is_approved', true)
                ->count();
            $newsItem->update(['comments_count' => $commentsCount]);
        }

        $totalComments = Comment::count();
        $totalReactions = Reaction::count();

        $this->command->info("Created {$totalComments} comments and {$totalReactions} reactions successfully!");
    }
}