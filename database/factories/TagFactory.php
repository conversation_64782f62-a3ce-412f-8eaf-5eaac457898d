<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Tag>
 */
class TagFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $tags = [
            'Breaking News',
            'Viral',
            'Trending',
            'Politik',
            'Ekonomi',
            '<PERSON><PERSON>bri<PERSON>',
            'Olahraga',
            'Teknologi',
            '<PERSON><PERSON><PERSON><PERSON>',
            'Pendidi<PERSON>',
            'Hiburan',
            'Lifestyle',
            'Kuliner',
            'Travel',
            'Fashion',
            'Music',
            'Film',
            'Drama Korea',
            'K-Pop',
            'Hollywood',
            'Bollywood',
            'Sepak Bola',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>ton',
            'Tenis',
            'MotoGP',
            'Formula 1',
            'Liga Indonesia',
            'Timnas',
            'Piala Dunia',
            'Pemilu 2024',
            'Capres',
            'DPR',
            'Jo<PERSON>wi',
            '<PERSON>rabow<PERSON>',
            '<PERSON><PERSON>jar',
            'An<PERSON>',
            'Jakarta',
            'Sur<PERSON>ya',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            'Bali',
            'Yogyakarta',
            'Corona',
            '<PERSON><PERSON><PERSON>',
            'BPJS',
            'Kesehatan Mental',
            'Diet',
            'Fitness',
            'Yoga',
            'Resep',
            'Makanan Sehat',
            'Street Food',
            'Wisata',
            'Pantai',
            'Gunung',
            'Hotel',
            'Kuliner Nusantara',
            'Startup',
            'AI',
            'Cryptocurrency',
            'NFT',
            'Metaverse',
            'iPhone',
            'Samsung',
            'Android',
            'Gaming',
            'Esports',
            'TikTok',
            'Instagram',
            'YouTube',
            'Facebook',
            'Twitter',
            'WhatsApp',
            'Telegram'
        ];

        return [
            'name' => fake()->unique()->randomElement($tags),
            'count' => fake()->numberBetween(50, 2000),
            'created_at' => fake()->dateTimeBetween('-1 year', 'now'),
            'updated_at' => fake()->dateTimeBetween('-1 year', 'now'),
        ];
    }
}