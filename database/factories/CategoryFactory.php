<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            'Politik',
            'Ekonomi', 
            'Se<PERSON><PERSON><PERSON>',
            'Olahraga',
            'Teknologi',
            'Kesehatan',
            'Pendidikan',
            'Hiburan',
            'Lifestyle',
            'Otomotif',
            'Kuliner',
            'Travel',
            'Fashion',
            'Music',
            'Film & Series',
            'Viral',
            'Breaking News',
            'Internasional'
        ];

        return [
            'name' => fake()->unique()->randomElement($categories),
            'views' => fake()->numberBetween(100, 5000),
            'created_at' => fake()->dateTimeBetween('-1 year', 'now'),
            'updated_at' => fake()->dateTimeBetween('-1 year', 'now'),
        ];
    }
}
