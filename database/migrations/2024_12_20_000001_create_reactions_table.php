<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->uuid('device_id')->nullable();
            $table->morphs('reactable');
            $table->enum('reaction_type', ['like', 'love', 'wow', 'haha', 'sad', 'angry'])->default('like');
            $table->timestamps();
            
            $table->unique(['user_id', 'reactable_id', 'reactable_type']);
            $table->unique(['device_id', 'reactable_id', 'reactable_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reactions');
    }
};