<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For MySQL, we need to alter the enum to include new reaction types
        // Remove default value since reactions are optional
        DB::statement("ALTER TABLE reactions MODIFY COLUMN reaction_type ENUM('like', 'love', 'wow', 'haha', 'sad', 'angry', 'suka', 'benci', 'cinta', 'lucu', 'marah', 'sedih') NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum values with default
        DB::statement("ALTER TABLE reactions MODIFY COLUMN reaction_type ENUM('like', 'love', 'wow', 'haha', 'sad', 'angry') DEFAULT 'like'");
    }
};
