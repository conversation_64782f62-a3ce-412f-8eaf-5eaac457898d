@extends('layouts.admin')

@section('content')
<div class="container">
    <div class="page-inner">
        <div class="page-header">
            <h3 class="fw-bold mb-3">Import WordPress Articles</h3>
            <ul class="breadcrumbs mb-3">
                <li class="nav-home">
                    <a href="{{ route('dashboard') }}">
                        <i class="icon-home"></i>
                    </a>
                </li>
                <li class="separator">
                    <i class="icon-arrow-right"></i>
                </li>
                <li class="nav-item">
                    <a href="#">Admin</a>
                </li>
                <li class="separator">
                    <i class="icon-arrow-right"></i>
                </li>
                <li class="nav-item">
                    <a href="#">Import</a>
                </li>
            </ul>
        </div>

        <!-- Missing Categories Alert -->
        @if($missingCategoriesSummary['has_missing_categories'])
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <h5><i class="fa fa-exclamation-triangle"></i> Missing WordPress Categories Detected</h5>
                    <p>
                        <strong>{{ $missingCategoriesSummary['unique_count'] }}</strong> unique categories from previous WordPress imports are missing from your system.
                        <br>
                        <small class="text-muted">Last import: {{ \Carbon\Carbon::parse($missingCategoriesSummary['latest_import'])->diffForHumans() }}</small>
                    </p>
                    <div class="mt-3">
                        <button type="button" class="btn btn-sm btn-warning" id="viewMissingCategoriesBtn">
                            <i class="fa fa-eye"></i> View Missing Categories
                        </button>
                        <button type="button" class="btn btn-sm btn-success" id="createRecommendedCategoriesBtn">
                            <i class="fa fa-plus"></i> Create Recommended ({{ $categoryRecommendations['creation_recommended'] }})
                        </button>
                        <button type="button" class="btn btn-sm btn-secondary" id="clearCacheBtn">
                            <i class="fa fa-trash"></i> Clear Cache
                        </button>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
        @endif

        @if(count($articlesNeedingAuthors) > 0)
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <h5><i class="fa fa-user-edit"></i> Articles Need Author Assignment</h5>
                    <p>
                        <strong>{{ count($articlesNeedingAuthors) }}</strong> articles from WordPress imports need proper author assignment.
                        <br>
                        <small class="text-muted">These articles are currently assigned to a placeholder author.</small>
                    </p>
                    <div class="mt-3">
                        <a href="{{ route('admin.news.manage') }}" class="btn btn-sm btn-primary">
                            <i class="fa fa-edit"></i> Manage Articles
                        </a>
                        <button type="button" class="btn btn-sm btn-info" id="viewArticlesNeedingAuthorsBtn">
                            <i class="fa fa-eye"></i> View Articles
                        </button>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
        @endif

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">Import WordPress SQL File</div>
                        <div class="card-category">
                            Upload a WordPress database export (SQL file) to import articles
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Import Form -->
                        <form id="importForm" enctype="multipart/form-data">
                            @csrf

                            <!-- File Upload -->
                            <div class="form-group">
                                <label for="sql_file">SQL File <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="sql_file" name="sql_file"
                                       accept=".sql,.txt" required>
                                <small class="form-text text-muted">
                                    Upload WordPress database export file (.sql or .txt). Maximum size: 50MB
                                </small>
                            </div>

                            <!-- WordPress Domain for Images -->
                            <div class="form-group">
                                <label for="wordpress_domain">WordPress Domain (for images)</label>
                                <input type="text" class="form-control" id="wordpress_domain" name="wordpress_domain"
                                       placeholder="lambeturah.com" value="">
                                <small class="form-text text-muted">
                                    Domain will be auto-detected from SQL file. You can modify it if needed for image downloads.
                                </small>
                            </div>

                            <!-- Optional Settings -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="fallback_category_id">Fallback Category (Optional)</label>
                                        <select class="form-control" id="fallback_category_id" name="fallback_category_id">
                                            <option value="">Auto-create or use "WordPress Import"</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                                            @endforeach
                                        </select>
                                        <small class="form-text text-muted">
                                            If not specified, missing categories will be auto-created or use "WordPress Import" category
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Author Assignment</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="auto_assign_authors"
                                                   name="auto_assign_authors" value="1" checked>
                                            <label class="form-check-label" for="auto_assign_authors">
                                                Use placeholder author (requires manual assignment later)
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">
                                            Articles will be assigned to a placeholder author for later review
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Import Options -->
                            <div class="form-group">
                                <label>Import Options</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="skip_duplicates"
                                           name="skip_duplicates" value="1" checked>
                                    <label class="form-check-label" for="skip_duplicates">
                                        Skip duplicate articles (based on title and date)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="create_missing_categories"
                                           name="create_missing_categories" value="1" checked>
                                    <label class="form-check-label" for="create_missing_categories">
                                        Auto-create missing categories
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="import_images"
                                           name="import_images" value="1">
                                    <label class="form-check-label" for="import_images">
                                        Import images (experimental)
                                    </label>
                                </div>
                            </div>

                            <!-- Status Mapping -->
                            <div class="form-group">
                                <label>WordPress Status Mapping</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="status_publish">Published Posts</label>
                                        <select class="form-control" name="post_status_mapping[publish]">
                                            <option value="Accept" selected>Accept</option>
                                            <option value="Pending">Pending</option>
                                            <option value="Reject">Reject</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="status_draft">Draft Posts</label>
                                        <select class="form-control" name="post_status_mapping[draft]">
                                            <option value="Accept">Accept</option>
                                            <option value="Pending" selected>Pending</option>
                                            <option value="Reject">Reject</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="status_private">Private Posts</label>
                                        <select class="form-control" name="post_status_mapping[private]">
                                            <option value="Accept">Accept</option>
                                            <option value="Pending" selected>Pending</option>
                                            <option value="Reject">Reject</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="form-group">
                                <button type="button" class="btn btn-info" id="previewBtn">
                                    <i class="fa fa-eye"></i> Preview Import
                                </button>
                                <button type="submit" class="btn btn-success" id="importBtn" disabled>
                                    <i class="fa fa-upload"></i> Start Import
                                </button>
                            </div>
                        </form>

                        <!-- Progress Bar -->
                        <div id="progressContainer" class="mt-4" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="progressText" class="mt-2 text-center"></div>
                        </div>

                        <!-- Preview Results -->
                        <div id="previewResults" class="mt-4" style="display: none;">
                            <h5>Import Preview</h5>
                            <div id="previewContent"></div>
                        </div>

                        <!-- Import Results -->
                        <div id="importResults" class="mt-4" style="display: none;">
                            <h5>Import Results</h5>
                            <div id="resultsContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Missing Categories Modal -->
        <div class="modal fade" id="missingCategoriesModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Missing WordPress Categories</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="missingCategoriesContent">
                            <div class="text-center">
                                <i class="fa fa-spinner fa-spin"></i> Loading...
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-success" id="createSelectedCategoriesBtn" disabled>
                            <i class="fa fa-plus"></i> Create Selected Categories
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Articles Needing Authors Modal -->
        <div class="modal fade" id="articlesNeedingAuthorsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Articles Needing Author Assignment</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <p><strong>{{ count($articlesNeedingAuthors) }}</strong> articles are currently assigned to a placeholder author and need proper author assignment.</p>
                        </div>

                        @if(count($articlesNeedingAuthors) > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach(array_slice($articlesNeedingAuthors, 0, 10) as $article)
                                    <tr>
                                        <td>
                                            <div class="text-truncate" style="max-width: 200px;" title="{{ $article['title'] }}">
                                                {{ $article['title'] }}
                                            </div>
                                        </td>
                                        <td>{{ $article['category'] }}</td>
                                        <td>
                                            <span class="badge badge-{{ $article['status'] === 'Accept' ? 'success' : ($article['status'] === 'Pending' ? 'warning' : 'secondary') }}">
                                                {{ $article['status'] }}
                                            </span>
                                        </td>
                                        <td>{{ \Carbon\Carbon::parse($article['created_at'])->format('M d, Y') }}</td>
                                        <td>
                                            <a href="{{ route('admin.news.edit', $article['id']) }}" class="btn btn-sm btn-primary" target="_blank">
                                                <i class="fa fa-edit"></i> Edit
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                    @if(count($articlesNeedingAuthors) > 10)
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">
                                            <em>... and {{ count($articlesNeedingAuthors) - 10 }} more articles</em>
                                        </td>
                                    </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                        @endif
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <a href="{{ route('admin.news.manage') }}" class="btn btn-primary">
                            <i class="fa fa-edit"></i> Manage All Articles
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('custom-footer')
<script>
$(document).ready(function() {
    const importForm = $('#importForm');
    const previewBtn = $('#previewBtn');
    const importBtn = $('#importBtn');
    const progressContainer = $('#progressContainer');
    const previewResults = $('#previewResults');
    const importResults = $('#importResults');
    const missingCategoriesModal = $('#missingCategoriesModal');
    const articlesNeedingAuthorsModal = $('#articlesNeedingAuthorsModal');

    // Missing Categories Management
    $('#viewMissingCategoriesBtn').on('click', function() {
        loadMissingCategories();
        missingCategoriesModal.modal('show');
    });

    $('#createRecommendedCategoriesBtn').on('click', function() {
        createRecommendedCategories();
    });

    $('#clearCacheBtn').on('click', function() {
        if (confirm('Are you sure you want to clear the missing categories cache?')) {
            clearMissingCategoriesCache();
        }
    });

    $('#createSelectedCategoriesBtn').on('click', function() {
        createSelectedCategories();
    });

    // Articles Needing Authors Management
    $('#viewArticlesNeedingAuthorsBtn').on('click', function() {
        articlesNeedingAuthorsModal.modal('show');
    });

    // Preview Import
    previewBtn.on('click', function() {
        const formData = new FormData(importForm[0]);
        
        previewBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Previewing...');
        
        $.ajax({
            url: '{{ route("admin.import.preview") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    displayPreview(response.data);
                    importBtn.prop('disabled', false);

                    // Auto-fill WordPress domain if detected
                    if (response.data.wordpress_domain && !$('#wordpress_domain').val()) {
                        $('#wordpress_domain').val(response.data.wordpress_domain);
                    }
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Preview failed';
                showAlert('error', message);
            },
            complete: function() {
                previewBtn.prop('disabled', false).html('<i class="fa fa-eye"></i> Preview Import');
            }
        });
    });

    // Start Import
    importForm.on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        importBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Importing...');
        progressContainer.show();
        
        $.ajax({
            url: '{{ route("admin.import.wordpress") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    displayResults(response.data);
                    showAlert('success', response.message);
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Import failed';
                showAlert('error', message);
            },
            complete: function() {
                importBtn.prop('disabled', false).html('<i class="fa fa-upload"></i> Start Import');
                progressContainer.hide();
            }
        });
    });

    function displayPreview(data) {
        const content = `
            <div class="alert alert-info">
                <h6>Preview Summary</h6>
                <ul class="mb-0">
                    <li>Total Posts Found: <strong>${data.total_posts}</strong></li>
                    <li>Date Range: <strong>${data.date_range.min || 'N/A'} - ${data.date_range.max || 'N/A'}</strong></li>
                    <li>Post Statuses: <strong>${JSON.stringify(data.post_statuses)}</strong></li>
                    ${data.category_analysis ? `
                        <li>WordPress Categories Found: <strong>${data.category_analysis.total_wp_categories}</strong></li>
                        <li>Missing Categories: <strong>${data.category_analysis.missing_categories.length}</strong></li>
                        <li>Suggested Category Mappings: <strong>${data.category_analysis.suggested_mappings.length}</strong></li>
                    ` : ''}
                    ${data.author_analysis ? `
                        <li>WordPress Authors Found: <strong>${data.author_analysis.total_wp_authors}</strong></li>
                        <li>Missing Authors: <AUTHORS>
                        <li>Suggested Author Mappings: <strong>${data.author_analysis.suggested_mappings.length}</strong></li>
                    ` : ''}
                    ${data.wordpress_domain ? `
                        <li>WordPress Domain Detected: <strong>${data.wordpress_domain}</strong></li>
                    ` : ''}
                </ul>
            </div>
            ${data.category_analysis && data.category_analysis.missing_categories.length > 0 ? `
                <div class="alert alert-warning">
                    <h6>Missing Categories Detected</h6>
                    <p>The following WordPress categories are not found in your system:</p>
                    <ul class="mb-0">
                        ${data.category_analysis.missing_categories.slice(0, 5).map(cat => `<li>${cat.name}</li>`).join('')}
                        ${data.category_analysis.missing_categories.length > 5 ? `<li><em>... and ${data.category_analysis.missing_categories.length - 5} more</em></li>` : ''}
                    </ul>
                    <small class="text-muted">Missing categories will be auto-created or cached for admin review.</small>
                </div>
            ` : ''}
            ${data.author_analysis && data.author_analysis.missing_authors.length > 0 ? `
                <div class="alert alert-info">
                    <h6>Missing Authors Detected</h6>
                    <p>The following WordPress authors are not found in your system:</p>
                    <ul class="mb-0">
                        ${data.author_analysis.missing_authors.slice(0, 5).map(author => `<li>${author.display_name} (${author.email})</li>`).join('')}
                        ${data.author_analysis.missing_authors.length > 5 ? `<li><em>... and ${data.author_analysis.missing_authors.length - 5} more</em></li>` : ''}
                    </ul>
                    <small class="text-muted">Articles will be assigned to a placeholder author for later review.</small>
                </div>
            ` : ''}
            ${data.sample_posts.length > 0 ? `
                <h6>Sample Posts (First 5)</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.sample_posts.map(post => `
                                <tr>
                                    <td>${post.post_title}</td>
                                    <td><span class="badge badge-info">${post.post_status}</span></td>
                                    <td>${post.post_date}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            ` : ''}
        `;
        
        $('#previewContent').html(content);
        previewResults.show();
    }

    function displayResults(data) {
        const content = `
            <div class="alert alert-success">
                <h6>Import Completed</h6>
                <ul class="mb-0">
                    <li>Total Processed: <strong>${data.total_processed}</strong></li>
                    <li>Successfully Imported: <strong>${data.imported}</strong></li>
                    <li>Skipped: <strong>${data.skipped}</strong></li>
                    <li>Errors: <strong>${data.errors.length}</strong></li>
                </ul>
            </div>
            ${data.errors.length > 0 ? `
                <div class="alert alert-warning">
                    <h6>Errors</h6>
                    <ul class="mb-0">
                        ${data.errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}
        `;
        
        $('#resultsContent').html(content);
        importResults.show();
    }

    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alert = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('.card-body').prepend(alert);
        
        // Auto dismiss after 5 seconds
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }

    // Missing Categories Functions
    function loadMissingCategories() {
        $('#missingCategoriesContent').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');

        $.ajax({
            url: '{{ route("admin.import.missing-categories") }}',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    displayMissingCategories(response.data);
                } else {
                    $('#missingCategoriesContent').html('<div class="alert alert-danger">Failed to load missing categories</div>');
                }
            },
            error: function() {
                $('#missingCategoriesContent').html('<div class="alert alert-danger">Error loading missing categories</div>');
            }
        });
    }

    function displayMissingCategories(data) {
        const summary = data.summary;
        const recommendations = data.recommendations;

        let content = `
            <div class="mb-4">
                <h6>Summary</h6>
                <ul>
                    <li>Total Import Sessions: <strong>${summary.total_imports}</strong></li>
                    <li>Unique Missing Categories: <strong>${summary.unique_count}</strong></li>
                    <li>High-Confidence Mappings: <strong>${recommendations.mapping_recommended}</strong></li>
                    <li>Recommended for Creation: <strong>${recommendations.creation_recommended}</strong></li>
                </ul>
            </div>
        `;

        if (recommendations.categories_to_create.length > 0) {
            content += `
                <div class="mb-4">
                    <h6>Categories to Create</h6>
                    <div class="form-check-all mb-2">
                        <input type="checkbox" id="selectAllCategories" class="form-check-input">
                        <label for="selectAllCategories" class="form-check-label">Select All</label>
                    </div>
                    <div class="max-height-200 overflow-auto border p-2">
                        ${recommendations.categories_to_create.map(cat => `
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input category-checkbox"
                                       value="${cat.name}" id="cat_${cat.id || cat.name.replace(/\s+/g, '_')}">
                                <label class="form-check-label" for="cat_${cat.id || cat.name.replace(/\s+/g, '_')}">
                                    ${cat.name}
                                </label>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        if (recommendations.high_confidence_mappings.length > 0) {
            content += `
                <div class="mb-4">
                    <h6>Suggested Mappings (High Confidence)</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>WordPress Category</th>
                                    <th>Suggested Local Category</th>
                                    <th>Similarity</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${recommendations.high_confidence_mappings.map(mapping => `
                                    <tr>
                                        <td>${mapping.wp_category}</td>
                                        <td>${mapping.suggested_local_category}</td>
                                        <td><span class="badge badge-success">${mapping.similarity_score}%</span></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        $('#missingCategoriesContent').html(content);

        // Enable create button if there are categories to create
        if (recommendations.categories_to_create.length > 0) {
            $('#createSelectedCategoriesBtn').prop('disabled', false);

            // Handle select all
            $('#selectAllCategories').on('change', function() {
                $('.category-checkbox').prop('checked', this.checked);
            });

            // Handle individual checkboxes
            $('.category-checkbox').on('change', function() {
                const totalCheckboxes = $('.category-checkbox').length;
                const checkedCheckboxes = $('.category-checkbox:checked').length;
                $('#selectAllCategories').prop('checked', totalCheckboxes === checkedCheckboxes);
            });
        }
    }

    function createRecommendedCategories() {
        const btn = $('#createRecommendedCategoriesBtn');
        btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Creating...');

        // Get recommended categories from server
        $.ajax({
            url: '{{ route("admin.import.missing-categories") }}',
            method: 'GET',
            success: function(response) {
                if (response.success && response.data.recommendations.categories_to_create.length > 0) {
                    const categoryNames = response.data.recommendations.categories_to_create.map(cat => cat.name);
                    createCategories(categoryNames, btn);
                } else {
                    showAlert('info', 'No categories to create');
                    btn.prop('disabled', false).html('<i class="fa fa-plus"></i> Create Recommended');
                }
            },
            error: function() {
                showAlert('error', 'Failed to get recommended categories');
                btn.prop('disabled', false).html('<i class="fa fa-plus"></i> Create Recommended');
            }
        });
    }

    function createSelectedCategories() {
        const selectedCategories = $('.category-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedCategories.length === 0) {
            showAlert('warning', 'Please select at least one category to create');
            return;
        }

        const btn = $('#createSelectedCategoriesBtn');
        btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Creating...');

        createCategories(selectedCategories, btn);
    }

    function createCategories(categoryNames, btn) {
        $.ajax({
            url: '{{ route("admin.import.create-categories") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                category_names: categoryNames
            },
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    missingCategoriesModal.modal('hide');
                    // Refresh the page to update the missing categories alert
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Failed to create categories';
                showAlert('error', message);
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fa fa-plus"></i> Create Selected Categories');
            }
        });
    }

    function clearMissingCategoriesCache() {
        const btn = $('#clearCacheBtn');
        btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Clearing...');

        $.ajax({
            url: '{{ route("admin.import.clear-cache") }}',
            method: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    // Refresh the page to remove the missing categories alert
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Failed to clear cache';
                showAlert('error', message);
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fa fa-trash"></i> Clear Cache');
            }
        });
    }
});
</script>

<style>
.max-height-200 {
    max-height: 200px;
}
</style>
@endsection
