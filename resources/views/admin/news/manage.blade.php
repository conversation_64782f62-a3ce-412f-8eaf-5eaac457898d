@extends('layouts.admin')

@section('content')
    <div class="container">
        <div class="page-inner">
            <div class="page-header">
                <h3 class="fw-bold mb-3">News</h3>
                <ul class="breadcrumbs mb-3">
                    <li class="nav-home">
                        <a href="{{ route('dashboard') }}">
                            <i class="icon-home"></i>
                        </a>
                    </li>
                    <li class="separator">
                        <i class="icon-arrow-right"></i>
                    </li>
                    <li class="nav-item">
                        <a href="">News</a>
                    </li>
                    <li class="separator">
                        <i class="icon-arrow-right"></i>
                    </li>
                    <li class="nav-item active">
                        <a>Manage</a>
                    </li>
                </ul>
            </div>
            {{-- Content --}}
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Manage News</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="basic-datatables" class="display table table-striped table-hover text-center">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>ID</th>
                                            <th>Title</th>
                                            <th>Category</th>
                                            <th>Author</th>
                                            <th>Updated At</th>
                                            <th>Status</th>
                                            <th style="width: 5%">Action</th>
                                        </tr>
                                    </thead>
                                    <tfoot>
                                        <tr>
                                            <th>No</th>
                                            <th>ID</th>
                                            <th>Title</th>
                                            <th>Category</th>
                                            <th>Author</th>
                                            <th>Updated At</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </tfoot>
                                    <tbody>
                                        @foreach ($allNews as $news)
                                            <tr>
                                                <td>{{ $loop->iteration }}</td>
                                                <td>{{ $news->id }}</td>
                                                <td>{{ $news->title }}</td>
                                                <td>{{ $news->category->name }}</td>
                                                <td>{{ $news->author->name }}</td>
                                                <td>{{ $news->updated_at->translatedFormat('m/d/Y H:i') }}</td>
                                                <td class="text-center">
                                                    <span
                                                        class="{{ $news->status == 'Accept' ? 'badge bg-success' : ($news->status == 'Reject' ? 'badge bg-danger' : ($news->status == 'Pending' ? 'badge bg-warning' : '')) }}">
                                                        {{ $news->status }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="form-button-action d-flex justify-content-center align-items-center gap-1">

                                                        {{-- Edit Button - Always visible, but conditional functionality --}}
                                                        @php
                                                            $canEditClick = false;
                                                            $editTooltip = 'Edit';
                                                            $editClass = 'btn btn-link btn-primary';

                                                            if (auth()->user()->hasRole('Super Admin')) {
                                                                $canEditClick = true; // Admin can always edit
                                                                $editTooltip = 'Edit Article';
                                                            } elseif (auth()->user()->hasRole('Writer')) {
                                                                if ($news->status === 'Reject') {
                                                                    $canEditClick = true; // Writer can edit rejected articles
                                                                    $editTooltip = 'Edit Article (Rejected)';
                                                                } else {
                                                                    $canEditClick = false; // Writer cannot edit non-rejected articles
                                                                    $editTooltip = 'Cannot edit - Only rejected articles can be edited by Writers';
                                                                    $editClass = 'btn btn-link btn-secondary';
                                                                }
                                                            } else {
                                                                $canEditClick = false; // Other roles cannot edit
                                                                $editTooltip = 'No permission to edit';
                                                                $editClass = 'btn btn-link btn-secondary';
                                                            }
                                                        @endphp

                                                        {{-- View Article Button - Direct to NewsDetail --}}
                                                        <span data-bs-toggle="tooltip" title="View Article Detail">
                                                            <a href="{{ route('news.show', $news->id) }}"
                                                               class="btn btn-link btn-info"
                                                               target="_blank">
                                                                <i class="fa fa-eye"></i>
                                                            </a>
                                                        </span>

                                                        {{-- Edit Button --}}
                                                        <span data-bs-toggle="tooltip" title="{{ $editTooltip }}">
                                                            @if($canEditClick)
                                                                <a href="{{ route('admin.news.edit', $news->id) }}"
                                                                   class="{{ $editClass }}">
                                                                    <i class="fa fa-edit"></i>
                                                                </a>
                                                            @else
                                                                <button class="{{ $editClass }}" disabled>
                                                                    <i class="fa fa-edit"></i>
                                                                </button>
                                                            @endif
                                                        </span>

                                                        {{-- Delete Button (Super Admin only) --}}
                                                        @if(auth()->user()->hasRole('Super Admin'))
                                                            <span data-bs-toggle="tooltip" title="Delete">
                                                                <form action="{{ route('admin.news.destroy', $news->id) }}"
                                                                    id="deleteButton" data-id="{{ $news->id }}" class="d-inline">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="btn btn-link btn-danger">
                                                                        <i class="fa fa-times"></i>
                                                                    </button>
                                                                </form>
                                                            </span>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Custom CSS for better button styling --}}
    <style>
        .btn-link:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-link:disabled:hover {
            text-decoration: none;
        }

        .form-button-action .btn {
            margin: 0 2px;
            min-width: 32px;
            height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .form-button-action {
            min-width: 120px; /* Ensure consistent width for action column */
        }

        /* Tooltip styling */
        .tooltip-inner {
            max-width: 300px;
            text-align: left;
        }

        /* View button styling */
        .btn-info {
            color: #17a2b8;
        }

        .btn-info:hover {
            color: #138496;
        }
    </style>

    {{-- JavaScript for better UX --}}
    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });

        // Confirm delete action
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('form[id="deleteButton"] button');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (confirm('Are you sure you want to delete this article? This action cannot be undone.')) {
                        this.closest('form').submit();
                    }
                });
            });
        });
    </script>
@endsection

@section('custom-footer')
    <script>
        $(document).ready(function() {
            $("#basic-datatables").DataTable({});
        });
    </script>
@endsection
