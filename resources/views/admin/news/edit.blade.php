@extends('layouts.admin')

@section('content')
    <div class="container">
        <div class="page-inner">
            <div class="page-header">
                <h3 class="fw-bold mb-3">News</h3>
                <ul class="breadcrumbs mb-3">
                    <li class="nav-home">
                        <a href="{{ route('dashboard') }}">
                            <i class="icon-home"></i>
                        </a>
                    </li>
                    <li class="separator">
                        <i class="icon-arrow-right"></i>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('admin.news.manage') }}">News</a>
                    </li>
                    <li class="separator">
                        <i class="icon-arrow-right"></i>
                    </li>
                    <li class="nav-item active">
                        <a>Edit</a>
                    </li>
                </ul>
            </div>
            {{-- Content --}}
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h4 class="card-title">Edit News Article</h4>
                                <div>
                                    <span class="badge {{ $news->status == 'Accept' ? 'bg-success' : ($news->status == 'Reject' ? 'bg-danger' : 'bg-warning') }}">
                                        {{ $news->status }}
                                    </span>
                                    @if(auth()->user()->hasRole('Writer') && $news->status === 'Reject')
                                        <small class="text-muted ms-2">Article will be set to Pending after update</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <form action="{{ route('admin.news.update', $news->id) }}" method="POST"
                                        enctype="multipart/form-data">
                                        @csrf
                                        @method('PUT')
                                        <div class="col-12 mx-auto">
                                            <div class="form-group row">
                                                <label for="inlineinput" class="col-12 col-form-label">Title</label>
                                                <div class="col-12">
                                                    <input type="text" class="form-control input-full" id="inlineinput"
                                                        placeholder="Enter Input" name="title"
                                                        value="{{ old('title', $news->title) }}" />
                                                    @error('title')
                                                        <small class="text-danger">{{ $message }}</small>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label for="editor" class="col-12">Content</label>
                                                <textarea class="form-control col-12" id="editor" rows="7" name="content">{{ old('content', $news->content) }}</textarea>
                                                @error('content')
                                                    <small class="text-danger">{{ $message }}</small>
                                                @enderror
                                            </div>
                                            <div class="form-group col-md-4">
                                                <label for="exampleFormControlSelect1">Category</label>
                                                <select class="form-select" id="exampleFormControlSelect1"
                                                    name="category_id">
                                                    @foreach ($allCategory as $categories)
                                                        <option value="{{ $categories->id }}"
                                                            {{ old('category_id', $news->category_id) == $categories->id ? 'selected' : '' }}>
                                                            {{ $categories->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('category_id')
                                                    <small class="text-danger">{{ $message }}</small>
                                                @enderror
                                            </div>
                                            <div class="form-group col-md-4">
                                                <label>Image</label>
                                                <input type="file" class="form-control" id="imageInput" name="image" />
                                                <img id="imagePreview"
                                                    src="{{ $news->image ? asset('storage/images/' . $news->image) : '#' }}"
                                                    alt="Preview"
                                                    style="display: {{ $news->image ? 'block' : 'none' }}; max-width: 200px;"
                                                    class="img-fluid mt-4">
                                                @error('image')
                                                    <small class="text-danger">{{ $message }}</small>
                                                @enderror
                                            </div>
                                            <div class="form-group col-md-8">
                                                <label for="image_alt">Image Alt Text</label>
                                                <input type="text" class="form-control" id="image_alt" name="image_alt" 
                                                    value="{{ old('image_alt', $news->image_alt) }}"
                                                    placeholder="Masukkan deskripsi gambar untuk aksesibilitas" />
                                                <small class="form-text text-muted">Deskripsi gambar ini akan membantu aksesibilitas dan SEO</small>
                                                @error('image_alt')
                                                    <small class="text-danger">{{ $message }}</small>
                                                @enderror
                                            </div>
                                            <div class="card-footer mt-3 d-flex justify-content-start">
                                                <button type="submit" id="CKsubmitButton"
                                                    class="btn btn-success me-1">Update Article</button>
                                                <a href="{{ route('admin.news.manage') }}" class="btn btn-secondary">Cancel</a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Include CKEditor --}}
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script>
        ClassicEditor
            .create(document.querySelector('#editor'))
            .catch(error => {
                console.error(error);
            });

        // Image preview functionality
        document.getElementById('imageInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            const preview = document.getElementById('imagePreview');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
@endsection
