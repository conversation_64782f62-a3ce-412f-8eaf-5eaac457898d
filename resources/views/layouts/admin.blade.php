<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Dashboard</title>
    <meta content="width=device-width, initial-scale=1.0, shrink-to-fit=no" name="viewport" />
    <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon" />
    <meta name="csrf-token" content="{{ csrf_token() }}">

    @include('components.admin-header')

</head>

<body>
    <div class="wrapper">
        <!-- Sidebar -->
         <style>
            /* Sidebar Styling - All White */
            .sidebar,
            .sidebar *,
            .sidebar a,
            .sidebar p,
            .sidebar span,
            .sidebar i,
            .sidebar .nav-link,
            .sidebar .sub-item,
            .sidebar .text-section,
            .sidebar .caret {
                color: white !important;
            }

            /* Active menu item styling */
            .sidebar .nav-item.active > a,
            .sidebar .nav-item.active > a *,
            .sidebar .nav-item.active {
                color: white !important;
                border-radius: 8px !important;
            }

            /* Hover effect */
            .sidebar .nav-item:hover > a,
            .sidebar .nav-item:hover > a * {
                color: white !important;
                border-radius: 8px !important;
            }

            /* FORCE NAV-TOGGLE BUTTONS TO BE WHITE */
            .nav-toggle .btn-toggle,
            .nav-toggle .btn-toggle i,
            .nav-toggle .btn-toggle .gg-menu-right,
            .nav-toggle .btn-toggle .gg-menu-left,
            .topbar-toggler,
            .topbar-toggler i,
            .topbar-toggler .gg-more-vertical-alt,
            .btn-toggle,
            .btn-toggle i {
                color: white !important;
                fill: white !important;
                stroke: white !important;
            }

            /* Force white for all gg-icons */
            .gg-menu-right,
            .gg-menu-left,
            .gg-more-vertical-alt,
            .gg-menu-right::before,
            .gg-menu-right::after,
            .gg-menu-left::before,
            .gg-menu-left::after,
            .gg-more-vertical-alt::before,
            .gg-more-vertical-alt::after {
                border-color: white !important;
                color: white !important;
            }

            /* Navbar Styling - All White */
            .navbar-header,
            .navbar-header *,
            .navbar-header .nav-link,
            .navbar-header .profile-username,
            .navbar-header i,
            .navbar-header .fa,
            .navbar-header .notification {
                color: white !important;
            }

            /* Profile Username */
            .profile-username,
            .profile-username .op-7,
            .profile-username .fw-bold {
                color: white !important;
            }

         </style>
        <div class="sidebar" style="background: linear-gradient(to bottom right, #FD1835, #970E20); color: white;">
            <div class="sidebar-logo">
            <!-- Logo Header -->
            <div class="logo-header" style="background:transparent !important; color: white;">
                <a href="{{ route('home') }}" class="logo me-5" >
                <img src="{{ asset('lambeturah.png') }}" alt="navbar brand"
                    class="navbar-brand img-fluid" height="10"  />
                </a>
                <div class="nav-toggle">
                <button class="btn btn-toggle toggle-sidebar">
                    <i class="gg-menu-right"></i>
                </button>
                <button class="btn btn-toggle sidenav-toggler">
                    <i class="gg-menu-left"></i>
                </button>
                </div>
                <button class="topbar-toggler more">
                <i class="gg-more-vertical-alt"></i>
                </button>
            </div>
            <!-- End Logo Header -->
            </div>
            <div class="sidebar-wrapper scrollbar scrollbar-inner">
            <div class="sidebar-content">
                <ul class="nav nav-secondary" style="color: white;">
                <li class="nav-item {{ Route::is('dashboard') ? 'active' : '' }}"  >
                    <a href="{{ route('dashboard') }}" style="color: white;">
                    <i style="color:white !important" class="fas fa-home"></i>
                    <p style="color:white !important">Dashboard</p>
                    </a>
                </li>
                <li class="nav-section">
                    <span class="sidebar-mini-icon">
                    <i class="fa fa-ellipsis-h" style="color:white;"></i>
                    </span>
                    <h4 class="text-section" style="color:#e0e0e0 !important">Components</h4>
                </li>
                <li
                    class="nav-item {{ Route::is('admin.news.*') || (Route::is('news.*') && !Route::is('news.draft')) ? 'active' : '' }}">
                    <a data-bs-toggle="collapse" href="#news" style="color: white;">
                    <i class="fas fa-newspaper" style="color:white !important"></i>
                    <p style="color:white !important">News</p>
                    <span class="caret" style="color:#e0e0e0 !important"></span>
                    </a>
                    <div class="collapse {{ Route::is('admin.news.*') || (Route::is('news.*') && !Route::is('news.draft')) ? 'show' : '' }}"
                    id="news">
                    <ul class="nav nav-collapse" style="color: white !;">
                        @if (auth()->user()->hasRole('Super Admin'))
                        <li style="color:#e0e0e0 !important">
                            <a href="{{ route('admin.news.manage') }}" style="color:white !important">
                            <span class="sub-item">Manage</span>
                            </a>
                        </li>
                        @endif
                        @if (auth()->user()->hasRole('Editor') || auth()->user()->hasRole('Super Admin'))
                        <li>
                            <a href="{{ route('news.staged') }}" style="color:white;">
                            <span class="sub-item">staged</span>
                            </a>
                        </li>
                        @endif
                        @if (auth()->user()->hasRole('Writer') || auth()->user()->hasRole('Super Admin'))
                        <li>
                            <a href="{{ route('news.create') }}" style="color:white;">
                            <span class="sub-item">Create</span>
                            </a>
                        </li>
                        @endif
                    </ul>
                    </div>
                </li>
                @if (auth()->user()->hasRole('Super Admin'))
                    <li class="nav-item {{ Route::is('admin.category.*') ? 'active' : '' }}">
                    <a data-bs-toggle="collapse" href="#category" style="color: white;">
                        <i class="fas fa-list"  style="color:white !important"></i>
                        <p  style="color:white !important">Category</p>
                        <span class="caret"  style="color:#e0e0e0 !important"></span>
                    </a>
                    <div class="collapse {{ Route::is('admin.category.*') ? 'show' : '' }}" id="category">
                        <ul class="nav nav-collapse" style="color: white;">
                        <li>
                            <a href="{{ route('admin.category.manage') }}" style="color:white;">
                            <span class="sub-item">Manage</span>
                            </a>
                        </li>
                        </ul>
                    </div>
                    </li>
                @endif
                <!-- @if (auth()->user()->hasRole('Super Admin') || auth()->user()->hasRole('Editor'))
                    <li class="nav-item {{ Route::is('admin.comments.*') ? 'active' : '' }}">
                    <a data-bs-toggle="collapse" href="#comments" style="color: white;">
                        <i class="fas fa-comments"  style="color:white !important"></i>
                        <p  style="color:white !important">Comments</p>
                        <span class="caret"  style="color:#e0e0e0 !important"></span>
                    </a>
                    <div class="collapse {{ Route::is('admin.comments.*') ? 'show' : '' }}" id="comments">
                        <ul class="nav nav-collapse" style="color: white;">
                        <li>
                            <a href="{{ route('admin.comments.moderate') }}" style="color:white;">
                            <span class="sub-item">Moderate</span>
                            </a>
                        </li>
                        </ul>
                    </div>
                    </li>
                @endif -->
                @if (auth()->user()->hasRole('Super Admin'))
                    <li class="nav-item {{ Route::is('admin.import.*') ? 'active' : '' }}">
                        <a href="{{ route('admin.import.index') }}" style="color: white;">
                            <i class="fas fa-file-import" style="color:white !important"></i>
                            <p style="color:white !important">Import WordPress</p>
                        </a>
                    </li>
                @endif
                @if (auth()->user()->hasRole('Writer') || auth()->user()->hasRole('Super Admin'))
                    <li
                    class="nav-item {{ Route::is('admin.users.*') || Route::is('news.draft') ? 'active' : '' }}">
                    <a data-bs-toggle="collapse" href="#users" style="color: white;">
                        <i class="fas fa-users-cog"  style="color:white !important"></i>
                        <p  style="color:white !important">Users</p>
                        <span class="caret"  style="color:#e0e0e0 !important"></span>
                    </a>
                    <div class="collapse {{ Route::is('admin.users.*') || Route::is('news.draft') ? 'show' : '' }}"
                        id="users">
                        <ul class="nav nav-collapse" style="color: white;">
                        @if (auth()->user()->hasRole('Super Admin'))
                            <li>
                            <a href="{{ route('admin.users.manage') }}" style="color:white;">
                                <span class="sub-item">Manage</span>
                            </a>
                            </li>
                        @endif
                        <li>
                            <a href="{{ route('news.draft') }}" style="color:white;">
                            <span class="sub-item">Draft</span>
                            </a>
                        </li>
                        </ul>
                    </div>
                    </li>
                @endif
                </ul>
            </div>
            </div>
        </div>
        <!-- End Sidebar -->

        <div class="main-panel" >
            <div class="main-header" >
                <div class="main-header-logo">
                    <!-- Logo Header -->
                    <div class="logo-header" style="background: linear-gradient(to bottom right, #FD1835, #970E20); color: white;">
                        <a href="{{ route('home') }}" class="logo">
                            <img src="{{ asset('lambeturah.png') }}" alt="navbar brand"
                                class="navbar-brand img-fluid" height="20"/>
                        </a>

                        <div class="nav-toggle">
                            <button class="btn btn-toggle toggle-sidebar" >
                                <i class="gg-menu-right"></i>
                            </button>
                            <button class="btn btn-toggle sidenav-toggler" >
                                <i class="gg-menu-left"></i>
                            </button>
                        </div>
                        <button class="topbar-toggler more">
                            <i class="gg-more-vertical-alt"></i>
                        </button>
                    </div>
                    <!-- End Logo Header -->
                </div>
                <!-- Navbar Header -->
                <nav class="navbar navbar-header navbar-header-transparent navbar-expand-lg border-bottom">
                    <div class="container-fluid">
                        <ul class="navbar-nav topbar-nav ms-md-auto align-items-center">
                            <li class="nav-item topbar-icon dropdown hidden-caret me-4">
                                <a class="nav-link dropdown-toggle" href="#" id="notifDropdown" role="button"
                                    data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fa fa-bell"></i>
                                    <span class="notification" id="unread-notification-count"></span>
                                </a>
                                <ul class="dropdown-menu notif-box animated fadeIn" aria-labelledby="notifDropdown">
                                    <li>
                                        <div class="dropdown-title">
                                            @if (auth()->user()->notifications->count() > 0)
                                                You have notifications
                                            @else
                                                Nothing notifications
                                            @endif
                                        </div>
                                    </li>
                                    <li>
                                        <div class="notif-scroll scrollbar-outer">
                                            <div class="notif-center" id="notifications-container">
                                                {{-- JS --}}
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </li>
                            <li class="nav-item topbar-user dropdown hidden-caret">
                                <a class="dropdown-toggle profile-pic" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    <div class="avatar-sm">
                                        <img src="{{ auth()->user()->image ? asset('storage/images/' . auth()->user()->image) : asset('img/default.jpeg') }}"
                                            alt="Profile Picture" class="avatar-img rounded-circle" />
                                    </div>
                                    <span class="profile-username">
                                        <span class="op-7">Hi,</span>
                                        <span class="fw-bold">{{ auth()->user()->name }}</span>
                                    </span>
                                </a>

                                <ul class="dropdown-menu dropdown-user animated fadeIn">
                                    <div class="dropdown-user-scroll scrollbar-outer">
                                        <li>
                                            <div class="user-box">
                                                <div class="avatar-lg">
                                                    <img src="{{ auth()->user()->image ? asset('storage/images/' . auth()->user()->image) : asset('img/default.jpeg') }}"
                                                        alt="Profile Picture" class="avatar-img rounded" />
                                                </div>
                                                <div class="u-text">
                                                    <h4>{{ auth()->user()->name }}</h4>
                                                    <p class="text-muted">{{ auth()->user()->email }}
                                                    </p>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="dropdown-divider bg-light"></div>
                                            <a class="dropdown-item"
                                                href="{{ route('profile.edit', auth()->user()->id) }}">My
                                                Profile</a>
                                            <div class="dropdown-divider bg-light"></div>
                                            <form action="{{ route('logout') }}" method="POST">
                                                @csrf
                                                <button type="submit" class="dropdown-item">Logout</button>
                                            </form>
                                        </li>
                                    </div>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </nav>
                <!-- End Navbar -->
            </div>

            @yield('content')

        </div>
    </div>

    @include('components.admin-footer')
</body>

</html>
