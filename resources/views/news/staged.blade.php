@extends('layouts.admin')

@section('content')
    <div class="container">
        <div class="page-inner">
            <div class="page-header">
                <h3 class="fw-bold mb-3">Staged Articles</h3>
                <ul class="breadcrumbs mb-3">
                    <li class="nav-home">
                        <a href="{{ route('dashboard') }}">
                            <i class="icon-home"></i>
                        </a>
                    </li>
                    <li class="separator">
                        <i class="icon-arrow-right"></i>
                    </li>
                    <li class="nav-item">
                        <a href="">News</a>
                    </li>
                    <li class="separator">
                        <i class="icon-arrow-right"></i>
                    </li>
                    <li class="nav-item active">
                        <a>Staged</a>
                    </li>
                </ul>
            </div>
            {{-- Content --}}
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Staged Articles</h4>
                            <p class="card-category">Articles that are staged and not yet released</p>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="basic-datatables" class="display table table-striped table-hover text-center">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>ID</th>
                                            <th>Title</th>
                                            <th>Category</th>
                                            <th>Author</th>
                                            <th>Status</th>
                                            <th>Updated At</th>
                                            <th style="width: 15%">Action</th>
                                        </tr>
                                    </thead>
                                    <tfoot>
                                        <tr>
                                            <th>No</th>
                                            <th>ID</th>
                                            <th>Title</th>
                                            <th>Category</th>
                                            <th>Author</th>
                                            <th>Status</th>
                                            <th>Updated At</th>
                                            <th>Action</th>
                                        </tr>
                                    </tfoot>
                                    <tbody>
                                        @foreach ($stagedNews as $news)
                                            <tr>
                                                <td>{{ $loop->iteration }}</td>
                                                <td>{{ $news->id }}</td>
                                                <td>{{ $news->title }}</td>
                                                <td>{{ $news->category->name }}</td>
                                                <td>{{ $news->author->name }}</td>
                                                <td>
                                                    @if($news->status === 'Pending')
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="fa fa-clock me-1"></i>Pending
                                                        </span>
                                                    @elseif($news->status === 'Reject')
                                                        <span class="badge bg-danger">
                                                            <i class="fa fa-times me-1"></i>Rejected
                                                        </span>
                                                    @endif
                                                </td>
                                                <td>{{ $news->updated_at->translatedFormat('m/d/Y H:i') }}</td>
                                                <td>
                                                    <div class="form-button-action d-flex justify-content-center align-items-center gap-2">

                                                        {{-- View Article Button --}}
                                                        <span data-bs-toggle="tooltip" title="View Article Detail">
                                                            <a href="{{ route('news.view', $news->id) }}"
                                                                class="btn btn-link btn-info">
                                                                <i class="far fa-eye"></i>
                                                            </a>
                                                        </span>

                                                        {{-- Accept/Reject Button Group --}}
                                                        <div class="" role="group" aria-label="Article Actions">
                                                            {{-- Accept Button --}}
                                                            <form action="{{ route('news.updateStatus', $news->id) }}" method="POST" class="d-inline accept-form">
                                                                @csrf
                                                                @method('PATCH')
                                                                <input type="hidden" name="status" value="Accept">
                                                                <button type="submit" class="btn btn-sm btn-outline-success rounded-circle" data-bs-toggle="tooltip" title="Accept Article">
                                                                    <i class="fa fa-check"></i>
                                                                </button>
                                                            </form>

                                                            {{-- Reject Button --}}
                                                            <form action="{{ route('news.updateStatus', $news->id) }}" method="POST" class="d-inline reject-form">
                                                                @csrf
                                                                @method('PATCH')
                                                                <input type="hidden" name="status" value="Reject">
                                                                <button type="submit" class="btn btn-sm btn-outline-danger rounded-circle" data-bs-toggle="tooltip" title="Reject Article">
                                                                    <i class="fa fa-times"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

    {{-- Custom CSS for better styling --}}
    <style>
        .form-button-action .btn {
            min-width: 36px;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .form-button-action {
            min-width: 160px;
        }

        /* Button group styling */
        .btn-group .btn {
            border-radius: 0;
        }

        .btn-group .btn:first-child {
            border-top-left-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }

        .btn-group .btn:last-child {
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }

        .btn-group .btn + .btn {
            margin-left: -1px;
        }

        .btn-success {
            color: #28a745;
        }

        .btn-success:hover {
            color: #1e7e34;
        }

        .btn-danger {
            color: #dc3545;
        }

        .btn-danger:hover {
            color: #bd2130;
        }

        .btn-info {
            color: #17a2b8;
        }

        .btn-info:hover {
            color: #138496;
        }

        /* Status badge styling */
        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
        }

        .bg-warning {
            background-color: #ffc107 !important;
        }

        .bg-danger {
            background-color: #dc3545 !important;
        }

        .text-dark {
            color: #212529 !important;
        }
    </style>

@section('custom-footer')
    <script>
        $(document).ready(function() {
            $("#basic-datatables").DataTable({
                "order": [[ 0, "asc" ]], // Sort by No column (ascending) - first column
                "pageLength": 25,
                "columnDefs": [
                    {
                        "targets": 0, // No column
                        "orderable": true,
                        "type": "num"
                    },
                    {
                        "targets": 5, // Status column
                        "orderable": true,
                        "searchable": true
                    }
                ]
            });

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Confirm Accept action
            $('.accept-form').on('submit', function(e) {
                e.preventDefault();
                const form = this;

                if (confirm('Are you sure you want to ACCEPT this article? It will be published and visible to users.')) {
                    // Show loading state
                    const button = $(form).find('button');
                    const originalHtml = button.html();
                    button.html('<i class="fa fa-spinner fa-spin"></i>').prop('disabled', true);

                    // Submit form
                    $.ajax({
                        url: $(form).attr('action'),
                        method: 'PATCH',
                        data: $(form).serialize(),
                        success: function(response) {
                            if (response.success) {
                                // Show success message
                                alert('Article accepted successfully!');
                                // Reload page to update the list
                                window.location.reload();
                            } else {
                                alert('Error: ' + response.message);
                                button.html(originalHtml).prop('disabled', false);
                            }
                        },
                        error: function() {
                            alert('An error occurred while accepting the article.');
                            button.html(originalHtml).prop('disabled', false);
                        }
                    });
                }
            });

            // Confirm Reject action
            $('.reject-form').on('submit', function(e) {
                e.preventDefault();
                const form = this;

                if (confirm('Are you sure you want to REJECT this article? The author will be notified.')) {
                    // Show loading state
                    const button = $(form).find('button');
                    const originalHtml = button.html();
                    button.html('<i class="fa fa-spinner fa-spin"></i>').prop('disabled', true);

                    // Submit form
                    $.ajax({
                        url: $(form).attr('action'),
                        method: 'PATCH',
                        data: $(form).serialize(),
                        success: function(response) {
                            if (response.success) {
                                // Show success message
                                alert('Article rejected successfully!');
                                // Reload page to update the list
                                window.location.reload();
                            } else {
                                alert('Error: ' + response.message);
                                button.html(originalHtml).prop('disabled', false);
                            }
                        },
                        error: function() {
                            alert('An error occurred while rejecting the article.');
                            button.html(originalHtml).prop('disabled', false);
                        }
                    });
                }
            });
        });
    </script>
@endsection
