@extends('layouts.admin')

@section('content')
    <div class="container">
        <div class="page-inner">
            <div class="page-header">
                <h3 class="fw-bold mb-3">News</h3>
                <ul class="breadcrumbs mb-3">
                    <li class="nav-home">
                        <a href="{{ route('dashboard') }}">
                            <i class="icon-home"></i>
                        </a>
                    </li>
                    <li class="separator">
                        <i class="icon-arrow-right"></i>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('news.staged') }}">News</a>
                    </li>
                    <li class="separator">
                        <i class="icon-arrow-right"></i>
                    </li>
                    <li class="nav-item active">
                        <a>View</a>
                    </li>
                </ul>
            </div>
            {{-- Content --}}
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            @if (!auth()->user()->hasRole('Writer') && isset($news) && $news->status != 'Accept')
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div class="card-title">{{ $news->title }}</div>
                                    <div class="d-flex">
                                        <form action="{{ route('news.updateStatus', $news->id) }}" method="POST"
                                            class="me-2">
                                            @csrf
                                            @method('PATCH')
                                            <input type="hidden" name="status" value="Accept">
                                            <button type="submit" class="btn btn-success text-light" id="submitButton">
                                                Accept
                                            </button>
                                        </form>
                                        <form action="{{ route('news.updateStatus', $news->id) }}" method="POST">
                                            @csrf
                                            @method('PATCH')
                                            <input type="hidden" name="status" value="Reject">
                                            <button type="submit" class="btn btn-danger text-light" id="rejectButton">
                                                Reject
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            @endif

                                {{-- Gunakan card dengan shadow untuk efek melayang dan tanpa border --}}
                                    {{-- BAGIAN ISI KONTEN --}}
                                    <div class="card-body p-4">

                                        {{-- 2. INFORMASI META (AUTHOR, TANGGAL, WAKTU BACA) --}}
                                        <div class="d-flex align-items-center gap-3 mb-3 text-muted">
                                            <img src="{{ $news->author->image ? asset('storage/images/' . $news->author->image) : asset('img/default.jpeg') }}"
                                                 class="rounded-circle" alt="{{ $news->author->name }}" style="width: 40px; height: 40px; object-fit: cover;">
                                            <div>
                                                <small class="d-block fw-bold">{{ $news->author->name }}</small>
                                                <small class="text-muted">{{ $news->author->bio }}</small>
                                            </div>
                                        </div>



                                        {{-- BAGIAN GAMBAR UTAMA (HERO) --}}
                                        <div class="position-relative">
                                            {{-- Gambar utama menjadi card-img-top agar menyatu dengan ujung atas card --}}
                                            <img src="{{ $news->image ? asset('storage/images/' . $news->image) : asset('img/noimg.jpg') }}"
                                                 class="card-img-top" alt="{{ $news->image_alt ?? $news->title }}" style="max-height: 400px; object-fit: cover;">

                                            {{-- Badge Kategori dengan style yang lebih menarik --}}
                                            <a href="#" class="badge text-decoration-none text-white bg-primary position-absolute m-3" style="top: 0; left: 0;">
                                                {{ $news->category->name }}
                                            </a>
                                        </div>

                                        {{-- Caption Gambar (jika ada) --}}
                                        @if($news->image_alt)
                                            <div class="text-center bg-light p-2 rounded mt-4">
                                                <small class="text-muted fst-italic">{{ $news->image_alt }}</small>
                                            </div>
                                        @endif



                                        {{-- 3. KONTEN UTAMA --}}
                                        {{-- Bungkus konten dalam div agar bisa diberi styling khusus --}}
                                        <div class="article-content mt-4">
                                            {!! $news->content !!}
                                        </div>
                                    </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
