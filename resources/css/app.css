@import "tailwindcss";

/* === THEME DESIGN TOKENS === */
@theme {
    --font-family-sans: "Instrument Sans", ui-sans-serif, system-ui, sans-serif;

    --radius: 10px;
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --color-lg-background: oklch(1 0 0);
    --color-lg-foreground: oklch(0.129 0.042 264.695);

    --color-dr-background: oklch(0.129 0.042 264.695);
    --color-dr-foreground: oklch(1 0 0);

    --color-primary: oklch(0.5814 0.2349 27.99);
    --color-primary-lighter: oklch(0.698 0.244 29.5);
    --color-primary-darker: oklch(0.419 0.244 29.5);
    --color-primary-hover: oklch(0.4194 0.2377 28.50);
    --color-primary-muted: oklch(0.8802 0.0659 28.71);
    --color-primary-foreground: oklch(1 0 0);

    --color-muted: oklch(0.279 0.041 260.031);
    --color-muted-foreground: oklch(0.704 0.04 256.788);

    --border: oklch(0.929 0.013 255.508);
    --input: oklch(0.929 0.013 255.508);
    --ring: oklch(0.704 0.04 256.788);

    --card: var(--background);
    --card-foreground: var(--foreground);

    --color-accent: var(--muted);
    --color-accent-foreground: var(--foreground);
}

/* === DARK MODE OVERRIDE === */
.dark {

    --muted: oklch(0.279 0.041 260.031);
    --muted-foreground: oklch(0.704 0.04 256.788);

    --card: oklch(0.208 0.042 265.755);
    --card-foreground: oklch(0.984 0.003 247.858);

    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.551 0.027 264.364);
}

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));


/* === KEYFRAMES === */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* === MEDIA QUERIES === */
@media (prefers-reduced-motion: reduce) {

    *,
    ::before,
    ::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* === LINE CLAMP UTILITIES === */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* === NEWS CARD STYLES === */
.news-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

.news-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.news-card-image {
    transition: transform 0.3s ease;
    object-fit: cover;
}

.news-card:hover .news-card-image {
    transform: scale(1.05);
}

/* === BADGE STYLES === */
.badge-hot {
    background: linear-gradient(135deg, #dc2626, #ef4444);
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
}

.badge-viral {
    background: linear-gradient(135deg, #ea580c, #f97316);
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    box-shadow: 0 2px 4px rgba(234, 88, 12, 0.3);
}

.badge-celebrity {
    background: linear-gradient(135deg, #be185d, #ec4899);
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    box-shadow: 0 2px 4px rgba(190, 24, 93, 0.3);
}

/* === SECTION HEADER STYLES === */
.section-header {
    position: relative;
    display: inline-block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1.5rem;
}

.dark .section-header {
    color: #f9fafb;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 60%;
    height: 3px;
    background: linear-gradient(90deg, #de0b66, #f43f5e);
    border-radius: 2px;
}

/* === HOVER EFFECTS === */
.hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .hover-lift:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* === TAG STYLES === */
.tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: #f3f4f6;
    color: #374151;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    transition: all 0.2s ease;
    text-decoration: none;
}

.tag:hover {
    background: #de0b66;
    color: white;
    transform: scale(1.05);
}

.dark .tag {
    background: #374151;
    color: #d1d5db;
}

.dark .tag:hover {
    background: #de0b66;
    color: white;
}

/* === GRADIENT BACKGROUNDS === */
.gradient-primary {
    background: linear-gradient(135deg, #de0b66, #f43f5e);
}

.gradient-secondary {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.gradient-accent {
    background: linear-gradient(135deg, #059669, #10b981);
}

/* === ANIMATION UTILITIES === */
@keyframes pulse-gentle {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.pulse-gentle {
    animation: pulse-gentle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fade-in-up 0.6s ease-out;
}

/* === SKELETON LOADING === */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, transparent 37%, #f0f0f0 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
}

.dark .skeleton {
    background: linear-gradient(90deg, #374151 25%, transparent 37%, #374151 63%);
    background-size: 400% 100%;
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0 50%;
    }
}

/* === RESPONSIVE GRID UTILITIES === */
.grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.grid-auto-fill {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

/* === SCROLL UTILITIES === */
.scroll-smooth {
    scroll-behavior: smooth;
}

.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

/* === SMOOTH CONTENT TRANSITIONS === */
.content-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-fade-enter {
    opacity: 0;
    transform: translateY(10px) scale(0.98);
}

.content-fade-enter-to {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.content-fade-leave {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.content-fade-leave-to {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
}

/* === STAGGER ANIMATIONS === */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn 0.4s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }

/* === TAB SMOOTH TRANSITIONS === */
.tab-smooth {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-smooth:hover {
    transform: translateY(-1px);
}

.tab-smooth:active {
    transform: translateY(0);
}

/* === LOADING SHIMMER EFFECT === */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.dark .loading-shimmer {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}