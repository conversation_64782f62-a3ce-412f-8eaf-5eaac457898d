/* Smooth Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Animation Classes */
.animate-fadeIn {
    animation: fadeIn 0.4s ease-out forwards;
}

.animate-fadeOut {
    animation: fadeOut 0.3s ease-in forwards;
}

.animate-slideInUp {
    animation: slideInUp 0.4s ease-out forwards;
}

.animate-slideInDown {
    animation: slideInDown 0.4s ease-out forwards;
}

.animate-scaleIn {
    animation: scaleIn 0.3s ease-out forwards;
}

/* Smooth Transitions */
.transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-smooth-fast {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-smooth-slow {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.dark .loading-shimmer {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.dark .hover-lift:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Tab Transitions */
.tab-transition {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-transition:hover {
    transform: translateY(-1px);
}

/* Content Fade Transitions */
.content-fade-enter {
    opacity: 0;
    transform: translateY(10px) scale(0.98);
}

.content-fade-enter-active {
    opacity: 1;
    transform: translateY(0) scale(1);
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.content-fade-exit {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.content-fade-exit-active {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
    transition: opacity 0.2s ease-in, transform 0.2s ease-in;
}

/* Stagger Animation for Lists */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn 0.4s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }
