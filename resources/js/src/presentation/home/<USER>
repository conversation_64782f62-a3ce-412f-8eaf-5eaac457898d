import React from "react";
import ReactDOM from "react-dom/client";
import { loadingWidget } from "../../shared/component/loading_widget";
import { useHomeLogic } from "./home_logic";
import { format } from "date-fns";
import { Row, Col, Card, Image, App, Tabs, Tag } from "antd";
import { Content } from "antd/es/layouts/layouts";
import Paragraph from "antd/es/typography/Paragraph";
import Title from "antd/es/typography/Title";
import Text from "antd/es/typography/Text";
import {
    ClockCircleOutlined,
    EyeOutlined,
    LikeOutlined,
} from "@ant-design/icons";
import { NewsResponse } from "../../model/response/news_response";
import { id } from "date-fns/locale";
import { htmlComponent } from "../../shared/component/html_component";
import TabPane from "antd/es/tabs/TabPane";
import { <PERSON><PERSON>er<PERSON>outer, <PERSON> } from "react-router-dom";
import { LatestComponent } from "./component/latest_component";
import { TrendingComponent } from "./component/trending_component";
const HomeUI = () => {
    const {
        latestNews,
        latestLoading,
        latestError,
        latestNewsRequest,
        setLatestNewsRequest,
        fetchLatestNewsData,
        topNews,
        topLoading,
        topError,
        topNewsRequest,
        setTopNewsRequest,
        fetchTopNewsData,
        popularNews,
        popularLoading,
        popularError,
        popularNewsRequest,
        setPopularNewsRequest,
        fetchPopularNewsData,
        activeKey,
        setActiveKey,
        listCategory,
        loadingCategory,
        errorCategory,
        categoryRequest,
        setCategoryRequest,
        fetchDataCategory,
    } = useHomeLogic();
    const formatDate = (dateString: any) => {
        return format(new Date(dateString), "d MMMM yyyy", { locale: id });
    };

    const formatDateTime = (dateString: any) => {
        return format(new Date(dateString), "d MMMM yyyy HH:mm", {
            locale: id,
        });
    };
    // <div className="floating-loader">
    //                 <div className="spinner"></div>
    //             </div>
    return (
        <Content style={{ padding: "10%", backgroundColor: "#fff" }}>
            <div
                style={{ maxWidth: 1200, margin: "0 auto", padding: "0 20px" }}
            >
                {latestLoading && loadingWidget()}
                <Row gutter={[24, 24]}>
                    {/* Kolom Kiri */}
                    <Col xs={24} lg={16} xl={17}>
                        {latestNews[0] && (
                            <div>
                                <Card
                                    cover={
                                        <Image
                                            src={
                                                NewsResponse.getImageUrl(
                                                    latestNews[0]
                                                ) || "/img/noimg.jpg"
                                            }
                                            alt=""
                                            style={{
                                                height: 475,
                                                objectFit: "contain",
                                            }}
                                            preview={false}
                                        />
                                    }
                                    style={{
                                        padding: 0,
                                        border: "none", // add this line to remove the border
                                    }}
                                    // bordered={false}
                                    // bodyStyle={{ padding: 0 }}
                                >
                                    <div
                                        style={{
                                            position: "absolute",
                                            bottom: 20,
                                            left: 0,
                                            right: 0,
                                            display: "flex",
                                            justifyContent: "center",
                                            color: "white",
                                            gap: 20,
                                        }}
                                    >
                                        <Text>
                                            <ClockCircleOutlined />{" "}
                                            {formatDate(
                                                latestNews[0].created_at
                                            )}
                                        </Text>
                                        <Text>
                                            <EyeOutlined />{" "}
                                            {latestNews[0].views}
                                        </Text>
                                        <Text>
                                            <LikeOutlined />{" "}
                                            {latestNews[0].likes_count ?? 0}
                                        </Text>
                                    </div>
                                </Card>

                                <Title
                                    level={1}
                                    style={{
                                        marginTop: 24,
                                        borderBottom: "1px solid #f0f0f0",
                                        paddingBottom: 16,
                                    }}
                                >
                                    <a
                                        href={`/news/${latestNews[0].id}`}
                                        style={{ color: "inherit" }}
                                    >
                                        {latestNews[0].title}
                                    </a>
                                </Title>

                                {htmlComponent({
                                    html: latestNews[0].content ?? "",
                                })}
                                {/* <Paragraph
                                    style={{ fontSize: 16, margin: "24px 0" }}
                                    ellipsis={{ rows: 3, expandable: true }}
                                >
                                    {latestNews[0].content}
                                </Paragraph> */}
                            </div>
                        )}

                        {/* Berita Populer */}
                        {popularNews[0] && (
                            <Card
                                title="Top Views"
                                // headStyle={{  }}
                                style={{
                                    marginTop: 24,
                                    fontSize: 24,
                                    fontWeight: "bold",
                                }}
                            >
                                <Row gutter={24} align="middle">
                                    <Col span={12}>
                                        <Image
                                            src={
                                                NewsResponse.getImageUrl(
                                                    popularNews[0]
                                                ) || "/img/noimg.jpg"
                                            }
                                            alt=""
                                            style={{ borderRadius: 8 }}
                                            preview={false}
                                        />
                                    </Col>
                                    <Col span={12}>
                                        <Title
                                            level={3}
                                            style={{ marginBottom: 12 }}
                                        >
                                            <a
                                                href={`/news/${popularNews[0].id}/show`}
                                            >
                                                {popularNews[0].title}
                                            </a>
                                        </Title>
                                        <Paragraph style={{ marginBottom: 8 }}>
                                            <ClockCircleOutlined />{" "}
                                            {formatDateTime(
                                                popularNews[0].created_at
                                            )}
                                        </Paragraph>
                                        <Paragraph style={{ marginBottom: 8 }}>
                                            <EyeOutlined />{" "}
                                            {popularNews[0].views} Views
                                        </Paragraph>
                                        <Paragraph>
                                            <LikeOutlined />{" "}
                                            {popularNews[0].likes_count ?? 0}{" "}
                                            Likes
                                        </Paragraph>
                                    </Col>
                                </Row>
                            </Card>
                        )}
                    </Col>

                    {/* Kolom Kanan */}
                    <Col xs={24} lg={8} xl={7}>
                        <Card
                            style={{ backgroundColor: "#fafafa", padding: 24 }}
                        >
                            {latestNews[1] && (
                                <>
                                    <Image
                                        src={
                                            latestNews[1].image ||
                                            "/img/noimg.jpg"
                                        }
                                        alt=""
                                        style={{
                                            borderRadius: 8,
                                            marginBottom: 24,
                                        }}
                                        preview={false}
                                    />
                                    <Title
                                        level={4}
                                        style={{ marginBottom: 16 }}
                                    >
                                        <a href={`/news/${latestNews[1].id}`}>
                                            {latestNews[1].title}
                                        </a>
                                    </Title>
                                    <Paragraph style={{ marginBottom: 8 }}>
                                        <ClockCircleOutlined />{" "}
                                        {formatDateTime(
                                            latestNews[1].created_at
                                        )}
                                    </Paragraph>
                                    <Paragraph style={{ marginBottom: 8 }}>
                                        <EyeOutlined /> {latestNews[1].views}
                                    </Paragraph>
                                    <Paragraph>
                                        <LikeOutlined />{" "}
                                        {latestNews[1].likes_count ?? 0}
                                    </Paragraph>
                                </>
                            )}

                            {latestNews.slice(2, 7).map((news, index) => (
                                <Row
                                    gutter={16}
                                    align="middle"
                                    style={{ marginTop: 24 }}
                                    key={index}
                                >
                                    <Col span={8}>
                                        <Image
                                            src={news.image || "/img/noimg.jpg"}
                                            alt=""
                                            style={{ borderRadius: 8 }}
                                            preview={false}
                                        />
                                    </Col>
                                    <Col span={16}>
                                        <Title
                                            level={5}
                                            style={{ marginBottom: 8 }}
                                        >
                                            <a href={`/news/${news.id}`}>
                                                {news.title}
                                            </a>
                                        </Title>
                                        <Text
                                            type="secondary"
                                            style={{ display: "block" }}
                                        >
                                            <ClockCircleOutlined />{" "}
                                            {formatDateTime(news.created_at)}
                                        </Text>
                                        <Text
                                            type="secondary"
                                            style={{ display: "block" }}
                                        >
                                            <EyeOutlined /> {news.views}
                                        </Text>
                                        <Text type="secondary">
                                            <LikeOutlined />{" "}
                                            {news.likes_count ?? 0}
                                        </Text>
                                    </Col>
                                </Row>
                            ))}
                        </Card>
                    </Col>
                </Row>
            </div>
            {latestLoading && loadingWidget()}
            {!latestLoading && <LatestComponent latestNews={latestNews} />}
            {loadingCategory && loadingWidget()}
            {!loadingCategory && (
                <TrendingComponent
                    listCategory={listCategory}
                    activeKey={activeKey}
                    setActiveKey={setActiveKey}
                />
            )}
        </Content>
    );
};

export default HomeUI;

const container = document.getElementById("root");

if (container) {
    const root = ReactDOM.createRoot(container);
    root.render(
        <React.StrictMode>
            <BrowserRouter>
                {" "}
                {/* 🔁 Tambahkan BrowserRouter di sini */}
                <App>
                    <HomeUI />
                </App>
            </BrowserRouter>
        </React.StrictMode>
    );
}
