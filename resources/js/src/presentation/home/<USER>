import { useNewsLogic } from "../news/news_logic";
import { NewsRequest } from "../../model/request/news_request";
import { useCallback, useEffect, useRef, useState } from "react";
import { CategoryResponse } from "../../model/response/category_response";
import { CategoryRequest } from "../../model/request/category_request";
import { NewsService } from "../../services/dashboard/news_service";

export const useHomeLogic = () => {
    const [activeKey, setActiveKey] = useState("0");
    const {
        listNews: latestNews,
        loading: latestLoading,
        error: latestError,
        newsRequest: latestNewsRequest,
        setNewsRequest: setLatestNewsRequest,
        fetchData: fetchLatestNewsData,
    } = useNewsLogic(new NewsRequest.Data(1, 10, NewsRequest.NewsType.latest));
    const {
        listNews: topNews,
        loading: topLoading,
        error: topError,
        newsRequest: topNewsRequest,
        setNewsRequest: setTopNewsRequest,
        fetchData: fetchTopNewsData,
    } = useNewsLogic(new NewsRequest.Data(1, 10, NewsRequest.NewsType.top));
    const {
        listNews: popularNews,
        loading: popularLoading,
        error: popularError,
        newsRequest: popularNewsRequest,
        setNewsRequest: setPopularNewsRequest,
        fetchData: fetchPopularNewsData,
    } = useNewsLogic(new NewsRequest.Data(1, 10, NewsRequest.NewsType.popular));

    const [listCategory, setCategory] = useState<CategoryResponse.Data[]>([]);

    const [loadingCategory, setLoadingCategory] = useState(false);
    const [errorCategory, setErrorCategory] = useState<string | null>(null);
    var [categoryRequest, setCategoryRequest] = useState<CategoryRequest.Data>(
        new CategoryRequest.Data(1, 10, CategoryRequest.CategoryType.top)
    );
    // console.log(`newsRequest`, newsRequest.type);

    const hasFetched = useRef(false);

    const fetchDataCategory = useCallback(async () => {
        if (hasFetched.current) return;
        hasFetched.current = true;
        // console.log(`start fetch`, newsRequest.type);
        setLoadingCategory(true);
        try {
            const response = await NewsService.getListCategoryNews(
                categoryRequest
            );
            console.log(`Response Category`, response);

            if (response.status == true) {
                setCategory(response.data ?? []);
            } else {
                setErrorCategory(response.message ?? "Error");
            }
        } catch (error) {
            console.error(`Error getting data`, error);
            setErrorCategory((error as any).message);
        } finally {
            setLoadingCategory(false);
            console.log(`finally fetch`, categoryRequest.type);
            // hasFetched.current = false;
        }
    }, [categoryRequest]);

    useEffect(() => {
        fetchDataCategory();
    }, [fetchDataCategory]);
    // return { listNews, loading, error, newsRequest, setNewsRequest, fetchData };

    return {
        latestNews,
        latestLoading,
        latestError,
        latestNewsRequest,
        setLatestNewsRequest,
        fetchLatestNewsData,
        topNews,
        topLoading,
        topError,
        topNewsRequest,
        setTopNewsRequest,
        fetchTopNewsData,
        popularNews,
        popularLoading,
        popularError,
        popularNewsRequest,
        setPopularNewsRequest,
        fetchPopularNewsData,
        activeKey,
        setActiveKey,
        listCategory,
        loadingCategory,
        errorCategory,
        categoryRequest,
        setCategoryRequest,
        fetchDataCategory,
    };
};
