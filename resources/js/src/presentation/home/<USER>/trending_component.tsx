import { CategoryResponse } from "@/js/src/model/response/category_response";
import { NewsResponse } from "@/js/src/model/response/news_response";
import { Tabs, Row, Col, Card, Tag, Typography, Image } from "antd";
import {
    ClockCircleOutlined,
    EyeOutlined,
    LikeOutlined,
    CalendarOutlined,
} from "@ant-design/icons";
const { Title, Text, Paragraph } = Typography;
import TabPane from "antd/es/tabs/TabPane";
import React from "react";
import { Link } from "react-router-dom";
import { htmlComponent } from "@/js/src/shared/component/html_component";

interface TrendingComponentProps {
    activeKey: string;
    setActiveKey: React.Dispatch<React.SetStateAction<string>>;
    listCategory: CategoryResponse.Data[];
}
export const TrendingComponent: React.FC<TrendingComponentProps> = ({
    activeKey,
    setActiveKey,
    listCategory,
}) => {
    return (
        <div className="py-10 bg-white">
            <Title level={2} className="mb-4 border-b pb-2">
                Trending Category
            </Title>

            <Tabs
                activeKey={activeKey}
                onChange={setActiveKey}
                type="card"
                tabBarGutter={16}
            >
                {listCategory.length > 0 &&
                    listCategory.slice(0, 3).map((category, index) => (
                        <TabPane tab={category.name} key={index.toString()}>
                            <Row gutter={[24, 24]}>
                                <Col lg={16}>
                                    {(category.news ?? []).length > 0 &&
                                        category.news![0] && (
                                            <Card
                                                cover={
                                                    <Image
                                                        alt={
                                                            category.news![0]
                                                                .title
                                                        }
                                                        src={
                                                            NewsResponse.getImageUrl(
                                                                category.news![0]
                                                            ) ||
                                                            "/img/noimg.jpg"
                                                        }
                                                        preview={false}
                                                    />
                                                }
                                                style={{
                                                    border: "none",
                                                }}
                                                // bordered={false}
                                            >
                                                <Tag
                                                    color="blue"
                                                    style={{
                                                        position: "absolute",
                                                        top: 20,
                                                        right: 20,
                                                    }}
                                                >
                                                    {category.name}
                                                </Tag>
                                                <Title level={4}>
                                                    <Link
                                                        to={`/news/${
                                                            category.news![0]
                                                                .id + "/show"
                                                        }`}
                                                    >
                                                        {
                                                            category.news![0]
                                                                .title
                                                        }
                                                    </Link>
                                                </Title>
                                                <div className="mb-2 flex flex-wrap gap-4 text-gray-600">
                                                    <Text>
                                                        <ClockCircleOutlined />{" "}
                                                        {category.news![0]
                                                            .created_at
                                                            ? new Date(
                                                                  category.news![0].created_at
                                                              ).toLocaleDateString()
                                                            : ""}
                                                    </Text>
                                                    <Text>
                                                        <EyeOutlined />{" "}
                                                        {
                                                            category.news![0]
                                                                .views
                                                        }
                                                    </Text>
                                                    <Text>
                                                        <LikeOutlined />{" "}
                                                        {
                                                            category.news![0]
                                                                .likes_count
                                                        }
                                                    </Text>
                                                </div>
                                                {htmlComponent({
                                                    html:
                                                        category.news![0].content?.slice(
                                                            0,
                                                            450
                                                        ) + " ...",
                                                })}
                                                {/* <Paragraph>
                                                        {category.news![0].content?.slice(
                                                            0,
                                                            450
                                                        )}
                                                        ...
                                                    </Paragraph> */}
                                            </Card>
                                        )}
                                </Col>

                                <Col lg={8}>
                                    {category.news &&
                                        category.news
                                            .slice(1, 6)
                                            .map((newsItem) => (
                                                <Row
                                                    gutter={16}
                                                    key={newsItem.id}
                                                    className="mb-4"
                                                >
                                                    <Col span={10}>
                                                        <Image
                                                            src={
                                                                newsItem.image ||
                                                                "/img/noimg.jpg"
                                                            }
                                                            alt={newsItem.title}
                                                            width="100%"
                                                            className="rounded"
                                                            preview={false}
                                                        />
                                                    </Col>
                                                    <Col span={14}>
                                                        <Text
                                                            type="secondary"
                                                            className="uppercase text-xs"
                                                        >
                                                            {category.name}
                                                        </Text>
                                                        <Title level={5}>
                                                            <Link
                                                                to={`/news/${newsItem.id}/show`}
                                                            >
                                                                {newsItem.title}
                                                            </Link>
                                                        </Title>
                                                        <Text className="block text-sm">
                                                            <CalendarOutlined />{" "}
                                                            {newsItem.created_at
                                                                ? new Date(
                                                                      newsItem.created_at
                                                                  ).toLocaleDateString()
                                                                : ""}
                                                        </Text>
                                                        <Text className="block text-sm">
                                                            <EyeOutlined />{" "}
                                                            {newsItem.views}
                                                        </Text>
                                                        <Text className="block text-sm">
                                                            <LikeOutlined />{" "}
                                                            {
                                                                newsItem.likes_count
                                                            }
                                                        </Text>
                                                    </Col>
                                                </Row>
                                            ))}
                                </Col>
                            </Row>
                        </TabPane>
                    ))}
            </Tabs>
        </div>
    );
};
