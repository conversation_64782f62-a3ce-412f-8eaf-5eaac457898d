import { Card, Typography } from "antd";
import React from "react";
import dayjs from "dayjs";
import "dayjs/locale/id";
import { NewsResponse } from "@/js/src/model/response/news_response";

const { Title, Text } = Typography;

interface LatestComponentProps {
    latestNews: NewsResponse.Data[];
}

export const LatestComponent: React.FC<LatestComponentProps> = ({
    latestNews,
}) => {
    function translatedFormat(dateString: string, formatStr: string) {
        return dayjs(dateString)
            .locale("id")
            .format(
                formatStr
                    .replace("j", "D")
                    .replace("F", "MMMM")
                    .replace("Y", "YYYY")
            );
    }

    return (
        <div style={{ padding: "2rem 0" }}>
            <Title level={2}>Latest News</Title>
            <div
                style={{
                    display: "flex",
                    gap: "1rem",
                    overflowX: "auto",
                    paddingBottom: "1rem",
                }}
            >
                {latestNews.map((news, index) => (
                    <Card
                        key={news.id ?? index}
                        hoverable
                        style={{
                            width: 300,
                            flexShrink: 0,
                            backgroundColor: "#f0f0f0",
                            padding: "1rem",
                        }}
                        cover={
                            <img
                                alt={news.title}
                                src={
                                    news.image
                                        ? `/storage/images/${news.image}`
                                        : "/img/noimg.jpg"
                                }
                                style={{
                                    // height: ,
                                    objectFit: "contain",
                                }}
                            />
                        }
                    >
                        <Card.Meta
                            title={
                                <a href={`/news/${news.id}/show`}>
                                    {news.title && news.title.length > 35
                                        ? news.title.slice(0, 35) + "..."
                                        : news.title}
                                </a>
                            }
                            description={
                                <div>
                                    <Text
                                        type="secondary"
                                        style={{ display: "block" }}
                                    >
                                        {news.author?.name
                                            ? `by ${news.author.name}`
                                            : ""}
                                    </Text>
                                    <Text type="secondary">
                                        {news.created_at
                                            ? translatedFormat(
                                                  typeof news.created_at ===
                                                      "string"
                                                      ? news.created_at
                                                      : news.created_at.toISOString(),
                                                  "j F Y"
                                              )
                                            : ""}
                                    </Text>
                                </div>
                            }
                        />
                    </Card>
                ))}
            </div>
        </div>
    );
};
