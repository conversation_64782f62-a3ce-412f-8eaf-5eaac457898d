import { useCallback, useEffect, useState, useRef } from "react";
import { NewsRequest } from "../../model/request/news_request";
import { NewsResponse } from "../../model/response/news_response";
import { NewsService } from "../../services/dashboard/news_service";

export const useNewsLogic = (request: NewsRequest.Data) => {
    const [listNews, setNews] = useState<NewsResponse.Data[]>([]);

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    var [newsRequest, setNewsRequest] = useState<NewsRequest.Data>(request);
    // console.log(`newsRequest`, newsRequest.type);

    const hasFetched = useRef(false);

    const fetchData = useCallback(async () => {
        if (hasFetched.current) return;
        hasFetched.current = true;
        // console.log(`start fetch`, newsRequest.type);
        setLoading(true);
        try {
            const response = await NewsService.getListNews(newsRequest);
            console.log(`Response News`, response);

            if (response.status == true) {
                setNews(response.data ?? []);
            } else {
                setError(response.message ?? "Error");
            }
        } catch (error) {
            console.error(`Error getting data`, error);
            setError((error as any).message);
        } finally {
            setLoading(false);
            console.log(`finally fetch`, newsRequest.type);
            // hasFetched.current = false;
        }
    }, [newsRequest]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);
    return { listNews, loading, error, newsRequest, setNewsRequest, fetchData };
};
