import { baseUrlImage } from "../../shared/constant/variable";
import { AuthorResponse } from "./author_response";
import { BaseResponse } from "./base_response";

export namespace NewsResponse {
    export type Response = BaseResponse<Data[]>;
    export interface Data {
        id?: number;
        created_at?: Date;
        updated_at?: Date;
        title?: string;
        content?: string;
        image?: string;
        image_alt?: string;
        status?: string;
        user_id?: number;
        category_id?: number;
        views?: number;
        likes_count?: number;
        author?: AuthorResponse.Data;
    }

    export function getImageUrl(data: Data): string {
        return baseUrlImage + data.image;
    }

    // Converts JSON strings to/from your types
    export class Convert {
        public static toData(json: string): Response {
            return JSON.parse(json);
        }

        public static dataToJson(value: Response): string {
            return JSON.stringify(value);
        }
    }
}
