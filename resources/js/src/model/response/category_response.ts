import { BaseResponse } from "./base_response";
import { NewsResponse } from "./news_response";

export namespace CategoryResponse {
    export type Response = BaseResponse<Data[]>;
    export interface Data {
        id?: number;
        created_at?: Date;
        updated_at?: Date;
        name?: string;
        views?: number;
        news?: NewsResponse.Data[];
    }

    // Converts JSON strings to/from your types
    export class Convert {
        public static toData(json: string): Response {
            return JSON.parse(json);
        }

        public static dataToJson(value: Data): string {
            return JSON.stringify(value);
        }
    }
}
