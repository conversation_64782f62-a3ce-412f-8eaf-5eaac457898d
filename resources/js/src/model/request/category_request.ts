export namespace CategoryRequest {
    export class Data {
        page: number;
        perPage: number;
        type: CategoryType;
        constructor(page: number, perPage: number, type: CategoryType) {
            this.page = page;
            this.perPage = perPage;
            this.type = type;
        }
    }

    export enum CategoryType {
        top = "top",
    }

    export function toMap(data: Data): { [key: string]: string } {
        return {
            page: data.page.toString(),
            per_page: data.perPage.toString(),
            type: typeToName(data.type),
        };
    }

    export function typeToName(type: CategoryType): string {
        switch (type) {
            case CategoryType.top:
                return "top";
            default:
                return "top";
        }
    }

    export function typeFromName(name: string): CategoryType {
        switch (name) {
            case "top":
                return CategoryType.top;
            default:
                return CategoryType.top;
        }
    }
}
