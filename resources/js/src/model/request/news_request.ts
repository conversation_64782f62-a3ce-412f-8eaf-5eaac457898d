export namespace NewsRequest {
    export class Data {
        page: number;
        perPage: number;
        type: NewsType;
        constructor(page: number, perPage: number, type: NewsType) {
            this.page = page;
            this.perPage = perPage;
            this.type = type;
        }
    }

    export enum NewsType {
        latest = "latest",
        top = "top",
        popular = "popular",
    }

    export function toMap(data: Data): { [key: string]: string } {
        return {
            page: data.page.toString(),
            per_page: data.perPage.toString(),
            type: typeToName(data.type),
        };
    }

    export function typeToName(type: NewsType): string {
        switch (type) {
            case NewsType.latest:
                return "latest";
            case NewsType.top:
                return "top";
            case NewsType.popular:
                return "popular";
            default:
                return "latest";
        }
    }

    export function typeFromName(name: string): NewsType {
        switch (name) {
            case "latest":
                return NewsType.latest;
            case "top":
                return NewsType.top;
            case "popular":
                return NewsType.popular;
            default:
                return NewsType.latest;
        }
    }
}
