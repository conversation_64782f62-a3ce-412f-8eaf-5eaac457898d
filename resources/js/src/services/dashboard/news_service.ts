import { CategoryRequest } from "../../model/request/category_request";
import { NewsRequest } from "../../model/request/news_request";
import { CategoryResponse } from "../../model/response/category_response";
import { NewsResponse } from "../../model/response/news_response";
import { UrlPath } from "../../shared/constant/url_path";
import { NetworkUtils } from "../../shared/network/network";

export class NewsService {
    static async getListNews(
        request: NewsRequest.Data
    ): Promise<NewsResponse.Response> {
        var response = await NetworkUtils(
            UrlPath.NEWS,
            "GET",
            NewsRequest.toMap(request)
        );
        // console.log(JSON.stringify(response.data));
        var result = NewsResponse.Convert.toData(JSON.stringify(response));
        return result;
    }
    static async getListCategoryNews(
        request: CategoryRequest.Data
    ): Promise<CategoryResponse.Response> {
        var response = await NetworkUtils(
            UrlPath.CATEGORY,
            "GET",
            CategoryRequest.toMap(request)
        );
        // console.log(JSON.stringify(response.data));
        var result = CategoryResponse.Convert.toData(JSON.stringify(response));
        return result;
    }
}
