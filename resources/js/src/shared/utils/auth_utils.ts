// import { useEffect } from "react";
// import { useNavigate } from "react-router-dom";

// export const useTokenFromUrl = () => {
//     useEffect(() => {
//         const urlParams = new URLSearchParams(window.location.search);
//         const tokenFromUrl = urlParams.get("token");

//         if (tokenFromUrl) {
//             localStorage.setItem("token", tokenFromUrl);
//             console.log("Token disimpan dari URL:", tokenFromUrl);
//         } else {
//             const tokenFromStorage = localStorage.getItem("token");
//             if (tokenFromStorage) {
//                 console.log("Token dari localStorage:", tokenFromStorage);
//             } else {
//                 console.log(
//                     "Token tidak ditemukan di URL maupun localStorage."
//                 );
//             }
//         }
//     }, []);
// };

// export const useNavigateFromRoute = () => {
//     const navigate = useNavigate();

//     useEffect(() => {
//         const urlParams = new URLSearchParams(window.location.search);
//         const route = urlParams.get("route");

//         if (route) {
//             navigate(`/${route}`);
//             console.log(`Navigating to /${route}`);
//         } else {
//             console.log("Route tidak ditemukan di URL.");
//         }
//     }, [navigate]);
// };
