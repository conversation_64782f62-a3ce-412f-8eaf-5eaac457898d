import axios from "axios";
import { ResponseModel } from "../../model/response_model";

let env = import.meta.env;
let baseUrl = env.VITE_BASE_URL;

export const NetworkUtils = async (
    path: string,
    method: string,
    params?: any,
    data?: any,
    url?: string,
    responseType: any = "json"
): Promise<ResponseModel> => {
    let endpoint = (url ?? baseUrl) + "/" + path;
    // let endpoint = '/' + path;
    // console.log(endpoint);

    const config: any = {
        headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
        },
        responseType: responseType,
    };
    const accessToken = localStorage.getItem("access_token");
    if (accessToken) config.headers["Authorization"] = `Bearer ${accessToken}`;

    let res = await axios({
        url: endpoint,
        method,
        data,
        params,
        ...config,
    });
    if (res.status === 200) {
        return ResponseModel.from(res);
    }
    var result = new ResponseModel();
    result.message = res.data.message ?? res.statusText ?? "Error";
    result.status = false;
    result.data = res.data;
    return result;
};
