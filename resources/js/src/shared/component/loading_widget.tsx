import { Spin } from "antd";
import React from "react";
import { FourSquare, ThreeDot, } from "react-loading-indicators";
import { PropagateLoader } from "react-spinners";

export const loadingWidget = (message?: string, color = "#3E93E1") => {
    return (
        <div className="loading-container">
            {/* <PropagateLoader color={color} /> */}
            <Spin size="large" />
            <br />
            <p>{message ?? "Loading data, please wait..."}</p>
        </div>
    );
}



export const AnimatedFourSquareLoader = (message?: string, color = "#3E93E1") => {
    return (
        <>
            <FourSquare color={["#003366", "#327fcd", "#f1b815", "yellow"]}
                size="medium" text="" textColor="black" />
        </>
    );
}

export const AnimatedThreeDotsLoader = (message?: string, color = "#3E93E1") => {
    return (
        <>
            <ThreeDot color={["#003366", "#327fcd", "#f1b815", "yellow"]} />
        </>

    )

}