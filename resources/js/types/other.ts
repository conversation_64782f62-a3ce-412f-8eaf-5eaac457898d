export interface User {
  id: number
  name: string
  email: string
  created_at: string
  role?: string
}

export interface Auth {
  user: User | null
}

export interface Flash {
  message?: string
  error?: string
  success?: string
}

export interface SharedProps {
  auth: Auth
  appName: string
  flash: Flash
}

export interface PageProps extends SharedProps {
  [key: string]: any
}

export interface NewsArticle {
  id: number
  title: string
  excerpt: string
  content: string
  slug: string
  featured_image?: string
  author: {
    id: number
    name: string
  }
  category: {
    id: number
    name: string
    slug: string
  }
  status: 'draft' | 'pending' | 'published'
  views_count: number
  likes_count: number
  created_at: string
  updated_at: string
}

export interface Category {
  id: number
  name: string
  slug: string
}

export interface NewsFilterOptions {
  search?: string
  category_id?: number
  date_from?: string
  date_to?: string
  sort?: 'latest' | 'popular' | 'top'
  page?: number
  perPage?: number
}

export interface PaginatedResponse<T> {
  data: T[]
  current_page: number
  last_page: number
  per_page: number
  total: number
  from: number
  to: number
}

export interface PaginationLink {
  url: string | null
  label: string
  active: boolean
}

export interface PaginationData<T = any> {
  current_page: number
  data: T[]
  first_page_url: string
  from: number
  last_page: number
  last_page_url: string
  links: PaginationLink[]
  next_page_url: string | null
  path: string
  per_page: number
  prev_page_url: string | null
  to: number
  total: number
}

export interface DashboardStats {
  totalNews: number
  totalUsers: number
  totalViews: number
  totalLikes: number
}

export interface RecentNewsItem {
  id: number
  title: string
  status: string
  created_at: string
  views_count: number
}