export interface WeatherLocation {
  latitude: number;
  longitude: number;
  city: string;
  country: string;
  countryCode: string;
  timezone: string;
  elevation?: number;
}

export interface CurrentWeather {
  temperature: number;
  humidity: number;
  windSpeed: number;
  windDirection: number;
  weatherCode: number;
  isDay: boolean;
  time: string;
  apparentTemperature: number;
  precipitation: number;
  cloudCover: number;
  pressure: number;
  visibility: number;
  uvIndex: number;
}

export interface HourlyWeather {
  time: string[];
  temperature: number[];
  humidity: number[];
  windSpeed: number[];
  windDirection: number[];
  weatherCode: number[];
  isDay: number[];
  precipitation: number[];
  cloudCover: number[];
  pressure: number[];
  visibility: number[];
  uvIndex: number[];
}

export interface DailyWeather {
  time: string[];
  weatherCode: number[];
  temperatureMax: number[];
  temperatureMin: number[];
  sunrise: string[];
  sunset: string[];
  precipitationSum: number[];
  windSpeedMax: number[];
  windDirectionDominant: number[];
  uvIndexMax: number[];
}

export interface WeatherData {
  location: WeatherLocation;
  current: CurrentWeather;
  hourly: HourlyWeather;
  daily: DailyWeather;
  timezone: string;
  timezoneAbbreviation: string;
  elevation: number;
  generationTimeMs: number;
}

export interface WeatherCondition {
  code: number;
  description: string;
  icon: string;
  isDaytime: boolean;
}

export interface ProcessedWeatherData {
  location: WeatherLocation;
  current: {
    temperature: number;
    condition: WeatherCondition;
    humidity: number;
    windSpeed: number;
    windDirection: string;
    feelsLike: number;
    pressure: number;
    visibility: number;
    uvIndex: number;
    isDay: boolean;
    time: Date;
  };
  hourlyForecast: Array<{
    time: Date;
    temperature: number;
    condition: WeatherCondition;
    precipitation: number;
    windSpeed: number;
  }>;
  dailyForecast: Array<{
    date: Date;
    condition: WeatherCondition;
    temperatureMax: number;
    temperatureMin: number;
    sunrise: Date;
    sunset: Date;
    precipitation: number;
    windSpeed: number;
    uvIndex: number;
  }>;
}

export interface GeocodingResult {
  id: number;
  name: string;
  latitude: number;
  longitude: number;
  elevation: number;
  featureCode: string;
  countryCode: string;
  admin1Id?: number;
  admin2Id?: number;
  admin3Id?: number;
  admin4Id?: number;
  timezone: string;
  population?: number;
  countryId: number;
  country: string;
  admin1?: string;
  admin2?: string;
  admin3?: string;
  admin4?: string;
}

export interface GeocodingResponse {
  results: GeocodingResult[];
  generationTimeMs: number;
}

export interface WeatherApiError {
  error: boolean;
  reason: string;
}