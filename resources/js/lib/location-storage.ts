interface LocationPreference {
  type: 'granted' | 'denied' | 'manual';
  city: string;
  timestamp: number;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

interface SavedLocation {
  city: string;
  country: string;
  latitude: number;
  longitude: number;
  countryCode: string;
  timezone: string;
  timestamp: number;
}

const LOCATION_PREFERENCE_KEY = 'weather-location-preference';
const SAVED_CITY_KEY = 'weather-saved-city';
const LOCATION_TIMESTAMP_KEY = 'weather-location-timestamp';
const SAVED_LOCATIONS_KEY = 'weather-saved-locations';
const LOCATION_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

export class LocationStorage {
  /**
   * Save user's location preference
   */
  static saveLocationPreference(preference: LocationPreference): void {
    try {
      localStorage.setItem(LOCATION_PREFERENCE_KEY, preference.type);
      localStorage.setItem(SAVED_CITY_KEY, preference.city);
      localStorage.setItem(LOCATION_TIMESTAMP_KEY, preference.timestamp.toString());
      
      if (preference.coordinates) {
        localStorage.setItem('weather-coordinates', JSON.stringify(preference.coordinates));
      }
    } catch (error) {
      console.warn('Failed to save location preference:', error);
    }
  }

  /**
   * Get user's location preference
   */
  static getLocationPreference(): LocationPreference | null {
    try {
      const type = localStorage.getItem(LOCATION_PREFERENCE_KEY) as 'granted' | 'denied' | 'manual' | null;
      const city = localStorage.getItem(SAVED_CITY_KEY);
      const timestamp = localStorage.getItem(LOCATION_TIMESTAMP_KEY);
      const coordinates = localStorage.getItem('weather-coordinates');

      if (!type || !city || !timestamp) {
        return null;
      }

      return {
        type,
        city,
        timestamp: parseInt(timestamp),
        coordinates: coordinates ? JSON.parse(coordinates) : undefined
      };
    } catch (error) {
      console.warn('Failed to get location preference:', error);
      return null;
    }
  }

  /**
   * Check if saved location preference is still valid
   */
  static isLocationPreferenceValid(): boolean {
    const preference = this.getLocationPreference();
    if (!preference) return false;

    const now = Date.now();
    const isExpired = (now - preference.timestamp) > LOCATION_CACHE_DURATION;
    
    return !isExpired;
  }

  /**
   * Get saved city name
   */
  static getSavedCity(): string | null {
    try {
      return localStorage.getItem(SAVED_CITY_KEY);
    } catch (error) {
      console.warn('Failed to get saved city:', error);
      return null;
    }
  }

  /**
   * Save city name
   */
  static setSavedCity(city: string): void {
    try {
      localStorage.setItem(SAVED_CITY_KEY, city);
      localStorage.setItem(LOCATION_TIMESTAMP_KEY, Date.now().toString());
    } catch (error) {
      console.warn('Failed to save city:', error);
    }
  }

  /**
   * Save a location to favorites
   */
  static addSavedLocation(location: Omit<SavedLocation, 'timestamp'>): void {
    try {
      const savedLocations = this.getSavedLocations();
      const newLocation: SavedLocation = {
        ...location,
        timestamp: Date.now()
      };

      // Check if location already exists
      const exists = savedLocations.some(
        saved => Math.abs(saved.latitude - location.latitude) < 0.001 && 
                Math.abs(saved.longitude - location.longitude) < 0.001
      );

      if (!exists) {
        savedLocations.push(newLocation);
        // Keep only last 10 locations
        const recentLocations = savedLocations.slice(-10);
        localStorage.setItem(SAVED_LOCATIONS_KEY, JSON.stringify(recentLocations));
      }
    } catch (error) {
      console.warn('Failed to save location:', error);
    }
  }

  /**
   * Get all saved locations
   */
  static getSavedLocations(): SavedLocation[] {
    try {
      const saved = localStorage.getItem(SAVED_LOCATIONS_KEY);
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.warn('Failed to get saved locations:', error);
      return [];
    }
  }

  /**
   * Remove a saved location
   */
  static removeSavedLocation(latitude: number, longitude: number): void {
    try {
      const savedLocations = this.getSavedLocations();
      const filtered = savedLocations.filter(
        location => !(Math.abs(location.latitude - latitude) < 0.001 && 
                     Math.abs(location.longitude - longitude) < 0.001)
      );
      localStorage.setItem(SAVED_LOCATIONS_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.warn('Failed to remove saved location:', error);
    }
  }

  /**
   * Check if location is saved
   */
  static isLocationSaved(latitude: number, longitude: number): boolean {
    const savedLocations = this.getSavedLocations();
    return savedLocations.some(
      location => Math.abs(location.latitude - latitude) < 0.001 && 
                 Math.abs(location.longitude - longitude) < 0.001
    );
  }

  /**
   * Clear all location data
   */
  static clearAllLocationData(): void {
    try {
      localStorage.removeItem(LOCATION_PREFERENCE_KEY);
      localStorage.removeItem(SAVED_CITY_KEY);
      localStorage.removeItem(LOCATION_TIMESTAMP_KEY);
      localStorage.removeItem(SAVED_LOCATIONS_KEY);
      localStorage.removeItem('weather-coordinates');
    } catch (error) {
      console.warn('Failed to clear location data:', error);
    }
  }

  /**
   * Get default fallback city based on browser settings
   */
  static getDefaultCity(): string {
    try {
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const language = navigator.language || 'en-US';

      // Indonesian timezone or language
      if (timeZone.includes('Jakarta') || 
          timeZone.includes('Indonesia') || 
          language.startsWith('id')) {
        return 'Jakarta';
      }

      // Other Southeast Asian countries
      if (timeZone.includes('Singapore')) return 'Singapore';
      if (timeZone.includes('Kuala_Lumpur')) return 'Kuala Lumpur';
      if (timeZone.includes('Bangkok')) return 'Bangkok';
      if (timeZone.includes('Manila')) return 'Manila';

      // Default fallback
      return 'Jakarta';
    } catch (error) {
      console.warn('Failed to determine default city:', error);
      return 'Jakarta';
    }
  }

  /**
   * Initialize location preference with default
   */
  static initializeDefault(): void {
    const existing = this.getLocationPreference();
    if (!existing) {
      const defaultCity = this.getDefaultCity();
      this.saveLocationPreference({
        type: 'manual',
        city: defaultCity,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Get location preference status for UI
   */
  static getLocationStatus(): {
    hasPermission: boolean;
    city: string;
    isExpired: boolean;
    preferenceType: 'granted' | 'denied' | 'manual' | null;
  } {
    const preference = this.getLocationPreference();
    const isValid = this.isLocationPreferenceValid();

    return {
      hasPermission: preference?.type === 'granted' || false,
      city: preference?.city || this.getDefaultCity(),
      isExpired: !isValid,
      preferenceType: preference?.type || null
    };
  }

  /**
   * Should show location dialog
   */
  static shouldShowLocationDialog(): boolean {
    const preference = this.getLocationPreference();
    
    // No preference saved
    if (!preference) return true;
    
    // Preference expired
    if (!this.isLocationPreferenceValid()) return true;
    
    // User denied and no city saved
    if (preference.type === 'denied' && !preference.city) return true;
    
    return false;
  }
}

export default LocationStorage;