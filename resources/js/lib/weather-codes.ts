export interface WeatherCodeInfo {
    description: string;
    icon: string;
    isDaytime: boolean;
}

export const weatherCodes: Record<
    number,
    { day: WeatherCodeInfo; night: WeatherCodeInfo }
> = {
    0: {
        day: { description: "Clear sky", icon: "☀️", isDaytime: true },
        night: { description: "Clear sky", icon: "🌙", isDaytime: false },
    },
    1: {
        day: { description: "Mainly clear", icon: "🌤️", isDaytime: true },
        night: { description: "Mainly clear", icon: "🌙", isDaytime: false },
    },
    2: {
        day: { description: "Partly cloudy", icon: "⛅", isDaytime: true },
        night: { description: "Partly cloudy", icon: "☁️", isDaytime: false },
    },
    3: {
        day: { description: "Overcast", icon: "☁️", isDaytime: true },
        night: { description: "Overcast", icon: "☁️", isDaytime: false },
    },
    45: {
        day: { description: "Fog", icon: "🌫️", isDaytime: true },
        night: { description: "Fog", icon: "🌫️", isDaytime: false },
    },
    48: {
        day: {
            description: "Depositing rime fog",
            icon: "🌫️",
            isDaytime: true,
        },
        night: {
            description: "Depositing rime fog",
            icon: "🌫️",
            isDaytime: false,
        },
    },
    51: {
        day: { description: "Light drizzle", icon: "🌦️", isDaytime: true },
        night: { description: "Light drizzle", icon: "🌧️", isDaytime: false },
    },
    53: {
        day: { description: "Moderate drizzle", icon: "🌦️", isDaytime: true },
        night: {
            description: "Moderate drizzle",
            icon: "🌧️",
            isDaytime: false,
        },
    },
    55: {
        day: { description: "Dense drizzle", icon: "🌧️", isDaytime: true },
        night: { description: "Dense drizzle", icon: "🌧️", isDaytime: false },
    },
    56: {
        day: {
            description: "Light freezing drizzle",
            icon: "🌨️",
            isDaytime: true,
        },
        night: {
            description: "Light freezing drizzle",
            icon: "🌨️",
            isDaytime: false,
        },
    },
    57: {
        day: {
            description: "Dense freezing drizzle",
            icon: "🌨️",
            isDaytime: true,
        },
        night: {
            description: "Dense freezing drizzle",
            icon: "🌨️",
            isDaytime: false,
        },
    },
    61: {
        day: { description: "Slight rain", icon: "🌦️", isDaytime: true },
        night: { description: "Slight rain", icon: "🌧️", isDaytime: false },
    },
    63: {
        day: { description: "Moderate rain", icon: "🌧️", isDaytime: true },
        night: { description: "Moderate rain", icon: "🌧️", isDaytime: false },
    },
    65: {
        day: { description: "Heavy rain", icon: "🌧️", isDaytime: true },
        night: { description: "Heavy rain", icon: "🌧️", isDaytime: false },
    },
    66: {
        day: {
            description: "Light freezing rain",
            icon: "🌨️",
            isDaytime: true,
        },
        night: {
            description: "Light freezing rain",
            icon: "🌨️",
            isDaytime: false,
        },
    },
    67: {
        day: {
            description: "Heavy freezing rain",
            icon: "🌨️",
            isDaytime: true,
        },
        night: {
            description: "Heavy freezing rain",
            icon: "🌨️",
            isDaytime: false,
        },
    },
    71: {
        day: { description: "Slight snow fall", icon: "🌨️", isDaytime: true },
        night: {
            description: "Slight snow fall",
            icon: "🌨️",
            isDaytime: false,
        },
    },
    73: {
        day: { description: "Moderate snow fall", icon: "❄️", isDaytime: true },
        night: {
            description: "Moderate snow fall",
            icon: "❄️",
            isDaytime: false,
        },
    },
    75: {
        day: { description: "Heavy snow fall", icon: "❄️", isDaytime: true },
        night: { description: "Heavy snow fall", icon: "❄️", isDaytime: false },
    },
    77: {
        day: { description: "Snow grains", icon: "🌨️", isDaytime: true },
        night: { description: "Snow grains", icon: "🌨️", isDaytime: false },
    },
    80: {
        day: {
            description: "Slight rain showers",
            icon: "🌦️",
            isDaytime: true,
        },
        night: {
            description: "Slight rain showers",
            icon: "🌧️",
            isDaytime: false,
        },
    },
    81: {
        day: {
            description: "Moderate rain showers",
            icon: "🌧️",
            isDaytime: true,
        },
        night: {
            description: "Moderate rain showers",
            icon: "🌧️",
            isDaytime: false,
        },
    },
    82: {
        day: {
            description: "Violent rain showers",
            icon: "⛈️",
            isDaytime: true,
        },
        night: {
            description: "Violent rain showers",
            icon: "⛈️",
            isDaytime: false,
        },
    },
    85: {
        day: {
            description: "Slight snow showers",
            icon: "🌨️",
            isDaytime: true,
        },
        night: {
            description: "Slight snow showers",
            icon: "🌨️",
            isDaytime: false,
        },
    },
    86: {
        day: { description: "Heavy snow showers", icon: "❄️", isDaytime: true },
        night: {
            description: "Heavy snow showers",
            icon: "❄️",
            isDaytime: false,
        },
    },
    95: {
        day: { description: "Thunderstorm", icon: "⛈️", isDaytime: true },
        night: { description: "Thunderstorm", icon: "⛈️", isDaytime: false },
    },
    96: {
        day: {
            description: "Thunderstorm with slight hail",
            icon: "⛈️",
            isDaytime: true,
        },
        night: {
            description: "Thunderstorm with slight hail",
            icon: "⛈️",
            isDaytime: false,
        },
    },
    99: {
        day: {
            description: "Thunderstorm with heavy hail",
            icon: "⛈️",
            isDaytime: true,
        },
        night: {
            description: "Thunderstorm with heavy hail",
            icon: "⛈️",
            isDaytime: false,
        },
    },
};

export function getWeatherCondition(
    code: number,
    isDay: boolean = true,
): WeatherCodeInfo {
    const weatherInfo = weatherCodes[code];
    if (!weatherInfo) {
        return {
            description: "Unknown weather condition",
            icon: "❓",
            isDaytime: isDay,
        };
    }
    return isDay ? weatherInfo.day : weatherInfo.night;
}

export function getWindDirection(degrees: number): string {
    const directions = [
        "N",
        "NNE",
        "NE",
        "ENE",
        "E",
        "ESE",
        "SE",
        "SSE",
        "S",
        "SSW",
        "SW",
        "WSW",
        "W",
        "WNW",
        "NW",
        "NNW",
    ];

    const index = Math.round(degrees / 22.5) % 16;
    return directions[index];
}

export function celsiusToFahrenheit(celsius: number): number {
    return (celsius * 9) / 5 + 32;
}

export function fahrenheitToCelsius(fahrenheit: number): number {
    return ((fahrenheit - 32) * 5) / 9;
}

export function kmhToMph(kmh: number): number {
    return kmh * 0.621371;
}

export function mphToKmh(mph: number): number {
    return mph * 1.60934;
}

export function hpaToInHg(hpa: number): number {
    return hpa * 0.02953;
}

export function inHgToHpa(inHg: number): number {
    return inHg * 33.8639;
}

export interface ConvertedWeatherValues {
    temperature: string;
    temperatureValue?: number;
    temperatureUnit: string;
    feelsLike: string;
    windSpeed: string;
    pressure: string;
    temperatureRange?: string;
}

export function convertWeatherUnits(
    temperature: number,
    feelsLike: number,
    windSpeed: number,
    pressure: number,
    temperatureUnit: "celsius" | "fahrenheit",
    windSpeedUnit: "kmh" | "mph",
    pressureUnit: "hpa" | "inHg",
    temperatureMin?: number,
    temperatureMax?: number,
): ConvertedWeatherValues {
    // Convert temperature
    const tempValue =
        temperatureUnit === "fahrenheit"
            ? celsiusToFahrenheit(temperature)
            : temperature;
    const feelsLikeValue =
        temperatureUnit === "fahrenheit"
            ? celsiusToFahrenheit(feelsLike)
            : feelsLike;
    const tempUnit = temperatureUnit === "fahrenheit" ? "°F" : "°C";

    // Convert wind speed
    const windValue = windSpeedUnit === "mph" ? kmhToMph(windSpeed) : windSpeed;
    const windUnit = windSpeedUnit === "mph" ? "mph" : "km/h";

    // Convert pressure
    const pressureValue =
        pressureUnit === "inHg" ? hpaToInHg(pressure) : pressure;
    const pressureUnitText = pressureUnit === "inHg" ? "inHg" : "hPa";

    // Format temperature range if provided
    let temperatureRange: string | undefined;
    if (temperatureMin !== undefined && temperatureMax !== undefined) {
        const minTemp =
            temperatureUnit === "fahrenheit"
                ? celsiusToFahrenheit(temperatureMin)
                : temperatureMin;
        const maxTemp =
            temperatureUnit === "fahrenheit"
                ? celsiusToFahrenheit(temperatureMax)
                : temperatureMax;
        temperatureRange = `${Math.round(minTemp)}° / ${Math.round(maxTemp)}${tempUnit}`;
    }

    return {
        temperature: `${Math.round(tempValue)}${tempUnit}`,
        temperatureValue: Math.round(tempValue),
        temperatureUnit: tempUnit,
        feelsLike: `${Math.round(feelsLikeValue)}${tempUnit}`,
        windSpeed: `${Math.round(windValue)} ${windUnit}`,
        pressure: `${Math.round(pressureValue * 100) / 100} ${pressureUnitText}`,
        temperatureRange,
    };
}

export function formatTime(date: Date, format: "12h" | "24h"): string {
    if (format === "12h") {
        return date.toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
        });
    } else {
        return date.toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
        });
    }
}

export function formatDate(date: Date, locale: string = "en-US"): string {
    return date.toLocaleDateString(locale, {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
    });
}

export function formatShortDate(date: Date, locale: string = "en-US"): string {
    return date.toLocaleDateString(locale, {
        weekday: "short",
        month: "short",
        day: "numeric",
    });
}

export function getWeatherAdvice(
    temperature: number,
    weatherCode: number,
    windSpeed: number,
    humidity: number,
): string[] {
    const advice: string[] = [];

    // Temperature advice
    if (temperature < 0) {
        advice.push("Bundle up! It's freezing outside.");
    } else if (temperature < 10) {
        advice.push("Dress warmly, it's quite cold.");
    } else if (temperature > 30) {
        advice.push("Stay hydrated and seek shade when possible.");
    } else if (temperature > 25) {
        advice.push("Perfect weather for outdoor activities!");
    }

    // Weather condition advice
    if ([61, 63, 65, 80, 81, 82].includes(weatherCode)) {
        advice.push("Don't forget your umbrella!");
    } else if ([71, 73, 75, 77, 85, 86].includes(weatherCode)) {
        advice.push("Watch out for slippery conditions due to snow.");
    } else if ([95, 96, 99].includes(weatherCode)) {
        advice.push("Stay indoors if possible due to thunderstorms.");
    }

    // Wind advice
    if (windSpeed > 30) {
        advice.push("Very windy conditions - secure loose objects.");
    } else if (windSpeed > 20) {
        advice.push("Breezy conditions today.");
    }

    // Humidity advice
    if (humidity > 80) {
        advice.push("High humidity may make it feel warmer than it is.");
    } else if (humidity < 30) {
        advice.push("Low humidity - consider using moisturizer.");
    }

    return advice.length > 0 ? advice : ["Have a great day!"];
}
