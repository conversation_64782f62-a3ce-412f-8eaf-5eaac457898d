import axios from 'axios';
import {
  WeatherData,
  WeatherLocation,
  GeocodingResponse,
  ProcessedWeatherData,
  WeatherApiError,
  CurrentWeather,
  HourlyWeather,
  DailyWeather
} from '@/types/weather';
import { getWeatherCondition, getWindDirection } from './weather-codes';

const OPEN_METEO_BASE_URL = 'https://api.open-meteo.com/v1';
const GEOCODING_BASE_URL = 'https://geocoding-api.open-meteo.com/v1';

// API timeout in milliseconds
const API_TIMEOUT = 10000;

// Rate limiting
let lastApiCall = 0;
const MIN_CALL_INTERVAL = 5000; // 5 seconds between calls
const requestCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 300000; // 5 minutes

// Create axios instance with default config
const apiClient = axios.create({
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Rate limiting helper
const checkRateLimit = (): void => {
  const now = Date.now();
  const timeSinceLastCall = now - lastApiCall;
  
  if (timeSinceLastCall < MIN_CALL_INTERVAL) {
    const waitTime = MIN_CALL_INTERVAL - timeSinceLastCall;
    throw new Error(`Please wait ${Math.ceil(waitTime / 1000)} seconds before making another request.`);
  }
  
  lastApiCall = now;
};

// Cache helper
const getCachedData = (key: string): any | null => {
  const cached = requestCache.get(key);
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    return cached.data;
  }
  return null;
};

const setCachedData = (key: string, data: any): void => {
  requestCache.set(key, { data, timestamp: Date.now() });
};

export class WeatherAPI {
  /**
   * Search for locations by name
   */
  static async searchLocations(query: string, limit: number = 10): Promise<WeatherLocation[]> {
    try {
      const cacheKey = `search_${query}_${limit}`;
      const cached = getCachedData(cacheKey);
      if (cached) {
        return cached;
      }

      checkRateLimit();

      const response = await apiClient.get<GeocodingResponse>(`${GEOCODING_BASE_URL}/search`, {
        params: {
          name: query,
          count: limit,
          language: 'en',
          format: 'json'
        }
      });

      if (!response.data.results || response.data.results.length === 0) {
        return [];
      }

      const results = response.data.results.map(result => ({
        latitude: result.latitude,
        longitude: result.longitude,
        city: result.name,
        country: result.country,
        countryCode: result.countryCode,
        timezone: result.timezone,
        elevation: result.elevation
      }));

      setCachedData(cacheKey, results);
      return results;
    } catch (error) {
      console.error('Error searching locations:', error);
      throw new Error('Failed to search locations');
    }
  }

  /**
   * Get current weather data for a location with retry logic
   */
  static async getCurrentWeather(latitude: number, longitude: number, retryCount = 0): Promise<ProcessedWeatherData> {
    const maxRetries = 2;
    
    try {
      const cacheKey = `weather_${latitude.toFixed(2)}_${longitude.toFixed(2)}`;
      const cached = getCachedData(cacheKey);
      if (cached) {
        return cached;
      }

      // Skip rate limiting on retries for better user experience
      if (retryCount === 0) {
        checkRateLimit();
      }

      const response = await apiClient.get(`${OPEN_METEO_BASE_URL}/forecast`, {
        params: {
          latitude,
          longitude,
          current: [
            'temperature_2m',
            'relative_humidity_2m',
            'apparent_temperature',
            'is_day',
            'precipitation',
            'weather_code',
            'cloud_cover',
            'surface_pressure',
            'wind_speed_10m',
            'wind_direction_10m',
            'uv_index'
          ].join(','),
          hourly: [
            'temperature_2m',
            'relative_humidity_2m',
            'weather_code',
            'wind_speed_10m',
            'wind_direction_10m',
            'precipitation',
            'is_day'
          ].join(','),
          daily: [
            'weather_code',
            'temperature_2m_max',
            'temperature_2m_min',
            'sunrise',
            'sunset',
            'precipitation_sum',
            'wind_speed_10m_max',
            'wind_direction_10m_dominant',
            'uv_index_max'
          ].join(','),
          timezone: 'auto',
          forecast_days: 7
        },
        timeout: 15000 // Increase timeout for weather data
      });

      const processedData = this.processWeatherData(response.data);
      setCachedData(cacheKey, processedData);
      return processedData;
    } catch (error) {
      console.error(`Error fetching weather data (attempt ${retryCount + 1}):`, error);
      
      // Retry logic
      if (retryCount < maxRetries) {
        console.log(`Retrying weather fetch in ${(retryCount + 1) * 2} seconds...`);
        await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 2000));
        return this.getCurrentWeather(latitude, longitude, retryCount + 1);
      }
      
      throw new Error(`Failed to fetch weather data after ${maxRetries + 1} attempts. Please check your internet connection.`);
    }
  }

  /**
   * Get weather forecast for a location
   */
  static async getForecast(
    latitude: number, 
    longitude: number, 
    days: number = 7
  ): Promise<ProcessedWeatherData> {
    try {
      const response = await apiClient.get(`${OPEN_METEO_BASE_URL}/forecast`, {
        params: {
          latitude,
          longitude,
          current: [
            'temperature_2m',
            'relative_humidity_2m',
            'apparent_temperature',
            'is_day',
            'precipitation',
            'weather_code',
            'cloud_cover',
            'surface_pressure',
            'wind_speed_10m',
            'wind_direction_10m',
            'uv_index'
          ].join(','),
          hourly: [
            'temperature_2m',
            'relative_humidity_2m',
            'weather_code',
            'wind_speed_10m',
            'wind_direction_10m',
            'precipitation',
            'is_day',
            'cloud_cover',
            'surface_pressure',
            'uv_index'
          ].join(','),
          daily: [
            'weather_code',
            'temperature_2m_max',
            'temperature_2m_min',
            'sunrise',
            'sunset',
            'precipitation_sum',
            'wind_speed_10m_max',
            'wind_direction_10m_dominant',
            'uv_index_max'
          ].join(','),
          timezone: 'auto',
          forecast_days: Math.min(days, 16) // Open-Meteo supports up to 16 days
        }
      });

      return this.processWeatherData(response.data);
    } catch (error) {
      console.error('Error fetching forecast data:', error);
      throw new Error('Failed to fetch forecast data');
    }
  }

  /**
   * Get historical weather data
   */
  static async getHistoricalWeather(
    latitude: number,
    longitude: number,
    startDate: string,
    endDate: string
  ): Promise<any> {
    try {
      const response = await apiClient.get(`${OPEN_METEO_BASE_URL}/historical`, {
        params: {
          latitude,
          longitude,
          start_date: startDate,
          end_date: endDate,
          daily: [
            'weather_code',
            'temperature_2m_max',
            'temperature_2m_min',
            'precipitation_sum',
            'wind_speed_10m_max',
            'wind_direction_10m_dominant'
          ].join(','),
          timezone: 'auto'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching historical data:', error);
      throw new Error('Failed to fetch historical weather data');
    }
  }

  /**
   * Get location info from coordinates (reverse geocoding)
   */
  static async getLocationFromCoordinates(
    latitude: number, 
    longitude: number
  ): Promise<WeatherLocation | null> {
    try {
      const response = await apiClient.get<GeocodingResponse>(`${GEOCODING_BASE_URL}/search`, {
        params: {
          latitude,
          longitude,
          count: 1,
          language: 'en',
          format: 'json'
        }
      });

      if (!response.data.results || response.data.results.length === 0) {
        return null;
      }

      const result = response.data.results[0];
      return {
        latitude: result.latitude,
        longitude: result.longitude,
        city: result.name,
        country: result.country,
        countryCode: result.countryCode,
        timezone: result.timezone,
        elevation: result.elevation
      };
    } catch (error) {
      console.error('Error getting location from coordinates:', error);
      return null;
    }
  }

  /**
   * Get user's current location using browser geolocation with fallbacks
   */
  static async getCurrentLocation(): Promise<{ latitude: number; longitude: number }> {
    // Fallback locations for major cities
    const fallbackLocations = [
      { name: 'Jakarta', latitude: -6.2088, longitude: 106.8456 },
      { name: 'New York', latitude: 40.7128, longitude: -74.0060 },
      { name: 'London', latitude: 51.5074, longitude: -0.1278 },
      { name: 'Tokyo', latitude: 35.6762, longitude: 139.6503 },
      { name: 'Sydney', latitude: -33.8688, longitude: 151.2093 },
      { name: 'Paris', latitude: 48.8566, longitude: 2.3522 }
    ];

    // Try to get location from IP geolocation first as fallback
    const getIPLocation = async (): Promise<{ latitude: number; longitude: number } | null> => {
      try {
        const response = await fetch('https://ipapi.co/json/');
        if (response.ok) {
          const data = await response.json();
          if (data.latitude && data.longitude) {
            return { latitude: data.latitude, longitude: data.longitude };
          }
        }
      } catch (error) {
        console.warn('IP geolocation failed:', error);
      }
      return null;
    };

    // Try browser geolocation first
    const getBrowserLocation = (): Promise<{ latitude: number; longitude: number }> => {
      return new Promise((resolve, reject) => {
        if (!navigator.geolocation) {
          reject(new Error('Geolocation not supported'));
          return;
        }

        let resolved = false;
        const timeoutId = setTimeout(() => {
          if (!resolved) {
            resolved = true;
            reject(new Error('Browser location timeout'));
          }
        }, 8000); // Shorter timeout for browser geolocation

        navigator.geolocation.getCurrentPosition(
          (position) => {
            if (!resolved) {
              resolved = true;
              clearTimeout(timeoutId);
              resolve({
                latitude: position.coords.latitude,
                longitude: position.coords.longitude
              });
            }
          },
          (error) => {
            if (!resolved) {
              resolved = true;
              clearTimeout(timeoutId);
              reject(new Error(`Geolocation error: ${error.message}`));
            }
          },
          {
            enableHighAccuracy: false,
            timeout: 6000,
            maximumAge: 300000 // 5 minutes cache
          }
        );
      });
    };

    try {
      // First attempt: Browser geolocation
      return await getBrowserLocation();
    } catch (browserError) {
      console.warn('Browser geolocation failed:', browserError);
      
      try {
        // Second attempt: IP geolocation
        const ipLocation = await getIPLocation();
        if (ipLocation) {
          console.log('Using IP-based location');
          return ipLocation;
        }
      } catch (ipError) {
        console.warn('IP geolocation failed:', ipError);
      }
      
      // Final fallback: Use a default location based on user's likely timezone
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      let defaultLocation = fallbackLocations[0]; // Default to Jakarta
      
      // Simple timezone-based location detection
      if (timeZone.includes('Europe')) {
        defaultLocation = fallbackLocations.find(loc => loc.name === 'London') || defaultLocation;
      } else if (timeZone.includes('Asia')) {
        // Prefer Jakarta for Indonesian/Southeast Asian timezones, Tokyo for East Asian
        if (timeZone.includes('Jakarta') || timeZone.includes('Indonesia') || timeZone.includes('Singapore') || timeZone.includes('Kuala_Lumpur')) {
          defaultLocation = fallbackLocations.find(loc => loc.name === 'Jakarta') || defaultLocation;
        } else {
          defaultLocation = fallbackLocations.find(loc => loc.name === 'Tokyo') || defaultLocation;
        }
      } else if (timeZone.includes('Australia')) {
        defaultLocation = fallbackLocations.find(loc => loc.name === 'Sydney') || defaultLocation;
      }
      
      console.log(`Using fallback location: ${defaultLocation.name} (Jakarta default)`);
      return {
        latitude: defaultLocation.latitude,
        longitude: defaultLocation.longitude
      };
    }
  }

  /**
   * Process raw weather data from Open-Meteo API
   */
  private static processWeatherData(data: any): ProcessedWeatherData {
    const location: WeatherLocation = {
      latitude: data.latitude,
      longitude: data.longitude,
      city: 'Unknown',
      country: 'Unknown',
      countryCode: 'XX',
      timezone: data.timezone || 'UTC',
      elevation: data.elevation
    };

    // Process current weather
    const currentCondition = getWeatherCondition(
      data.current.weather_code,
      data.current.is_day === 1
    );

    const current = {
      temperature: Math.round(data.current.temperature_2m),
      condition: {
        ...currentCondition,
        code: data.current.weather_code
      },
      humidity: data.current.relative_humidity_2m,
      windSpeed: Math.round(data.current.wind_speed_10m),
      windDirection: getWindDirection(data.current.wind_direction_10m),
      feelsLike: Math.round(data.current.apparent_temperature),
      pressure: Math.round(data.current.surface_pressure),
      visibility: 10, // Open-Meteo doesn't provide visibility, default to 10km
      uvIndex: data.current.uv_index || 0,
      isDay: data.current.is_day === 1,
      time: new Date(data.current.time)
    };

    // Process hourly forecast (next 24 hours)
    const hourlyForecast = data.hourly.time.slice(0, 24).map((time: string, index: number) => {
      const hourlyCondition = getWeatherCondition(
        data.hourly.weather_code[index],
        data.hourly.is_day[index] === 1
      );

      return {
        time: new Date(time),
        temperature: Math.round(data.hourly.temperature_2m[index]),
        condition: {
          ...hourlyCondition,
          code: data.hourly.weather_code[index]
        },
        precipitation: data.hourly.precipitation[index] || 0,
        windSpeed: Math.round(data.hourly.wind_speed_10m[index])
      };
    });

    // Process daily forecast
    const dailyForecast = data.daily.time.map((date: string, index: number) => {
      const dailyCondition = getWeatherCondition(data.daily.weather_code[index], true);

      return {
        date: new Date(date),
        condition: {
          ...dailyCondition,
          code: data.daily.weather_code[index]
        },
        temperatureMax: Math.round(data.daily.temperature_2m_max[index]),
        temperatureMin: Math.round(data.daily.temperature_2m_min[index]),
        sunrise: new Date(data.daily.sunrise[index]),
        sunset: new Date(data.daily.sunset[index]),
        precipitation: data.daily.precipitation_sum[index] || 0,
        windSpeed: Math.round(data.daily.wind_speed_10m_max[index]),
        uvIndex: data.daily.uv_index_max[index] || 0
      };
    });

    return {
      location,
      current,
      hourlyForecast,
      dailyForecast
    };
  }

  /**
   * Check if the API is available
   */
  static async checkApiStatus(): Promise<boolean> {
    try {
      const response = await apiClient.get(`${OPEN_METEO_BASE_URL}/forecast`, {
        params: {
          latitude: 52.52,
          longitude: 13.41,
          current: 'temperature_2m'
        },
        timeout: 5000
      });
      return response.status === 200;
    } catch (error) {
      console.error('API status check failed:', error);
      return false;
    }
  }
}

export default WeatherAPI;