import React from 'react';
import { useThemeStore } from '@/stores/theme-store';
import { Sun, Moon, Monitor } from 'lucide-react';

const ThemeTest: React.FC = () => {
  const { theme, setTheme, toggleTheme } = useThemeStore();

  const getSystemTheme = () => {
    if (typeof window === 'undefined') return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  };

  const getEffectiveTheme = () => {
    return theme === 'system' ? getSystemTheme() : theme;
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-lg shadow-sm p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Theme System Test
        </h1>
        
        <div className="space-y-4">
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
              Current State
            </h2>
            <div className="space-y-2 text-sm">
              <div className="text-gray-600 dark:text-gray-300">
                <strong>Selected Theme:</strong> {theme}
              </div>
              <div className="text-gray-600 dark:text-gray-300">
                <strong>Effective Theme:</strong> {getEffectiveTheme()}
              </div>
              <div className="text-gray-600 dark:text-gray-300">
                <strong>System Preference:</strong> {getSystemTheme()}
              </div>
              <div className="text-gray-600 dark:text-gray-300">
                <strong>Document Class:</strong> {typeof document !== 'undefined' ? (document.documentElement.classList.contains('dark') ? 'dark' : 'light') : 'unknown'}
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              Theme Controls
            </h3>
            
            <div className="flex gap-2">
              <button
                onClick={() => setTheme('light')}
                className={`flex items-center gap-2 px-4 py-2 rounded-md border transition-colors ${
                  theme === 'light'
                    ? 'bg-blue-100 border-blue-300 text-blue-800 dark:bg-blue-900 dark:border-blue-600 dark:text-blue-200'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <Sun className="w-4 h-4" />
                Light
              </button>
              
              <button
                onClick={() => setTheme('dark')}
                className={`flex items-center gap-2 px-4 py-2 rounded-md border transition-colors ${
                  theme === 'dark'
                    ? 'bg-blue-100 border-blue-300 text-blue-800 dark:bg-blue-900 dark:border-blue-600 dark:text-blue-200'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <Moon className="w-4 h-4" />
                Dark
              </button>
              
              <button
                onClick={() => setTheme('system')}
                className={`flex items-center gap-2 px-4 py-2 rounded-md border transition-colors ${
                  theme === 'system'
                    ? 'bg-blue-100 border-blue-300 text-blue-800 dark:bg-blue-900 dark:border-blue-600 dark:text-blue-200'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <Monitor className="w-4 h-4" />
                System
              </button>
            </div>
            
            <button
              onClick={toggleTheme}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Toggle Theme
            </button>
          </div>

          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
              Color Test
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="w-full h-8 bg-white dark:bg-gray-800 border dark:border-gray-600 rounded flex items-center justify-center text-sm">
                  Background
                </div>
                <div className="w-full h-8 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center text-sm">
                  Secondary
                </div>
                <div className="w-full h-8 bg-blue-500 dark:bg-blue-600 rounded flex items-center justify-center text-sm text-white">
                  Primary
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-gray-900 dark:text-white font-medium">Primary Text</div>
                <div className="text-gray-600 dark:text-gray-300">Secondary Text</div>
                <div className="text-gray-400 dark:text-gray-500">Muted Text</div>
              </div>
            </div>
          </div>

          <div className="text-xs text-gray-500 dark:text-gray-400">
            Try changing your system theme preference to test the 'system' option.
            You can also use DevTools to toggle dark mode simulation.
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeTest;