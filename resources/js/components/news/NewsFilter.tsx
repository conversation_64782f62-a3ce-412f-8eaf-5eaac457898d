import React, { useState, useEffect } from 'react';
import { Search, Filter, Calendar, X } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Category {
    id: number;
    name: string;
}

interface FilterOptions {
    search?: string;
    category_id?: number;
    date_from?: string;
    date_to?: string;
    sort?: 'latest' | 'popular' | 'top';
}

interface NewsFilterProps {
    categories: Category[];
    onFilterChange: (filters: FilterOptions) => void;
    initialFilters?: FilterOptions;
    className?: string;
}

const NewsFilter: React.FC<NewsFilterProps> = ({
    categories,
    onFilterChange,
    initialFilters = {},
    className = '',
}) => {
    const [filters, setFilters] = useState<FilterOptions>(initialFilters);
    const [isExpanded, setIsExpanded] = useState(false);

    // Update filters when they change
    useEffect(() => {
        onFilterChange(filters);
    }, [filters, onFilterChange]);

    const handleFilterChange = (key: keyof FilterOptions, value: any) => {
        setFilters(prev => ({
            ...prev,
            [key]: value || undefined
        }));
    };

    const clearFilters = () => {
        setFilters({});
        setIsExpanded(false);
    };

    const hasActiveFilters = Object.values(filters).some(value => value !== undefined && value !== '');

    return (
        <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                    <Filter size={20} className="text-gray-600 dark:text-gray-400" />
                    <h3 className="font-semibold text-gray-900 dark:text-white">Filter Artikel</h3>
                    {hasActiveFilters && (
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                            {Object.values(filters).filter(v => v !== undefined && v !== '').length} aktif
                        </span>
                    )}
                </div>
                <div className="flex items-center gap-2">
                    {hasActiveFilters && (
                        <button
                            onClick={clearFilters}
                            className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 flex items-center gap-1"
                        >
                            <X size={14} />
                            Clear
                        </button>
                    )}
                    <button
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                    >
                        {isExpanded ? 'Tutup' : 'Tampilkan Filter'}
                    </button>
                </div>
            </div>

            {/* Search Bar - Always visible */}
            <div className="relative mb-4">
                <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                    type="text"
                    placeholder="Cari artikel..."
                    value={filters.search || ''}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
            </div>

            {/* Expanded Filters */}
            {isExpanded && (
                <div className="space-y-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                    {/* Category and Sort in one row */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Category Filter */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Kategori
                            </label>
                            <Select
                                value={filters.category_id?.toString() || ''}
                                onValueChange={(value) => handleFilterChange('category_id', value ? parseInt(value) : undefined)}
                            >
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Semua Kategori" />
                                </SelectTrigger>
                                <SelectContent className="bg-white dark:bg-gray-800">
                                    <SelectItem value="" className="text-gray-900 dark:text-white">
                                        Semua Kategori
                                    </SelectItem>
                                    {categories.map((category) => (
                                        <SelectItem 
                                            key={category.id} 
                                            value={category.id.toString()}
                                            className="text-gray-900 dark:text-white"
                                        >
                                            {category.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Sort Filter */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Urutkan
                            </label>
                            <Select
                                value={filters.sort || 'latest'}
                                onValueChange={(value) => handleFilterChange('sort', value)}
                            >
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Terbaru" />
                                </SelectTrigger>
                                <SelectContent className="bg-white dark:bg-gray-800">
                                    <SelectItem value="latest" className="text-gray-900 dark:text-white">
                                        Terbaru
                                    </SelectItem>
                                    <SelectItem value="popular" className="text-gray-900 dark:text-white">
                                        Terpopuler
                                    </SelectItem>
                                    <SelectItem value="top" className="text-gray-900 dark:text-white">
                                        Paling Dilihat
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Date Range */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Dari Tanggal
                            </label>
                            <div className="relative">
                                <Calendar size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                <input
                                    type="date"
                                    value={filters.date_from || ''}
                                    onChange={(e) => handleFilterChange('date_from', e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Sampai Tanggal
                            </label>
                            <div className="relative">
                                <Calendar size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                <input
                                    type="date"
                                    value={filters.date_to || ''}
                                    onChange={(e) => handleFilterChange('date_to', e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default NewsFilter;
