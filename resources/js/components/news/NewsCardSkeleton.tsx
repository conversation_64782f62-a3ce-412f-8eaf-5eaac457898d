import React from 'react';

interface NewsCardSkeletonProps {
    className?: string;
}

const NewsCardSkeleton: React.FC<NewsCardSkeletonProps> = ({ className = '' }) => {
    return (
        <div className={`${className} animate-fadeIn`}>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                {/* Image skeleton */}
                <div className="w-full h-48 loading-shimmer"></div>

                {/* Content skeleton */}
                <div className="p-4 space-y-3">
                    {/* Title skeleton */}
                    <div className="space-y-2">
                        <div className="h-4 loading-shimmer rounded w-full"></div>
                        <div className="h-4 loading-shimmer rounded w-3/4"></div>
                    </div>

                    {/* Content skeleton */}
                    <div className="space-y-2">
                        <div className="h-3 loading-shimmer rounded w-full"></div>
                        <div className="h-3 loading-shimmer rounded w-5/6"></div>
                        <div className="h-3 loading-shimmer rounded w-4/6"></div>
                    </div>

                    {/* Meta info skeleton */}
                    <div className="flex items-center justify-between pt-2">
                        <div className="flex items-center space-x-2">
                            <div className="w-6 h-6 loading-shimmer rounded-full"></div>
                            <div className="h-3 loading-shimmer rounded w-20"></div>
                        </div>
                        <div className="h-3 loading-shimmer rounded w-16"></div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default NewsCardSkeleton;
