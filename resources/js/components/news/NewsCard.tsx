import React from 'react';
import { Link } from '@inertiajs/react';
import ReactionCounter from '@/components/reactions/ReactionCounter';

interface News {
    id: number;
    title: string;
    content?: string;
    image?: string;
    image_url?: string;
    image_alt?: string;
    views?: number;
    created_at: string;
    author?: {
        id: number;
        name: string;
    };
    category?: {
        id: number;
        name: string;
    };
    reactions?: {
        counts: {
            suka?: number;
            benci?: number;
            cinta?: number;
            lucu?: number;
            marah?: number;
            sedih?: number;
            wow?: number;
            like?: number;
            love?: number;
            haha?: number;
            sad?: number;
            angry?: number;
        };
        total_count: number;
        user_reaction?: string | null;
    };
    comments_count?: number;
}

interface NewsCardProps {
    news: News;
    variant?: 'default' | 'compact' | 'featured';
    showReactions?: boolean;
    showAuthor?: boolean;
    showCategory?: boolean;
    showViews?: boolean;
    showComments?: boolean;
    className?: string;
}

const NewsCard: React.FC<NewsCardProps> = ({
    news,
    variant = 'default',
    showReactions = true,
    showAuthor = true,
    showCategory = true,
    showViews = true,
    showComments = true,
    className = '',
}) => {
    // Helper function to get image URL
    const getImageUrl = (): string => {
        if (news.image_url) {
            return news.image_url;
        }
        if (news.image) {
            return `/storage/images/${news.image}`;
        }
        return `/img/noimg.jpg`;
    };

    // Helper function to truncate text
    const truncateText = (text: string, maxLength: number): string => {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength).trim() + '...';
    };

    // Helper function to format date
    const formatDate = (dateString: string): string => {
        const date = new Date(dateString);
        return date.toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
        });
    };

    // Variant configurations
    const variants = {
        default: {
            containerClass: 'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300',
            imageClass: 'w-full h-48 object-cover',
            contentClass: 'p-4',
            titleClass: 'text-lg font-semibold text-gray-900 mb-2 line-clamp-2',
            excerptClass: 'text-gray-600 text-sm mb-3 line-clamp-3',
            metaClass: 'flex items-center justify-between text-xs text-gray-500',
        },
        compact: {
            containerClass: 'bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300',
            imageClass: 'w-20 h-20 object-cover flex-shrink-0',
            contentClass: 'flex-1 p-3',
            titleClass: 'text-sm font-semibold text-gray-900 mb-1 line-clamp-2',
            excerptClass: 'text-gray-600 text-xs mb-2 line-clamp-2',
            metaClass: 'flex items-center justify-between text-xs text-gray-500',
        },
        featured: {
            containerClass: 'bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300',
            imageClass: 'w-full h-64 object-cover',
            contentClass: 'p-6',
            titleClass: 'text-xl font-bold text-gray-900 mb-3 line-clamp-2',
            excerptClass: 'text-gray-600 text-base mb-4 line-clamp-4',
            metaClass: 'flex items-center justify-between text-sm text-gray-500',
        },
    };

    const config = variants[variant];

    return (
        <article className={`news-card ${config.containerClass} ${className}`}>
            <Link href={`/news/${news.id}/show`} className="block">
                {variant === 'compact' ? (
                    // Compact layouts (horizontal)
                    <div className="flex">
                        <img
                            src={getImageUrl()}
                            alt={news.image_alt || news.title}
                            className={config.imageClass}
                            loading="lazy"
                            onError={(e) => {
                                e.currentTarget.src = "/img/noimg.jpg";
                            }}
                        />
                        <div className={config.contentClass}>
                            <h3 className={config.titleClass}>
                                {truncateText(news.title, 80)}
                            </h3>

                            {news.content && (
                                <p className={config.excerptClass}>
                                    {truncateText(news.content.replace(/<[^>]*>/g, ''), 100)}
                                </p>
                            )}

                            <div className={config.metaClass}>
                                <div className="flex items-center space-x-2">
                                    {showCategory && news.category && (
                                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                            {news.category.name}
                                        </span>
                                    )}
                                    <span>{formatDate(news.created_at)}</span>
                                </div>

                                <div className="flex items-center space-x-3">
                                    {showReactions && news.reactions && news.reactions.total_count > 0 && (
                                        <ReactionCounter
                                            counts={news.reactions.counts}
                                            totalCount={news.reactions.total_count}
                                            size="small"
                                            maxDisplay={2}
                                        />
                                    )}
                                    {showViews && news.views && (
                                        <span>{news.views} views</span>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                ) : (
                    // Default and featured layouts (vertical)
                    <>
                        <img
                            src={getImageUrl()}
                            alt={news.image_alt || news.title}
                            className={config.imageClass}
                            loading="lazy"
                            onError={(e) => {
                                e.currentTarget.src = "/img/noimg.jpg";
                            }}
                        />
                        <div className={config.contentClass}>
                            {showCategory && news.category && (
                                <div className="mb-2">
                                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
                                        {news.category.name}
                                    </span>
                                </div>
                            )}

                            <h3 className={config.titleClass}>
                                {variant === 'featured' ? news.title : truncateText(news.title, 100)}
                            </h3>

                            {news.content && (
                                <p className={config.excerptClass}>
                                    {truncateText(news.content.replace(/<[^>]*>/g, ''), variant === 'featured' ? 200 : 150)}
                                </p>
                            )}

                            <div className={config.metaClass}>
                                <div className="flex items-center space-x-2">
                                    {showAuthor && news.author && (
                                        <span>By {news.author.name}</span>
                                    )}
                                    <span>•</span>
                                    <span>{formatDate(news.created_at)}</span>
                                </div>

                                <div className="flex items-center space-x-4">
                                    {showReactions && news.reactions && news.reactions.total_count > 0 && (
                                        <ReactionCounter
                                            counts={news.reactions.counts}
                                            totalCount={news.reactions.total_count}
                                            size={variant === 'featured' ? 'medium' : 'small'}
                                            maxDisplay={3}
                                        />
                                    )}
                                    {showComments && news.comments_count && news.comments_count > 0 && (
                                        <span className="flex items-center space-x-1">
                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                            </svg>
                                            <span>{news.comments_count}</span>
                                        </span>
                                    )}
                                    {showViews && news.views && (
                                        <span className="flex items-center space-x-1">
                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                            <span>{news.views}</span>
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>
                    </>
                )}
            </Link>
        </article>
    );
};

export default NewsCard;
