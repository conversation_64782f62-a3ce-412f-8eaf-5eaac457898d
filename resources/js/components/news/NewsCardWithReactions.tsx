import React from 'react';
import { Link } from '@inertiajs/react';
import ReactionDisplay from '@/components/reactions/ReactionDisplay';

interface News {
    id: number;
    title: string;
    content?: string;
    image?: string;
    image_url?: string;
    image_alt?: string;
    views?: number;
    created_at: string;
    author?: {
        id: number;
        name: string;
    };
    category?: {
        id: number;
        name: string;
    };
    reactions?: {
        counts: {
            suka?: number;
            benci?: number;
            cinta?: number;
            lucu?: number;
            marah?: number;
            sedih?: number;
            wow?: number;
            like?: number;
            love?: number;
            haha?: number;
            sad?: number;
            angry?: number;
        };
        total_count: number;
        user_reaction?: string | null;
    };
    comments_count?: number;
}

interface NewsCardWithReactionsProps {
    news: News;
    showAuthor?: boolean;
    showViews?: boolean;
    className?: string;
    imageClassName?: string;
    titleClassName?: string;
    metaClassName?: string;
    reactionSize?: 'small' | 'medium' | 'large';
    reactionIconType?: 'svg' | 'emoji' | 'image';
    customReactionIcons?: Record<string, string>;
}

const NewsCardWithReactions: React.FC<NewsCardWithReactionsProps> = ({
    news,
    showAuthor = true,
    showViews = true,
    className = '',
    imageClassName = '',
    titleClassName = '',
    metaClassName = '',
    reactionSize = 'small',
    reactionIconType = 'svg',
    customReactionIcons = {},
}) => {
    // Helper function to get image URL
    const getImageUrl = (): string => {
        if (news.image_url) {
            return news.image_url;
        }
        if (news.image) {
            return `/storage/images/${news.image}`;
        }
        return `/img/noimg.jpg`;
    };

    // Helper function to format date
    const formatDate = (dateString: string): string => {
        const date = new Date(dateString);
        return date.toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
        });
    };

    return (
        <article className={`news-card-with-reactions relative group ${className}`}>
            <Link href={`/news/${news.id}/show`} className="block">
                {/* Image Container */}
                <div className="relative overflow-hidden rounded-lg">
                    <img
                        src={getImageUrl()}
                        alt={news.image_alt || news.title}
                        className={`w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105 ${imageClassName}`}
                        loading="lazy"
                        onError={(e) => {
                            e.currentTarget.src = "/img/noimg.jpg";
                        }}
                    />
                    
                    {/* Category Badge */}
                    {news.category && (
                        <div className="absolute top-3 left-3">
                            <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
                                {news.category.name}
                            </span>
                        </div>
                    )}

                    {/* Views Badge */}
                    {showViews && news.views && (
                        <div className="absolute top-3 right-3">
                            <div className="bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full flex items-center space-x-1">
                                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                                </svg>
                                <span>{news.views}</span>
                            </div>
                        </div>
                    )}
                </div>

                {/* Content */}
                <div className="mt-3">
                    {/* Title */}
                    <h3 className={`font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors line-clamp-2 mb-2 ${titleClassName}`}>
                        {news.title}
                    </h3>

                    {/* Reactions */}
                    {news.reactions && news.reactions.total_count > 0 && (
                        <div className="mb-2">
                            <ReactionDisplay
                                counts={news.reactions.counts}
                                size={reactionSize}
                                iconType={reactionIconType}
                                customIcons={customReactionIcons}
                                maxDisplay={6}
                            />
                        </div>
                    )}

                    {/* Meta Information */}
                    <div className={`flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 ${metaClassName}`}>
                        <div className="flex items-center space-x-2">
                            {showAuthor && news.author && (
                                <>
                                    <span>Oleh</span>
                                    <span className="font-medium text-gray-700 dark:text-gray-300">
                                        {news.author.name}
                                    </span>
                                </>
                            )}
                        </div>
                        <span>{formatDate(news.created_at)}</span>
                    </div>
                </div>
            </Link>
        </article>
    );
};

export default NewsCardWithReactions;
