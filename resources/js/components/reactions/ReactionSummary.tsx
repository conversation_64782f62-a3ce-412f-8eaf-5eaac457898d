import React from 'react';
import { REACTION_ICONS, REACTION_LABELS } from './ReactionIcons';

interface ReactionCounts {
    suka?: number;
    benci?: number;
    cinta?: number;
    lucu?: number;
    marah?: number;
    sedih?: number;
    wow?: number;
    // Old types for backward compatibility
    like?: number;
    love?: number;
    haha?: number;
    sad?: number;
    angry?: number;
}

interface ReactionSummaryProps {
    counts: ReactionCounts;
    totalCount?: number;
    userReaction?: string | null;
    showPercentage?: boolean;
    showUserIndicator?: boolean;
    showTopReaction?: boolean;
    maxDisplay?: number;
    variant?: 'default' | 'compact' | 'detailed';
    className?: string;
}

const ReactionSummary: React.FC<ReactionSummaryProps> = ({
    counts,
    totalCount,
    userReaction = null,
    showPercentage = true,
    showUserIndicator = true,
    showTopReaction = true,
    maxDisplay = 5,
    variant = 'default',
    className = '',
}) => {
    // Calculate total if not provided
    const calculatedTotal = totalCount || Object.values(counts).reduce((sum, count) => sum + (count || 0), 0);
    
    // Get reactions with counts > 0, sorted by count
    const reactionsWithCounts = Object.entries(counts)
        .filter(([_, count]) => count && count > 0)
        .sort(([, a], [, b]) => (b || 0) - (a || 0))
        .slice(0, maxDisplay);

    // Get top reaction
    const topReaction = reactionsWithCounts[0];

    if (calculatedTotal === 0) {
        return (
            <div className={`reaction-summary text-center py-4 ${className}`}>
                <p className="text-gray-400 text-sm">Belum ada reaksi</p>
                <p className="text-xs text-gray-300 mt-1">Jadilah yang pertama memberikan reaksi!</p>
            </div>
        );
    }

    if (variant === 'compact') {
        return (
            <div className={`reaction-summary-compact flex items-center space-x-2 ${className}`}>
                {/* Top 3 reaction icons */}
                <div className="flex items-center -space-x-1">
                    {reactionsWithCounts.slice(0, 3).map(([type, count]) => {
                        const IconComponent = REACTION_ICONS[type as keyof typeof REACTION_ICONS];
                        return (
                            <div
                                key={type}
                                className="relative bg-white rounded-full border border-gray-200 p-1"
                                title={`${REACTION_LABELS[type as keyof typeof REACTION_LABELS]}: ${count}`}
                            >
                                <IconComponent size={16} />
                            </div>
                        );
                    })}
                </div>
                
                {/* Total count */}
                <span className="text-sm text-gray-600 font-medium">
                    {calculatedTotal}
                </span>
                
                {/* User indicator */}
                {showUserIndicator && userReaction && (
                    <span className="text-xs bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full">
                        Kamu
                    </span>
                )}
            </div>
        );
    }

    if (variant === 'detailed') {
        return (
            <div className={`reaction-summary-detailed bg-gray-50 rounded-lg p-4 ${className}`}>
                {/* Header */}
                <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-semibold text-gray-800">
                        Reaksi ({calculatedTotal})
                    </h4>
                    {showTopReaction && topReaction && (
                        <span className="text-xs text-gray-500">
                            Terpopuler: {REACTION_LABELS[topReaction[0] as keyof typeof REACTION_LABELS]}
                        </span>
                    )}
                </div>

                {/* Detailed list */}
                <div className="space-y-2">
                    {reactionsWithCounts.map(([type, count]) => {
                        const IconComponent = REACTION_ICONS[type as keyof typeof REACTION_ICONS];
                        const percentage = calculatedTotal > 0 ? Math.round((count! / calculatedTotal) * 100) : 0;
                        const isUserReaction = userReaction === type;
                        
                        return (
                            <div key={type} className={`flex items-center justify-between p-2 rounded ${
                                isUserReaction ? 'bg-blue-50 border border-blue-200' : 'bg-white'
                            }`}>
                                <div className="flex items-center space-x-2">
                                    <IconComponent size={18} />
                                    <span className={`text-sm ${
                                        isUserReaction ? 'text-blue-700 font-medium' : 'text-gray-700'
                                    }`}>
                                        {REACTION_LABELS[type as keyof typeof REACTION_LABELS]}
                                    </span>
                                    {isUserReaction && showUserIndicator && (
                                        <span className="text-xs bg-blue-100 text-blue-600 px-1.5 py-0.5 rounded">
                                            Kamu
                                        </span>
                                    )}
                                </div>
                                <div className="flex items-center space-x-1">
                                    <span className={`text-sm font-medium ${
                                        isUserReaction ? 'text-blue-600' : 'text-gray-600'
                                    }`}>
                                        {count}
                                    </span>
                                    {showPercentage && (
                                        <span className="text-xs text-gray-400">
                                            ({percentage}%)
                                        </span>
                                    )}
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        );
    }

    // Default variant
    return (
        <div className={`reaction-summary ${className}`}>
            {/* Header with total */}
            <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700">
                    {calculatedTotal} reaksi
                </span>
                {showUserIndicator && userReaction && (
                    <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                        ✓ Kamu sudah bereaksi
                    </span>
                )}
            </div>

            {/* Reaction breakdown */}
            <div className="flex flex-wrap gap-2">
                {reactionsWithCounts.map(([type, count]) => {
                    const IconComponent = REACTION_ICONS[type as keyof typeof REACTION_ICONS];
                    const percentage = calculatedTotal > 0 ? Math.round((count! / calculatedTotal) * 100) : 0;
                    const isUserReaction = userReaction === type;
                    
                    return (
                        <div
                            key={type}
                            className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                                isUserReaction 
                                    ? 'bg-blue-100 text-blue-700 border border-blue-200' 
                                    : 'bg-gray-100 text-gray-600'
                            }`}
                            title={`${REACTION_LABELS[type as keyof typeof REACTION_LABELS]}: ${count} (${percentage}%)`}
                        >
                            <IconComponent size={14} />
                            <span className="font-medium">{count}</span>
                            {showPercentage && (
                                <span className="text-xs opacity-75">
                                    {percentage}%
                                </span>
                            )}
                        </div>
                    );
                })}
            </div>

            {/* Top reaction info */}
            {showTopReaction && topReaction && (
                <div className="mt-2 pt-2 border-t border-gray-200">
                    <p className="text-xs text-gray-500">
                        Reaksi terpopuler: <span className="font-medium">
                            {REACTION_LABELS[topReaction[0] as keyof typeof REACTION_LABELS]}
                        </span> ({topReaction[1]} orang)
                    </p>
                </div>
            )}
        </div>
    );
};

export default ReactionSummary;
