# Reaction Components

This folder contains reaction components for your news website.

## Components

### 1. SimpleReactionBar (Recommended)
- **File**: `SimpleReactionBar.tsx`
- **Description**: Simple reaction bar similar to your original ReactionBar but with new reaction types
- **Features**: 
  - Supports both emojis and PNG images
  - Easy to customize
  - Similar UI to original ReactionBar
  - "Apa Reaksimu?" question text

### 2. NewsReactionBar
- **File**: `NewsReactionBar.tsx` 
- **Description**: More advanced version with custom SVG icons
- **Features**:
  - Custom SVG icons with colors
  - Grid layout like your reference image
  - More complex styling

### 3. ReactionIcons
- **File**: `ReactionIcons.tsx`
- **Description**: Custom SVG icons for reactions
- **Used by**: NewsReactionBar

## How to Use PNG Images Instead of Emojis

To use PNG images instead of emojis in `SimpleReactionBar`, follow these steps:

### Step 1: Add Your PNG Images
Place your reaction images in the `public/img/reactions/` folder:
```
public/
  img/
    reactions/
      suka.png
      benci.png
      cinta.png
      lucu.png
      marah.png
      sedih.png
      wow.png
```

### Step 2: Update REACTION_TYPES
In `SimpleReactionBar.tsx`, change the `REACTION_TYPES` object:

```typescript
// Replace this:
const REACTION_TYPES = {
    suka: '👍',
    benci: '👎',
    cinta: '❤️',
    lucu: '😂',
    marah: '😡',
    sedih: '😢',
    wow: '😮',
};

// With this:
const REACTION_TYPES = {
    suka: '/img/reactions/suka.png',
    benci: '/img/reactions/benci.png',
    cinta: '/img/reactions/cinta.png',
    lucu: '/img/reactions/lucu.png',
    marah: '/img/reactions/marah.png',
    sedih: '/img/reactions/sedih.png',
    wow: '/img/reactions/wow.png',
};
```

### Step 3: That's It!
The component will automatically detect that these are image URLs and render them as `<img>` tags instead of emojis.

## Reaction Types

The new reaction system supports these types:
- `suka` - Like/Thumbs up
- `benci` - Dislike/Thumbs down  
- `cinta` - Love/Heart
- `lucu` - Funny/Laugh
- `marah` - Angry
- `sedih` - Sad
- `wow` - Surprised/Wow

## Backend Support

The backend has been updated to support both old and new reaction types:
- **Old types**: `like`, `love`, `wow`, `haha`, `sad`, `angry`
- **New types**: `suka`, `benci`, `cinta`, `lucu`, `marah`, `sedih`, `wow`

## Usage in Components

### In NewsDetail.tsx:
```typescript
<SimpleReactionBar
    reactableType="news"
    reactableId={news.id}
    initialCounts={{
        suka: safeReactions.counts.suka || 0,
        benci: safeReactions.counts.benci || 0,
        cinta: safeReactions.counts.cinta || 0,
        lucu: safeReactions.counts.lucu || 0,
        marah: safeReactions.counts.marah || 0,
        sedih: safeReactions.counts.sedih || 0,
        wow: safeReactions.counts.wow || 0,
    }}
    initialUserReaction={safeReactions.user_reaction}
    totalCount={safeReactions.total_count}
    isAuthenticated={!!user}
    className="mb-6"
/>
```

## Testing

Visit `/demo/reactions` to test both components and see how they work.

## Customization

You can easily customize:
- **Labels**: Change `REACTION_LABELS` object
- **Icons/Images**: Change `REACTION_TYPES` object  
- **Styling**: Modify Tailwind classes
- **Layout**: Adjust the JSX structure
