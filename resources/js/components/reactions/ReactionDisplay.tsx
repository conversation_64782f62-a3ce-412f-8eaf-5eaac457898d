import React from 'react';
import { REACTION_ICONS, REACTION_LABELS } from './ReactionIcons';

interface ReactionCounts {
    suka?: number;
    benci?: number;
    cinta?: number;
    lucu?: number;
    marah?: number;
    sedih?: number;
    wow?: number;
    // Old types for backward compatibility
    like?: number;
    love?: number;
    haha?: number;
    sad?: number;
    angry?: number;
}

interface ReactionDisplayProps {
    counts: ReactionCounts;
    maxDisplay?: number;
    size?: 'small' | 'medium' | 'large';
    showZeroCounts?: boolean;
    className?: string;
    iconType?: 'svg' | 'emoji' | 'image';
    customIcons?: Record<string, string>; // For custom image URLs
}

// Default emoji icons (fallback)
const EMOJI_ICONS = {
    suka: '👍',
    benci: '👎',
    cinta: '❤️',
    lucu: '😂',
    marah: '😡',
    sedih: '😢',
    wow: '😮',
    like: '👍',
    love: '❤️',
    haha: '😂',
    sad: '😢',
    angry: '😡',
};

const ReactionDisplay: React.FC<ReactionDisplayProps> = ({
    counts,
    maxDisplay = 7,
    size = 'small',
    showZeroCounts = false,
    className = '',
    iconType = 'svg',
    customIcons = {},
}) => {
    // Size configurations
    const sizeConfig = {
        small: {
            iconSize: 16,
            textSize: 'text-xs',
            spacing: 'space-x-1',
            padding: 'px-1.5 py-0.5',
        },
        medium: {
            iconSize: 20,
            textSize: 'text-sm',
            spacing: 'space-x-2',
            padding: 'px-2 py-1',
        },
        large: {
            iconSize: 24,
            textSize: 'text-base',
            spacing: 'space-x-2',
            padding: 'px-3 py-1.5',
        },
    };

    const config = sizeConfig[size];

    // Filter and sort reactions
    const reactions = Object.entries(counts)
        .filter(([_, count]) => showZeroCounts || (count && count > 0))
        .sort(([, a], [, b]) => (b || 0) - (a || 0))
        .slice(0, maxDisplay);

    if (reactions.length === 0) {
        return null;
    }

    // Render icon based on type
    const renderIcon = (type: string) => {
        switch (iconType) {
            case 'svg':
                const IconComponent = REACTION_ICONS[type as keyof typeof REACTION_ICONS];
                return IconComponent ? <IconComponent size={config.iconSize} /> : null;
            
            case 'image':
                const imageUrl = customIcons[type] || `/img/reactions/${type}.png`;
                return (
                    <img
                        src={imageUrl}
                        alt={REACTION_LABELS[type as keyof typeof REACTION_LABELS] || type}
                        className={`w-${config.iconSize/4} h-${config.iconSize/4}`}
                        style={{ width: config.iconSize, height: config.iconSize }}
                        onError={(e) => {
                            // Fallback to emoji if image fails
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const parent = target.parentElement;
                            if (parent) {
                                parent.innerHTML = EMOJI_ICONS[type as keyof typeof EMOJI_ICONS] || '👍';
                            }
                        }}
                    />
                );
            
            case 'emoji':
            default:
                return <span>{EMOJI_ICONS[type as keyof typeof EMOJI_ICONS] || '👍'}</span>;
        }
    };

    return (
        <div className={`reaction-display flex items-center ${config.spacing} ${className}`}>
            {reactions.map(([type, count]) => (
                <div
                    key={type}
                    className={`flex items-center ${config.spacing} bg-gray-100 rounded-full ${config.padding} hover:bg-gray-200 transition-colors`}
                    title={`${REACTION_LABELS[type as keyof typeof REACTION_LABELS] || type}: ${count}`}
                >
                    <div className="flex-shrink-0">
                        {renderIcon(type)}
                    </div>
                    <span className={`${config.textSize} font-medium text-gray-700`}>
                        {count && count > 999 ? `${Math.floor(count / 1000)}k` : count}
                    </span>
                </div>
            ))}
        </div>
    );
};

export default ReactionDisplay;
