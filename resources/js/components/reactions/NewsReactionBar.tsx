import React, { useState } from 'react';
import { REACTION_ICONS, REACTION_LABELS } from './ReactionIcons';

interface ReactionCounts {
    suka: number;
    benci: number;
    cinta: number;
    lucu: number;
    marah: number;
    sedih: number;
    wow: number;
}

interface NewsReactionBarProps {
    reactableType: 'news' | 'comment';
    reactableId: number;
    initialCounts?: ReactionCounts;
    initialUserReaction?: string | null;
    totalCount?: number;
    isAuthenticated?: boolean;
    className?: string;
}

const NewsReactionBar: React.FC<NewsReactionBarProps> = ({
    reactableType,
    reactableId,
    initialCounts,
    initialUserReaction,
    totalCount,
    isAuthenticated = false,
    className = '',
}) => {
    // Safely handle initial data
    const safeCounts = initialCounts || { 
        suka: 0, benci: 0, cinta: 0, lucu: 0, marah: 0, sedih: 0, wow: 0 
    };
    const safeUserReaction = initialUserReaction || null;
    
    const [counts, setCounts] = useState<ReactionCounts>(safeCounts);
    const [userReaction, setUserReaction] = useState<string | null>(safeUserReaction);
    const [isLoading, setIsLoading] = useState(false);

    const handleReactionToggle = async (reactionType: string) => {
        if (isLoading) return;

        setIsLoading(true);

        try {
            const endpoint = isAuthenticated
                ? '/reactions/toggle'
                : '/guest/reactions/toggle';

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    reactable_type: reactableType,
                    reactable_id: reactableId,
                    reaction_type: reactionType,
                }),
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Response not OK:', response.status, errorText);
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }

            const data = await response.json();

            if (data.success) {
                setCounts(data.counts);
                setUserReaction(data.user_reaction || null);
            } else {
                console.error('API returned success=false:', data);
                throw new Error(data.message || 'Failed to toggle reaction');
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            alert(`Terjadi kesalahan saat memberikan reaksi: ${errorMessage}. Silakan coba lagi.`);
        } finally {
            setIsLoading(false);
        }
    };

    const getTotalReactions = () => {
        try {
            return Object.values(counts).reduce((sum, count) => sum + (count || 0), 0);
        } catch {
            return 0;
        }
    };

    const totalReactions = getTotalReactions();

    return (
        <div className={`news-reaction-bar relative ${className}`}>
            {/* Question Text */}
            <div className="mb-4">
                <h3 className="text-lg font-medium text-gray-800">
                    Apa Reaksimu?
                </h3>
            </div>

            {/* Reaction Buttons - Similar to original ReactionBar */}
            <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
                {Object.entries(REACTION_ICONS).map(([type, IconComponent]) => {
                    const count = counts[type as keyof ReactionCounts] || 0;
                    const isActive = userReaction === type;

                    return (
                        <button
                            key={type}
                            onClick={() => handleReactionToggle(type)}
                            disabled={isLoading}
                            className={`
                                relative flex flex-col items-center p-3 rounded-lg transition-all duration-200
                                min-w-[80px] hover:scale-105 group
                                ${isActive
                                    ? 'bg-blue-50 border-2 border-blue-300 shadow-md'
                                    : 'bg-gray-50 border-2 border-gray-200 hover:bg-gray-100 hover:border-gray-300'
                                }
                                ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                            `}
                        >
                            {/* Reaction Count Badge - Always show if count > 0 */}
                            {count > 0 && (
                                <div className={`absolute -top-2 -right-2 text-white text-xs font-bold rounded-full min-w-[22px] h-6 flex items-center justify-center shadow-sm ${
                                    isActive ? 'bg-blue-500' : 'bg-red-500'
                                }`}>
                                    {count > 99 ? '99+' : count}
                                </div>
                            )}

                            {/* Reaction Icon */}
                            <div className="mb-2">
                                <IconComponent size={32} />
                            </div>

                            {/* Reaction Label */}
                            <span className={`text-xs font-medium ${isActive ? 'text-blue-700' : 'text-gray-700'}`}>
                                {REACTION_LABELS[type as keyof typeof REACTION_LABELS]}
                            </span>

                            {/* Count Display Below Label */}
                            <span className={`text-xs mt-1 ${
                                count > 0
                                    ? (isActive ? 'text-blue-600 font-semibold' : 'text-gray-600 font-medium')
                                    : 'text-gray-400'
                            }`}>
                                {count || 0}
                            </span>
                        </button>
                    );
                })}
            </div>

            {/* Detailed Reactions Summary */}
            {totalReactions > 0 && (
                <div className="mt-6 pt-4 border-t border-gray-200">
                    {/* Total Count Header */}
                    <div className="flex items-center justify-between mb-4">
                        <h4 className="text-sm font-semibold text-gray-800">
                            Total Reaksi: {totalReactions}
                        </h4>
                        <span className="text-xs text-gray-500">
                            {Object.entries(counts).filter(([_, count]) => count && count > 0).length} jenis reaksi
                        </span>
                    </div>

                    {/* Detailed Breakdown */}
                    <div className="space-y-2">
                        {Object.entries(counts)
                            .filter(([_, count]) => count && count > 0)
                            .sort(([, a], [, b]) => (b || 0) - (a || 0))
                            .map(([type, count]) => {
                                const IconComponent = REACTION_ICONS[type as keyof typeof REACTION_ICONS];
                                const percentage = totalReactions > 0 ? Math.round((count! / totalReactions) * 100) : 0;
                                const isUserReaction = userReaction === type;

                                return (
                                    <div key={type} className={`flex items-center justify-between p-2 rounded-md transition-colors ${
                                        isUserReaction ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 hover:bg-gray-100'
                                    }`}>
                                        <div className="flex items-center space-x-3">
                                            <IconComponent size={20} />
                                            <span className={`text-sm font-medium ${
                                                isUserReaction ? 'text-blue-700' : 'text-gray-700'
                                            }`}>
                                                {REACTION_LABELS[type as keyof typeof REACTION_LABELS]}
                                                {isUserReaction && (
                                                    <span className="ml-2 text-xs bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full">
                                                        Kamu
                                                    </span>
                                                )}
                                            </span>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <span className={`text-sm font-semibold ${
                                                isUserReaction ? 'text-blue-600' : 'text-gray-600'
                                            }`}>
                                                {count}
                                            </span>
                                            <span className="text-xs text-gray-400">
                                                ({percentage}%)
                                            </span>
                                        </div>
                                    </div>
                                );
                            })
                        }
                    </div>

                    {/* Quick Stats */}
                    <div className="mt-4 pt-3 border-t border-gray-100">
                        <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>
                                Reaksi terpopuler: {
                                    Object.entries(counts)
                                        .filter(([_, count]) => count && count > 0)
                                        .sort(([, a], [, b]) => (b || 0) - (a || 0))[0]?.[0]
                                        ? REACTION_LABELS[Object.entries(counts)
                                            .filter(([_, count]) => count && count > 0)
                                            .sort(([, a], [, b]) => (b || 0) - (a || 0))[0][0] as keyof typeof REACTION_LABELS]
                                        : 'Belum ada'
                                }
                            </span>
                            {userReaction && (
                                <span className="text-blue-600">
                                    ✓ Kamu sudah bereaksi
                                </span>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {/* Empty State */}
            {totalReactions === 0 && (
                <div className="mt-6 pt-4 border-t border-gray-200 text-center">
                    <div className="text-gray-400 text-sm">
                        <p className="mb-2">👆 Jadilah yang pertama memberikan reaksi!</p>
                        <p className="text-xs">Pilih salah satu reaksi di atas untuk mengekspresikan perasaanmu</p>
                    </div>
                </div>
            )}

            {/* Loading Indicator */}
            {isLoading && (
                <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center rounded-lg">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
            )}
        </div>
    );
};

export default NewsReactionBar;
