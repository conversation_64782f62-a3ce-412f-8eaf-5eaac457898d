import React, { useState } from 'react';
import axios from 'axios';

interface ReactionCounts {
    like: number;
    love: number;
    wow: number;
    haha: number;
    sad: number;
    angry: number;
}

interface ReactionBarProps {
    reactableType: 'news' | 'comment';
    reactableId: number;
    initialCounts?: ReactionCounts;
    initialUserReaction?: string | null;
    totalCount?: number;
    isAuthenticated?: boolean;
    className?: string;
}

const REACTION_TYPES = {
    like: '👍',
    love: '❤️',
    wow: '😮',
    haha: '😂',
    sad: '😢',
    angry: '😡',
};

const REACTION_LABELS = {
    like: 'Suka',
    love: 'Cinta',
    wow: 'Wow',
    haha: 'Haha',
    sad: 'Sedih',
    angry: 'Marah',
};

const ReactionBar: React.FC<ReactionBarProps> = ({
    reactableType,
    reactableId,
    initialCounts,
    initialUserReaction,
    totalCount,
    isAuthenticated = false,
    className = '',
}) => {
    // Safely handle initial data
    const safeCounts = initialCounts || { like: 0, love: 0, wow: 0, haha: 0, sad: 0, angry: 0 };
    const safeUserReaction = initialUserReaction || null;
    
    const [counts, setCounts] = useState<ReactionCounts>(safeCounts);
    const [userReaction, setUserReaction] = useState<string | null>(safeUserReaction);
    const [isLoading, setIsLoading] = useState(false);
    const [showReactionPicker, setShowReactionPicker] = useState(false);

    const handleReactionToggle = async (reactionType: string) => {
        if (isLoading) return;

        setIsLoading(true);
        
        try {
            const endpoint = isAuthenticated 
                ? '/reactions/toggle' 
                : '/guest/reactions/toggle';

            const response = await axios.post(endpoint, {
                reactable_type: reactableType,
                reactable_id: reactableId,
                reaction_type: reactionType,
            });

            if (response.data && response.data.success) {
                if (response.data.counts) {
                    setCounts(response.data.counts);
                }
                setUserReaction(response.data.user_reaction || null);
                setShowReactionPicker(false);
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
            // Show user-friendly error message
            alert('Terjadi kesalahan saat memberikan reaksi. Silakan coba lagi.');
        } finally {
            setIsLoading(false);
        }
    };

    const getTotalReactions = () => {
        try {
            return Object.values(counts).reduce((sum, count) => sum + (count || 0), 0);
        } catch {
            return 0;
        }
    };

    const getMostPopularReactions = () => {
        try {
            return Object.entries(counts)
                .filter(([_, count]) => count && count > 0)
                .sort(([, a], [, b]) => (b || 0) - (a || 0))
                .slice(0, 3);
        } catch {
            return [];
        }
    };

    const totalReactions = getTotalReactions();
    const popularReactions = getMostPopularReactions();

    return (
        <div className={`reaction-bar ${className}`}>
            {/* Reaction Summary */}
            {totalReactions > 0 && (
                <div className="flex items-center space-x-2 mb-3">
                    <div className="flex items-center space-x-1">
                        {popularReactions.map(([type, count]) => (
                            <span key={type} className="text-lg">
                                {REACTION_TYPES[type as keyof typeof REACTION_TYPES] || '👍'}
                            </span>
                        ))}
                    </div>
                    <span className="text-sm text-gray-600">
                        {totalReactions} reaksi
                    </span>
                </div>
            )}

            {/* Main Reaction Button */}
            <div className="flex items-center space-x-4">
                <div className="relative">
                    <button
                        onClick={() => setShowReactionPicker(!showReactionPicker)}
                        className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                            userReaction
                                ? 'bg-blue-50 border-blue-300 text-blue-700'
                                : 'bg-gray-50 border-gray-300 text-gray-700 hover:bg-gray-100'
                        }`}
                        disabled={isLoading}
                    >
                        <span className="text-lg">
                            {userReaction && REACTION_TYPES[userReaction as keyof typeof REACTION_TYPES] 
                                ? REACTION_TYPES[userReaction as keyof typeof REACTION_TYPES]
                                : '👍'
                            }
                        </span>
                        <span className="text-sm font-medium">
                            {userReaction && REACTION_LABELS[userReaction as keyof typeof REACTION_LABELS] 
                                ? REACTION_LABELS[userReaction as keyof typeof REACTION_LABELS]
                                : 'Bereaksi'
                            }
                        </span>
                        {isLoading && <span className="text-xs">...</span>}
                    </button>

                    {/* Reaction Picker */}
                    {showReactionPicker && (
                        <div className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-10">
                            <div className="flex space-x-1">
                                {Object.entries(REACTION_TYPES).map(([type, emoji]) => (
                                    <button
                                        key={type}
                                        onClick={() => handleReactionToggle(type)}
                                        className={`p-2 rounded-lg hover:bg-gray-100 transition-colors ${
                                            userReaction === type ? 'bg-blue-100' : ''
                                        }`}
                                        title={REACTION_LABELS[type as keyof typeof REACTION_LABELS]}
                                        disabled={isLoading}
                                    >
                                        <span className="text-2xl">{emoji}</span>
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                {/* Individual Reaction Counts */}
                {totalReactions > 0 && (
                    <div className="flex items-center space-x-3">
                        {Object.entries(counts)
                            .filter(([_, count]) => count && count > 0)
                            .map(([type, count]) => (
                                <button
                                    key={type}
                                    onClick={() => handleReactionToggle(type)}
                                    className={`flex items-center space-x-1 px-2 py-1 rounded-md text-xs transition-colors ${
                                        userReaction === type
                                            ? 'bg-blue-100 text-blue-700'
                                            : 'text-gray-600 hover:bg-gray-100'
                                    }`}
                                    disabled={isLoading}
                                >
                                    <span>{REACTION_TYPES[type as keyof typeof REACTION_TYPES] || '👍'}</span>
                                    <span>{count}</span>
                                </button>
                            ))}
                    </div>
                )}
            </div>

            {/* Click outside to close picker */}
            {showReactionPicker && (
                <div
                    className="fixed inset-0 z-5"
                    onClick={() => setShowReactionPicker(false)}
                />
            )}
        </div>
    );
};

export default ReactionBar;