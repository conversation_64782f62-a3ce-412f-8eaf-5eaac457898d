import React from 'react';

interface IconProps {
    className?: string;
    size?: number;
}

// Suka (Like/Thumbs Up)
export const SukaIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
    <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        className={className}
    >
        <circle cx="12" cy="12" r="12" fill="#FF8C42"/>
        <path
            d="M8 14h1.5l1.5-4h4c.83 0 1.5.67 1.5 1.5S15.83 13 15 13h-1l-.5 2H16c.83 0 1.5.67 1.5 1.5S16.83 18 16 18H9c-.83 0-1.5-.67-1.5-1.5V15c0-.83.67-1.5 1.5-1.5z"
            fill="white"
        />
        <circle cx="7" cy="8" r="1" fill="white"/>
        <circle cx="17" cy="8" r="1" fill="white"/>
    </svg>
);

// Benci (Dislike)
export const BenciIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
    <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        className={className}
    >
        <circle cx="12" cy="12" r="12" fill="#6B7280"/>
        <path
            d="M16 10h-1.5l-1.5 4H9c-.83 0-1.5-.67-1.5-1.5S8.17 11 9 11h1l.5-2H8c-.83 0-1.5-.67-1.5-1.5S7.17 6 8 6h7c.83 0 1.5.67 1.5 1.5V9c0 .83-.67 1.5-1.5 1.5z"
            fill="white"
        />
        <circle cx="7" cy="16" r="1" fill="white"/>
        <circle cx="17" cy="16" r="1" fill="white"/>
    </svg>
);

// Cinta (Love/Heart)
export const CintaIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
    <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        className={className}
    >
        <circle cx="12" cy="12" r="12" fill="#FF69B4"/>
        <path
            d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"
            fill="white"
        />
        <circle cx="8" cy="8" r="1" fill="white"/>
        <circle cx="16" cy="8" r="1" fill="white"/>
    </svg>
);

// Lucu (Funny/Laugh) - Yellow
export const LucuIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
    <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={className}
    >
        <circle cx="12" cy="12" r="12" fill="#FFD700"/>
        <circle cx="8" cy="9" r="1.5" fill="black"/>
        <circle cx="16" cy="9" r="1.5" fill="black"/>
        <path 
            d="M7 14c0 2.76 2.24 5 5 5s5-2.24 5-5H7z" 
            fill="black"
        />
        <path 
            d="M12 16c1.1 0 2-.9 2-2H10c0 1.1.9 2 2 2z" 
            fill="#FFD700"
        />
    </svg>
);

// Marah (Angry) - Red
export const MarahIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
    <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={className}
    >
        <circle cx="12" cy="12" r="12" fill="#FF4444"/>
        <path 
            d="M6 8l2 2 1-1-2-2-1 1zm10 0l1 1 2-2-1-1-2 2z" 
            fill="black"
        />
        <circle cx="8" cy="10" r="1" fill="black"/>
        <circle cx="16" cy="10" r="1" fill="black"/>
        <path 
            d="M16 16c0-2.21-1.79-4-4-4s-4 1.79-4 4h8z" 
            fill="black"
        />
    </svg>
);

// Sedih (Sad) - Blue
export const SedihIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
    <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={className}
    >
        <circle cx="12" cy="12" r="12" fill="#4A90E2"/>
        <circle cx="8" cy="9" r="1" fill="black"/>
        <circle cx="16" cy="9" r="1" fill="black"/>
        <path 
            d="M12 14c-2.21 0-4 1.79-4 4h8c0-2.21-1.79-4-4-4z" 
            fill="black"
        />
        <path 
            d="M9 7l1 1 1-1-1-1-1 1zm6 0l-1 1-1-1 1-1 1 1z" 
            fill="black"
        />
        <path 
            d="M10 6c0-.55-.45-1-1-1s-1 .45-1 1v2c0 .55.45 1 1 1s1-.45 1-1V6zm6 0c0-.55-.45-1-1-1s-1 .45-1 1v2c0 .55.45 1 1 1s1-.45 1-1V6z" 
            fill="#87CEEB"
        />
    </svg>
);

// Wow (Surprised) - Yellow/Orange
export const WowIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
    <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        className={className}
    >
        <circle cx="12" cy="12" r="12" fill="#FFA500"/>
        <circle cx="8" cy="9" r="2" fill="black"/>
        <circle cx="16" cy="9" r="2" fill="black"/>
        <ellipse cx="12" cy="16" rx="2" ry="3" fill="black"/>
        <circle cx="8" cy="9" r="0.5" fill="white"/>
        <circle cx="16" cy="9" r="0.5" fill="white"/>
    </svg>
);

// Reaction Icons Map
export const REACTION_ICONS = {
    suka: SukaIcon,
    benci: BenciIcon,
    cinta: CintaIcon,
    lucu: LucuIcon,
    marah: MarahIcon,
    sedih: SedihIcon,
    wow: WowIcon,
};

// Reaction Labels
export const REACTION_LABELS = {
    suka: 'Suka',
    benci: 'Benci', 
    cinta: 'Cinta',
    lucu: 'Lucu',
    marah: 'Marah',
    sedih: 'Sedih',
    wow: 'Wow',
};


