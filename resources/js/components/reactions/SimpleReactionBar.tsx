import React, { useState } from 'react';

interface ReactionCounts {
    suka: number;
    benci: number;
    cinta: number;
    lucu: number;
    marah: number;
    sedih: number;
    wow: number;
}

interface SimpleReactionBarProps {
    reactableType: 'news' | 'comment';
    reactableId: number;
    initialCounts?: ReactionCounts;
    initialUserReaction?: string | null;
    totalCount?: number;
    isAuthenticated?: boolean;
    className?: string;
}

// Simple reaction types with emojis (can be replaced with image URLs)
const REACTION_TYPES = {
    suka: '👍',      // You can replace with: '/img/reactions/suka.png'
    benci: '👎',     // You can replace with: '/img/reactions/benci.png'
    cinta: '❤️',     // You can replace with: '/img/reactions/cinta.png'
    lucu: '😂',      // You can replace with: '/img/reactions/lucu.png'
    marah: '😡',     // You can replace with: '/img/reactions/marah.png'
    sedih: '😢',     // You can replace with: '/img/reactions/sedih.png'
    wow: '😮',       // You can replace with: '/img/reactions/wow.png'
};

const REACTION_LABELS = {
    suka: 'Suka',
    benci: 'Benci',
    cinta: 'Cinta',
    lucu: 'Lucu',
    marah: 'Marah',
    sedih: 'Sedih',
    wow: 'Wow',
};

const SimpleReactionBar: React.FC<SimpleReactionBarProps> = ({
    reactableType,
    reactableId,
    initialCounts,
    initialUserReaction,
    totalCount,
    isAuthenticated = false,
    className = '',
}) => {
    // Safely handle initial data
    const safeCounts = initialCounts || { 
        suka: 0, benci: 0, cinta: 0, lucu: 0, marah: 0, sedih: 0, wow: 0 
    };
    const safeUserReaction = initialUserReaction || null;
    
    const [counts, setCounts] = useState<ReactionCounts>(safeCounts);
    const [userReaction, setUserReaction] = useState<string | null>(safeUserReaction);
    const [isLoading, setIsLoading] = useState(false);
    const [showReactionPicker, setShowReactionPicker] = useState(false);

    const handleReactionToggle = async (reactionType: string) => {
        if (isLoading) return;

        setIsLoading(true);

        try {
            const endpoint = isAuthenticated
                ? '/reactions/toggle'
                : '/guest/reactions/toggle';

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    reactable_type: reactableType,
                    reactable_id: reactableId,
                    reaction_type: reactionType,
                }),
            });

            const data = await response.json();

            if (data.success) {
                setCounts(data.counts);
                setUserReaction(data.user_reaction || null);
                setShowReactionPicker(false);
            } else {
                throw new Error('Failed to toggle reaction');
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
            alert('Terjadi kesalahan saat memberikan reaksi. Silakan coba lagi.');
        } finally {
            setIsLoading(false);
        }
    };

    const getTotalReactions = () => {
        try {
            return Object.values(counts).reduce((sum, count) => sum + (count || 0), 0);
        } catch {
            return 0;
        }
    };

    const getMostPopularReactions = () => {
        try {
            return Object.entries(counts)
                .filter(([_, count]) => count && count > 0)
                .sort(([, a], [, b]) => (b || 0) - (a || 0))
                .slice(0, 3);
        } catch {
            return [];
        }
    };

    const totalReactions = getTotalReactions();
    const popularReactions = getMostPopularReactions();

    // Helper function to render reaction icon (emoji or image)
    const renderReactionIcon = (type: string, size: 'small' | 'medium' | 'large' = 'medium') => {
        const reaction = REACTION_TYPES[type as keyof typeof REACTION_TYPES];
        
        // Check if it's an image URL (starts with / or http)
        if (reaction.startsWith('/') || reaction.startsWith('http')) {
            const sizeClass = size === 'small' ? 'w-4 h-4' : size === 'large' ? 'w-8 h-8' : 'w-6 h-6';
            return (
                <img 
                    src={reaction} 
                    alt={REACTION_LABELS[type as keyof typeof REACTION_LABELS]}
                    className={sizeClass}
                />
            );
        }
        
        // Otherwise, it's an emoji
        const fontSize = size === 'small' ? 'text-sm' : size === 'large' ? 'text-2xl' : 'text-lg';
        return <span className={fontSize}>{reaction}</span>;
    };

    return (
        <div className={`reaction-bar ${className}`}>
            {/* Question Text */}
            <div className="mb-4">
                <h3 className="text-lg font-medium text-gray-800">
                    Apa Reaksimu?
                </h3>
            </div>

            {/* Reaction Summary */}
            {totalReactions > 0 && (
                <div className="flex items-center space-x-2 mb-3">
                    <div className="flex items-center space-x-1">
                        {popularReactions.map(([type, count]) => (
                            <span key={type}>
                                {renderReactionIcon(type, 'small')}
                            </span>
                        ))}
                    </div>
                    <span className="text-sm text-gray-600">
                        {totalReactions} reaksi
                    </span>
                </div>
            )}

            {/* Main Reaction Button */}
            <div className="flex items-center space-x-4">
                <div className="relative">
                    <button
                        onClick={() => setShowReactionPicker(!showReactionPicker)}
                        className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                            userReaction
                                ? 'bg-blue-50 border-blue-300 text-blue-700'
                                : 'bg-gray-50 border-gray-300 text-gray-700 hover:bg-gray-100'
                        }`}
                        disabled={isLoading}
                    >
                        <span>
                            {userReaction 
                                ? renderReactionIcon(userReaction, 'medium')
                                : renderReactionIcon('suka', 'medium')
                            }
                        </span>
                        <span className="text-sm font-medium">
                            {userReaction && REACTION_LABELS[userReaction as keyof typeof REACTION_LABELS] 
                                ? REACTION_LABELS[userReaction as keyof typeof REACTION_LABELS]
                                : 'Bereaksi'
                            }
                        </span>
                        {isLoading && <span className="text-xs">...</span>}
                    </button>

                    {/* Reaction Picker */}
                    {showReactionPicker && (
                        <div className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-10">
                            <div className="flex space-x-1">
                                {Object.entries(REACTION_TYPES).map(([type, _]) => (
                                    <button
                                        key={type}
                                        onClick={() => handleReactionToggle(type)}
                                        className={`p-2 rounded-lg hover:bg-gray-100 transition-colors ${
                                            userReaction === type ? 'bg-blue-100' : ''
                                        }`}
                                        title={REACTION_LABELS[type as keyof typeof REACTION_LABELS]}
                                        disabled={isLoading}
                                    >
                                        {renderReactionIcon(type, 'large')}
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                {/* Individual Reaction Counts */}
                {totalReactions > 0 && (
                    <div className="flex items-center space-x-3">
                        {Object.entries(counts)
                            .filter(([_, count]) => count && count > 0)
                            .map(([type, count]) => (
                                <button
                                    key={type}
                                    onClick={() => handleReactionToggle(type)}
                                    className={`flex items-center space-x-1 px-2 py-1 rounded-md text-xs transition-colors ${
                                        userReaction === type
                                            ? 'bg-blue-100 text-blue-700'
                                            : 'text-gray-600 hover:bg-gray-100'
                                    }`}
                                    disabled={isLoading}
                                >
                                    {renderReactionIcon(type, 'small')}
                                    <span>{count}</span>
                                </button>
                            ))}
                    </div>
                )}
            </div>

            {/* Click outside to close picker */}
            {showReactionPicker && (
                <div
                    className="fixed inset-0 z-5"
                    onClick={() => setShowReactionPicker(false)}
                />
            )}
        </div>
    );
};

export default SimpleReactionBar;
