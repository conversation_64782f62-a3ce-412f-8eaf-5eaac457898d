import React from 'react';
import { REACTION_ICONS, REACTION_LABELS } from './ReactionIcons';

interface ReactionCounts {
    suka?: number;
    benci?: number;
    cinta?: number;
    lucu?: number;
    marah?: number;
    sedih?: number;
    wow?: number;
    // Old types for backward compatibility
    like?: number;
    love?: number;
    haha?: number;
    sad?: number;
    angry?: number;
}

interface ReactionCounterProps {
    counts: ReactionCounts;
    totalCount?: number;
    showLabels?: boolean;
    showTotal?: boolean;
    maxDisplay?: number;
    size?: 'small' | 'medium' | 'large';
    className?: string;
}

const ReactionCounter: React.FC<ReactionCounterProps> = ({
    counts,
    totalCount,
    showLabels = false,
    showTotal = true,
    maxDisplay = 3,
    size = 'small',
    className = '',
}) => {
    // Calculate total if not provided
    const calculatedTotal = totalCount || Object.values(counts).reduce((sum, count) => sum + (count || 0), 0);
    
    // Get most popular reactions
    const popularReactions = Object.entries(counts)
        .filter(([_, count]) => count && count > 0)
        .sort(([, a], [, b]) => (b || 0) - (a || 0))
        .slice(0, maxDisplay);

    // Size configurations
    const sizeConfig = {
        small: {
            iconSize: 16,
            textSize: 'text-xs',
            spacing: 'space-x-1',
        },
        medium: {
            iconSize: 20,
            textSize: 'text-sm',
            spacing: 'space-x-2',
        },
        large: {
            iconSize: 24,
            textSize: 'text-base',
            spacing: 'space-x-2',
        },
    };

    const config = sizeConfig[size];

    if (calculatedTotal === 0) {
        return null;
    }

    return (
        <div className={`reaction-counter flex items-center ${config.spacing} ${className}`}>
            {/* Popular Reactions Icons */}
            {popularReactions.length > 0 && (
                <div className="flex items-center -space-x-1">
                    {popularReactions.map(([type, count]) => {
                        const IconComponent = REACTION_ICONS[type as keyof typeof REACTION_ICONS];
                        if (!IconComponent) return null;
                        
                        return (
                            <div
                                key={type}
                                className="relative bg-white rounded-full border border-gray-200 p-0.5"
                                title={`${REACTION_LABELS[type as keyof typeof REACTION_LABELS]}: ${count}`}
                            >
                                <IconComponent size={config.iconSize} />
                            </div>
                        );
                    })}
                </div>
            )}

            {/* Total Count */}
            {showTotal && (
                <span className={`text-gray-600 ${config.textSize} font-medium`}>
                    {calculatedTotal > 999 ? `${Math.floor(calculatedTotal / 1000)}k` : calculatedTotal}
                    {showLabels && (
                        <span className="ml-1">
                            {calculatedTotal === 1 ? 'reaksi' : 'reaksi'}
                        </span>
                    )}
                </span>
            )}

            {/* Individual Reaction Counts (for detailed view) */}
            {showLabels && popularReactions.length > 0 && (
                <div className={`flex items-center ${config.spacing}`}>
                    {popularReactions.slice(0, 2).map(([type, count]) => (
                        <span key={type} className={`text-gray-500 ${config.textSize}`}>
                            {REACTION_LABELS[type as keyof typeof REACTION_LABELS]} {count}
                        </span>
                    ))}
                    {popularReactions.length > 2 && (
                        <span className={`text-gray-500 ${config.textSize}`}>
                            +{popularReactions.length - 2} lainnya
                        </span>
                    )}
                </div>
            )}
        </div>
    );
};

export default ReactionCounter;
