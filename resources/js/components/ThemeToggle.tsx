import { cn } from "@/lib/utils";
import { useThemeStore } from "@/stores/theme-store";
import { Moon, Sun } from "lucide-react";
import { HTMLAttributes } from "react";

export default function ThemeToggle({
  className = "",
  ...props
}: HTMLAttributes<HTMLButtonElement>) {
  const { theme, setTheme } = useThemeStore();

  const isDark = theme === "dark";

  return (
    <button
      onClick={() => setTheme(isDark ? "light" : "dark")}
      className={cn(
        "inline-flex items-center justify-center rounded-md p-2 transition-colors",
        "bg-neutral-100/10 hover:bg-neutral-200/30 dark:bg-neutral-800/10 dark:hover:bg-neutral-700/30",
        className
      )}
      aria-label="Toggle theme"
      {...props}
    >
      {isDark ? (
        <Sun className="h-5 w-5 text-yellow-500" />
      ) : (
        <Moon className="h-5 w-5 text-gray-700 dark:text-gray-200" />
      )}
    </button>
  );
}
