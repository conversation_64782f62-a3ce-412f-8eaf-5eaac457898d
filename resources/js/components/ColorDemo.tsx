import React from 'react';

const ColorDemo: React.FC = () => {
  const primaryColors = [
    { name: 'primary-lighter', bg: 'bg-primary-lighter', text: 'text-white' },
    { name: 'primary-light', bg: 'bg-primary-light', text: 'text-primary-darker' },
    { name: 'primary', bg: 'bg-primary', text: 'text-primary-foreground' },
    { name: 'primary-dark', bg: 'bg-primary-dark', text: 'text-white' },
    { name: 'primary-darker', bg: 'bg-primary-darker', text: 'text-white' },
  ];

  const componentColors = [
    { name: 'secondary', bg: 'bg-secondary', text: 'text-secondary-foreground' },
    { name: 'tertiary', bg: 'bg-tertiary', text: 'text-tertiary-foreground' },
    { name: 'muted', bg: 'bg-muted', text: 'text-muted-foreground' },
    { name: 'accent', bg: 'bg-accent', text: 'text-accent-foreground' },
    { name: 'card', bg: 'bg-card', text: 'text-card-foreground' },
    { name: 'popover', bg: 'bg-popover', text: 'text-popover-foreground' },
  ];

  const statusColors = [
    { name: 'success', bg: 'bg-success', text: 'text-success-foreground' },
    { name: 'warning', bg: 'bg-warning', text: 'text-warning-foreground' },
    { name: 'info', bg: 'bg-info', text: 'text-info-foreground' },
    { name: 'destructive', bg: 'bg-destructive', text: 'text-destructive-foreground' },
  ];

  const weatherColors = [
    { name: 'weather-sunny', bg: 'bg-weather-sunny', text: 'text-black', icon: '☀️' },
    { name: 'weather-sunny-light', bg: 'bg-weather-sunny-light', text: 'text-black', icon: '🌤️' },
    { name: 'weather-sunny-dark', bg: 'bg-weather-sunny-dark', text: 'text-white', icon: '☀️' },
    { name: 'weather-cloudy', bg: 'bg-weather-cloudy', text: 'text-white', icon: '☁️' },
    { name: 'weather-cloudy-light', bg: 'bg-weather-cloudy-light', text: 'text-black', icon: '⛅' },
    { name: 'weather-cloudy-dark', bg: 'bg-weather-cloudy-dark', text: 'text-white', icon: '☁️' },
    { name: 'weather-rainy', bg: 'bg-weather-rainy', text: 'text-white', icon: '🌧️' },
    { name: 'weather-rainy-light', bg: 'bg-weather-rainy-light', text: 'text-black', icon: '🌦️' },
    { name: 'weather-rainy-dark', bg: 'bg-weather-rainy-dark', text: 'text-white', icon: '⛈️' },
    { name: 'weather-snowy', bg: 'bg-weather-snowy', text: 'text-black', icon: '❄️' },
    { name: 'weather-snowy-light', bg: 'bg-weather-snowy-light', text: 'text-black', icon: '🌨️' },
    { name: 'weather-snowy-dark', bg: 'bg-weather-snowy-dark', text: 'text-white', icon: '❄️' },
    { name: 'weather-stormy', bg: 'bg-weather-stormy', text: 'text-white', icon: '⛈️' },
    { name: 'weather-stormy-light', bg: 'bg-weather-stormy-light', text: 'text-black', icon: '🌩️' },
    { name: 'weather-stormy-dark', bg: 'bg-weather-stormy-dark', text: 'text-white', icon: '⛈️' },
    { name: 'weather-windy', bg: 'bg-weather-windy', text: 'text-white', icon: '💨' },
    { name: 'weather-windy-light', bg: 'bg-weather-windy-light', text: 'text-black', icon: '🌬️' },
    { name: 'weather-windy-dark', bg: 'bg-weather-windy-dark', text: 'text-white', icon: '💨' },
  ];

  const chartColors = [
    { name: 'chart-1', bg: 'bg-chart-1', text: 'text-white' },
    { name: 'chart-2', bg: 'bg-chart-2', text: 'text-white' },
    { name: 'chart-3', bg: 'bg-chart-3', text: 'text-white' },
    { name: 'chart-4', bg: 'bg-chart-4', text: 'text-white' },
    { name: 'chart-5', bg: 'bg-chart-5', text: 'text-white' },
  ];

  const ColorGrid = ({ title, colors }: { title: string; colors: any[] }) => (
    <div className="mb-8">
      <h3 className="text-lg font-semibold mb-4 text-foreground">{title}</h3>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
        {colors.map((color) => (
          <div
            key={color.name}
            className={`${color.bg} ${color.text} p-4 rounded-lg border border-border transition-all duration-200 hover:scale-105 cursor-pointer`}
          >
            <div className="font-mono text-sm font-medium">
              {color.icon && <span className="text-lg mr-2">{color.icon}</span>}
              {color.name}
            </div>
            <div className="text-xs mt-1 opacity-75">
              .{color.bg}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const ButtonDemo = () => (
    <div className="mb-8">
      <h3 className="text-lg font-semibold mb-4 text-foreground">Button Examples</h3>
      <div className="flex flex-wrap gap-3 mb-4">
        <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary-dark transition-colors">
          Primary
        </button>
        <button className="bg-primary-lighter text-primary-darker px-4 py-2 rounded-md hover:bg-primary-light transition-colors">
          Primary Lighter
        </button>
        <button className="bg-primary-darker text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors">
          Primary Darker
        </button>
        <button className="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-accent transition-colors">
          Secondary
        </button>
      </div>
      <div className="flex flex-wrap gap-3">
        <button className="bg-success text-success-foreground px-4 py-2 rounded-md hover:opacity-90 transition-colors">
          Success
        </button>
        <button className="bg-warning text-warning-foreground px-4 py-2 rounded-md hover:opacity-90 transition-colors">
          Warning
        </button>
        <button className="bg-destructive text-destructive-foreground px-4 py-2 rounded-md hover:opacity-90 transition-colors">
          Destructive
        </button>
        <button className="bg-info text-info-foreground px-4 py-2 rounded-md hover:opacity-90 transition-colors">
          Info
        </button>
      </div>
    </div>
  );

  const TextColorDemo = () => (
    <div className="mb-8">
      <h3 className="text-lg font-semibold mb-4 text-foreground">Text Color Examples</h3>
      <div className="space-y-2">
        <p className="text-primary">This text uses .text-primary</p>
        <p className="text-primary-light">This text uses .text-primary-light</p>
        <p className="text-primary-dark">This text uses .text-primary-dark</p>
        <p className="text-primary-darker">This text uses .text-primary-darker</p>
        <p className="text-success">This text uses .text-success</p>
        <p className="text-warning">This text uses .text-warning</p>
        <p className="text-destructive">This text uses .text-destructive</p>
        <p className="text-info">This text uses .text-info</p>
        <p className="text-muted-foreground">This text uses .text-muted-foreground</p>
      </div>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-6 bg-background min-h-screen">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 text-foreground">Available Tailwind Color Utilities</h1>
        <p className="text-muted-foreground">
          All semantic colors from your app.css that can be used with Tailwind utilities like bg-*, text-*, border-*, etc.
        </p>
      </div>

      <ButtonDemo />
      <TextColorDemo />
      
      <ColorGrid title="Primary Colors" colors={primaryColors} />
      <ColorGrid title="Component Colors" colors={componentColors} />
      <ColorGrid title="Status Colors" colors={statusColors} />
      <ColorGrid title="Weather Colors" colors={weatherColors} />
      <ColorGrid title="Chart Colors" colors={chartColors} />

      <div className="mt-12 p-6 bg-muted rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-foreground">Usage Examples</h3>
        <div className="space-y-2 text-sm font-mono">
          <div><span className="text-primary">&lt;div className="bg-primary text-primary-foreground"&gt;</span>Primary background<span className="text-primary">&lt;/div&gt;</span></div>
          <div><span className="text-primary">&lt;div className="bg-primary-lighter hover:bg-primary-darker"&gt;</span>Hover effect<span className="text-primary">&lt;/div&gt;</span></div>
          <div><span className="text-primary">&lt;div className="text-primary border-primary"&gt;</span>Text and border<span className="text-primary">&lt;/div&gt;</span></div>
          <div><span className="text-primary">&lt;div className="bg-weather-sunny text-black"&gt;</span>Weather colors<span className="text-primary">&lt;/div&gt;</span></div>
          <div><span className="text-primary">&lt;div className="bg-success text-success-foreground"&gt;</span>Status colors<span className="text-primary">&lt;/div&gt;</span></div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-info border border-info rounded-lg">
        <p className="text-info-foreground text-sm">
          <strong>Note:</strong> All these colors automatically adapt to dark mode and use the semantic variables defined in your app.css!
        </p>
      </div>
    </div>
  );
};

export default ColorDemo;