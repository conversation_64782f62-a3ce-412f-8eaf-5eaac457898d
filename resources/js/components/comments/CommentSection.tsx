import React, { useState, useEffect } from 'react';
import axios from 'axios';
import ReactionBar from '../reactions/ReactionBar';
import { MessageCircle, Edit, Trash2, Reply, Send } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
}

interface CommentReactions {
    counts: {
        like: number;
        love: number;
        wow: number;
        haha: number;
        sad: number;
        angry: number;
    };
    total_count: number;
    user_reaction: string | null;
}

interface CommentData {
    id: number;
    content: string;
    author_name: string;
    author_email: string;
    is_approved: boolean;
    is_reply: boolean;
    can_be_replied_to: boolean;
    can_edit: boolean;
    created_at: string;
    created_at_human: string;
    reactions: CommentReactions;
    replies: CommentData[];
}

interface CommentSectionProps {
    newsId: number;
    user?: User;
    isAuthenticated?: boolean;
    initialComments?: CommentData[];
    commentsCount?: number;
}

const CommentSection: React.FC<CommentSectionProps> = ({
    newsId,
    user,
    isAuthenticated = false,
    initialComments = [],
    commentsCount = 0,
}) => {
    const [comments, setComments] = useState<CommentData[]>(initialComments || []);
    const [newComment, setNewComment] = useState('');
    const [guestName, setGuestName] = useState('');
    const [guestEmail, setGuestEmail] = useState('');
    const [replyingTo, setReplyingTo] = useState<number | null>(null);
    const [replyContent, setReplyContent] = useState('');
    const [editingComment, setEditingComment] = useState<number | null>(null);
    const [editContent, setEditContent] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [guestInfoSaved, setGuestInfoSaved] = useState(false);

    useEffect(() => {
        if (initialComments.length === 0) {
            loadComments();
        }
        // Load saved guest info for non-authenticated users
        if (!isAuthenticated) {
            loadSavedGuestInfo();
        }
    }, [newsId, isAuthenticated]);

    const loadSavedGuestInfo = () => {
        try {
            const savedName = localStorage.getItem('guest_name');
            const savedEmail = localStorage.getItem('guest_email');
            if (savedName) {
                setGuestName(savedName);
                setGuestInfoSaved(true);
            }
            if (savedEmail) {
                setGuestEmail(savedEmail);
                setGuestInfoSaved(true);
            }
        } catch (error) {
            // localStorage might not be available in some browsers
            console.log('Could not load saved guest info:', error);
        }
    };

    const saveGuestInfo = (name: string, email: string) => {
        try {
            localStorage.setItem('guest_name', name);
            localStorage.setItem('guest_email', email);
            setGuestInfoSaved(true);
        } catch (error) {
            // localStorage might not be available in some browsers
            console.log('Could not save guest info:', error);
        }
    };

    const clearGuestInfo = () => {
        try {
            localStorage.removeItem('guest_name');
            localStorage.removeItem('guest_email');
            setGuestName('');
            setGuestEmail('');
            setGuestInfoSaved(false);
        } catch (error) {
            console.log('Could not clear guest info:', error);
        }
    };

    const loadComments = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const response = await axios.get(`/news/${newsId}/comments`);
            if (response.data && response.data.success) {
                setComments(response.data.comments || []);
            }
        } catch (error) {
            console.error('Error loading comments:', error);
            setError('Gagal memuat komentar. Silakan refresh halaman.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleSubmitComment = async (e: React.FormEvent) => {
        e.preventDefault();
        if (isSubmitting || !newComment.trim()) return;
        
        if (!isAuthenticated && (!guestName.trim() || !guestEmail.trim())) {
            setError('Silakan isi nama dan email untuk mengirim komentar');
            return;
        }

        setIsSubmitting(true);
        setError(null);
        
        try {
            const endpoint = isAuthenticated 
                ? `/news/${newsId}/comments`
                : `/guest/news/${newsId}/comments`;

            const data: any = {
                content: newComment.trim(),
            };

            if (!isAuthenticated) {
                data.guest_name = guestName.trim();
                data.guest_email = guestEmail.trim();
            }

            const response = await axios.post(endpoint, data);
            
            if (response.data && response.data.success) {
                const newCommentData = response.data.comment;
                if (newCommentData) {
                    setComments([newCommentData, ...comments]);
                    setNewComment('');
                    if (!isAuthenticated) {
                        // Save guest info for future use
                        saveGuestInfo(guestName.trim(), guestEmail.trim());
                    }
                }
            } else {
                throw new Error(response.data?.message || 'Gagal mengirim komentar');
            }
        } catch (error: any) {
            console.error('Error submitting comment:', error);
            const errorMessage = error.response?.data?.message || error.message || 'Terjadi kesalahan saat mengirim komentar';
            setError(errorMessage);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleSubmitReply = async (parentId: number) => {
        if (isSubmitting || !replyContent.trim()) return;
        
        if (!isAuthenticated && (!guestName.trim() || !guestEmail.trim())) {
            setError('Silakan isi nama dan email untuk mengirim balasan');
            return;
        }

        setIsSubmitting(true);
        setError(null);
        
        try {
            const endpoint = isAuthenticated 
                ? `/news/${newsId}/comments`
                : `/guest/news/${newsId}/comments`;

            const data: any = {
                content: replyContent.trim(),
                parent_id: parentId,
            };

            if (!isAuthenticated) {
                data.guest_name = guestName.trim();
                data.guest_email = guestEmail.trim();
            }

            const response = await axios.post(endpoint, data);
            
            if (response.data && response.data.success && response.data.comment) {
                const updatedComments = comments.map(comment => {
                    if (comment.id === parentId) {
                        return {
                            ...comment,
                            replies: [...(comment.replies || []), response.data.comment]
                        };
                    }
                    return comment;
                });
                setComments(updatedComments);
                setReplyContent('');
                setReplyingTo(null);
                if (!isAuthenticated) {
                    // Save guest info for future use
                    saveGuestInfo(guestName.trim(), guestEmail.trim());
                }
            } else {
                throw new Error(response.data?.message || 'Gagal mengirim balasan');
            }
        } catch (error: any) {
            console.error('Error submitting reply:', error);
            const errorMessage = error.response?.data?.message || error.message || 'Terjadi kesalahan saat mengirim balasan';
            setError(errorMessage);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleEditComment = async (commentId: number) => {
        if (isSubmitting || !editContent.trim()) return;

        setIsSubmitting(true);
        setError(null);
        
        try {
            const response = await axios.patch(`/comments/${commentId}`, {
                content: editContent.trim(),
            });
            
            if (response.data && response.data.success) {
                const updateCommentInList = (commentsList: CommentData[]): CommentData[] => {
                    return commentsList.map(comment => {
                        if (comment.id === commentId) {
                            return { ...comment, content: editContent.trim() };
                        }
                        if (comment.replies && comment.replies.length > 0) {
                            return {
                                ...comment,
                                replies: updateCommentInList(comment.replies)
                            };
                        }
                        return comment;
                    });
                };
                
                setComments(updateCommentInList(comments));
                setEditingComment(null);
                setEditContent('');
            } else {
                throw new Error(response.data?.message || 'Gagal mengedit komentar');
            }
        } catch (error: any) {
            console.error('Error editing comment:', error);
            const errorMessage = error.response?.data?.message || error.message || 'Terjadi kesalahan saat mengedit komentar';
            setError(errorMessage);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleDeleteComment = async (commentId: number) => {
        if (!confirm('Apakah Anda yakin ingin menghapus komentar ini?')) return;

        setError(null);
        try {
            const response = await axios.delete(`/comments/${commentId}`);
            
            if (response.data && response.data.success) {
                const removeCommentFromList = (commentsList: CommentData[]): CommentData[] => {
                    return commentsList.filter(comment => {
                        if (comment.id === commentId) {
                            return false;
                        }
                        if (comment.replies && comment.replies.length > 0) {
                            comment.replies = removeCommentFromList(comment.replies);
                        }
                        return true;
                    });
                };
                
                setComments(removeCommentFromList(comments));
            } else {
                throw new Error(response.data?.message || 'Gagal menghapus komentar');
            }
        } catch (error: any) {
            console.error('Error deleting comment:', error);
            const errorMessage = error.response?.data?.message || error.message || 'Terjadi kesalahan saat menghapus komentar';
            setError(errorMessage);
        }
    };

    const CommentItem: React.FC<{ comment: CommentData; isReply?: boolean }> = ({ 
        comment, 
        isReply = false 
    }) => (
        <div className={`border-l-2 border-gray-200 pl-4 ${isReply ? 'ml-8 mt-3' : 'mb-6'}`}>
            <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-start justify-between mb-2">
                    <div>
                        <h4 className="font-semibold text-gray-900">{comment.author_name}</h4>
                        <p className="text-xs text-gray-500">{comment.created_at_human}</p>
                    </div>
                    {comment.can_edit && (
                        <div className="flex space-x-2">
                            <button
                                onClick={() => {
                                    setEditingComment(comment.id);
                                    setEditContent(comment.content);
                                }}
                                className="text-gray-400 hover:text-blue-600"
                            >
                                <Edit size={16} />
                            </button>
                            <button
                                onClick={() => handleDeleteComment(comment.id)}
                                className="text-gray-400 hover:text-red-600"
                            >
                                <Trash2 size={16} />
                            </button>
                        </div>
                    )}
                </div>

                {editingComment === comment.id ? (
                    <div className="mb-3">
                        <textarea
                            value={editContent}
                            onChange={(e) => setEditContent(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-lg resize-none"
                            rows={3}
                        />
                        <div className="flex space-x-2 mt-2">
                            <button
                                onClick={() => handleEditComment(comment.id)}
                                disabled={isSubmitting}
                                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
                            >
                                Simpan
                            </button>
                            <button
                                onClick={() => {
                                    setEditingComment(null);
                                    setEditContent('');
                                }}
                                className="px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
                            >
                                Batal
                            </button>
                        </div>
                    </div>
                ) : (
                    <p className="text-gray-800 mb-3">{comment.content}</p>
                )}

                <div className="flex items-center justify-between">
                    <ReactionBar
                        reactableType="comment"
                        reactableId={comment.id}
                        initialCounts={comment.reactions?.counts || { like: 0, love: 0, wow: 0, haha: 0, sad: 0, angry: 0 }}
                        initialUserReaction={comment.reactions?.user_reaction || null}
                        totalCount={comment.reactions?.total_count || 0}
                        isAuthenticated={isAuthenticated}
                        className="flex-1"
                    />
                    
                    {comment.can_be_replied_to && !isReply && (
                        <button
                            onClick={() => {
                                setReplyingTo(replyingTo === comment.id ? null : comment.id);
                                setReplyContent('');
                            }}
                            className="flex items-center space-x-1 text-gray-600 hover:text-blue-600 text-sm"
                        >
                            <Reply size={16} />
                            <span>Balas</span>
                        </button>
                    )}
                </div>

                {replyingTo === comment.id && (
                    <div className="mt-4 border-t pt-4">
                        <div className="space-y-3">
                            {!isAuthenticated && (
                                <div className="grid grid-cols-2 gap-3">
                                    <div className="relative">
                                        <input
                                            type="text"
                                            placeholder="Nama"
                                            value={guestName}
                                            onChange={(e) => setGuestName(e.target.value)}
                                            className={`px-3 py-2 border rounded-lg text-sm w-full ${
                                                guestInfoSaved && guestName 
                                                    ? 'border-green-300 bg-green-50' 
                                                    : 'border-gray-300'
                                            }`}
                                            required
                                            title={guestInfoSaved ? "Menggunakan nama tersimpan" : "Nama untuk balasan"}
                                        />
                                        {guestInfoSaved && guestName && (
                                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                                                <svg className="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                        )}
                                    </div>
                                    <div className="relative">
                                        <input
                                            type="email"
                                            placeholder="Email"
                                            value={guestEmail}
                                            onChange={(e) => setGuestEmail(e.target.value)}
                                            className={`px-3 py-2 border rounded-lg text-sm w-full ${
                                                guestInfoSaved && guestEmail 
                                                    ? 'border-green-300 bg-green-50' 
                                                    : 'border-gray-300'
                                            }`}
                                            required
                                            title={guestInfoSaved ? "Menggunakan email tersimpan" : "Email untuk balasan"}
                                        />
                                        {guestInfoSaved && guestEmail && (
                                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                                                <svg className="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                            <textarea
                                value={replyContent}
                                onChange={(e) => setReplyContent(e.target.value)}
                                placeholder="Tulis balasan..."
                                className="w-full p-3 border border-gray-300 rounded-lg resize-none"
                                rows={3}
                            />
                            <div className="flex space-x-2">
                                <button
                                    onClick={() => handleSubmitReply(comment.id)}
                                    disabled={isSubmitting || !replyContent.trim()}
                                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                                >
                                    <Send size={16} />
                                    <span>Kirim Balasan</span>
                                </button>
                                <button
                                    onClick={() => {
                                        setReplyingTo(null);
                                        setReplyContent('');
                                    }}
                                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
                                >
                                    Batal
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {comment.replies && comment.replies.length > 0 && (
                    <div className="mt-4">
                        {comment.replies.map((reply) => (
                            <CommentItem key={reply.id} comment={reply} isReply={true} />
                        ))}
                    </div>
                )}
            </div>
        </div>
    );

    return (
        <div className="comments-section">
            <div className="flex items-center space-x-2 mb-6">
                <MessageCircle className="text-gray-600" size={24} />
                <h3 className="text-xl font-bold text-gray-900">
                    Komentar ({comments?.length || 0})
                </h3>
            </div>

            {error && (
                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                    {error}
                    <button 
                        onClick={() => setError(null)}
                        className="float-right font-bold"
                    >
                        ×
                    </button>
                </div>
            )}

            <form onSubmit={handleSubmitComment} className="mb-8">
                <div className="space-y-4">
                    {!isAuthenticated && (
                        <div className="space-y-3">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-700">Info Pengomentar</span>
                                {guestInfoSaved && (
                                    <div className="flex items-center space-x-2">
                                        <span className="text-xs text-green-600 flex items-center">
                                            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                            Tersimpan
                                        </span>
                                        <button
                                            type="button"
                                            onClick={clearGuestInfo}
                                            className="text-xs text-red-600 hover:text-red-800 underline"
                                            title="Hapus info tersimpan"
                                        >
                                            Hapus
                                        </button>
                                    </div>
                                )}
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="relative">
                                    <input
                                        type="text"
                                        placeholder="Nama"
                                        value={guestName}
                                        onChange={(e) => setGuestName(e.target.value)}
                                        className={`px-4 py-3 border rounded-lg w-full ${
                                            guestInfoSaved && guestName 
                                                ? 'border-green-300 bg-green-50' 
                                                : 'border-gray-300'
                                        }`}
                                        required
                                        title={guestInfoSaved ? "Menggunakan nama tersimpan" : "Nama akan disimpan untuk komentar berikutnya"}
                                    />
                                    {guestInfoSaved && guestName && (
                                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                                            <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                    )}
                                </div>
                                <div className="relative">
                                    <input
                                        type="email"
                                        placeholder="Email"
                                        value={guestEmail}
                                        onChange={(e) => setGuestEmail(e.target.value)}
                                        className={`px-4 py-3 border rounded-lg w-full ${
                                            guestInfoSaved && guestEmail 
                                                ? 'border-green-300 bg-green-50' 
                                                : 'border-gray-300'
                                        }`}
                                        required
                                        title={guestInfoSaved ? "Menggunakan email tersimpan" : "Email akan disimpan untuk komentar berikutnya"}
                                    />
                                    {guestInfoSaved && guestEmail && (
                                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                                            <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                    )}
                                </div>
                            </div>
                            {guestInfoSaved && (
                                <p className="text-xs text-gray-600">
                                    💡 Info Anda sudah tersimpan dan akan digunakan otomatis untuk komentar berikutnya
                                </p>
                            )}
                        </div>
                    )}
                    <textarea
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder={isAuthenticated 
                            ? `Tulis komentar sebagai ${user?.name}...` 
                            : "Tulis komentar..."
                        }
                        className="w-full p-4 border border-gray-300 rounded-lg resize-none"
                        rows={4}
                        required
                    />
                    <button
                        type="submit"
                        disabled={isSubmitting || !newComment.trim()}
                        className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                    >
                        <Send size={20} />
                        <span>Kirim Komentar</span>
                    </button>
                </div>
            </form>

            <div className="space-y-6">
                {isLoading ? (
                    <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="text-gray-600 mt-2">Memuat komentar...</p>
                    </div>
                ) : comments.length > 0 ? (
                    comments.map((comment) => (
                        <CommentItem key={comment.id} comment={comment} />
                    ))
                ) : (
                    <div className="text-center py-8 text-gray-500">
                        <MessageCircle size={48} className="mx-auto mb-4 text-gray-300" />
                        <p>Belum ada komentar. Jadilah yang pertama berkomentar!</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default CommentSection;