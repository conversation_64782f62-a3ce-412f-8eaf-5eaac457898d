import React, { useState } from 'react';
import { Check, X, Info, Code, Zap, Settings } from 'lucide-react';

const TailwindApproachComparison: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'css' | 'config'>('css');

  const approaches = {
    css: {
      title: 'CSS-First Approach',
      subtitle: 'Tailwind v4 Recommended',
      status: 'Current Implementation',
      icon: <Zap className="w-5 h-5" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
    },
    config: {
      title: 'Config File Approach', 
      subtitle: 'Traditional Method',
      status: 'Alternative Option',
      icon: <Settings className="w-5 h-5" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
    }
  };

  const cssApproach = {
    pros: [
      'Tailwind v4 officially recommended approach',
      'Better runtime performance with CSS custom properties',
      'No build-time configuration needed',
      'Better for SSR and hydration',
      'Easier to debug - everything in one CSS file',
      'Dynamic theming support at runtime',
      'Smaller bundle size',
      'Works without Node.js build process'
    ],
    cons: [
      'Less familiar to developers used to config files',
      'Requires understanding CSS custom properties',
      'Limited IntelliSense support in some editors'
    ],
    usage: [
      'bg-primary',
      'text-primary-foreground', 
      'bg-weather-sunny',
      'text-muted-foreground',
      'bg-destructive'
    ],
    setup: `/* In app.css */
@theme {
  --color-primary: var(--primary);
  --color-weather-sunny: var(--weather-sunny);
}

/* Usage */
<div className="bg-primary text-primary-foreground">
  Primary button
</div>`
  };

  const configApproach = {
    pros: [
      'Familiar to most Tailwind developers',
      'Better IDE support and IntelliSense',
      'Type-safe with TypeScript',
      'More explicit configuration',
      'Easier to share config across projects'
    ],
    cons: [
      'Requires build-time configuration',
      'Larger bundle size',
      'More complex setup',
      'Less performant at runtime',
      'Harder to implement dynamic theming',
      'Not the v4 recommended approach'
    ],
    usage: [
      'bg-primary',
      'text-primary-foreground',
      'bg-weather-sunny', 
      'text-muted-foreground',
      'bg-destructive'
    ],
    setup: `// tailwind.config.js
export default {
  theme: {
    extend: {
      colors: {
        primary: 'var(--primary)',
        weather: {
          sunny: 'var(--weather-sunny)'
        }
      }
    }
  }
}

/* Usage */
<div className="bg-primary text-primary-foreground">
  Primary button  
</div>`
  };

  const data = activeTab === 'css' ? cssApproach : configApproach;
  const approach = approaches[activeTab];

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Tailwind v4 Color Configuration Approaches
        </h1>
        <p className="text-muted-foreground">
          Compare the CSS-first approach vs traditional config file approach for your weather app
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex bg-muted rounded-lg p-1 mb-8 max-w-md mx-auto">
        {Object.entries(approaches).map(([key, approach]) => (
          <button
            key={key}
            onClick={() => setActiveTab(key as 'css' | 'config')}
            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-all ${
              activeTab === key
                ? `${approach.bgColor} ${approach.color} shadow-sm`
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            {approach.icon}
            <span className="font-medium">{approach.title}</span>
          </button>
        ))}
      </div>

      {/* Current Approach Info */}
      <div className={`${approach.bgColor} ${approach.borderColor} border rounded-lg p-4 mb-8`}>
        <div className="flex items-center space-x-2 mb-2">
          {approach.icon}
          <h2 className={`text-lg font-semibold ${approach.color}`}>
            {approach.title}
          </h2>
          <span className={`px-2 py-1 text-xs rounded-full ${approach.bgColor} ${approach.color} border ${approach.borderColor}`}>
            {approach.status}
          </span>
        </div>
        <p className={approach.color}>{approach.subtitle}</p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Pros */}
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
            <Check className="w-5 h-5 text-green-500 mr-2" />
            Advantages
          </h3>
          <ul className="space-y-3">
            {data.pros.map((pro, index) => (
              <li key={index} className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-foreground">{pro}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Cons */}
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
            <X className="w-5 h-5 text-red-500 mr-2" />
            Considerations
          </h3>
          <ul className="space-y-3">
            {data.cons.map((con, index) => (
              <li key={index} className="flex items-start space-x-2">
                <X className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-foreground">{con}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Usage Examples */}
      <div className="mt-8 bg-card border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
          <Code className="w-5 h-5 text-primary mr-2" />
          Available Utilities
        </h3>
        <div className="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
          {data.usage.map((utility, index) => (
            <div key={index} className="bg-muted px-3 py-2 rounded font-mono text-sm">
              .{utility}
            </div>
          ))}
        </div>
      </div>

      {/* Setup Code */}
      <div className="mt-8 bg-card border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4">Setup Example</h3>
        <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
          <code>{data.setup}</code>
        </pre>
      </div>

      {/* Recommendation */}
      <div className="mt-8 bg-primary-lighter border border-primary-light rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <Info className="w-5 h-5 text-primary-dark mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-semibold text-primary-darker mb-2">Recommendation for Your Weather App</h3>
            <p className="text-primary-dark text-sm mb-3">
              Since you're already using the CSS-first approach and it's working well, I recommend sticking with it. 
              Your current setup with minimal, essential colors is actually following Tailwind v4 best practices perfectly.
            </p>
            <div className="text-primary-dark text-sm">
              <strong>Current setup is ideal because:</strong>
              <ul className="list-disc list-inside mt-1 space-y-1 ml-4">
                <li>You only expose the colors you actually need</li>
                <li>Better performance for your weather app</li>
                <li>Cleaner, more maintainable codebase</li>
                <li>Future-proof with Tailwind v4</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Available Colors */}
      <div className="mt-8 bg-card border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4">Currently Available Colors</h3>
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-foreground">Brand Colors</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-primary rounded"></div>
                <span>bg-primary</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-secondary rounded"></div>
                <span>bg-secondary</span>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-foreground">Weather Colors</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-weather-sunny rounded"></div>
                <span>bg-weather-sunny</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-weather-cloudy rounded"></div>
                <span>bg-weather-cloudy</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-weather-rainy rounded"></div>
                <span>bg-weather-rainy</span>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-foreground">UI Colors</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-muted rounded"></div>
                <span>bg-muted</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-accent rounded"></div>
                <span>bg-accent</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-destructive rounded"></div>
                <span>bg-destructive</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TailwindApproachComparison;