import React from 'react';
import { Cloud, Sun, MapPin, Search, Bell, User } from 'lucide-react';

const GradientHeaderExample: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* Method 1: Tailwind Gradient Classes */}
      <header className="bg-gradient-to-br from-primary to-primary-darker text-primary-foreground">
        <div className="container mx-auto px-6 py-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <Sun className="w-8 h-8" />
              <h1 className="text-2xl font-bold">Weather Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Bell className="w-5 h-5 opacity-80 hover:opacity-100 cursor-pointer transition-opacity" />
              <User className="w-5 h-5 opacity-80 hover:opacity-100 cursor-pointer transition-opacity" />
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MapPin className="w-5 h-5 opacity-80" />
              <span className="text-lg">Jakarta, Indonesia</span>
            </div>
            <div className="flex items-center bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
              <Search className="w-4 h-4 mr-2 opacity-80" />
              <input 
                type="text" 
                placeholder="Search location..." 
                className="bg-transparent placeholder-white/70 text-white outline-none"
              />
            </div>
          </div>
        </div>
      </header>

      {/* Method 2: Custom Gradient Classes */}
      <header className="gradient-primary text-primary-foreground">
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Cloud className="w-7 h-7" />
              <div>
                <h1 className="text-xl font-bold">Weather App</h1>
                <p className="text-sm opacity-90">Real-time weather updates</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">28°C</div>
              <div className="text-sm opacity-90">Partly Cloudy</div>
            </div>
          </div>
        </div>
      </header>

      {/* Method 3: Weather-themed Gradient */}
      <header className="gradient-weather-header text-white">
        <div className="container mx-auto px-6 py-10">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-2">Today's Weather</h1>
            <p className="text-lg opacity-90 mb-6">Beautiful sunny day in Jakarta</p>
            <div className="flex items-center justify-center space-x-8">
              <div className="text-center">
                <div className="text-4xl mb-1">☀️</div>
                <div className="text-sm opacity-80">Sunny</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold mb-1">30°</div>
                <div className="text-sm opacity-80">Temperature</div>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-1">💨</div>
                <div className="text-sm opacity-80">15 km/h</div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Method 4: Complex Multi-stop Gradient */}
      <header className="bg-gradient-to-br from-primary-lighter via-primary to-primary-darker text-primary-foreground">
        <div className="container mx-auto px-6 py-8">
          <nav className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Sun className="w-6 h-6" />
                <span className="font-bold text-lg">WeatherPro</span>
              </div>
              <nav className="hidden md:flex space-x-6 text-sm">
                <a href="#" className="opacity-90 hover:opacity-100 transition-opacity">Dashboard</a>
                <a href="#" className="opacity-90 hover:opacity-100 transition-opacity">Forecast</a>
                <a href="#" className="opacity-90 hover:opacity-100 transition-opacity">Maps</a>
                <a href="#" className="opacity-90 hover:opacity-100 transition-opacity">Alerts</a>
              </nav>
            </div>
            <button className="bg-white/20 hover:bg-white/30 backdrop-blur-sm px-4 py-2 rounded-lg transition-colors">
              Settings
            </button>
          </nav>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <Sun className="w-8 h-8 text-yellow-300" />
                <div>
                  <div className="text-2xl font-bold">28°C</div>
                  <div className="text-sm opacity-80">Current</div>
                </div>
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <Cloud className="w-8 h-8 text-blue-200" />
                <div>
                  <div className="text-2xl font-bold">65%</div>
                  <div className="text-sm opacity-80">Humidity</div>
                </div>
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <MapPin className="w-8 h-8 text-green-300" />
                <div>
                  <div className="text-lg font-bold">Jakarta</div>
                  <div className="text-sm opacity-80">Indonesia</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Method 5: Button with Gradient Hover */}
      <div className="bg-card p-8 rounded-lg">
        <h3 className="text-lg font-semibold text-foreground mb-4">Interactive Gradient Button</h3>
        <button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary-darker text-primary-foreground px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
          Get Current Weather
        </button>
      </div>
    </div>
  );
};

export default GradientHeaderExample;