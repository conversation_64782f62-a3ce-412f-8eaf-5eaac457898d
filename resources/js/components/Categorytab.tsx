import { Link } from "@inertiajs/react";


type CategoryTab = { label: string; color: string; icon?: React.ReactNode };

interface CategoryTabsProps {
    tabs: CategoryTab[];
}

const CategoryTabs: React.FC<CategoryTabsProps> = ({ tabs }) => {
    return (
        <div className="flex overflow-x-auto scrollbar-hide space-x-4 py-3">
            {tabs.map((tab: CategoryTab, index: number) => (
                <Link
                    key={index}
                    href="#"
                    className={`px-6 py-2 rounded-full text-sm font-medium transition-colors bg-red-400/5 border border-red-700 text-red-500 hover:text-white hover:bg-red-600 `}
                >
                    {tab.icon} {tab.label}
                </Link>
            ))}
        </div>
    );
};

export default CategoryTabs;