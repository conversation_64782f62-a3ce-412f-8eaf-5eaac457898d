import React from "react";

type CategoryTab = { label: string; color: string; icon?: React.ReactNode };

interface CategoryTabsProps {
    tabs: CategoryTab[];
    activeTab?: string;
    onTabClick?: (tabLabel: string) => void;
}

const CategoryTabs: React.FC<CategoryTabsProps> = ({
    tabs,
    activeTab = tabs[0]?.label || "",
    onTabClick
}) => {
    const handleTabClick = (tabLabel: string) => {
        if (onTabClick) {
            onTabClick(tabLabel);
        }
    };

    return (
        <div className="flex overflow-x-auto scrollbar-hide space-x-4 py-3">
            {tabs.map((tab: CategoryTab, index: number) => {
                const isActive = activeTab === tab.label;
                return (
                    <button
                        key={index}
                        onClick={() => handleTabClick(tab.label)}
                        className={`px-6 py-2 rounded-full text-sm font-medium transition-colors whitespace-nowrap ${
                            isActive
                                ? 'bg-red-600 text-white border border-red-600'
                                : 'bg-red-400/5 border border-red-700 text-red-500 hover:text-white hover:bg-red-600'
                        }`}
                    >
                        {tab.icon} {tab.label}
                    </button>
                );
            })}
        </div>
    );
};

export default CategoryTabs;