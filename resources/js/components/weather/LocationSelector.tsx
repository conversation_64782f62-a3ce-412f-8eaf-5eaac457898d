import React, { useState } from 'react';
import { Search, MapPin, Globe, Clock } from 'lucide-react';
import { useWeatherStore } from '@/stores/weather-store';
import { WeatherLocation } from '@/types/weather';
import { cn } from '@/lib/utils';

interface LocationSelectorProps {
  onLocationSelect?: (location: WeatherLocation) => void;
  className?: string;
  showTitle?: boolean;
}

const LocationSelector: React.FC<LocationSelectorProps> = ({
  onLocationSelect,
  className,
  showTitle = true
}) => {
  const {
    searchResults,
    isSearching,
    searchLocations,
    fetchWeatherForLocation,
    clearError
  } = useWeatherStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [showResults, setShowResults] = useState(false);

  const popularCities = [
    { name: 'New York', country: 'United States', latitude: 40.7128, longitude: -74.0060, city: 'New York', countryCode: 'US', timezone: 'America/New_York' },
    { name: 'London', country: 'United Kingdom', latitude: 51.5074, longitude: -0.1278, city: 'London', countryCode: 'GB', timezone: 'Europe/London' },
    { name: 'Tokyo', country: 'Japan', latitude: 35.6762, longitude: 139.6503, city: 'Tokyo', countryCode: 'JP', timezone: 'Asia/Tokyo' },
    { name: 'Paris', country: 'France', latitude: 48.8566, longitude: 2.3522, city: 'Paris', countryCode: 'FR', timezone: 'Europe/Paris' },
    { name: 'Sydney', country: 'Australia', latitude: -33.8688, longitude: 151.2093, city: 'Sydney', countryCode: 'AU', timezone: 'Australia/Sydney' },
    { name: 'Dubai', country: 'United Arab Emirates', latitude: 25.2048, longitude: 55.2708, city: 'Dubai', countryCode: 'AE', timezone: 'Asia/Dubai' },
    { name: 'Singapore', country: 'Singapore', latitude: 1.3521, longitude: 103.8198, city: 'Singapore', countryCode: 'SG', timezone: 'Asia/Singapore' },
    { name: 'Los Angeles', country: 'United States', latitude: 34.0522, longitude: -118.2437, city: 'Los Angeles', countryCode: 'US', timezone: 'America/Los_Angeles' }
  ];

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (query.length > 2) {
      try {
        await searchLocations(query);
        setShowResults(true);
      } catch (error) {
        console.error('Search failed:', error);
        setShowResults(false);
      }
    } else {
      setShowResults(false);
    }
  };

  const handleLocationSelect = async (location: WeatherLocation) => {
    try {
      clearError();
      await fetchWeatherForLocation(location);
      if (onLocationSelect) {
        onLocationSelect(location);
      }
      setSearchQuery('');
      setShowResults(false);
    } catch (error) {
      console.error('Failed to select location:', error);
    }
  };

  const handlePopularCitySelect = async (city: any) => {
    const location: WeatherLocation = {
      latitude: city.latitude,
      longitude: city.longitude,
      city: city.city,
      country: city.country,
      countryCode: city.countryCode,
      timezone: city.timezone
    };
    await handleLocationSelect(location);
  };

  return (
    <div className={cn("bg-white border rounded-lg shadow-sm p-4", className)}>
      {showTitle && (
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-1 flex items-center">
            <MapPin className="w-5 h-5 mr-2 text-blue-500" />
            Select Your Location
          </h3>
          <p className="text-sm text-gray-600">
            Search for your city or choose from popular locations
          </p>
        </div>
      )}

      {/* Search Input */}
      <div className="relative mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            placeholder="Search for a city (e.g., London, New York, Tokyo)"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Search Results Dropdown */}
        {showResults && (searchResults.length > 0 || isSearching) && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
            {isSearching ? (
              <div className="p-3 text-sm text-gray-500 flex items-center">
                <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full mr-2"></div>
                Searching...
              </div>
            ) : (
              searchResults.map((location, index) => (
                <button
                  key={index}
                  onClick={() => handleLocationSelect(location)}
                  className="w-full text-left px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors"
                >
                  <div className="font-medium text-sm text-gray-900">{location.city}</div>
                  <div className="text-xs text-gray-500 flex items-center">
                    <Globe className="w-3 h-3 mr-1" />
                    {location.country}
                  </div>
                </button>
              ))
            )}
          </div>
        )}
      </div>

      {/* Popular Cities */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
          <Clock className="w-4 h-4 mr-1 text-gray-500" />
          Popular Cities
        </h4>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
          {popularCities.map((city, index) => (
            <button
              key={index}
              onClick={() => handlePopularCitySelect(city)}
              className="p-2 text-left border border-gray-200 rounded-md hover:border-blue-300 hover:bg-blue-50 transition-colors group"
            >
              <div className="text-sm font-medium text-gray-900 group-hover:text-blue-900">
                {city.name}
              </div>
              <div className="text-xs text-gray-500 group-hover:text-blue-700">
                {city.country}
              </div>
            </button>
          ))}
        </div>
      </div>

      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <p className="text-xs text-blue-800">
          <strong>Tip:</strong> If you're having trouble with location detection, try searching for your city manually or select a nearby major city from the popular options above.
        </p>
      </div>
    </div>
  );
};

export default LocationSelector;