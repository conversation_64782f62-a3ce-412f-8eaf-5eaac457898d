import React, { useState, useEffect } from 'react';
import { MapPin, X, Globe, AlertCircle, Check } from 'lucide-react';
import { useWeatherStore } from '@/stores/weather-store';
import { cn } from '@/lib/utils';

interface LocationPermissionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onLocationGranted?: (city: string) => void;
  onLocationDenied?: () => void;
}

const LocationPermissionDialog: React.FC<LocationPermissionDialogProps> = ({
  isOpen,
  onClose,
  onLocationGranted,
  onLocationDenied
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showManualSelect, setShowManualSelect] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  const { 
    fetchCurrentLocationWeather, 
    fetchWeatherForLocation,
    searchLocations,
    searchResults,
    isSearching
  } = useWeatherStore();

  // Popular Indonesian cities
  const popularCities = [
    { name: 'Jakarta', country: 'Indonesia', latitude: -6.2088, longitude: 106.8456 },
    { name: 'Surabaya', country: 'Indonesia', latitude: -7.2575, longitude: 112.7521 },
    { name: 'Bandung', country: 'Indonesia', latitude: -6.9175, longitude: 107.6191 },
    { name: 'Medan', country: 'Indonesia', latitude: 3.5952, longitude: 98.6722 },
    { name: 'Semarang', country: 'Indonesia', latitude: -6.9667, longitude: 110.4167 },
    { name: 'Makassar', country: 'Indonesia', latitude: -5.1477, longitude: 119.4327 },
    { name: 'Palembang', country: 'Indonesia', latitude: -2.9761, longitude: 104.7754 },
    { name: 'Yogyakarta', country: 'Indonesia', latitude: -7.7956, longitude: 110.3695 }
  ];

  useEffect(() => {
    // Check if user has already granted/denied permission
    const savedPreference = localStorage.getItem('weather-location-preference');
    const savedCity = localStorage.getItem('weather-saved-city');
    
    if (savedPreference === 'granted' && savedCity) {
      // User previously granted and we have saved city
      onClose();
      return;
    }
    
    if (savedPreference === 'denied') {
      // User previously denied, show manual selection
      setShowManualSelect(true);
    }
  }, [onClose]);

  const handleAllowLocation = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await fetchCurrentLocationWeather();
      
      // Save permission grant
      localStorage.setItem('weather-location-preference', 'granted');
      localStorage.setItem('weather-location-timestamp', Date.now().toString());
      
      // Try to save city name from weather data
      const weatherData = useWeatherStore.getState().currentWeather;
      if (weatherData?.location?.city) {
        const cityName = weatherData.location.city === 'Unknown' ? 'Jakarta' : weatherData.location.city;
        localStorage.setItem('weather-saved-city', cityName);
        onLocationGranted?.(cityName);
      } else {
        localStorage.setItem('weather-saved-city', 'Jakarta');
        onLocationGranted?.('Jakarta');
      }
      
      onClose();
    } catch (error) {
      console.error('Location access failed:', error);
      setError('Unable to access your location. Please try manual selection.');
      setShowManualSelect(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDenyLocation = () => {
    // Save permission denial
    localStorage.setItem('weather-location-preference', 'denied');
    localStorage.setItem('weather-saved-city', 'Jakarta');
    
    onLocationDenied?.();
    setShowManualSelect(true);
  };

  const handleManualCitySelect = async (city: any) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const location = {
        latitude: city.latitude,
        longitude: city.longitude,
        city: city.name,
        country: city.country,
        countryCode: city.countryCode || 'ID',
        timezone: city.timezone || 'Asia/Jakarta'
      };
      
      await fetchWeatherForLocation(location);
      
      // Save manual selection
      localStorage.setItem('weather-location-preference', 'manual');
      localStorage.setItem('weather-saved-city', city.name);
      localStorage.setItem('weather-location-timestamp', Date.now().toString());
      
      onLocationGranted?.(city.name);
      onClose();
    } catch (error) {
      console.error('Failed to load weather for selected city:', error);
      setError('Failed to load weather data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (query.length > 2) {
      try {
        await searchLocations(query);
      } catch (error) {
        console.error('Search failed:', error);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b dark:border-gray-700">
          <div className="flex items-center space-x-2">
            <MapPin className="w-5 h-5 text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Location for Weather
            </h2>
          </div>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {!showManualSelect ? (
            <>
              {/* Location Permission Request */}
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Globe className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Allow Location Access?
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  We'd like to show weather for your current location. Your location data will be saved locally for future use.
                </p>
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md flex items-start space-x-2">
                  <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  onClick={handleAllowLocation}
                  disabled={isLoading}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                      <span>Getting Location...</span>
                    </>
                  ) : (
                    <>
                      <Check className="w-4 h-4" />
                      <span>Allow Location Access</span>
                    </>
                  )}
                </button>
                
                <button
                  onClick={handleDenyLocation}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Choose Location Manually
                </button>
              </div>

              <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  <strong>Privacy:</strong> Your location will only be used to show weather data and will be stored locally on your device.
                </p>
              </div>
            </>
          ) : (
            <>
              {/* Manual Location Selection */}
              <div className="mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Choose Your Location
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Search for your city or select from popular locations in Indonesia.
                </p>
              </div>

              {/* Search Input */}
              <div className="mb-4">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  placeholder="Search for a city..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                
                {/* Search Results */}
                {searchQuery.length > 2 && (
                  <div className="mt-2 max-h-32 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md">
                    {isSearching ? (
                      <div className="p-3 text-sm text-gray-500 text-center">Searching...</div>
                    ) : searchResults.length > 0 ? (
                      searchResults.slice(0, 5).map((result, index) => (
                        <button
                          key={index}
                          onClick={() => handleManualCitySelect({
                            name: result.city,
                            country: result.country,
                            latitude: result.latitude,
                            longitude: result.longitude,
                            countryCode: result.countryCode,
                            timezone: result.timezone
                          })}
                          className="w-full text-left px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 border-b last:border-b-0 border-gray-200 dark:border-gray-600"
                        >
                          <div className="font-medium text-sm">{result.city}</div>
                          <div className="text-xs text-gray-500">{result.country}</div>
                        </button>
                      ))
                    ) : (
                      <div className="p-3 text-sm text-gray-500 text-center">No cities found</div>
                    )}
                  </div>
                )}
              </div>

              {/* Popular Cities */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Popular Cities in Indonesia
                </h4>
                <div className="grid grid-cols-2 gap-2">
                  {popularCities.map((city, index) => (
                    <button
                      key={index}
                      onClick={() => handleManualCitySelect(city)}
                      disabled={isLoading}
                      className="p-2 text-left border border-gray-200 dark:border-gray-600 rounded-md hover:border-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors disabled:opacity-50"
                    >
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {city.name}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {city.country}
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {error && (
                <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md flex items-start space-x-2">
                  <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
                </div>
              )}

              {isLoading && (
                <div className="mt-4 text-center">
                  <div className="inline-flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                    <span>Loading weather data...</span>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default LocationPermissionDialog;