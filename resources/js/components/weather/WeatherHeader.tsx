import React, { useEffect } from 'react';
import { MapPin, RefreshCw, Sun, Cloud, CloudRain, Wind, Droplets, ChevronDown } from 'lucide-react';
import { useWeather } from '@/hooks/use-weather';

interface WeatherHeaderProps {
  className?: string;
  showDetails?: boolean;
  onToggleDetails?: () => void;
}

const WeatherHeader: React.FC<WeatherHeaderProps> = ({ 
  className = '',
  showDetails = false,
  onToggleDetails
}) => {
  const {
    weather,
    isLoading,
    error,
    timeSinceLastUpdate,
    refreshWeather,
    getCurrentLocationWeather,
    hasWeatherData
  } = useWeather();

  useEffect(() => {
    if (!hasWeatherData) {
      getCurrentLocationWeather();
    }
  }, [hasWeatherData, getCurrentLocationWeather]);

  const getWeatherIcon = (condition: string, isDay: boolean = true) => {
    const iconClass = "w-4 h-4";
    
    switch (condition.toLowerCase()) {
      case 'clear sky':
        return isDay ? <Sun className={iconClass} /> : <div className="text-yellow-400 text-sm">🌙</div>;
      case 'partly cloudy':
      case 'mainly clear':
        return <Cloud className={iconClass} />;
      case 'overcast':
        return <Cloud className={`${iconClass} text-gray-500`} />;
      case 'rain':
      case 'light rain':
      case 'moderate rain':
      case 'heavy rain':
        return <CloudRain className={iconClass} />;
      default:
        return <Sun className={iconClass} />;
    }
  };

  if (error) {
    return (
      <div className={`flex items-center space-x-2 text-red-600 ${className}`}>
        <div className="text-xs">Weather unavailable</div>
        <button 
          onClick={getCurrentLocationWeather}
          className="text-xs hover:underline"
        >
          Retry
        </button>
      </div>
    );
  }

  if (isLoading || !weather) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="animate-pulse flex items-center space-x-1">
          <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
          <div className="h-3 bg-gray-300 rounded w-12"></div>
          <div className="h-3 bg-gray-300 rounded w-8"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Weather Icon & Temperature */}
      <div className="flex items-center space-x-2">
        <div className="text-lg">{weather.current.condition.icon}</div>
        <div className="flex items-center space-x-1">
          <span className="font-medium text-sm">{weather.formatted.temperature}</span>
          {onToggleDetails && (
            <button
              onClick={onToggleDetails}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ChevronDown className={`w-3 h-3 transition-transform ${showDetails ? 'rotate-180' : ''}`} />
            </button>
          )}
        </div>
      </div>

      {/* Location */}
      <div className="flex items-center space-x-1 text-xs text-gray-600">
        <MapPin className="w-3 h-3" />
        <span className="truncate max-w-20">{weather.location.city}</span>
      </div>

      {/* Additional Info (visible on larger screens) */}
      <div className="hidden sm:flex items-center space-x-3 text-xs text-gray-600">
        <div className="flex items-center space-x-1">
          <span>{weather.current.condition.description}</span>
        </div>
        <div className="flex items-center space-x-1">
          <Droplets className="w-3 h-3 text-blue-500" />
          <span>{weather.current.humidity}%</span>
        </div>
        <div className="flex items-center space-x-1">
          <Wind className="w-3 h-3 text-gray-500" />
          <span>{weather.formatted.windSpeed}</span>
        </div>
      </div>

      {/* Refresh Button */}
      <button 
        onClick={refreshWeather}
        disabled={isLoading}
        className="text-gray-400 hover:text-gray-600 disabled:animate-spin transition-colors"
        title="Refresh weather"
      >
        <RefreshCw className="w-3 h-3" />
      </button>

      {/* Last Updated (on hover or expanded) */}
      {showDetails && timeSinceLastUpdate && (
        <div className="text-xs text-gray-500">
          {timeSinceLastUpdate}
        </div>
      )}
    </div>
  );
};

export default WeatherHeader;