import React, { useEffect, useState } from 'react';
import { Cloud, Sun, CloudRain, Wind, Droplets, Eye, Gauge, RefreshCw, MapPin, Star, Search, Settings } from 'lucide-react';
import { useWeather } from '@/hooks/use-weather';
import { WeatherLocation } from '@/types/weather';

interface WeatherWidgetProps {
  className?: string;
  compact?: boolean;
  showSearch?: boolean;
  showSettings?: boolean;
}

const WeatherWidget: React.FC<WeatherWidgetProps> = ({
  className = '',
  compact = false,
  showSearch = true,
  showSettings = true
}) => {
  const {
    weather,
    hourlyForecast,
    dailyForecast,
    isLoading,
    error,
    searchResults,
    isSearching,
    savedLocations,
    selectedLocation,
    timeSinceLastUpdate,
    selectLocation,
    searchLocation,
    refreshWeather,
    getCurrentLocationWeather,
    toggleSavedLocation,
    isLocationSaved,
    clearError,
    hasWeatherData,
    preferences
  } = useWeather();

  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);

  useEffect(() => {
    if (!hasWeatherData) {
      getCurrentLocationWeather();
    }
  }, [hasWeatherData, getCurrentLocationWeather]);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (query.length > 2) {
      await searchLocation(query);
      setShowSearchResults(true);
    } else {
      setShowSearchResults(false);
    }
  };

  const handleLocationSelect = async (location: WeatherLocation) => {
    await selectLocation(location);
    setSearchQuery('');
    setShowSearchResults(false);
  };

  const getWeatherIcon = (condition: string, isDay: boolean = true) => {
    const iconClass = "w-6 h-6";
    
    switch (condition.toLowerCase()) {
      case 'clear sky':
        return isDay ? <Sun className={iconClass} /> : <div className={iconClass}>🌙</div>;
      case 'partly cloudy':
      case 'mainly clear':
        return <Cloud className={iconClass} />;
      case 'overcast':
        return <Cloud className={iconClass} />;
      case 'rain':
      case 'light rain':
      case 'moderate rain':
      case 'heavy rain':
        return <CloudRain className={iconClass} />;
      default:
        return <Sun className={iconClass} />;
    }
  };

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="text-red-600 text-sm">{error}</div>
          <button 
            onClick={clearError}
            className="text-red-600 hover:text-red-800"
          >
            ×
          </button>
        </div>
        <button 
          onClick={getCurrentLocationWeather}
          className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  if (isLoading && !hasWeatherData) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-300 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!weather) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="text-center">
          <div className="text-gray-500 mb-2">No weather data available</div>
          <button 
            onClick={getCurrentLocationWeather}
            className="text-blue-600 hover:text-blue-800 text-sm underline"
          >
            Get weather for current location
          </button>
        </div>
      </div>
    );
  }

  if (compact) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-3 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{weather.current.condition.icon}</div>
            <div>
              <div className="font-semibold">{weather.formatted.temperature}</div>
              <div className="text-xs text-gray-500">{weather.location.city}</div>
            </div>
          </div>
          <button 
            onClick={refreshWeather}
            disabled={isLoading}
            className="text-gray-400 hover:text-gray-600 disabled:animate-spin"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MapPin className="w-4 h-4 text-gray-500" />
            <span className="font-medium">{weather.location.city}, {weather.location.country}</span>
            <button
              onClick={() => toggleSavedLocation(weather.location)}
              className={`text-sm ${isLocationSaved(weather.location) ? 'text-yellow-500' : 'text-gray-400'} hover:text-yellow-600`}
            >
              <Star className="w-4 h-4" fill={isLocationSaved(weather.location) ? 'currentColor' : 'none'} />
            </button>
          </div>
          <div className="flex items-center space-x-2">
            {showSettings && (
              <button
                onClick={() => setShowPreferences(!showPreferences)}
                className="text-gray-400 hover:text-gray-600"
              >
                <Settings className="w-4 h-4" />
              </button>
            )}
            <button 
              onClick={refreshWeather}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 disabled:animate-spin"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          </div>
        </div>

        {showSearch && (
          <div className="mt-3 relative">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                placeholder="Search for a city..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {showSearchResults && (searchResults.length > 0 || isSearching) && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                {isSearching ? (
                  <div className="p-3 text-sm text-gray-500">Searching...</div>
                ) : (
                  searchResults.map((location, index) => (
                    <button
                      key={index}
                      onClick={() => handleLocationSelect(location)}
                      className="w-full text-left px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                    >
                      <div className="font-medium text-sm">{location.city}</div>
                      <div className="text-xs text-gray-500">{location.country}</div>
                    </button>
                  ))
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Current Weather */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <div className="text-4xl">{weather.current.condition.icon}</div>
            <div>
              <div className="text-3xl font-bold">{weather.formatted.temperature}</div>
              <div className="text-gray-600">{weather.current.condition.description}</div>
              <div className="text-sm text-gray-500">Feels like {weather.formatted.feelsLike}</div>
            </div>
          </div>
        </div>

        {/* Weather Details */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <Droplets className="w-4 h-4 text-blue-500" />
            <span>Humidity: {weather.current.humidity}%</span>
          </div>
          <div className="flex items-center space-x-2">
            <Wind className="w-4 h-4 text-gray-500" />
            <span>Wind: {weather.formatted.windSpeed} {weather.current.windDirection}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Gauge className="w-4 h-4 text-purple-500" />
            <span>Pressure: {weather.formatted.pressure}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Eye className="w-4 h-4 text-green-500" />
            <span>UV Index: {weather.current.uvIndex}</span>
          </div>
        </div>

        {/* Hourly Forecast */}
        {hourlyForecast.length > 0 && (
          <div className="mt-6">
            <h4 className="font-medium text-gray-700 mb-3">Hourly Forecast</h4>
            <div className="flex space-x-4 overflow-x-auto pb-2">
              {hourlyForecast.slice(0, 8).map((hour, index) => (
                <div key={index} className="flex-shrink-0 text-center">
                  <div className="text-xs text-gray-500 mb-1">{hour.formatted.time}</div>
                  <div className="text-lg mb-1">{hour.condition.icon}</div>
                  <div className="text-sm font-medium">{hour.formatted.temperature}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Daily Forecast */}
        {dailyForecast.length > 0 && (
          <div className="mt-6">
            <h4 className="font-medium text-gray-700 mb-3">7-Day Forecast</h4>
            <div className="space-y-2">
              {dailyForecast.slice(0, 5).map((day, index) => (
                <div key={index} className="flex items-center justify-between py-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{day.condition.icon}</span>
                    <span className="text-sm font-medium w-16">{day.formatted.shortDate}</span>
                    <span className="text-sm text-gray-600">{day.condition.description}</span>
                  </div>
                  <span className="text-sm font-medium">{day.formatted.temperatureRange}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Last Updated */}
        {timeSinceLastUpdate && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="text-xs text-gray-500">
              Last updated: {timeSinceLastUpdate}
            </div>
          </div>
        )}

        {/* Weather Advice */}
        {weather.formatted.advice && weather.formatted.advice.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <div className="text-sm text-blue-800">
              <div className="font-medium mb-1">Weather Advice</div>
              <ul className="space-y-1">
                {weather.formatted.advice.map((tip, index) => (
                  <li key={index}>• {tip}</li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>

      {/* Saved Locations */}
      {savedLocations.length > 0 && (
        <div className="p-4 border-t border-gray-100">
          <h4 className="font-medium text-gray-700 mb-3">Saved Locations</h4>
          <div className="flex flex-wrap gap-2">
            {savedLocations.map((location, index) => (
              <button
                key={index}
                onClick={() => selectLocation(location)}
                className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
              >
                {location.city}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WeatherWidget;