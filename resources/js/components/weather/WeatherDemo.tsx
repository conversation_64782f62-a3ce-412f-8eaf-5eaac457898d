import React, { useState } from 'react';
import { MapP<PERSON>, Settings, Bug, Thermometer, Wind, Droplets, Eye } from 'lucide-react';
import ModernWeatherCard from './ModernWeatherCard';
import SimpleWeatherCard from './SimpleWeatherCard';
import LocationSelector from './LocationSelector';
import SafeWeatherCard from './SafeWeatherCard';
import WeatherTroubleshoot from './WeatherTroubleshoot';
import { useWeatherStore } from '@/stores/weather-store';

const WeatherDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'cards' | 'selector' | 'debug'>('cards');
  const [showSettings, setShowSettings] = useState(false);
  
  const { 
    currentWeather, 
    temperatureUnit, 
    setTemperatureUnit,
    clearError,
    fetchCurrentLocationWeather 
  } = useWeatherStore();

  const tabs = [
    { id: 'cards', label: 'Weather Cards', icon: Thermometer },
    { id: 'selector', label: 'Location Selector', icon: MapPin },
    { id: 'debug', label: 'Debug Tools', icon: Bug }
  ];

  const renderCards = () => (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center py-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Weather Widget Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Explore different weather card variants, features, and functionality. 
          All components use Zustand for state management and support light/dark themes.
        </p>
      </div>

      {/* Quick Stats */}
      {currentWeather && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Current Weather</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <Thermometer className="w-5 h-5 text-red-500" />
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Temperature</div>
                <div className="font-semibold text-gray-900 dark:text-white">
                  {Math.round(currentWeather.current.temperature)}°C
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Wind className="w-5 h-5 text-gray-500" />
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Wind</div>
                <div className="font-semibold text-gray-900 dark:text-white">
                  {Math.round(currentWeather.current.windSpeed)} km/h
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Droplets className="w-5 h-5 text-blue-500" />
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Humidity</div>
                <div className="font-semibold text-gray-900 dark:text-white">
                  {currentWeather.current.humidity}%
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Eye className="w-5 h-5 text-green-500" />
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400">UV Index</div>
                <div className="font-semibold text-gray-900 dark:text-white">
                  {currentWeather.current.uvIndex}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modern Weather Card - Full */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Modern Weather Card</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">Default with Search</h3>
            <ModernWeatherCard className="w-full" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">Without Search</h3>
            <ModernWeatherCard className="w-full" showSearch={false} />
          </div>
        </div>
      </div>

      {/* Compact Cards */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Compact Variants</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Modern Compact</h3>
            <ModernWeatherCard compact className="w-full" />
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Simple Card</h3>
            <SimpleWeatherCard className="w-full" />
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Safe Wrapper</h3>
            <SafeWeatherCard compact className="w-full" />
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Weather Settings</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Temperature Unit
              </label>
              <div className="flex space-x-2">
                <button
                  onClick={() => setTemperatureUnit('celsius')}
                  className={`px-3 py-1 rounded text-sm ${
                    temperatureUnit === 'celsius'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  Celsius (°C)
                </button>
                <button
                  onClick={() => setTemperatureUnit('fahrenheit')}
                  className={`px-3 py-1 rounded text-sm ${
                    temperatureUnit === 'fahrenheit'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  Fahrenheit (°F)
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
        >
          <Settings className="w-4 h-4" />
          <span>{showSettings ? 'Hide' : 'Show'} Settings</span>
        </button>
        <button
          onClick={() => {
            clearError();
            fetchCurrentLocationWeather();
          }}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <MapPin className="w-4 h-4" />
          <span>Refresh Location</span>
        </button>
      </div>
    </div>
  );

  const renderSelector = () => (
    <div className="space-y-6">
      <div className="text-center py-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Location Selector
        </h2>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          When geolocation fails or is denied, users can manually search for their city 
          or select from popular locations worldwide.
        </p>
      </div>
      
      <div className="max-w-2xl mx-auto">
        <LocationSelector />
      </div>

      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
          Usage Scenarios:
        </h3>
        <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
          <li>• Location permission denied by user</li>
          <li>• GPS/location services unavailable</li>
          <li>• Network timeout during location detection</li>
          <li>• User wants to check weather for different cities</li>
          <li>• Geolocation API not supported</li>
        </ul>
      </div>
    </div>
  );

  const renderDebug = () => (
    <div className="space-y-6">
      <div className="text-center py-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Debug & Troubleshooting
        </h2>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Diagnostic tools to help identify and resolve weather widget issues.
          Perfect for development and troubleshooting user problems.
        </p>
      </div>
      
      <WeatherTroubleshoot />
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Navigation Tabs */}
        <div className="flex justify-center mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm border dark:border-gray-700">
            {tabs.map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === id
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="text-sm font-medium">{label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <div className="max-w-7xl mx-auto">
          {activeTab === 'cards' && renderCards()}
          {activeTab === 'selector' && renderSelector()}
          {activeTab === 'debug' && renderDebug()}
        </div>

        {/* Footer */}
        <div className="mt-16 text-center text-sm text-gray-500 dark:text-gray-400">
          <p>Weather data provided by Open-Meteo API</p>
          <p className="mt-1">
            Built with React, TypeScript, Zustand, and Tailwind CSS
          </p>
        </div>
      </div>
    </div>
  );
};

export default WeatherDemo;