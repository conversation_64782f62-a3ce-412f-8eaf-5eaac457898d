import React, { useEffect, useState } from 'react';
import { Cloud, Sun, CloudRain, Wind, Droplets, Eye, Gauge, RefreshCw, MapPin, Star, Search, Settings } from 'lucide-react';
import { useWeather } from '@/hooks/use-weather';

interface WeatherWidgetProps {
    className?: string;
}

const SimpleWeatherWidget: React.FC<WeatherWidgetProps> = ({
    className = '',
}) => {
    const {
        weather,
        isLoading,
        error,
        getCurrentLocationWeather,
        clearError,
        hasWeatherData,
    } = useWeather();


    useEffect(() => {
        if (!hasWeatherData) {
            getCurrentLocationWeather();
        }
    }, [hasWeatherData, getCurrentLocationWeather]);



    if (error) {
        return (
            <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
                <div className="flex items-center justify-between">
                    <div className="text-red-600 text-sm">{error}</div>
                    <button
                        onClick={clearError}
                        className="text-red-600 hover:text-red-800"
                    >
                        ×
                    </button>
                </div>
                <button
                    onClick={getCurrentLocationWeather}
                    className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                >
                    Try again
                </button>
            </div>
        );
    }

    if (isLoading && !hasWeatherData) {
        return (
            <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
                <div className="animate-pulse">
                    <div className="flex items-center space-x-3">
                        <div className="w-18 h-18 bg-gray-300 rounded-full"></div>
                        <div className="flex-1">
                            <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                            <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (!weather) {
        return (
            <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
                <div className="text-center">
                    <div className="text-gray-500 mb-2">No weather data available</div>
                    <button
                        onClick={getCurrentLocationWeather}
                        className="text-blue-600 hover:text-blue-800 text-sm underline"
                    >
                        Get weather for current location
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className={`flex items-center ${className}`}>
            <div className="text-6xl">{weather.current.condition.icon}</div>
            <div>
                <div className="text-3xl dark:text-white"><b> {weather.formatted.temperatureValue}</b> <sup>{weather.formatted.temperatureUnit}</sup></div>
            </div>
        </div>
    );
};

export default SimpleWeatherWidget;