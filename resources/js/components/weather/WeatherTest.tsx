import React from 'react';
import WeatherCard from './WeatherCard';
import ModernWeatherCard from './ModernWeatherCard';
import { useWeatherStore } from '@/stores/weather-store';

const WeatherTest: React.FC = () => {
  const { 
    currentWeather, 
    isLoading, 
    error, 
    selectedLocation,
    savedLocations,
    temperatureUnit,
    fetchCurrentLocationWeather,
    setTemperatureUnit,
    refreshWeatherData
  } = useWeatherStore();

  const handleGetCurrentLocation = () => {
    fetchCurrentLocationWeather();
  };

  const toggleTemperatureUnit = () => {
    setTemperatureUnit(temperatureUnit === 'celsius' ? 'fahrenheit' : 'celsius');
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold">Weather Cards Test Page</h1>
        <p className="text-muted-foreground">
          Testing weather card components with Zustand store integration
        </p>
        
        {/* Debug Info */}
        <div className="bg-muted p-4 rounded-lg text-sm">
          <h3 className="font-semibold mb-2">Debug Information:</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Error:</strong> {error || 'None'}
            </div>
            <div>
              <strong>Has Weather Data:</strong> {currentWeather ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Selected Location:</strong> {selectedLocation?.city || 'None'}
            </div>
            <div>
              <strong>Saved Locations:</strong> {savedLocations.length}
            </div>
            <div>
              <strong>Temperature Unit:</strong> {temperatureUnit}
            </div>
          </div>
        </div>

        {/* Control Panel */}
        <div className="flex gap-4">
          <button
            onClick={handleGetCurrentLocation}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            disabled={isLoading}
          >
            Get Current Location Weather
          </button>
          
          <button
            onClick={toggleTemperatureUnit}
            className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80"
          >
            Toggle to {temperatureUnit === 'celsius' ? 'Fahrenheit' : 'Celsius'}
          </button>
          
          <button
            onClick={refreshWeatherData}
            className="px-4 py-2 bg-accent text-accent-foreground rounded-md hover:bg-accent/80"
            disabled={isLoading}
          >
            Refresh Weather Data
          </button>
        </div>
      </div>

      {/* Modern Weather Card - Default */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Modern Weather Card (Default)</h2>
        <ModernWeatherCard className="max-w-md" />
      </div>

      {/* Modern Weather Card - Compact */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Modern Weather Card (Compact)</h2>
        <ModernWeatherCard className="max-w-sm" compact />
      </div>

      {/* Modern Weather Card - No Search */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Modern Weather Card (No Search)</h2>
        <ModernWeatherCard className="max-w-md" showSearch={false} />
      </div>

      {/* Original Weather Card - Default */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Original Weather Card (Default)</h2>
        <WeatherCard className="max-w-md" />
      </div>

      {/* Original Weather Card - Compact */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Original Weather Card (Compact)</h2>
        <WeatherCard className="max-w-sm" variant="compact" />
      </div>

      {/* Original Weather Card - Detailed */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Original Weather Card (Detailed)</h2>
        <WeatherCard className="max-w-lg" variant="detailed" />
      </div>

      {/* Grid Layout */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Grid Layout - Compact Cards</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <ModernWeatherCard compact showSearch={false} />
          <WeatherCard variant="compact" showSearch={false} />
          <ModernWeatherCard compact showSearch={false} />
        </div>
      </div>

      {/* Side by Side Comparison */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Side by Side Comparison</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Modern Card</h3>
            <ModernWeatherCard />
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Original Card</h3>
            <WeatherCard />
          </div>
        </div>
      </div>

      {/* Error State Test */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Error State (if any)</h2>
        {error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
            <p className="text-destructive">{error}</p>
          </div>
        )}
        {!error && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <p className="text-green-700">No errors detected</p>
          </div>
        )}
      </div>

      {/* Loading State Test */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Loading State</h2>
        {isLoading && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-blue-700">Currently loading weather data...</p>
          </div>
        )}
        {!isLoading && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <p className="text-gray-700">Not loading</p>
          </div>
        )}
      </div>

      {/* Raw Data Display */}
      {currentWeather && (
        <div className="space-y-2">
          <h2 className="text-xl font-semibold">Raw Weather Data</h2>
          <div className="bg-muted p-4 rounded-lg">
            <pre className="text-xs overflow-auto">
              {JSON.stringify(currentWeather, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default WeatherTest;