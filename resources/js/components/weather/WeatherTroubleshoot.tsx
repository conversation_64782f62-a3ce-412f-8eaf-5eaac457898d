import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, XCircle, RefreshCw, MapPin, Wifi, Shield, Database } from 'lucide-react';
import { useWeatherStore } from '@/stores/weather-store';

interface TroubleshootStep {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message?: string;
  action?: () => Promise<void>;
}

const WeatherTroubleshoot: React.FC = () => {
  const [steps, setSteps] = useState<TroubleshootStep[]>([
    { id: 'geolocation', name: 'Geolocation Support', status: 'pending' },
    { id: 'permissions', name: 'Location Permissions', status: 'pending' },
    { id: 'network', name: 'Network Connectivity', status: 'pending' },
    { id: 'api', name: 'Weather API Access', status: 'pending' },
    { id: 'store', name: 'Zustand Store', status: 'pending' },
    { id: 'weather', name: 'Weather Data Fetch', status: 'pending' },
  ]);

  const [isRunning, setIsRunning] = useState(false);
  const weatherStore = useWeatherStore();

  const updateStep = (id: string, status: TroubleshootStep['status'], message?: string) => {
    setSteps(prev => prev.map(step => 
      step.id === id ? { ...step, status, message } : step
    ));
  };

  const checkGeolocation = async () => {
    updateStep('geolocation', 'running');
    try {
      if (!navigator.geolocation) {
        updateStep('geolocation', 'error', 'Geolocation not supported by browser');
        return false;
      }
      updateStep('geolocation', 'success', 'Geolocation API available');
      return true;
    } catch (error) {
      updateStep('geolocation', 'error', `Geolocation check failed: ${error}`);
      return false;
    }
  };

  const checkPermissions = async () => {
    updateStep('permissions', 'running');
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          timeout: 5000,
          enableHighAccuracy: false
        });
      });
      updateStep('permissions', 'success', `Location: ${position.coords.latitude.toFixed(2)}, ${position.coords.longitude.toFixed(2)}`);
      return position;
    } catch (error: any) {
      let message = 'Permission denied or location unavailable';
      if (error.code === 1) message = 'Location permission denied';
      else if (error.code === 2) message = 'Location position unavailable';
      else if (error.code === 3) message = 'Location request timeout';
      updateStep('permissions', 'error', message);
      return null;
    }
  };

  const checkNetwork = async () => {
    updateStep('network', 'running');
    try {
      const response = await fetch('https://api.open-meteo.com/v1/forecast?latitude=0&longitude=0&current=temperature_2m', {
        method: 'HEAD',
        mode: 'cors'
      });
      if (response.ok) {
        updateStep('network', 'success', 'Network connection established');
        return true;
      } else {
        updateStep('network', 'error', `Network error: ${response.status}`);
        return false;
      }
    } catch (error) {
      updateStep('network', 'error', `Network failed: ${error}`);
      return false;
    }
  };

  const checkAPI = async () => {
    updateStep('api', 'running');
    try {
      const response = await fetch('https://api.open-meteo.com/v1/forecast?latitude=52.52&longitude=13.41&current=temperature_2m');
      const data = await response.json();
      
      if (data.current && data.current.temperature_2m !== undefined) {
        updateStep('api', 'success', `API working - Sample temp: ${data.current.temperature_2m}°C`);
        return true;
      } else {
        updateStep('api', 'error', 'API returned invalid data structure');
        return false;
      }
    } catch (error) {
      updateStep('api', 'error', `API request failed: ${error}`);
      return false;
    }
  };

  const checkStore = async () => {
    updateStep('store', 'running');
    try {
      const currentState = weatherStore.currentWeather;
      const hasError = weatherStore.error;
      const isLoading = weatherStore.isLoading;
      
      if (hasError) {
        updateStep('store', 'error', `Store error: ${hasError}`);
        return false;
      } else if (currentState) {
        updateStep('store', 'success', 'Store has weather data');
        return true;
      } else {
        updateStep('store', 'success', `Store ready (loading: ${isLoading})`);
        return true;
      }
    } catch (error) {
      updateStep('store', 'error', `Store check failed: ${error}`);
      return false;
    }
  };

  const checkWeatherFetch = async () => {
    updateStep('weather', 'running');
    try {
      await weatherStore.fetchCurrentLocationWeather();
      
      if (weatherStore.error) {
        updateStep('weather', 'error', weatherStore.error);
        return false;
      } else if (weatherStore.currentWeather) {
        updateStep('weather', 'success', `Weather loaded for ${weatherStore.currentWeather.location.city}`);
        return true;
      } else {
        updateStep('weather', 'error', 'Weather fetch completed but no data received');
        return false;
      }
    } catch (error) {
      updateStep('weather', 'error', `Weather fetch failed: ${error}`);
      return false;
    }
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    
    // Reset all steps
    setSteps(prev => prev.map(step => ({ ...step, status: 'pending' as const, message: undefined })));
    
    try {
      const geoOk = await checkGeolocation();
      if (!geoOk) return;
      
      const position = await checkPermissions();
      if (!position) return;
      
      const networkOk = await checkNetwork();
      if (!networkOk) return;
      
      const apiOk = await checkAPI();
      if (!apiOk) return;
      
      const storeOk = await checkStore();
      if (!storeOk) return;
      
      await checkWeatherFetch();
      
    } catch (error) {
      console.error('Diagnostics failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TroubleshootStep['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'running':
        return <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <div className="w-5 h-5 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStepIcon = (id: string) => {
    switch (id) {
      case 'geolocation':
      case 'permissions':
        return <MapPin className="w-4 h-4" />;
      case 'network':
        return <Wifi className="w-4 h-4" />;
      case 'api':
        return <Database className="w-4 h-4" />;
      case 'store':
      case 'weather':
        return <Shield className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="bg-white border rounded-lg shadow-sm">
        <div className="p-6 border-b">
          <h2 className="text-xl font-semibold mb-2">Weather Widget Troubleshoot</h2>
          <p className="text-gray-600 text-sm">
            This tool will help identify and fix issues with the weather widget
          </p>
        </div>
        
        <div className="p-6">
          <button
            onClick={runDiagnostics}
            disabled={isRunning}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed mb-6"
          >
            {isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'}
          </button>

          <div className="space-y-4">
            {steps.map((step) => (
              <div key={step.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                <div className="flex-shrink-0 mt-0.5">
                  {getStatusIcon(step.status)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    {getStepIcon(step.id)}
                    <span className="font-medium text-sm">{step.name}</span>
                  </div>
                  {step.message && (
                    <p className={`text-xs mt-1 ${
                      step.status === 'error' ? 'text-red-600' : 
                      step.status === 'success' ? 'text-green-600' : 'text-gray-600'
                    }`}>
                      {step.message}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>

          {weatherStore.error && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <h3 className="font-medium text-red-800 mb-2">Current Error</h3>
              <p className="text-red-700 text-sm">{weatherStore.error}</p>
              <button
                onClick={() => weatherStore.clearError()}
                className="mt-2 text-red-600 hover:text-red-800 text-sm underline"
              >
                Clear Error
              </button>
            </div>
          )}

          {weatherStore.currentWeather && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-medium text-green-800 mb-2">Weather Data Available</h3>
              <div className="text-green-700 text-sm space-y-1">
                <div>Location: {weatherStore.currentWeather.location.city}, {weatherStore.currentWeather.location.country}</div>
                <div>Temperature: {weatherStore.currentWeather.current.temperature}°C</div>
                <div>Condition: {weatherStore.currentWeather.current.condition.description}</div>
                <div>Last Updated: {weatherStore.lastUpdated?.toLocaleString() || 'Unknown'}</div>
              </div>
            </div>
          )}

          <div className="mt-6 p-4 bg-gray-50 border rounded-lg">
            <h3 className="font-medium text-gray-800 mb-2">Common Solutions</h3>
            <ul className="text-gray-700 text-sm space-y-1 list-disc list-inside">
              <li>Enable location permissions in your browser</li>
              <li>Check if you're behind a firewall blocking api.open-meteo.com</li>
              <li>Try refreshing the page</li>
              <li>Clear browser cache and cookies</li>
              <li>Disable ad blockers temporarily</li>
              <li>Check browser console for additional errors</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WeatherTroubleshoot;