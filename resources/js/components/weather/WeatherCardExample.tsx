import React from 'react';
import WeatherCard from './WeatherCard';
import ModernWeatherCard from './ModernWeatherCard';

const WeatherCardExample: React.FC = () => {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold">Weather Card Examples</h1>
        <p className="text-muted-foreground">
          Different variants of weather cards using Zustand store and TypeScript
        </p>
      </div>

      {/* Modern Weather Card - Default */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Modern Weather Card (Default)</h2>
        <ModernWeatherCard className="max-w-md" />
      </div>

      {/* Modern Weather Card - Compact */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Modern Weather Card (Compact)</h2>
        <ModernWeatherCard className="max-w-sm" compact />
      </div>

      {/* Modern Weather Card - No Search */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Modern Weather Card (No Search)</h2>
        <ModernWeatherCard className="max-w-md" showSearch={false} />
      </div>

      {/* Original Weather Card - Default */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Original Weather Card (Default)</h2>
        <WeatherCard className="max-w-md" />
      </div>

      {/* Original Weather Card - Compact */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Original Weather Card (Compact)</h2>
        <WeatherCard className="max-w-sm" variant="compact" />
      </div>

      {/* Original Weather Card - Detailed */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Original Weather Card (Detailed)</h2>
        <WeatherCard className="max-w-lg" variant="detailed" />
      </div>

      {/* Grid Layout Example */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Grid Layout</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <ModernWeatherCard compact />
          <ModernWeatherCard compact />
          <ModernWeatherCard compact />
        </div>
      </div>

      {/* Side by Side Comparison */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Side by Side Comparison</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Modern Card</h3>
            <ModernWeatherCard />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">Original Card</h3>
            <WeatherCard />
          </div>
        </div>
      </div>
    </div>
  );
};

export default WeatherCardExample;