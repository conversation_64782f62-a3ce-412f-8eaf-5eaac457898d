import React, { useEffect, useState } from 'react';
import { useWeatherStore } from '@/stores/weather-store';
import { useWeather } from '@/hooks/use-weather';

const WeatherDebug: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [error, setError] = useState<string | null>(null);

  // Test store directly
  const storeData = useWeatherStore();
  
  // Test hook
  let hookData: any = null;
  let hookError: string | null = null;
  
  try {
    hookData = useWeather();
  } catch (err) {
    hookError = err instanceof Error ? err.message : 'Unknown hook error';
  }

  useEffect(() => {
    const runDebug = async () => {
      try {
        // Test API availability
        const apiTest = await fetch('https://api.open-meteo.com/v1/forecast?latitude=52.52&longitude=13.41&current=temperature_2m', {
          method: 'GET',
          headers: { 'Accept': 'application/json' }
        });
        
        const debug = {
          timestamp: new Date().toISOString(),
          apiStatus: apiTest.ok ? 'Available' : 'Unavailable',
          storeState: {
            hasCurrentWeather: !!storeData.currentWeather,
            isLoading: storeData.isLoading,
            error: storeData.error,
            savedLocationsCount: storeData.savedLocations.length,
            searchResultsCount: storeData.searchResults.length,
            temperatureUnit: storeData.temperatureUnit
          },
          hookState: hookError ? { error: hookError } : {
            hasWeather: !!hookData?.weather,
            hasError: !!hookData?.error,
            hasWeatherData: hookData?.hasWeatherData,
            isLoading: hookData?.isLoading
          },
          geolocationSupport: 'geolocation' in navigator,
          userAgent: navigator.userAgent,
          localStorage: typeof localStorage !== 'undefined'
        };
        
        setDebugInfo(debug);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Debug failed');
      }
    };

    runDebug();
  }, [storeData, hookData, hookError]);

  const testCurrentLocation = async () => {
    try {
      await storeData.fetchCurrentLocationWeather();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Location test failed');
    }
  };

  const testSearch = async () => {
    try {
      await storeData.searchLocations('London');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search test failed');
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Weather Component Debug</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error: </strong>{error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <button
          onClick={testCurrentLocation}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          disabled={storeData.isLoading}
        >
          Test Current Location
        </button>
        
        <button
          onClick={testSearch}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          disabled={storeData.isLoading}
        >
          Test Search
        </button>
      </div>

      <div className="bg-gray-100 p-4 rounded mb-4">
        <h2 className="text-lg font-semibold mb-2">Debug Information</h2>
        <pre className="text-xs overflow-auto whitespace-pre-wrap">
          {JSON.stringify(debugInfo, null, 2)}
        </pre>
      </div>

      {hookError && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          <strong>Hook Error: </strong>{hookError}
        </div>
      )}

      {storeData.currentWeather && (
        <div className="bg-green-100 p-4 rounded mb-4">
          <h2 className="text-lg font-semibold mb-2">Current Weather Data</h2>
          <pre className="text-xs overflow-auto whitespace-pre-wrap">
            {JSON.stringify(storeData.currentWeather, null, 2)}
          </pre>
        </div>
      )}

      {hookData?.weather && (
        <div className="bg-blue-100 p-4 rounded mb-4">
          <h2 className="text-lg font-semibold mb-2">Hook Weather Data</h2>
          <pre className="text-xs overflow-auto whitespace-pre-wrap">
            {JSON.stringify(hookData.weather, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-6">
        <h2 className="text-lg font-semibold mb-2">Store Actions Test</h2>
        <div className="space-y-2">
          <div>Temperature Unit: {storeData.temperatureUnit}</div>
          <button
            onClick={() => storeData.setTemperatureUnit(storeData.temperatureUnit === 'celsius' ? 'fahrenheit' : 'celsius')}
            className="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600"
          >
            Toggle Unit
          </button>
        </div>
      </div>
    </div>
  );
};

export default WeatherDebug;