import React, { useEffect } from 'react';
import { Cloud, Sun, CloudRain, RefreshCw, MapPin } from 'lucide-react';
import { useWeatherStore } from '@/stores/weather-store';
import { cn } from '@/lib/utils';

interface SimpleWeatherCardProps {
  className?: string;
}

const SimpleWeatherCard: React.FC<SimpleWeatherCardProps> = ({ className }) => {
  const {
    currentWeather,
    isLoading,
    error,
    temperatureUnit,
    fetchCurrentLocationWeather,
    refreshWeatherData,
    clearError
  } = useWeatherStore();

  useEffect(() => {
    if (!currentWeather && !error && !isLoading) {
      fetchCurrentLocationWeather().catch((err) => {
        console.error('Failed to fetch weather:', err);
      });
    }
  }, [currentWeather, error, isLoading]);

  const formatTemperature = (temp: number) => {
    if (temperatureUnit === 'fahrenheit') {
      return `${Math.round((temp * 9/5) + 32)}°F`;
    }
    return `${Math.round(temp)}°C`;
  };

  const getWeatherIcon = (condition: any) => {
    if (!condition) return <Sun className="w-8 h-8" />;
    
    if (condition.icon?.includes('sun') || condition.description?.toLowerCase().includes('clear')) {
      return <Sun className="w-8 h-8 text-yellow-500" />;
    }
    if (condition.icon?.includes('cloud') || condition.description?.toLowerCase().includes('cloud')) {
      return <Cloud className="w-8 h-8 text-gray-500" />;
    }
    if (condition.icon?.includes('rain') || condition.description?.toLowerCase().includes('rain')) {
      return <CloudRain className="w-8 h-8 text-blue-500" />;
    }
    return <Sun className="w-8 h-8 text-yellow-500" />;
  };

  if (error) {
    return (
      <div className={cn("bg-red-50 border border-red-200 rounded-lg p-4", className)}>
        <div className="flex items-center justify-between mb-2">
          <span className="text-red-600 text-sm">Error: {error}</span>
          <button onClick={clearError} className="text-red-600 hover:text-red-800">×</button>
        </div>
        <button 
          onClick={fetchCurrentLocationWeather}
          className="text-red-600 hover:text-red-800 text-sm underline"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={cn("bg-white border rounded-lg p-4", className)}>
        <div className="animate-pulse flex items-center space-x-4">
          <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
          <div>
            <div className="h-4 bg-gray-300 rounded w-20 mb-2"></div>
            <div className="h-3 bg-gray-300 rounded w-16"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!currentWeather) {
    return (
      <div className={cn("bg-gray-50 border rounded-lg p-4 text-center", className)}>
        <div className="text-gray-500 mb-2">No weather data</div>
        <button 
          onClick={fetchCurrentLocationWeather}
          className="text-blue-600 hover:text-blue-800 text-sm underline"
        >
          Get Weather
        </button>
      </div>
    );
  }

  return (
    <div className={cn("bg-white border rounded-lg shadow-sm p-4", className)}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <MapPin className="w-4 h-4 text-gray-500" />
          <span className="font-medium">{currentWeather.location.city}</span>
        </div>
        <button 
          onClick={refreshWeatherData}
          disabled={isLoading}
          className="text-gray-400 hover:text-gray-600 disabled:animate-spin"
        >
          <RefreshCw className="w-4 h-4" />
        </button>
      </div>

      <div className="flex items-center space-x-4">
        {getWeatherIcon(currentWeather.current.condition)}
        <div>
          <div className="text-2xl font-bold">
            {formatTemperature(currentWeather.current.temperature)}
          </div>
          <div className="text-gray-600 capitalize">
            {currentWeather.current.condition.description}
          </div>
          <div className="text-sm text-gray-500">
            Feels like {formatTemperature(currentWeather.current.feelsLike)}
          </div>
        </div>
      </div>

      <div className="mt-4 grid grid-cols-2 gap-2 text-sm text-gray-600">
        <div>Humidity: {currentWeather.current.humidity}%</div>
        <div>Wind: {Math.round(currentWeather.current.windSpeed)} km/h</div>
        <div>Pressure: {Math.round(currentWeather.current.pressure)} hPa</div>
        <div>UV Index: {currentWeather.current.uvIndex}</div>
      </div>
    </div>
  );
};

export default SimpleWeatherCard;