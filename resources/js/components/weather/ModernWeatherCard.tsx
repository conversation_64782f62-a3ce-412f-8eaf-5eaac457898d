import React, { useEffect, useState } from 'react';
import { Cloud, Sun, CloudRain, Wind, Droplets, Eye, Gauge, RefreshCw, MapPin, Star, Search, Thermometer, Clock, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useWeatherStore, useWeatherData, useWeatherLoading, useWeatherError } from '@/stores/weather-store';
import { WeatherLocation } from '@/types/weather';
import LocationSelector from './LocationSelector';
import LocationPermissionDialog from './LocationPermissionDialog';
import { cn } from '@/lib/utils';

interface ModernWeatherCardProps {
  className?: string;
  showSearch?: boolean;
  compact?: boolean;
}

const ModernWeatherCard: React.FC<ModernWeatherCardProps> = ({
  className,
  showSearch = true,
  compact = false
}) => {
  const {
    selectedLocation,
    savedLocations,
    searchResults,
    isSearching,
    temperatureUnit,
    lastUpdated,
    showLocationDialog,
    fetchCurrentLocationWeather,
    fetchWeatherForLocation,
    searchLocations,
    addSavedLocation,
    removeSavedLocation,
    refreshWeatherData,
    clearError,
    checkLocationPermission,
    setShowLocationDialog,
    handleLocationPermissionGranted,
    handleLocationPermissionDenied
  } = useWeatherStore();

  const weatherData = useWeatherData();
  const isLoading = useWeatherLoading();
  const error = useWeatherError();

  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [showLocationSelector, setShowLocationSelector] = useState(false);

  useEffect(() => {
    // Check location permission status on mount
    checkLocationPermission();
  }, [checkLocationPermission]);

  useEffect(() => {
    if (!weatherData && !error && !isLoading && !showLocationDialog) {
      fetchCurrentLocationWeather().catch((err) => {
        console.error('Failed to fetch weather:', err);
      });
    }
  }, [weatherData, error, isLoading, showLocationDialog]);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (query.length > 2) {
      try {
        await searchLocations(query);
        setShowSearchResults(true);
      } catch (err) {
        console.error('Search failed:', err);
        setShowSearchResults(false);
      }
    } else {
      setShowSearchResults(false);
    }
  };

  const handleLocationSelect = async (location: WeatherLocation) => {
    try {
      await fetchWeatherForLocation(location);
      setSearchQuery('');
      setShowSearchResults(false);
    } catch (err) {
      console.error('Failed to select location:', err);
      setSearchQuery('');
      setShowSearchResults(false);
    }
  };

  const isLocationSaved = (location: any) => {
    return savedLocations.some(
      saved => Math.abs(saved.latitude - location.latitude) < 0.001 && 
               Math.abs(saved.longitude - location.longitude) < 0.001
    );
  };

  const toggleSavedLocation = (location: any) => {
    if (isLocationSaved(location)) {
      removeSavedLocation(location.latitude, location.longitude);
    } else {
      addSavedLocation(location);
    }
  };

  const formatTemperature = (temp: number) => {
    if (temperatureUnit === 'fahrenheit') {
      return `${Math.round((temp * 9/5) + 32)}°F`;
    }
    return `${Math.round(temp)}°C`;
  };

  const getWeatherIcon = (condition: any, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: "w-5 h-5",
      md: "w-8 h-8",
      lg: "w-16 h-16"
    };
    
    const iconClass = sizeClasses[size];
    
    if (condition.icon.includes('sun') || condition.icon.includes('clear')) {
      return <Sun className={cn(iconClass, "text-yellow-500")} />;
    }
    if (condition.icon.includes('cloud')) {
      return <Cloud className={cn(iconClass, "text-gray-500")} />;
    }
    if (condition.icon.includes('rain')) {
      return <CloudRain className={cn(iconClass, "text-blue-500")} />;
    }
    return <Sun className={cn(iconClass, "text-yellow-500")} />;
  };

  if (error) {
    const isLocationError = error.includes('Location') || error.includes('permission') || error.includes('timeout') || error.includes('denied') || error.includes('unavailable');
    
    if (isLocationError && showLocationSelector) {
      return (
        <Card className={className}>
          <CardContent className="p-4">
            <LocationSelector 
              onLocationSelect={() => setShowLocationSelector(false)}
              showTitle={true}
            />
            <button 
              onClick={() => setShowLocationSelector(false)}
              className="mt-2 text-sm text-gray-500 hover:text-gray-700 underline"
            >
              Back to error message
            </button>
          </CardContent>
        </Card>
      );
    }
    
    return (
      <Card className={cn("border-destructive/50", className)}>
        <CardContent className="p-4">
          <div className="flex items-start space-x-2">
            <AlertCircle className="w-4 h-4 text-destructive mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-destructive text-sm font-medium mb-1">Weather Error</div>
              <div className="text-destructive text-xs mb-3">{error}</div>
              <div className="flex gap-2 flex-wrap">
                <button 
                  onClick={() => {
                    clearError();
                    fetchCurrentLocationWeather();
                  }}
                  className="px-3 py-1 text-xs bg-destructive text-destructive-foreground rounded hover:bg-destructive/90"
                >
                  Retry
                </button>
                {isLocationError && (
                  <button 
                    onClick={() => setShowLocationSelector(true)}
                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Choose Location
                  </button>
                )}
                <button 
                  onClick={clearError}
                  className="px-3 py-1 text-xs border border-destructive text-destructive rounded hover:bg-destructive/10"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading && !weatherData) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-muted rounded-full"></div>
              <div className="flex-1">
                <div className="h-8 bg-muted rounded w-24 mb-2"></div>
                <div className="h-4 bg-muted rounded w-32 mb-2"></div>
                <div className="h-3 bg-muted rounded w-20"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!weatherData) {
    if (showLocationSelector) {
      return (
        <Card className={className}>
          <CardContent className="p-4">
            <LocationSelector 
              onLocationSelect={() => setShowLocationSelector(false)}
              showTitle={true}
            />
          </CardContent>
        </Card>
      );
    }
    
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <div className="text-muted-foreground mb-4">
            {isLoading ? 'Initializing weather...' : 'No weather data available'}
          </div>
          <div className="flex gap-2 justify-center">
            <button 
              onClick={() => {
                clearError();
                fetchCurrentLocationWeather();
              }}
              disabled={isLoading}
              className="px-4 py-2 bg-primary text-primary-foreground text-sm rounded-md hover:bg-primary/90 disabled:opacity-50"
            >
              {isLoading ? 'Loading...' : 'Get Current Weather'}
            </button>
            <button 
              onClick={() => setShowLocationSelector(true)}
              className="px-4 py-2 bg-secondary text-secondary-foreground text-sm rounded-md hover:bg-secondary/80"
            >
              Choose Location
            </button>
          </div>
          <div className="mt-2 text-xs text-muted-foreground">
            Location permission will be requested, or you can choose manually
          </div>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getWeatherIcon(weatherData.current.condition, 'md')}
              <div>
                <div className="text-2xl font-bold">{formatTemperature(weatherData.current.temperature)}</div>
                <div className="text-sm text-muted-foreground">{weatherData.location.city}</div>
              </div>
            </div>
            <button 
              onClick={refreshWeatherData}
              disabled={isLoading}
              className="text-muted-foreground hover:text-foreground disabled:animate-spin transition-colors"
            >
              <RefreshCw className="w-5 h-5" />
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <LocationPermissionDialog
        isOpen={showLocationDialog}
        onClose={() => setShowLocationDialog(false)}
        onLocationGranted={handleLocationPermissionGranted}
        onLocationDenied={handleLocationPermissionDenied}
      />
      
      <Card className={className}>
        <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MapPin className="w-4 h-4 text-muted-foreground" />
            <CardTitle className="text-lg">{weatherData.location.city}</CardTitle>
            <button
              onClick={() => toggleSavedLocation(weatherData.location)}
              className={cn(
                "transition-colors",
                isLocationSaved(weatherData.location) ? "text-yellow-500" : "text-muted-foreground hover:text-yellow-500"
              )}
            >
              <Star className="w-4 h-4" fill={isLocationSaved(weatherData.location) ? 'currentColor' : 'none'} />
            </button>
          </div>
          <button 
            onClick={() => {
              clearError();
              refreshWeatherData();
            }}
            disabled={isLoading}
            className="text-muted-foreground hover:text-foreground disabled:animate-spin transition-colors"
            title="Refresh weather data"
          >
            <RefreshCw className="w-5 h-5" />
          </button>
        </div>
        <CardDescription>{weatherData.location.country}</CardDescription>

        {showSearch && (
          <div className="mt-4 relative">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                placeholder="Search for a city..."
                className="w-full pl-10 pr-4 py-2 bg-background border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all"
              />
            </div>

            {showSearchResults && (searchResults.length > 0 || isSearching) && (
              <div className="absolute z-10 w-full mt-1 bg-popover border rounded-md shadow-lg max-h-48 overflow-y-auto">
                {isSearching ? (
                  <div className="p-3 text-sm text-muted-foreground">Searching...</div>
                ) : (
                  searchResults.map((location, index) => (
                    <button
                      key={index}
                      onClick={() => handleLocationSelect(location)}
                      className="w-full text-left px-3 py-2 hover:bg-accent transition-colors border-b border-border last:border-b-0"
                    >
                      <div className="font-medium text-sm">{location.city}</div>
                      <div className="text-xs text-muted-foreground">{location.country}</div>
                    </button>
                  ))
                )}
              </div>
            )}
          </div>
        )}
      </CardHeader>

      <CardContent className="pb-4">
        {/* Main Weather Display */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            {getWeatherIcon(weatherData.current.condition, 'lg')}
            <div>
              <div className="text-4xl font-bold">{formatTemperature(weatherData.current.temperature)}</div>
              <div className="text-muted-foreground capitalize">{weatherData.current.condition.description}</div>
              <div className="text-sm text-muted-foreground flex items-center mt-1">
                <Thermometer className="w-3 h-3 mr-1" />
                Feels like {formatTemperature(weatherData.current.feelsLike)}
              </div>
            </div>
          </div>
        </div>

        {/* Weather Stats Grid */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center space-x-2 p-3 bg-muted/30 rounded-lg">
            <Droplets className="w-4 h-4 text-blue-500" />
            <div>
              <div className="text-muted-foreground text-xs">Humidity</div>
              <div className="font-medium">{weatherData.current.humidity}%</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 p-3 bg-muted/30 rounded-lg">
            <Wind className="w-4 h-4 text-gray-500" />
            <div>
              <div className="text-muted-foreground text-xs">Wind</div>
              <div className="font-medium">{Math.round(weatherData.current.windSpeed)} km/h</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 p-3 bg-muted/30 rounded-lg">
            <Gauge className="w-4 h-4 text-purple-500" />
            <div>
              <div className="text-muted-foreground text-xs">Pressure</div>
              <div className="font-medium">{Math.round(weatherData.current.pressure)} hPa</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 p-3 bg-muted/30 rounded-lg">
            <Eye className="w-4 h-4 text-green-500" />
            <div>
              <div className="text-muted-foreground text-xs">UV Index</div>
              <div className="font-medium">{weatherData.current.uvIndex}</div>
            </div>
          </div>
        </div>

        {/* Hourly Forecast */}
        {weatherData.hourlyForecast && weatherData.hourlyForecast.length > 0 && (
          <div className="mt-6">
            <h4 className="font-medium mb-3 flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              Hourly Forecast
            </h4>
            <div className="flex space-x-4 overflow-x-auto pb-2">
              {weatherData.hourlyForecast.slice(0, 6).map((hour, index) => (
                <div key={index} className="flex-shrink-0 text-center p-2 bg-muted/20 rounded-lg min-w-[70px]">
                  <div className="text-xs text-muted-foreground mb-2">
                    {hour.time.toLocaleTimeString('en-US', { hour: 'numeric' })}
                  </div>
                  <div className="flex justify-center mb-2">
                    {getWeatherIcon(hour.condition, 'sm')}
                  </div>
                  <div className="text-sm font-medium">{formatTemperature(hour.temperature)}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="flex-col items-start space-y-2">
        {/* Saved Locations */}
        {savedLocations.length > 0 && (
          <div className="w-full">
            <div className="text-sm font-medium mb-2">Quick Access</div>
            <div className="flex flex-wrap gap-2">
              {savedLocations.slice(0, 3).map((location, index) => (
                <button
                  key={index}
                  onClick={() => fetchWeatherForLocation(location)}
                  className="px-3 py-1 text-xs bg-secondary hover:bg-secondary/80 rounded-full transition-colors"
                >
                  {location.city}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Last Updated */}
        {lastUpdated && (
          <div className="text-xs text-muted-foreground w-full">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </div>
        )}
        
        {/* Debug info in development */}
        {process.env.NODE_ENV === 'development' && (
          <details className="text-xs text-muted-foreground w-full mt-2">
            <summary className="cursor-pointer">Debug Info</summary>
            <div className="mt-1 p-2 bg-muted/20 rounded text-xs">
              <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
              <div>Error: {error || 'None'}</div>
              <div>Saved Locations: {savedLocations.length}</div>
              <div>Temperature Unit: {temperatureUnit}</div>
            </div>
          </details>
        )}
      </CardFooter>
    </Card>
    </>
  );
};

export default ModernWeatherCard;