import React from 'react';
import ErrorBoundary from '@/components/ui/error-boundary';
import ModernWeatherCard from './ModernWeatherCard';
import { AlertCircle } from 'lucide-react';

interface SafeWeatherCardProps {
  className?: string;
  showSearch?: boolean;
  compact?: boolean;
  fallbackComponent?: React.ReactNode;
}

const WeatherFallback: React.FC = () => (
  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-center space-x-2">
    <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0" />
    <div>
      <div className="text-yellow-800 font-medium text-sm">Weather Unavailable</div>
      <div className="text-yellow-700 text-xs">
        Weather service is temporarily unavailable. Please try again later.
      </div>
    </div>
  </div>
);

const SafeWeatherCard: React.FC<SafeWeatherCardProps> = ({
  className,
  showSearch = true,
  compact = false,
  fallbackComponent
}) => {
  return (
    <ErrorBoundary fallback={fallbackComponent || <WeatherFallback />}>
      <ModernWeatherCard 
        className={className}
        showSearch={showSearch}
        compact={compact}
      />
    </ErrorBoundary>
  );
};

export default SafeWeatherCard;