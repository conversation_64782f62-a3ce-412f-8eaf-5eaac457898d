import React, { useEffect, useState } from 'react';
import { MapPin, RefreshCw, Settings, Trash2, <PERSON>, EyeOff } from 'lucide-react';
import { useWeatherStore } from '@/stores/weather-store';
import LocationStorage from '@/lib/location-storage';
import ModernWeatherCard from './ModernWeatherCard';
import LocationPermissionDialog from './LocationPermissionDialog';

const LocationPermissionDemo: React.FC = () => {
  const [locationStatus, setLocationStatus] = useState(LocationStorage.getLocationStatus());
  const [showDialog, setShowDialog] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const {
    currentWeather,
    savedCityName,
    locationPermissionStatus,
    showLocationDialog,
    setShowLocationDialog,
    checkLocationPermission,
    clearError,
    fetchCurrentLocationWeather,
    handleLocationPermissionGranted,
    handleLocationPermissionDenied
  } = useWeatherStore();

  useEffect(() => {
    // Update location status when component mounts
    setLocationStatus(LocationStorage.getLocationStatus());
  }, [savedCityName, locationPermissionStatus]);

  const handleReset = () => {
    LocationStorage.clearAllLocationData();
    setLocationStatus(LocationStorage.getLocationStatus());
    clearError();
    checkLocationPermission();
  };

  const handleShowPermissionDialog = () => {
    setShowLocationDialog(true);
  };

  const handleRefreshStatus = () => {
    setLocationStatus(LocationStorage.getLocationStatus());
    checkLocationPermission();
  };

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case 'granted': return 'text-green-600 bg-green-50 border-green-200';
      case 'denied': return 'text-red-600 bg-red-50 border-red-200';
      case 'manual': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Location Permission Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Test the location permission dialog and weather location management system
        </p>
      </div>

      {/* Status Panel */}
      <div className="bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            Location Status
          </h2>
          <div className="flex gap-2">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
              title={showDetails ? 'Hide details' : 'Show details'}
            >
              {showDetails ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
            <button
              onClick={handleRefreshStatus}
              className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
              title="Refresh status"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Permission Status
            </label>
            <div className={`px-3 py-2 rounded-md border text-sm font-medium ${getStatusColor(locationStatus.preferenceType)}`}>
              {locationPermissionStatus || locationStatus.preferenceType || 'None'}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Saved City
            </label>
            <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 border dark:border-gray-600 rounded-md text-sm">
              {savedCityName || locationStatus.city || 'None'}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Has Permission
            </label>
            <div className={`px-3 py-2 rounded-md border text-sm font-medium ${
              locationStatus.hasPermission ? 'text-green-600 bg-green-50 border-green-200' : 'text-red-600 bg-red-50 border-red-200'
            }`}>
              {locationStatus.hasPermission ? 'Yes' : 'No'}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Is Expired
            </label>
            <div className={`px-3 py-2 rounded-md border text-sm font-medium ${
              locationStatus.isExpired ? 'text-yellow-600 bg-yellow-50 border-yellow-200' : 'text-green-600 bg-green-50 border-green-200'
            }`}>
              {locationStatus.isExpired ? 'Yes' : 'No'}
            </div>
          </div>
        </div>

        {showDetails && (
          <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Storage Details</h3>
            <pre className="text-xs text-gray-600 dark:text-gray-400 overflow-x-auto">
              {JSON.stringify({
                preference: LocationStorage.getLocationPreference(),
                savedLocations: LocationStorage.getSavedLocations(),
                shouldShowDialog: LocationStorage.shouldShowLocationDialog(),
                defaultCity: LocationStorage.getDefaultCity()
              }, null, 2)}
            </pre>
          </div>
        )}
      </div>

      {/* Control Panel */}
      <div className="bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <MapPin className="w-5 h-5 mr-2" />
          Actions
        </h2>

        <div className="flex flex-wrap gap-3">
          <button
            onClick={handleShowPermissionDialog}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Show Permission Dialog
          </button>

          <button
            onClick={() => {
              clearError();
              fetchCurrentLocationWeather();
            }}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Fetch Current Location
          </button>

          <button
            onClick={handleReset}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Reset All Data
          </button>

          <button
            onClick={() => checkLocationPermission()}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            Check Permission
          </button>
        </div>

        <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
          <p className="text-sm text-yellow-800 dark:text-yellow-200">
            <strong>Testing Tips:</strong>
          </p>
          <ul className="text-sm text-yellow-700 dark:text-yellow-300 mt-1 space-y-1">
            <li>• Use "Reset All Data" to clear localStorage and test fresh user experience</li>
            <li>• Try different browser permission settings (allow, deny, ask)</li>
            <li>• Check how the system handles expired preferences (24hr cache)</li>
            <li>• Test manual city selection when location access fails</li>
          </ul>
        </div>
      </div>

      {/* Weather Card Demo */}
      <div className="bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Weather Card with Permission System
        </h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Full Card</h3>
            <ModernWeatherCard />
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Compact Card</h3>
            <ModernWeatherCard compact showSearch={false} />
          </div>
        </div>
      </div>

      {/* Current Weather Info */}
      {currentWeather && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border dark:border-gray-700 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Current Weather Data
          </h2>
          <div className="text-sm text-gray-600 dark:text-gray-300">
            <div><strong>Location:</strong> {currentWeather.location.city}, {currentWeather.location.country}</div>
            <div><strong>Temperature:</strong> {currentWeather.current.temperature}°C</div>
            <div><strong>Condition:</strong> {currentWeather.current.condition.description}</div>
            <div><strong>Coordinates:</strong> {currentWeather.location.latitude.toFixed(4)}, {currentWeather.location.longitude.toFixed(4)}</div>
          </div>
        </div>
      )}

      {/* Location Permission Dialog */}
      <LocationPermissionDialog
        isOpen={showLocationDialog}
        onClose={() => setShowLocationDialog(false)}
        onLocationGranted={handleLocationPermissionGranted}
        onLocationDenied={handleLocationPermissionDenied}
      />
    </div>
  );
};

export default LocationPermissionDemo;