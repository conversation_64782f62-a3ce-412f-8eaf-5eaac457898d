import React, { useEffect, useState } from 'react';
import { Cloud, Sun, CloudRain, Wind, Droplets, Eye, Gauge, RefreshCw, MapPin, Star, Search, Settings, Thermometer } from 'lucide-react';
import { useWeatherStore, useWeatherData, useWeatherLoading, useWeatherError } from '@/stores/weather-store';
import { WeatherLocation } from '@/types/weather';
import { cn } from '@/lib/utils';

interface WeatherCardProps {
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
  showSearch?: boolean;
  showSettings?: boolean;
}

const WeatherCard: React.FC<WeatherCardProps> = ({
  className,
  variant = 'default',
  showSearch = true,
  showSettings = true
}) => {
  const {
    selectedLocation,
    savedLocations,
    searchResults,
    isSearching,
    temperatureUnit,
    showHourlyForecast,
    showDailyForecast,
    lastUpdated,
    fetchCurrentLocationWeather,
    fetchWeatherForLocation,
    searchLocations,
    addSavedLocation,
    removeSavedLocation,
    refreshWeatherData,
    clearError,
    setShowHourlyForecast,
    setShowDailyForecast
  } = useWeatherStore();

  const weatherData = useWeatherData();
  const isLoading = useWeatherLoading();
  const error = useWeatherError();

  const [searchQuery, setSearchQuery] = React.useState('');
  const [showSearchResults, setShowSearchResults] = React.useState(false);

  useEffect(() => {
    if (!weatherData && !error && !isLoading) {
      fetchCurrentLocationWeather().catch((err) => {
        console.error('Failed to fetch weather:', err);
      });
    }
  }, [weatherData, error, isLoading]);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (query.length > 2) {
      await searchLocations(query);
      setShowSearchResults(true);
    } else {
      setShowSearchResults(false);
    }
  };

  const handleLocationSelect = async (location: WeatherLocation) => {
    await fetchWeatherForLocation(location);
    setSearchQuery('');
    setShowSearchResults(false);
  };

  const isLocationSaved = (location: any) => {
    return savedLocations.some(
      saved => Math.abs(saved.latitude - location.latitude) < 0.001 && 
               Math.abs(saved.longitude - location.longitude) < 0.001
    );
  };

  const toggleSavedLocation = (location: any) => {
    if (isLocationSaved(location)) {
      removeSavedLocation(location.latitude, location.longitude);
    } else {
      addSavedLocation(location);
    }
  };

  const formatTemperature = (temp: number) => {
    if (temperatureUnit === 'fahrenheit') {
      return `${Math.round((temp * 9/5) + 32)}°F`;
    }
    return `${Math.round(temp)}°C`;
  };

  const getWeatherIcon = (condition: any) => {
    const iconClass = "w-6 h-6";
    
    if (condition.icon.includes('sun') || condition.icon.includes('clear')) {
      return <Sun className={iconClass} />;
    }
    if (condition.icon.includes('cloud')) {
      return <Cloud className={iconClass} />;
    }
    if (condition.icon.includes('rain')) {
      return <CloudRain className={iconClass} />;
    }
    return <Sun className={iconClass} />;
  };

  if (error) {
    return (
      <div className={cn(
        "bg-card border border-destructive/20 rounded-lg p-4",
        className
      )}>
        <div className="flex items-center justify-between">
          <div className="text-destructive text-sm">{error}</div>
          <button 
            onClick={clearError}
            className="text-destructive hover:text-destructive/80"
          >
            ×
          </button>
        </div>
        <button 
          onClick={fetchCurrentLocationWeather}
          className="mt-2 text-sm text-destructive hover:text-destructive/80 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  if (isLoading && !weatherData) {
    return (
      <div className={cn(
        "bg-card border rounded-lg p-4",
        className
      )}>
        <div className="animate-pulse">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-muted rounded-full"></div>
            <div className="flex-1">
              <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!weatherData) {
    return (
      <div className={cn(
        "bg-card border rounded-lg p-4",
        className
      )}>
        <div className="text-center">
          <div className="text-muted-foreground mb-2">No weather data available</div>
          <button 
            onClick={fetchCurrentLocationWeather}
            className="text-primary hover:text-primary/80 text-sm underline"
          >
            Get weather for current location
          </button>
        </div>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn(
        "bg-card border rounded-lg p-3",
        className
      )}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{weatherData.current.condition.icon}</div>
            <div>
              <div className="font-semibold">{formatTemperature(weatherData.current.temperature)}</div>
              <div className="text-xs text-muted-foreground">{weatherData.location.city}</div>
            </div>
          </div>
          <button 
            onClick={refreshWeatherData}
            disabled={isLoading}
            className="text-muted-foreground hover:text-foreground disabled:animate-spin"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "bg-card border rounded-lg shadow-sm",
      className
    )}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MapPin className="w-4 h-4 text-muted-foreground" />
            <span className="font-medium">{weatherData.location.city}, {weatherData.location.country}</span>
            <button
              onClick={() => toggleSavedLocation(weatherData.location)}
              className={cn(
                "text-sm hover:text-yellow-600 transition-colors",
                isLocationSaved(weatherData.location) ? "text-yellow-500" : "text-muted-foreground"
              )}
            >
              <Star className="w-4 h-4" fill={isLocationSaved(weatherData.location) ? 'currentColor' : 'none'} />
            </button>
          </div>
          <div className="flex items-center space-x-2">
            {showSettings && (
              <button className="text-muted-foreground hover:text-foreground">
                <Settings className="w-4 h-4" />
              </button>
            )}
            <button 
              onClick={refreshWeatherData}
              disabled={isLoading}
              className="text-muted-foreground hover:text-foreground disabled:animate-spin"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          </div>
        </div>

        {showSearch && (
          <div className="mt-3 relative">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                placeholder="Search for a city..."
                className="w-full pl-10 pr-4 py-2 bg-background border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              />
            </div>

            {showSearchResults && (searchResults.length > 0 || isSearching) && (
              <div className="absolute z-10 w-full mt-1 bg-popover border rounded-md shadow-lg max-h-60 overflow-y-auto">
                {isSearching ? (
                  <div className="p-3 text-sm text-muted-foreground">Searching...</div>
                ) : (
                  searchResults.map((location, index) => (
                    <button
                      key={index}
                      onClick={() => handleLocationSelect(location)}
                      className="w-full text-left px-3 py-2 hover:bg-accent border-b border-border last:border-b-0"
                    >
                      <div className="font-medium text-sm">{location.city}</div>
                      <div className="text-xs text-muted-foreground">{location.country}</div>
                    </button>
                  ))
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Current Weather */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <div className="text-4xl">{weatherData.current.condition.icon}</div>
            <div>
              <div className="text-3xl font-bold">{formatTemperature(weatherData.current.temperature)}</div>
              <div className="text-muted-foreground">{weatherData.current.condition.description}</div>
              <div className="text-sm text-muted-foreground">Feels like {formatTemperature(weatherData.current.feelsLike)}</div>
            </div>
          </div>
        </div>

        {/* Weather Details */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <Droplets className="w-4 h-4 text-blue-500" />
            <span>Humidity: {weatherData.current.humidity}%</span>
          </div>
          <div className="flex items-center space-x-2">
            <Wind className="w-4 h-4 text-muted-foreground" />
            <span>Wind: {Math.round(weatherData.current.windSpeed)} km/h {weatherData.current.windDirection}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Gauge className="w-4 h-4 text-purple-500" />
            <span>Pressure: {Math.round(weatherData.current.pressure)} hPa</span>
          </div>
          <div className="flex items-center space-x-2">
            <Eye className="w-4 h-4 text-green-500" />
            <span>UV Index: {weatherData.current.uvIndex}</span>
          </div>
        </div>

        {/* Hourly Forecast */}
        {showHourlyForecast && weatherData.hourlyForecast && weatherData.hourlyForecast.length > 0 && (
          <div className="mt-6">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium">Hourly Forecast</h4>
              <button
                onClick={() => setShowHourlyForecast(false)}
                className="text-xs text-muted-foreground hover:text-foreground"
              >
                Hide
              </button>
            </div>
            <div className="flex space-x-4 overflow-x-auto pb-2">
              {weatherData.hourlyForecast.slice(0, 8).map((hour, index) => (
                <div key={index} className="flex-shrink-0 text-center">
                  <div className="text-xs text-muted-foreground mb-1">
                    {hour.time.toLocaleTimeString('en-US', { hour: 'numeric' })}
                  </div>
                  <div className="text-lg mb-1">{hour.condition.icon}</div>
                  <div className="text-sm font-medium">{formatTemperature(hour.temperature)}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Daily Forecast */}
        {showDailyForecast && weatherData.dailyForecast && weatherData.dailyForecast.length > 0 && (
          <div className="mt-6">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium">7-Day Forecast</h4>
              <button
                onClick={() => setShowDailyForecast(false)}
                className="text-xs text-muted-foreground hover:text-foreground"
              >
                Hide
              </button>
            </div>
            <div className="space-y-2">
              {weatherData.dailyForecast.slice(0, 5).map((day, index) => (
                <div key={index} className="flex items-center justify-between py-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{day.condition.icon}</span>
                    <span className="text-sm font-medium w-16">
                      {day.date.toLocaleDateString('en-US', { weekday: 'short' })}
                    </span>
                    <span className="text-sm text-muted-foreground">{day.condition.description}</span>
                  </div>
                  <span className="text-sm font-medium">
                    {formatTemperature(day.temperatureMax)} / {formatTemperature(day.temperatureMin)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Last Updated */}
        {lastUpdated && (
          <div className="mt-4 pt-4 border-t">
            <div className="text-xs text-muted-foreground">
              Last updated: {lastUpdated.toLocaleString()}
            </div>
          </div>
        )}
      </div>

      {/* Saved Locations */}
      {savedLocations.length > 0 && (
        <div className="p-4 border-t">
          <h4 className="font-medium mb-3">Saved Locations</h4>
          <div className="flex flex-wrap gap-2">
            {savedLocations.map((location, index) => (
              <button
                key={index}
                onClick={() => fetchWeatherForLocation(location)}
                className="px-3 py-1 text-sm bg-secondary hover:bg-secondary/80 rounded-full transition-colors"
              >
                {location.city}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WeatherCard;