import { Link } from "@inertiajs/react";

export function HomeLocationTabs() {
  interface LocationTabProps {
    title: string;
    url: string;
  }

  const tabs: LocationTabProps[] = [
    { title: "Jakarta", url: "/jakarta" },
    { title: "Bandung", url: "/bandung" },
    { title: "Surabaya", url: "/surabaya" },
    { title: "Semarang", url: "/semarang" },
    { title: "Makassar", url: "/makassar" },
    { title: "Jogja", url: "/jogja" },
    { title: "Bali", url: "/bali" },
    { title: "Medan", url: "/medan" },
  ];

  return (
    <div>
      {tabs.map((tab, index) => (
        <Link key={index} href={tab.url}>
          {tab.title}
        </Link>
      ))}
    </div>
  );
}
