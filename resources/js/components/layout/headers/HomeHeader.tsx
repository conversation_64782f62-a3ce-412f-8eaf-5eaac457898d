import { Link, router } from "@inertiajs/react";
import AppLogo from "@/components/assets/AppLogo";
import ThemeToggle from "@/components/ThemeToggle";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { LogOutIcon, UserIcon, SettingsIcon } from "lucide-react";


interface headerProps {
  user?: {
    name: string;
    email: string;
  };
}

export default function HomeHeader(data: headerProps) {

  const handleLogout = () => {
    router.post('/logout');
  };

  const headerNavs: headerProps[] = [
    { label: "Home", href: "/", subNavs: [] },
    { label: "Hangpong Jadul", href: "/hangpong-jadul", subNavs: [] },
    { label: "Bala Bala Mince", href: "/bala-bala-mince", subNavs: [] },
    { label: "Hot News", href: "/hot-news", subNavs: [] },
    { label: "Life Style", href: "/life-style", subNavs: [] },
    {
      label: "more",
      href: "",
      subNavs: [
        { label: "Olahraga", href: "/olahraga", },
        { label: "Kesehatan", href: "/kesehatan", },
        { label: "Selebriti", href: "/selebriti", },
        { label: "Teknologi", href: "/teknologi", },
        { label: "Politik", href: "/politik", },
        { label: "Film & Series", href: "/film-series" },
        { label: "Music", href: "/music" },
      ],
    },
  ];
  interface headerProps {
    label: string;
    href: string;
    subNavs: { label: string; href: string }[];
  }

  const user = {
    name: data.user?.name,
    email: data.user?.email,
  };

  return (
    <header className="border-b border-border bg-primary-darker bg-linear-[156deg] from-primary-lighter/70 to-primary-darker sticky top-0 z-50" >
      <div className="container mx-auto px-4 py-3">
        {/* Main Header Row */}
        <div className="flex justify-between items-center">
          {/* Logo and Navigation */}
          <div className="flex items-center space-x-6">
            <div className="text-xl font-bold text-gray-900 dark:text-white">
              <Link href="/">
                <AppLogo size="100%" className="mx-auto" />
              </Link>
            </div>
            <NavigationMenu viewport={false}>
              <NavigationMenuList>
                {headerNavs.map((nav, idx) => (
                  nav.subNavs && nav.subNavs.length > 0 ? (
                    <NavigationMenuItem key={nav.label}>
                      <NavigationMenuTrigger className="bg-transparent hover:bg-!transparent dark:hover:bg-!transparent  data-[state=open]:!bg-transparent dark:focus:!bg-transparent rounded-none border-b-2 border-transparent hover:border-white text-white">
                        {nav.label}
                      </NavigationMenuTrigger>
                      <NavigationMenuContent className='bg-lg-background dark:bg-dr-background text-black dark:text-white shadow-lg rounded-md'>
                        <ul className="grid gap-2">
                          {nav.subNavs.map((sub, subIdx) => (
                            <li key={sub.label + subIdx}>
                              <Link href={sub.href} className="block px-2 py-1 hover:bg-primary/10 rounded">
                                {sub.label}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </NavigationMenuContent>
                    </NavigationMenuItem>
                  ) : (
                    <NavigationMenuItem key={nav.label}>
                      <NavigationMenuLink asChild>
                        <Link
                          href={nav.href}
                          className={`text-white px-1 py-2 text-sm font-medium border-b-2 !rounded-none focus:!bg-transparent hover:!bg-transparent ${nav.href === "/" ? "border-white" : "border-transparent"
                            } hover:border-white transition`}
                        >
                          {nav.label}
                        </Link>
                      </NavigationMenuLink>
                    </NavigationMenuItem>
                  )
                ))}
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Right Side: Weather, Theme Toggle, User Menu */}
          <div className="flex items-center space-x-4">

            {/* User Menu / Login */}
            {user.name ? (
              <div className="flex items-center space-x-4">
                <a
                  href="/dashboard"
                  className="flex items-center space-x-2 text-white hover:text-gray-300 transition-colors"
                >
                  <UserIcon size={16} />
                  <span className="text-sm">{user.name}</span>
                </a>
                <a
                  href="/dashboard"
                  className="text-white border-b-2 border-transparent hover:border-white py-1 text-sm"
                >
                  Dashboard
                </a>
                <button
                  onClick={handleLogout}
                  className="flex items-center space-x-1 text-white hover:text-gray-300 transition-colors text-sm"
                >
                  <LogOutIcon size={16} />
                  <span>Logout</span>
                </button>
              </div>
            ) : (
              <></>
              // <div className="flex items-center space-x-2 text-sm">
              //   <Link
              //     href="/login"
              //     className="text-white border-b-2 border-transparent hover:border-white py-6"
              //   >
              //     Login
              //   </Link>
              //   <span className="text-gray-400">|</span>
              //   <Link
              //     href="/register"
              //     className="text-white border-b-2 border-transparent hover:border-white py-6"
              //   >
              //     Register
              //   </Link>
              // </div>
            )}

            {/* Theme Toggle */}
            <ThemeToggle className="hidden sm:flex" />
          </div>
        </div>

      </div>
    </header >
  );
}
