import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface LocationSelectorProps {
  className?: string;
}

const LocationSelector: React.FC<LocationSelectorProps> = ({ className = '' }) => {
  const locations = [
    'Jakarta',
    'Bandung', 
    'Surabaya',
    'Medan',
    'Palembang',
    'Semarang',
    'Yogyakarta'
  ];

  return (
    <section className={`mx-auto container px-4 py-3 flex justify-end ${className}`}>
      <div className="py-4 px-8 rounded-lg text-white">
        <Select>
          <SelectTrigger className="w-[180px] border-0 outline-0">
            <SelectValue placeholder="<PERSON>lih <PERSON>" />
          </SelectTrigger>
          <SelectContent className="bg-lg-background dark:bg-dr-background">
            {locations.map((location) => (
              <SelectItem
                key={location}
                className="text-black dark:text-white"
                value={location.toLowerCase()}
              >
                {location}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </section>
  );
};

export default LocationSelector;
