import React from 'react';

interface News {
    id: number;
    title: string;
    content: string;
    views: number;
    created_at: string;
}

interface Category {
    id: number;
    name: string;
}

interface Tag {
    id: number;
    name: string;
    count: number;
}

interface NewsDebugPanelProps {
    latestNews: News[];
    hotNews: News[];
    popularNews: News[];
    mostSeeNews: News[];
    viralNews: News[];
    celebrityNews: News[];
    categories: Category[];
    tags: Tag[];
}

export default function NewsDebugPanel({
    latestNews = [],
    hotNews = [],
    popularNews = [],
    mostSeeNews = [],
    viralNews = [],
    celebrityNews = [],
    categories = [],
    tags = []
}: NewsDebugPanelProps) {
    return (
        <section className="mx-auto container px-4 py-3">
            <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
                <h3 className="text-sm font-bold text-blue-800 dark:text-blue-200 mb-3">
                    🔍 Debug Panel - Data Status
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3 text-xs">
                    <div className="bg-white dark:bg-gray-800 p-2 rounded">
                        <span className="text-gray-500 dark:text-gray-400">
                            Latest
                        </span>
                        <div className="font-bold text-blue-600">
                            {latestNews.length}
                        </div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 p-2 rounded">
                        <span className="text-gray-500 dark:text-gray-400">
                            Hot
                        </span>
                        <div className="font-bold text-red-600">
                            {hotNews.length}
                        </div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 p-2 rounded">
                        <span className="text-gray-500 dark:text-gray-400">
                            Popular
                        </span>
                        <div className="font-bold text-green-600">
                            {popularNews.length}
                        </div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 p-2 rounded">
                        <span className="text-gray-500 dark:text-gray-400">
                            Most See
                        </span>
                        <div className="font-bold text-purple-600">
                            {mostSeeNews.length}
                        </div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 p-2 rounded">
                        <span className="text-gray-500 dark:text-gray-400">
                            Viral
                        </span>
                        <div className="font-bold text-orange-600">
                            {viralNews.length}
                        </div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 p-2 rounded">
                        <span className="text-gray-500 dark:text-gray-400">
                            Celebrity
                        </span>
                        <div className="font-bold text-pink-600">
                            {celebrityNews.length}
                        </div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 p-2 rounded">
                        <span className="text-gray-500 dark:text-gray-400">
                            Categories
                        </span>
                        <div className="font-bold text-indigo-600">
                            {categories.length}
                        </div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 p-2 rounded">
                        <span className="text-gray-500 dark:text-gray-400">
                            Tags
                        </span>
                        <div className="font-bold text-yellow-600">
                            {tags.length}
                        </div>
                    </div>
                </div>
                {latestNews.length > 0 && (
                    <div className="mt-3 text-xs text-blue-700 dark:text-blue-300">
                        ✅ Sample Latest: "
                        {latestNews[0]?.title.substring(0, 50)}..."
                    </div>
                )}
            </div>
        </section>
    );
}
