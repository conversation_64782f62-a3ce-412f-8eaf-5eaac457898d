import React, { useState } from "react";
import { Head } from "@inertiajs/react";
import HomeHeader from "@/components/layout/headers/HomeHeader";
import NewsReactionBar from "@/components/reactions/NewsReactionBar";
import ReactionSummary from "@/components/reactions/ReactionSummary";
import CommentSection from "@/components/comments/CommentSection";
// import NewsHeader from "@/components/news/NewsHeader";
// import NewsImage from "@/components/news/NewsImage";
// import NewsContent from "@/components/news/NewsContent";
// import NewsMetadata from "@/components/news/NewsMetadata";
// import ShareButton from "@/components/news/ShareButton";
// import RelatedNews from "@/components/news/RelatedNews";
// import AuthorInfo from "@/components/news/AuthorInfo";
// import ArticleStats from "@/components/news/ArticleStats";

interface User {
    id: number;
    name: string;
    email: string;
}

interface News {
    id: number;
    title: string;
    content: string;
    image?: string;
    image_url?: string;
    image_alt?: string;
    views: number;
    created_at: string;
    updated_at: string;
    author: User;
    category: {
        id: number;
        name: string;
    };
    reactions: {
        counts: {
            like?: number;
            love?: number;
            wow?: number;
            haha?: number;
            sad?: number;
            angry?: number;
            suka?: number;
            benci?: number;
            cinta?: number;
            lucu?: number;
            marah?: number;
            sedih?: number;
        };
        total_count: number;
        user_reaction: string | null;
    };
    comments_count: number;
}

interface NewsDetailProps {
    user?: User;
    news: News;
    randomNews: News[];
    deviceId: string;
    reactionTypes: Record<string, string>;
    pageTitle?: string;
}

const NewsDetail: React.FC<NewsDetailProps> = ({
    user,
    news,
    randomNews = [],
    deviceId,
    reactionTypes = {},
    pageTitle = "Detail Berita",
}) => {
    // Safety checks for required data
    if (!news) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900 mb-4">Artikel tidak ditemukan</h1>
                    <p className="text-gray-600">Artikel yang Anda cari tidak tersedia.</p>
                </div>
            </div>
        );
    }

    // Ensure reactions data exists
    const safeReactions = news.reactions || {
        counts: {
            like: 0, love: 0, wow: 0, haha: 0, sad: 0, angry: 0,
            suka: 0, benci: 0, cinta: 0, lucu: 0, marah: 0, sedih: 0
        },
        total_count: 0,
        user_reaction: null
    };

    // Ensure category and author exist
    const safeCategory = news.category || { id: 0, name: 'Uncategorized' };
    const safeAuthor = news.author || { id: 0, name: 'Unknown', email: '' };

    // Handle share functionality
    const handleShare = async () => {
        if (navigator.share) {
            try {
                await navigator.share({
                    title: news.title,
                    text: `Baca artikel menarik: ${news.title}`,
                    url: window.location.href,
                });
            } catch (error) {
                console.log('Error sharing:', error);
            }
        } else {
            // Fallback: copy to clipboard
            try {
                await navigator.clipboard.writeText(window.location.href);
                alert('Link artikel telah disalin ke clipboard!');
            } catch (error) {
                console.log('Error copying to clipboard:', error);
            }
        }
    };

    return (
        <>
            <Head title={`${news.title} - ${pageTitle}`} />
            <HomeHeader user={user} />

            <main className="container mx-auto px-4 py-6">
                <NewsHeader title={news.title} category={safeCategory} />

                <NewsImage news={news} />

                <div className="flex mt-12">
                    {/* Article Content */}
                    <div className="flex-3/4 ">
                        <NewsContent content={news.content} />
                        {/* Article Metadata */}
                        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
                            <NewsMetadata
                                author={safeAuthor}
                                createdAt={news.created_at}
                                views={news.views}
                            />
                            <ShareButton title={news.title} />
                        </div>
                    </div>
                    <div className="flex-1/4">hello </div>
                </div>

                <div className="flex items-center space-x-2 mb-4">
                </div>
                <div className="max-w-6xl mx-auto px-4 py-8">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Main Content */}
                        <div className="lg:col-span-2">
                            <article className="bg-white rounded-xl shadow-lg overflow-hidden">
                                {/* Article Footer */}
                                <div className="px-6 py-4 bg-gray-50 border-t">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                                            <span>{safeReactions.total_count} reaksi</span>
                                            <span>{news.comments_count || 0} komentar</span>
                                        </div>
                                        <button
                                            onClick={handleShare}
                                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                        >
                                            Bagikan artikel ini
                                        </button>
                                    </div>
                                </div>
                            </article>

                            {/* Comments Section */}
                            <div className="mt-8 bg-white rounded-xl shadow-lg p-6">
                                <NewsReactionBar
                                    reactableType="news"
                                    reactableId={news.id}
                                    initialCounts={{
                                        suka: safeReactions.counts.suka || 0,
                                        benci: safeReactions.counts.benci || 0,
                                        cinta: safeReactions.counts.cinta || 0,
                                        lucu: safeReactions.counts.lucu || 0,
                                        marah: safeReactions.counts.marah || 0,
                                        sedih: safeReactions.counts.sedih || 0,
                                        wow: safeReactions.counts.wow || 0,
                                    }}
                                    initialUserReaction={safeReactions.user_reaction}
                                    totalCount={safeReactions.total_count}
                                    isAuthenticated={!!user}
                                    className="mb-6"
                                />

                                <CommentSection
                                    newsId={news.id}
                                    user={user}
                                    isAuthenticated={!!user}
                                    commentsCount={news.comments_count || 0}
                                />
                            </div>
                        </div>

                        {/* Sidebar */}
                        <div className="lg:col-span-1">
                            <div className="sticky top-8 space-y-6">
                                {/* Reaction Summary */}
                                {safeReactions.total_count > 0 && (
                                    <div className="bg-white rounded-xl shadow-lg p-6">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                            Reaksi Pembaca
                                        </h3>
                                        <ReactionSummary
                                            counts={{
                                                suka: safeReactions.counts.suka || 0,
                                                benci: safeReactions.counts.benci || 0,
                                                cinta: safeReactions.counts.cinta || 0,
                                                lucu: safeReactions.counts.lucu || 0,
                                                marah: safeReactions.counts.marah || 0,
                                                sedih: safeReactions.counts.sedih || 0,
                                                wow: safeReactions.counts.wow || 0,
                                            }}
                                            totalCount={safeReactions.total_count}
                                            userReaction={safeReactions.user_reaction}
                                            variant="detailed"
                                            showPercentage={true}
                                            showUserIndicator={true}
                                            showTopReaction={true}
                                        />
                                    </div>
                                )}

                                {/* Related News */}
                                {randomNews.length > 0 && (
                                    <RelatedNews news={randomNews} />
                                )}

                                {/* Author Info */}
                                <AuthorInfo author={safeAuthor} />

                                {/* Article Stats */}
                                <ArticleStats
                                    views={news.views}
                                    reactionsCount={safeReactions.total_count}
                                    commentsCount={news.comments_count}
                                    category={safeCategory}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </>
    );
};

export default NewsDetail;
