import React from "react";
import { Head, Link, usePage } from "@inertiajs/react";

interface WelcomeProps {
  user?: {
    name: string;
    email: string;
  };
  appName?: string;
}

const Welcome: React.FC<WelcomeProps> = ({
  user,
  appName = "Lambe Turah News",
}) => {
  return (
    <>
      <Head title="Welcome" />

      <div className="min-h-screen bg-indigo-100 dark:bg-gray-800 flex items-center justify-center">
        <div className="max-w-4xl mx-auto px-6 py-12 text-center">
          <div className="mb-8">
            <h1 className="text-5xl font-bold text-gray-900 mb-4">
              Welcome to {appName}
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Your trusted source for the latest news and updates
            </p>
          </div>

          {user ? (
            <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Hello, {user.name}!
              </h2>
              <p className="text-gray-600">
                Welcome back to your news dashboard.
              </p>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Get Started
              </h2>
              <p className="text-gray-600 mb-6">
                Join our community to stay updated with the latest news.
              </p>
              <div className="space-x-4">
                <button className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                  Sign Up
                </button>
                <button className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-6 rounded-lg transition duration-200">
                  Learn More
                </button>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-3xl mb-4">📰</div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Latest News
              </h3>
              <p className="text-gray-600 text-sm">
                Stay updated with breaking news and trending stories
              </p>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-3xl mb-4">🌍</div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Global Coverage
              </h3>
              <p className="text-gray-600 text-sm">
                News from around the world, right at your fingertips
              </p>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-3xl mb-4">⚡</div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Real-time Updates
              </h3>
              <p className="text-gray-600 text-sm">
                Get notified instantly when important news breaks
              </p>
            </div>
          </div>

          <div className="mt-12">
            <p className="text-gray-500 text-sm">
              Powered by Inertia.js & React ⚛️
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Welcome;
