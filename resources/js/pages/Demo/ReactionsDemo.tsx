import React from 'react';
import { Head } from '@inertiajs/react';
import NewsReactionBar from '@/components/reactions/NewsReactionBar';
import SimpleReactionBar from '@/components/reactions/SimpleReactionBar';

interface DemoNews {
    id: number;
    title: string;
    reactions: {
        counts: {
            suka: number;
            benci: number;
            cinta: number;
            lucu: number;
            marah: number;
            sedih: number;
            wow: number;
        };
        total_count: number;
        user_reaction: string | null;
    };
}

interface ReactionsDemoProps {
    demoNews: DemoNews;
}

const ReactionsDemo: React.FC<ReactionsDemoProps> = ({ demoNews }) => {
    return (
        <>
            <Head title="Demo - News Reaction Bar" />
            
            <div className="min-h-screen bg-gray-50 py-8">
                <div className="max-w-4xl mx-auto px-4">
                    {/* Header */}
                    <div className="text-center mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            News Reaction Bar Demo
                        </h1>
                        <p className="text-gray-600">
                            Test the new reaction component with SVG icons and counts
                        </p>
                    </div>

                    {/* Demo Article Card 1 - Custom SVG Version */}
                    <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
                        <div className="p-6">
                            <h2 className="text-2xl font-bold text-gray-900 mb-4">
                                Custom SVG Version - {demoNews.title}
                            </h2>
                            <p className="text-gray-600 mb-6">
                                This version uses custom SVG icons with the layout similar to your reference image.
                            </p>

                            {/* News Reaction Bar */}
                            <NewsReactionBar
                                reactableType="news"
                                reactableId={demoNews.id}
                                initialCounts={demoNews.reactions.counts}
                                initialUserReaction={demoNews.reactions.user_reaction}
                                totalCount={demoNews.reactions.total_count}
                                isAuthenticated={false}
                                className="border-t pt-6"
                            />
                        </div>
                    </div>

                    {/* Demo Article Card 2 - Simple Version */}
                    <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
                        <div className="p-6">
                            <h2 className="text-2xl font-bold text-gray-900 mb-4">
                                Simple Version (Like Original) - {demoNews.title}
                            </h2>
                            <p className="text-gray-600 mb-6">
                                This version is more like your original ReactionBar but with new reaction types.
                                You can easily replace emojis with PNG images by changing the REACTION_TYPES object.
                            </p>

                            {/* Simple Reaction Bar */}
                            <SimpleReactionBar
                                reactableType="news"
                                reactableId={demoNews.id + 1}
                                initialCounts={demoNews.reactions.counts}
                                initialUserReaction={demoNews.reactions.user_reaction}
                                totalCount={demoNews.reactions.total_count}
                                isAuthenticated={false}
                                className="border-t pt-6"
                            />
                        </div>
                    </div>

                    {/* Features List */}
                    <div className="bg-white rounded-xl shadow-lg p-6">
                        <h3 className="text-xl font-bold text-gray-900 mb-4">
                            Component Features
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                            <li className="flex items-center">
                                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                                Custom SVG icons for each reaction type
                            </li>
                            <li className="flex items-center">
                                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                                Reaction counts displayed as badges
                            </li>
                            <li className="flex items-center">
                                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                                Hover effects and animations
                            </li>
                            <li className="flex items-center">
                                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                                Active state highlighting
                            </li>
                            <li className="flex items-center">
                                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                                Total reactions summary
                            </li>
                            <li className="flex items-center">
                                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                                Most popular reactions preview
                            </li>
                            <li className="flex items-center">
                                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                                Loading states and error handling
                            </li>
                        </ul>
                    </div>

                    {/* Back to Home */}
                    <div className="text-center mt-8">
                        <a 
                            href="/" 
                            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            ← Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </>
    );
};

export default ReactionsDemo;
