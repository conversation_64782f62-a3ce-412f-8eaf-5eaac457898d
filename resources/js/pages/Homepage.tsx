import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON> } from "@inertiajs/react";
import SimpleWeatherWidget from "@/components/weather/SimpleWeatherWidget";
import { CircleIcon } from "lucide-react";

import HomeHeader from "@/components/layout/headers/HomeHeader";
import HomeFooter from "@/components/layout/footers/HomeFooter";

import clx from "clsx";
import CategoryTabs from "@/components/Categorytab";
import NewsCard from "@/components/news/NewsCard";
import NewsCardWithReactions from "@/components/news/NewsCardWithReactions";
import NewsFilter from "@/components/news/NewsFilter";
import NewsCardSkeleton from "@/components/news/NewsCardSkeleton";
import { NewsFilterOptions } from "@/types/other";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
interface News {
    id: number;
    title: string;
    content: string;
    image?: string;
    image_url?: string;
    views: number;
    created_at: string;
    author: {
        name: string;
    };
    category: {
        id: number;
        name: string;
    };
    likes_count?: number;
}

interface Category {
    id: number;
    name: string;
}

interface Tag {
    id: number;
    name: string;
    count: number;
}

interface HomepageProps {
    user?: {
        name: string;
        email: string;
    };
    pageTitle?: string;
    latestNews: News[];
    hotNews: News[];
    popularNews: News[];
    mostSeeNews: News[];
    viralNews: News[];
    celebrityNews: News[];
    categories: Category[];
    tags: Tag[];
}

const Homepage: React.FC<HomepageProps> = ({
    user,
    pageTitle = "Lambe Turah News",
    latestNews = [],
    hotNews = [],
    popularNews = [],
    mostSeeNews = [],
    viralNews = [],
    celebrityNews = [],
    categories = [],
    tags = [],
}) => {
    const [isLoading, setIsLoading] = useState(true);
    const [filteredNews, setFilteredNews] = useState<News[]>(latestNews);
    const [isFilterLoading, setIsFilterLoading] = useState(false);
    const [currentFilters, setCurrentFilters] = useState<NewsFilterOptions>({});

    // State for Hot News section filtering
    const [activeHotNewsTab, setActiveHotNewsTab] = useState<string>("Hot News");
    const [filteredHotNews, setFilteredHotNews] = useState<News[]>(hotNews);
    const [isHotNewsLoading, setIsHotNewsLoading] = useState(false);
    const [isTransitioning, setIsTransitioning] = useState(false);

    // Helper function to get image URL with fallbacks
    const getImageUrl = (news: News): string => {
        if (news.image_url) {
            return news.image_url;
        }
        if (news.image) {
            return `/storage/images/${news.image}`;
        }
        return `/img/noimg.jpg`;
    };

    // Function to fetch filtered news
    const fetchFilteredNews = useCallback(async (filters: NewsFilterOptions) => {
        setIsFilterLoading(true);
        try {
            const params = new URLSearchParams();

            // Add filter parameters
            if (filters.search) params.append('search', filters.search);
            if (filters.category_id) params.append('category_id', filters.category_id.toString());
            if (filters.date_from) params.append('date_from', filters.date_from);
            if (filters.date_to) params.append('date_to', filters.date_to);
            if (filters.sort) params.append('type', filters.sort);
            params.append('perPage', (filters.perPage || 6).toString());

            const response = await fetch(`/api/news?${params.toString()}`);
            const data = await response.json();

            // ResponseHelper format: { status: boolean, message: string, data: array, total, perPage, currentPage }
            if (data.status && data.data) {
                setFilteredNews(data.data || []);
            } else {
                setFilteredNews([]);
            }
        } catch (error) {
            console.error('Error fetching filtered news:', error);
            // Fallback to original latest news
            setFilteredNews(latestNews);
        } finally {
            setIsFilterLoading(false);
        }
    }, [latestNews]);

    // Handle filter changes
    const handleFilterChange = useCallback((filters: NewsFilterOptions) => {
        setCurrentFilters(filters);

        // If no filters are applied, show original latest news
        const hasActiveFilters = Object.values(filters).some(value =>
            value !== undefined && value !== '' && value !== null
        );

        if (!hasActiveFilters) {
            setFilteredNews(latestNews);
            setIsFilterLoading(false);
        } else {
            fetchFilteredNews(filters);
        }
    }, [fetchFilteredNews, latestNews]);

    // Function to fetch Hot News based on category
    const fetchHotNewsByCategory = useCallback(async (categoryName: string) => {
        setIsTransitioning(true);
        setIsHotNewsLoading(true);

        // Small delay to allow fade out animation
        await new Promise(resolve => setTimeout(resolve, 150));

        try {
            const params = new URLSearchParams();
            params.append('type', 'top'); // Use 'top' for hot news
            params.append('perPage', '10');

            // Map category names to category IDs based on seeder data
            const categoryMapping: { [key: string]: { type?: string, search?: string, category_id?: number } } = {
                "Hot News": { type: 'top' },
                "Viral": { category_id: 16 }, // Viral category ID from seeder
                "Selebriti": { category_id: 3 }, // Selebriti category ID from seeder
                "Film & Series": { category_id: 15 }, // Film & Series category ID from seeder
                "Music": { category_id: 14 }, // Music category ID from seeder
                "Sport": { category_id: 4 }, // Olahraga category ID from seeder
                "Lifestyle": { category_id: 9 } // Lifestyle category ID from seeder
            };

            const filterConfig = categoryMapping[categoryName];
            if (filterConfig) {
                if (filterConfig.search) {
                    params.append('search', filterConfig.search);
                }
                if (filterConfig.category_id) {
                    params.append('category_id', filterConfig.category_id.toString());
                }
                if (filterConfig.type) {
                    params.set('type', filterConfig.type);
                }
            }

            const response = await fetch(`/api/news?${params.toString()}`);
            const data = await response.json();

            if (data.status && data.data) {
                setFilteredHotNews(data.data || []);
            } else {
                setFilteredHotNews([]);
            }
        } catch (error) {
            console.error('Error fetching hot news by category:', error);
            // Fallback to original hot news
            setFilteredHotNews(hotNews);
        } finally {
            setIsHotNewsLoading(false);
        }
    }, [hotNews]);

    // Handle Hot News tab change
    const handleHotNewsTabChange = useCallback((tabLabel: string) => {
        setActiveHotNewsTab(tabLabel);

        if (tabLabel === "Hot News") {
            // Show original hot news
            setFilteredHotNews(hotNews);
            setIsHotNewsLoading(false);
        } else {
            fetchHotNewsByCategory(tabLabel);
        }
    }, [fetchHotNewsByCategory, hotNews]);

    useEffect(() => {
        const hasData = latestNews.length > 0 || hotNews.length > 0 || popularNews.length > 0;
        if (hasData) {
            setIsLoading(false);
        } else {
            const timer = setTimeout(() => {
                setIsLoading(false);
            }, 3000);
            return () => clearTimeout(timer);
        }
    }, [latestNews, hotNews, popularNews]);

    // Initialize filtered news with latest news
    useEffect(() => {
        setFilteredNews(latestNews);
    }, [latestNews]);

    // Initialize filtered hot news
    useEffect(() => {
        setFilteredHotNews(hotNews);
    }, [hotNews]);

    return (
        <>
            <Head title={pageTitle} />
            <HomeHeader user={user} />
            <div className="min-h-screen bg-lg-background dark:bg-dr-background text-foreground ease-in-out duration-300">

                {/* Debug Panel */}
                <section className="mx-auto container px-4 py-3">
                    <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
                        <h3 className="text-sm font-bold text-blue-800 dark:text-blue-200 mb-3">🔍 Debug Panel - Data Status</h3>
                        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3 text-xs">
                            <div className="bg-white dark:bg-gray-800 p-2 rounded">
                                <span className="text-gray-500 dark:text-gray-400">Latest</span>
                                <div className="font-bold text-blue-600">{latestNews.length}</div>
                            </div>
                            <div className="bg-white dark:bg-gray-800 p-2 rounded">
                                <span className="text-gray-500 dark:text-gray-400">Hot</span>
                                <div className="font-bold text-red-600">{hotNews.length}</div>
                            </div>
                            <div className="bg-white dark:bg-gray-800 p-2 rounded">
                                <span className="text-gray-500 dark:text-gray-400">Popular</span>
                                <div className="font-bold text-green-600">{popularNews.length}</div>
                            </div>
                            <div className="bg-white dark:bg-gray-800 p-2 rounded">
                                <span className="text-gray-500 dark:text-gray-400">Most See</span>
                                <div className="font-bold text-purple-600">{mostSeeNews.length}</div>
                            </div>
                            <div className="bg-white dark:bg-gray-800 p-2 rounded">
                                <span className="text-gray-500 dark:text-gray-400">Viral</span>
                                <div className="font-bold text-orange-600">{viralNews.length}</div>
                            </div>
                            <div className="bg-white dark:bg-gray-800 p-2 rounded">
                                <span className="text-gray-500 dark:text-gray-400">Celebrity</span>
                                <div className="font-bold text-pink-600">{celebrityNews.length}</div>
                            </div>
                            <div className="bg-white dark:bg-gray-800 p-2 rounded">
                                <span className="text-gray-500 dark:text-gray-400">Categories</span>
                                <div className="font-bold text-indigo-600">{categories.length}</div>
                            </div>
                            <div className="bg-white dark:bg-gray-800 p-2 rounded">
                                <span className="text-gray-500 dark:text-gray-400">Tags</span>
                                <div className="font-bold text-yellow-600">{tags.length}</div>
                            </div>
                        </div>
                        {latestNews.length > 0 && (
                            <div className="mt-3 text-xs text-blue-700 dark:text-blue-300">
                                ✅ Sample Latest: "{latestNews[0]?.title.substring(0, 50)}..."
                            </div>
                        )}
                    </div>
                </section>

                {/* Location Section */}
                <section className="mx-auto container px-4 py-3 flex justify-end">
                    <div className="py-4 px-8 rounded-lg text-white">
                        <Select>
                          <SelectTrigger className="w-[180px] border-0 outline-0 ">
                            <SelectValue placeholder="Theme" />
                          </SelectTrigger>
                          <SelectContent className={`bg-lg-background dark:bg-dr-background`}>
                            <SelectItem
                                className={`text-black dark:text-white`}
                                value="Jakarta">Jakarta</SelectItem>
                            <SelectItem
                                className={`text-black dark:text-white`}
                                value="Bandung">Bandung</SelectItem>
                            <SelectItem
                                className={`text-black dark:text-white`}
                                value="system">System</SelectItem>
                            <SelectItem
                                className={`text-black dark:text-white`}
                                value="Surabaya">Surabaya</SelectItem>
                            <SelectItem
                                className={`text-black dark:text-white`}
                                value="Medan">Medan</SelectItem>
                            <SelectItem
                                className={`text-black dark:text-white`}
                                value="Palembang">Palembang</SelectItem>
                            <SelectItem
                                className={`text-black dark:text-white`}
                                value="Semarang">Semarang</SelectItem>
                            <SelectItem
                                className={`text-black dark:text-white`}
                                value="Yogyakarta">Yogyakarta</SelectItem>
                          </SelectContent>
                        </Select>
                    </div>
                </section>

                {/* Main Content */}
                <main className="container mx-auto px-4 py-6">
                    <div className="space-y-8">

                        {/* Hot News Section */}
                        <section className="space-y-6">
                            <div className="bg-[#de0b66]/5 dark:bg-[#270212]/70 transition-all ease-in-out duration-300 flex flex-row items-center justify-between p-12 rounded-xl">
                                <div className='flex flex-row items-center gap-5'>
                                    <div className="relative">
                                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white inline-block z-10 relative tracking-wide">
                                            HOT NEWS
                                        </h1>
                                        <span className="absolute -left-4 -bottom-2 w-3/4 h-6 bg-red-100 dark:bg-red-100/10 z-0" aria-hidden="true"></span>
                                    </div>
                                    <div>
                                        <b className="text-sm text-gray-900 dark:text-white">
                                            {new Date().toLocaleDateString("id-ID", {
                                                weekday: "long",
                                                year: "numeric",
                                                month: "long",
                                                day: "numeric",
                                            })}
                                        </b>
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            Todays News 🔥
                                        </p>
                                    </div>
                                </div>
                                <SimpleWeatherWidget />
                            </div>

                            {/* Category Tabs */}
                            <CategoryTabs
                                tabs={[
                                    { label: "Hot News", color: "red" },
                                    { label: "Viral", color: "orange", icon: <span role="img" aria-label="fire">🔥</span> },
                                    { label: "Selebriti", color: "pink" },
                                    { label: "Film & Series", color: "red" },
                                    { label: "Music", color: "red" },
                                    { label: "Sport", color: "red" },
                                    { label: "Lifestyle", color: "red" },
                                ]}
                                activeTab={activeHotNewsTab}
                                onTabClick={handleHotNewsTabChange}
                            />

                            <div className="flex flex-col lg:flex-row items-start md:items-stretch gap-6">
                                <div className="flex flex-col lg:flex-3/4">
                                    {/* Content Container with Smooth Transitions */}
                                    <div className={`transition-all duration-300 ease-in-out ${
                                        isTransitioning ? 'opacity-50 transform scale-95' : 'opacity-100 transform scale-100'
                                    }`}>
                                        {/* Loading State for Hot News */}
                                        {isHotNewsLoading && (
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <NewsCardSkeleton />
                                                <NewsCardSkeleton />
                                            </div>
                                        )}

                                        {/* Hot News Content */}
                                        {!isHotNewsLoading && filteredHotNews && filteredHotNews.length > 0 && (
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                {filteredHotNews.slice(0, 2).map((news, index) => (
                                                <div key={news.id} className={`flex flex-col transition-all hover:scale-[102%] group stagger-item`}>
                                                    <Link href={`/news/${news.id}/show`}>
                                                        <img
                                                            src={getImageUrl(news)}
                                                            alt={news.title}
                                                            className="rounded-lg mb-4 object-cover w-full h-86"
                                                            onError={(e) => {
                                                                e.currentTarget.src = "/storage/images/news-1.jpg";
                                                            }}
                                                        />
                                                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 hover:text-[#de0b66] transition-colors group::">
                                                            {news.title}
                                                        </h2>
                                                    </Link>
                                                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-2">
                                                        <span>Oleh <b>{news.author.name}</b></span>
                                                        <span>{new Date(news.created_at).toLocaleDateString("id-ID")}</span>
                                                        <span className="flex items-center gap-1"><CircleIcon size={14} /> {news.views}</span>
                                                    </div>
                                                </div>
                                                ))}
                                            </div>
                                        )}

                                        {/* No Results State */}
                                        {!isHotNewsLoading && (!filteredHotNews || filteredHotNews.length === 0) && (
                                            <div className="text-center py-12 animate-fadeIn">
                                                <p className="text-gray-500 dark:text-gray-400 mb-4">
                                                    Tidak ada berita {activeHotNewsTab.toLowerCase()} yang ditemukan
                                                </p>
                                                <button
                                                    onClick={() => handleHotNewsTabChange("Hot News")}
                                                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm transition-colors duration-200"
                                                >
                                                    Kembali ke Hot News
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                {/* Celebrity News Sidebar */}
                                <div className="w-full lg:flex-1/4 mt-8 md:16 lg:mt-0 border-l border-l-gray-200 dark:border-gray-700">
                                    <div className="rounded-xl p-3 ">
                                        <div className="flex items-center gap-3 mb-4 px-4">
                                            <h3 className="text-lg font-bold text-gray-900 dark:text-white">Ssst... Ini Juga Seru!</h3>
                                            <span className="bg-[#de0b66] text-white text-xs px-3 py-1 rounded-full font-semibold">Selebriti</span>
                                        </div>
                                        <div className="space-y-3">
                                            {celebrityNews && celebrityNews.length > 0 ? (
                                                celebrityNews.slice(0, 4).map((news, idx) => (
                                                    <div
                                                        key={news.id}
                                                        className={
                                                            "transition-all border-b border-gray-200 dark:border-gray-700 px-4 pt-2 pb-3 hover:rounded-lg hover:bg-[#de0b66]/5 hover:border-gray-400/20 hover:border hover:dark:bg-red-700/20 hover:dark:border-gray-400/50 hover:pt-4 hover:pb-4"
                                                        }
                                                    >
                                                        <Link href={`/news/${news.id}/show`}>
                                                            <h4 className={clx(
                                                                "font-semibold text-gray-900 dark:text-white mb-1",
                                                                idx === 0 ? "text-base" : "text-sm"
                                                            )}>
                                                                {news.title}
                                                            </h4>
                                                        </Link>
                                                        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-2">
                                                            <span>Oleh <b>Admin</b></span>
                                                            <span>{new Date(news.created_at).toLocaleDateString("id-ID", { month: "short", day: "2-digit", year: "numeric" })}</span>
                                                            <span className="flex items-center gap-1">
                                                                <span className="flex items-center gap-1"><CircleIcon size={14} /> {news.views}</span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                ))
                                            ) : (
                                                <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                                                    Belum ada berita selebriti
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>

                        {/* Horizontal Ads Section */}
                        <section>
                            <div className="rounded-lg overflow-hidden">
                                <img src="https://placehold.co/1440x240" alt="horizontal ads" className="w-full h-auto" />
                            </div>
                        </section>

                        {/* Most See Section */}
                        <section className="space-y-6">
                            <div className="flex items-center gap-4">
                                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">MOST SEE</h2>
                                <span className="bg-[#de0b66] text-white text-xs px-3 py-1 rounded-full font-semibold">🔥</span>
                            </div>

                            <div className="flex flex-col lg:flex-row items-start md:items-stretch gap-6">
                                <div className="flex flex-col lg:flex-3/4">
                                    {/* Top 2 Cards Layout */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        {mostSeeNews && mostSeeNews.length > 0 ? (
                                            mostSeeNews.slice(0, 2).map((news, idx) => (
                                                <article key={news.id} className="flex flex-col mb-6">
                                                    <Link href={`/news/${news.id}/show`} className=" w-full">
                                                        <img
                                                            src={getImageUrl(news)}
                                                            alt={news.title}
                                                            className="rounded-lg object-cover w-full aspect-[16/9] mb-4"
                                                            onError={(e) => {
                                                                e.currentTarget.src = "/storage/images/news-2.jpg";
                                                            }}
                                                        />
                                                    </Link>
                                                    <div className="flex flex-col justify-between flex-1">
                                                        <div>
                                                            <span className={clx(
                                                                "inline-block mb-2 px-3 py-1 rounded-full text-xs font-semibold",
                                                                idx === 0 ? "bg-red-600 text-white" : "bg-[#de0b66] text-white"
                                                            )}>
                                                                {idx === 0 ? "Viral" : "Hot News"}
                                                            </span>
                                                            <Link href={`/news/${news.id}/show`}>
                                                                <h3 className="text-lg font-bold text-gray-900 dark:text-white hover:text-[#de0b66] transition-colors mb-2">
                                                                    {news.title}
                                                                </h3>
                                                            </Link>
                                                            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-2 mb-2">
                                                                <span>Oleh <b>{news.author.name}</b></span>
                                                                <span>{new Date(news.created_at).toLocaleDateString("id-ID")}</span>
                                                            </div>
                                                        </div>
                                                        {/* Reaction & Views */}
                                                        <div className="flex items-center gap-4 mt-2">
                                                            {/* Example reactions, replace with your actual data if available */}
                                                            <div className="flex items-center gap-2">
                                                                <span role="img" aria-label="like">👍</span> <span>10</span>
                                                                <span role="img" aria-label="love">😍</span> <span>5</span>
                                                                <span role="img" aria-label="laugh">😂</span> <span>20</span>
                                                                <span role="img" aria-label="angry">😡</span> <span>0</span>
                                                            </div>
                                                            <div className="flex items-center gap-1 text-gray-700 dark:text-gray-300 ml-auto">
                                                                <CircleIcon size={16} /> {news.views}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </article>
                                            ))
                                        ) : null}
                                    </div>

                                    {/* List for the rest */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                                        {mostSeeNews && mostSeeNews.length > 2 ? (
                                            mostSeeNews.slice(2, 8).map((news) => (
                                                <NewsCardWithReactions
                                                    key={news.id}
                                                    news={news}
                                                    showAuthor={true}
                                                    showViews={true}
                                                    className="h-full"
                                                    imageClassName="h-32"
                                                    titleClassName="text-sm"
                                                    metaClassName="text-xs"
                                                    reactionSize="small"
                                                    reactionIconType="svg"
                                                />
                                            ))
                                        ) : (
                                            mostSeeNews && mostSeeNews.length === 0 && (
                                                <div className="col-span-full text-center py-8">
                                                    <p className="text-gray-500 dark:text-gray-400">Belum ada berita most see</p>
                                                </div>
                                            )
                                        )}
                                    </div>
                                </div>

                                <div className="w-full lg:flex-1/4 px-4 mt-8 md:mt-0 space-y-4 border-l border-l-gray-200 dark:border-gray-700">

                                    {/* Popular Post */}
                                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                                            <span className="text-[#de0b66]">📈</span>
                                            Popular Post
                                        </h3>
                                        <div className="space-y-4">
                                            {popularNews && popularNews.length > 0 ? (
                                                popularNews.slice(0, 5).map((news) => (
                                                    <div key={news.id} className="flex gap-3 pb-3 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                                                        <img
                                                            src={getImageUrl(news)}
                                                            alt={news.title}
                                                            className="w-16 h-12 object-cover rounded"
                                                            onError={(e) => {
                                                                e.currentTarget.src = "/storage/images/news-5.jpg";
                                                            }}
                                                        />
                                                        <div className="flex-1">
                                                            <Link href={`/news/${news.id}/show`}>
                                                                <h4 className="text-sm font-medium text-gray-900 dark:text-white hover:text-[#de0b66] transition-colors line-clamp-2 mb-1">
                                                                    {news.title}
                                                                </h4>
                                                            </Link>
                                                            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-2">
                                                                <span>{new Date(news.created_at).toLocaleDateString("id-ID")}</span>
                                                                <span className="flex items-center gap-1">
                                                                    <CircleIcon size={10} />
                                                                    {news.views}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))
                                            ) : (
                                                <div className="text-center py-8">
                                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                                        Belum ada berita populer
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    {/* Tags */}
                                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                                            <span className="text-[#de0b66]">#</span>
                                            Tags
                                        </h3>
                                        <div className="flex flex-wrap gap-2">
                                            {tags && tags.length > 0 ? (
                                                tags.map((tag) => (
                                                    <Link
                                                        key={tag.id}
                                                        href={`#`}
                                                        className="inline-block px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full hover:bg-[#de0b66] hover:text-white transition-colors"
                                                    >
                                                        {tag.name} ({tag.count})
                                                    </Link>
                                                ))
                                            ) : (
                                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                                    Belum ada tags
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <div className="flex flex-col lg:flex-row items-start md:items-stretch gap-6 mt-24 lg:mt-16">
                            <div className="flex flex-col lg:flex-3/4">
                                {/* Viral Section */}
                                <section className="space-y-6">
                                    <div className="flex items-center gap-4">
                                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">VIRAL 🔥</h2>
                                        <span className="bg-orange-500 text-white text-xs px-3 py-1 rounded-full font-semibold">Trending</span>
                                    </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                            {viralNews && viralNews.length > 0 ? (
                                                viralNews.slice(0, 8).map((news) => (
                                                    <NewsCardWithReactions
                                                        key={news.id}
                                                        news={news}
                                                        showAuthor={true}
                                                        showViews={true}
                                                        className="h-full"
                                                        imageClassName="h-32"
                                                        titleClassName="text-sm"
                                                        metaClassName="text-xs"
                                                        reactionSize="small"
                                                        reactionIconType="svg"
                                                    />
                                                ))
                                            ) : (
                                                <div className="col-span-full text-center py-8">
                                                    <p className="text-gray-500 dark:text-gray-400">Belum ada berita viral</p>
                                                </div>
                                            )}
                                        </div>

                                </section>

                                {/* Celebrity Section */}
                                <section className="space-y-6 mt-24 lg:mt-16">
                                    <div className="flex items-center gap-4">
                                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">CELEBRITY</h2>
                                        <span className="bg-pink-500 text-white text-xs px-3 py-1 rounded-full font-semibold">⭐</span>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                        {celebrityNews && celebrityNews.length > 0 ? (
                                            celebrityNews.slice(0, 8).map((news) => (
                                                <NewsCardWithReactions
                                                    key={news.id}
                                                    news={news}
                                                    showAuthor={true}
                                                    showViews={true}
                                                    className="h-full"
                                                    imageClassName="h-32"
                                                    titleClassName="text-sm"
                                                    metaClassName="text-xs"
                                                    reactionSize="small"
                                                    reactionIconType="svg"
                                                />
                                            ))
                                        ) : (
                                            <div className="col-span-full text-center py-8">
                                                <p className="text-gray-500 dark:text-gray-400">Belum ada berita selebriti</p>
                                            </div>
                                        )}
                                    </div>
                                </section>
                            </div>

                            <div className="w-full flex flex-row lg:flex-col gap-4 lg:flex-1/4 lg:px-4 mt-8 md:mt-0 lg:border-l border-l-gray-200 dark:border-gray-700">
                                 <div className="rounded-lg overflow-hidden">
                                     <img src="https://placehold.co/1080x1350" alt="horizontal ads" className="w-full h-auto" />
                                 </div>
                                <div className="rounded-lg overflow-hidden">
                                     <img src="https://placehold.co/1080x1350" alt="horizontal ads" className="w-full h-auto" />
                                 </div>
                            </div>
                        </div>

                    </div>
                </main>
            </div>

            <HomeFooter/>
        </>
    );
};

export default Homepage;
