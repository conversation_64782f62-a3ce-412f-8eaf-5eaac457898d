import React from "react";
import { <PERSON>, <PERSON> } from "@inertiajs/react";
import { CircleIcon, ArrowLeftIcon, CalendarIcon, UserIcon, EyeIcon } from "lucide-react";
import HomeHeader from "@/components/layouts/headers/HomeHeader";
import SimpleWeatherWidget from "@/components/weather/SimpleWeatherWidget";

interface News {
    id: number;
    title: string;
    content: string;
    image?: string;
    image_url?: string;
    views: number;
    created_at: string;
    author: {
        name: string;
    };
    category: {
        id: number;
        name: string;
    };
    likes_count?: number;
}

interface Category {
    id: number;
    name: string;
    views: number;
    created_at: string;
}

interface CategoryNewsProps {
    user?: {
        name: string;
        email: string;
    };
    pageTitle?: string;
    categories: Category;
    latestNews: News[];
    topNews: News[];
    popularNews: News[];
}

const CategoryNews: React.FC<CategoryNewsProps> = ({
    user,
    pageTitle = "Lambe Turah News",
    categories,
    latestNews,
    topNews,
    popularNews,
}) => {
    // Helper function to get image URL with fallbacks
    const getImageUrl = (news: News): string => {
        if (news.image_url) {
            return news.image_url;
        }
        if (news.image) {
            return `/storage/images/${news.image}`;
        }
        return `/storage/images/news-${(news.id % 8) + 1}.jpg`;
    };

    return (
        <>
            <Head title={`${categories.name} - ${pageTitle}`} />
            <HomeHeader user={user} />

            <div className="min-h-screen bg-lg-background dark:bg-dr-background text-foreground ease-in-out duration-300">
                {/* Breadcrumb */}
                <div className="container mx-auto px-4 py-4">
                    <nav className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                        <Link
                            href="/"
                            className="hover:text-[#de0b66] transition-colors flex items-center gap-1"
                        >
                            <ArrowLeftIcon size={16} />
                            Beranda
                        </Link>
                        <span>/</span>
                        <span className="text-gray-700 dark:text-gray-300">{categories.name}</span>
                    </nav>
                </div>

                <section className="mx-auto container px-4 py-3">
                </section>

                {/* Main Content */}
                <main className="container mx-auto px-4 py-6">
                    <div className="gap-8">
                        {/* Main Content Area */}
                        <section className="space-y-6">
                            {/* Category Header */}
                            <div className="bg-[#de0b66]/5 dark:bg-[#270212]/70 transition-all ease-in-out duration-300 flex flex-row items-center justify-between p-6 rounded-xl">
                                <div className='flex flex-row space-y-2 items-center gap-5'>
                                    <div className="relative m-4">
                                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white inline-block z-10 relative tracking-wide">
                                            {categories.name.toUpperCase()}
                                        </h1>
                                        <span className="absolute -left-4 -bottom-2 w-3/4 h-6 bg-red-100 dark:bg-red-100/10 z-0" aria-hidden="true"></span>
                                    </div>
                                    <div className="">
                                        <b className="text-sm text-gray-900 dark:text-white">
                                            {new Date().toLocaleDateString("id-ID", {
                                                weekday: "long",
                                                year: "numeric",
                                                month: "long",
                                                day: "numeric",
                                            })}
                                        </b>
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            Kategori Berita <span className='text-xs'>📰</span>
                                        </p>
                                    </div>
                                </div>
                                <SimpleWeatherWidget />
                            </div>

                            {/* Category Stats */}
                            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">Total berita dalam kategori ini</p>
                                        <p className="text-2xl font-bold text-[#de0b66]">{latestNews.length}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">Total views kategori</p>
                                        <p className="text-2xl font-bold text-[#de0b66]">{categories.views}</p>
                                    </div>
                                </div>
                            </div>

                            {/* Featured News Content */}
                            <div className="space-y-8">
                                {/* Top News Section */}
                                {topNews.length > 0 && (
                                    <section className="">
                                        <div className="flex items-center gap-2 mb-6">
                                            <h2 className="text-xl font-bold text-gray-900 dark:text-white">Paling Banyak Dibaca</h2>
                                            <span className="bg-[#de0b66] text-white text-xs px-3 py-1 rounded-full font-semibold">Trending</span>
                                        </div>

                                        <div className="flex">
                                            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                                                {topNews.slice(0, 2).map((news) => (
                                                    <div key={news.id} className="flex flex-col">
                                                        <Link href={`/news/${news.id}/show`}>
                                                            <img
                                                                src={getImageUrl(news)}
                                                                alt={news.title}
                                                                className="rounded-lg mb-4 object-cover w-full h-48"
                                                                onError={(e) => {
                                                                    e.currentTarget.src = "/storage/images/news-1.jpg";
                                                                }}
                                                            />
                                                            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 hover:text-[#de0b66] transition-colors">
                                                                {news.title}
                                                            </h3>
                                                        </Link>
                                                        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-2 mt-2">
                                                            <span>Oleh <b>{news.author.name}</b></span>
                                                            <span>{new Date(news.created_at).toLocaleDateString("id-ID", {
                                                                year: "numeric",
                                                                month: "short",
                                                                day: "numeric",
                                                            })}</span>
                                                            <span className="flex items-center gap-1"><EyeIcon size={14} /> {news.views}</span>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>

                                            {popularNews.length > 0 && (
                                                <div className="border-l border-gray-200 dark:border-gray-700 ml-4 pl-2">
                                                    <div className="w-64">
                                                        <div className="flex items-center gap-2 mb-4 p-4">
                                                            <h3 className="text-sm font-bold text-gray-900 dark:text-white">Paling Populer</h3>
                                                            <span className="bg-[#de0b66] text-white text-xs px-3 py-1 rounded-full font-semibold">Popular</span>
                                                        </div>
                                                        <div className="space-y-4">
                                                            {popularNews.slice(0, 4).map((news, index) => (
                                                                <div key={news.id} className={index === 0 ? "bg-[#de0b66]/10 dark:bg-[#de0b66]/20 rounded-lg p-4 shadow-sm" : "p-4"}>
                                                                    <Link href={`/news/${news.id}/show`}>
                                                                        <h4 className="text-xs font-semibold text-gray-900 dark:text-white mb-1 hover:text-[#de0b66] transition-colors">
                                                                            {news.title}
                                                                        </h4>
                                                                    </Link>
                                                                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-2">
                                                                        <span>Oleh <b>{news.author.name}</b></span>
                                                                        <span>{new Date(news.created_at).toLocaleDateString("id-ID", {
                                                                            month: "short",
                                                                            day: "numeric",
                                                                            year: "numeric",
                                                                        })}</span>
                                                                        <span className="flex items-center gap-1"><EyeIcon size={14} /> {news.views}</span>
                                                                    </div>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </section>
                                )}

                                {/* All News Grid */}
                                <div className="space-y-6">
                                    <div className="flex items-center justify-between">
                                        <h2 className="text-xl font-bold text-gray-900 dark:text-white">Semua Berita {categories.name}</h2>
                                        <span className="text-sm text-gray-500 dark:text-gray-400">{latestNews.length} artikel</span>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                        {latestNews.map((news) => (
                                            <article key={news.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden group hover:shadow-md transition-shadow">
                                                <Link href={`/news/${news.id}/show`}>
                                                    <div className="aspect-video overflow-hidden">
                                                        <img
                                                            src={getImageUrl(news)}
                                                            alt={news.title}
                                                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                                            onError={(e) => {
                                                                e.currentTarget.src = "/storage/images/news-2.jpg";
                                                            }}
                                                        />
                                                    </div>
                                                    <div className="p-4">
                                                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-[#de0b66] transition-colors line-clamp-2">
                                                            {news.title}
                                                        </h3>
                                                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                                                            {news.content.replace(/<[^>]*>/g, '').substring(0, 120)}...
                                                        </p>
                                                        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                                                            <div className="flex items-center gap-2">
                                                                <UserIcon size={12} />
                                                                <span>{news.author.name}</span>
                                                            </div>
                                                            <div className="flex items-center gap-3">
                                                                <div className="flex items-center gap-1">
                                                                    <CalendarIcon size={12} />
                                                                    <span>{new Date(news.created_at).toLocaleDateString("id-ID")}</span>
                                                                </div>
                                                                <div className="flex items-center gap-1">
                                                                    <EyeIcon size={12} />
                                                                    <span>{news.views}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </Link>
                                            </article>
                                        ))}
                                    </div>

                                    {latestNews.length === 0 && (
                                        <div className="text-center py-12">
                                            <div className="max-w-md mx-auto">
                                                <div className="mb-4">
                                                    <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto">
                                                        <span className="text-2xl">📰</span>
                                                    </div>
                                                </div>
                                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                                    Belum Ada Berita
                                                </h3>
                                                <p className="text-gray-500 dark:text-gray-400 mb-4">
                                                    Belum ada berita dalam kategori {categories.name} saat ini.
                                                </p>
                                                <Link
                                                    href="/"
                                                    className="inline-flex items-center gap-2 bg-[#de0b66] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#de0b66]/90 transition-colors"
                                                >
                                                    <ArrowLeftIcon size={16} />
                                                    Kembali ke Beranda
                                                </Link>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </section>
                    </div>
                </main>
            </div>

            {/* Footer */}
            <footer className="bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-300 py-8 border-t border-gray-200 dark:border-gray-700">
                <div className="container mx-auto px-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div>
                            <h4 className="font-bold text-gray-900 dark:text-white mb-3">
                                Lambe Turah
                            </h4>
                            <p className="text-sm">
                                Your trusted source for the latest news and updates.
                            </p>
                        </div>
                        <div>
                            <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                                Quick Links
                            </h4>
                            <ul className="space-y-2 text-sm">
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-gray-900 dark:hover:text-white"
                                    >
                                        About Us
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-gray-900 dark:hover:text-white"
                                    >
                                        Contact
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-gray-900 dark:hover:text-white"
                                    >
                                        Privacy Policy
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 text-center text-sm">
                        &copy; {new Date().getFullYear()} Lambe Turah. All rights reserved.
                    </div>
                </div>
            </footer>
        </>
    );
};

export default CategoryNews;
