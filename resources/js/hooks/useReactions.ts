import { useState, useEffect } from 'react';

interface ReactionCounts {
    suka?: number;
    benci?: number;
    cinta?: number;
    lucu?: number;
    marah?: number;
    sedih?: number;
    wow?: number;
    // Old types for backward compatibility
    like?: number;
    love?: number;
    haha?: number;
    sad?: number;
    angry?: number;
}

interface ReactionData {
    counts: ReactionCounts;
    total_count: number;
    user_reaction: string | null;
}

interface UseReactionsProps {
    reactableType: 'news' | 'comment';
    reactableId: number;
    initialData?: ReactionData;
    autoFetch?: boolean;
}

interface UseReactionsReturn {
    data: ReactionData | null;
    loading: boolean;
    error: string | null;
    refetch: () => Promise<void>;
}

export const useReactions = ({
    reactableType,
    reactableId,
    initialData,
    autoFetch = false,
}: UseReactionsProps): UseReactionsReturn => {
    const [data, setData] = useState<ReactionData | null>(initialData || null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const fetchReactions = async () => {
        if (!reactableId) return;

        setLoading(true);
        setError(null);

        try {
            const response = await fetch('/reactions?' + new URLSearchParams({
                reactable_type: reactableType,
                reactable_id: reactableId.toString(),
            }), {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                setData({
                    counts: result.counts,
                    total_count: result.total_count,
                    user_reaction: result.user_reaction,
                });
            } else {
                throw new Error(result.message || 'Failed to fetch reactions');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Unknown error';
            setError(errorMessage);
            console.error('Error fetching reactions:', err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (autoFetch && !initialData) {
            fetchReactions();
        }
    }, [reactableType, reactableId, autoFetch]);

    return {
        data,
        loading,
        error,
        refetch: fetchReactions,
    };
};

export default useReactions;
