import { useEffect, useCallback, useRef } from "react";
import { useWeatherStore } from "@/stores/weather-store";
import { WeatherLocation } from "@/types/weather";
import {
    convertWeatherUnits,
    formatTime,
    formatDate,
    getWeatherAdvice,
} from "@/lib/weather-codes";

export const useWeather = () => {
    const {
        currentWeather,
        isLoading,
        error,
        selectedLocation,
        savedLocations,
        searchResults,
        isSearching,
        temperatureUnit,
        windSpeedUnit,
        pressureUnit,
        timeFormat,
        autoRefresh,
        refreshInterval,
        lastUpdated,

        // Actions
        fetchWeatherForLocation,
        fetchCurrentLocationWeather,
        searchLocations,
        refreshWeatherData,
        addSavedLocation,
        removeSavedLocation,
        setTemperatureUnit,
        setWindSpeedUnit,
        setPressureUnit,
        setTimeFormat,
        setAutoRefresh,
        setRefreshInterval,
        clearError,
    } = useWeatherStore();

    const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

    // Auto refresh functionality
    useEffect(() => {
        if (autoRefresh && refreshInterval > 0 && currentWeather) {
            refreshIntervalRef.current = setInterval(
                () => {
                    refreshWeatherData();
                },
                refreshInterval * 60 * 1000,
            ); // Convert minutes to milliseconds

            return () => {
                if (refreshIntervalRef.current) {
                    clearInterval(refreshIntervalRef.current);
                }
            };
        }
    }, [autoRefresh, refreshInterval, currentWeather]);

    // Cleanup interval on unmount
    useEffect(() => {
        return () => {
            if (refreshIntervalRef.current) {
                clearInterval(refreshIntervalRef.current);
            }
        };
    }, []);

    const getFormattedWeather = useCallback(() => {
        if (!currentWeather) return null;

        try {
            const { current } = currentWeather;
            const converted = convertWeatherUnits(
                current.temperature,
                current.feelsLike,
                current.windSpeed,
                current.pressure,
                temperatureUnit,
                windSpeedUnit,
                pressureUnit,
            );

            return {
                ...currentWeather,
                formatted: {
                    temperatureValue: current.temperature,
                    temperatureUnit:
                        temperatureUnit === "celsius" ? "°C" : "°F",
                    temperature: converted.temperature,
                    feelsLike: converted.feelsLike,
                    windSpeed: converted.windSpeed,
                    pressure: converted.pressure,
                    time: formatTime(current.time, timeFormat),
                    date: formatDate(current.time),
                    advice: getWeatherAdvice(
                        current.temperature,
                        current.condition.code,
                        current.windSpeed,
                        current.humidity,
                    ),
                },
            };
        } catch (error) {
            console.error("Error formatting weather data:", error);
            return null;
        }
    }, [
        currentWeather,
        temperatureUnit,
        windSpeedUnit,
        pressureUnit,
        timeFormat,
    ]);

    const getFormattedHourlyForecast = useCallback(() => {
        if (!currentWeather?.hourlyForecast) return [];

        try {
            return currentWeather.hourlyForecast.map((hour) => {
                const converted = convertWeatherUnits(
                    hour.temperature,
                    hour.temperature, // Use same temp for feels like in hourly
                    hour.windSpeed,
                    1013, // Default pressure for hourly
                    temperatureUnit,
                    windSpeedUnit,
                    pressureUnit,
                );

                return {
                    ...hour,
                    formatted: {
                        temperature: converted.temperature,
                        windSpeed: converted.windSpeed,
                        time: formatTime(hour.time, timeFormat),
                    },
                };
            });
        } catch (error) {
            console.error("Error formatting hourly forecast:", error);
            return [];
        }
    }, [
        currentWeather?.hourlyForecast,
        temperatureUnit,
        windSpeedUnit,
        pressureUnit,
        timeFormat,
    ]);

    const getFormattedDailyForecast = useCallback(() => {
        if (!currentWeather?.dailyForecast) return [];

        try {
            return currentWeather.dailyForecast.map((day) => {
                const converted = convertWeatherUnits(
                    (day.temperatureMax + day.temperatureMin) / 2, // Average temp
                    (day.temperatureMax + day.temperatureMin) / 2,
                    day.windSpeed,
                    1013, // Default pressure for daily
                    temperatureUnit,
                    windSpeedUnit,
                    pressureUnit,
                    day.temperatureMin,
                    day.temperatureMax,
                );

                return {
                    ...day,
                    formatted: {
                        temperatureRange: converted.temperatureRange,
                        windSpeed: converted.windSpeed,
                        date: formatDate(day.date),
                        shortDate: day.date.toLocaleDateString("en-US", {
                            weekday: "short",
                        }),
                        sunrise: formatTime(day.sunrise, timeFormat),
                        sunset: formatTime(day.sunset, timeFormat),
                    },
                };
            });
        } catch (error) {
            console.error("Error formatting daily forecast:", error);
            return [];
        }
    }, [
        currentWeather?.dailyForecast,
        temperatureUnit,
        windSpeedUnit,
        pressureUnit,
        timeFormat,
    ]);

    const isLocationSaved = useCallback(
        (location: WeatherLocation) => {
            return savedLocations.some(
                (saved) =>
                    Math.abs(saved.latitude - location.latitude) < 0.001 &&
                    Math.abs(saved.longitude - location.longitude) < 0.001,
            );
        },
        [savedLocations],
    );

    const toggleSavedLocation = useCallback(
        (location: WeatherLocation) => {
            if (isLocationSaved(location)) {
                removeSavedLocation(location.latitude, location.longitude);
            } else {
                addSavedLocation(location);
            }
        },
        [isLocationSaved, addSavedLocation, removeSavedLocation],
    );

    const selectLocation = useCallback(
        async (location: WeatherLocation) => {
            try {
                await fetchWeatherForLocation(location);
            } catch (error) {
                console.error("Failed to select location:", error);
            }
        },
        [fetchWeatherForLocation],
    );

    const searchLocation = useCallback(
        async (query: string) => {
            try {
                await searchLocations(query);
            } catch (error) {
                console.error("Failed to search locations:", error);
            }
        },
        [searchLocations],
    );

    const refreshWeather = useCallback(async () => {
        try {
            await refreshWeatherData();
        } catch (error) {
            console.error("Failed to refresh weather:", error);
        }
    }, [refreshWeatherData]);

    const getCurrentLocationWeather = useCallback(async () => {
        try {
            await fetchCurrentLocationWeather();
        } catch (error) {
            console.error("Failed to get current location weather:", error);
        }
    }, [fetchCurrentLocationWeather]);

    const updatePreferences = useCallback(
        (preferences: {
            temperatureUnit?: "celsius" | "fahrenheit";
            windSpeedUnit?: "kmh" | "mph";
            pressureUnit?: "hpa" | "inHg";
            timeFormat?: "12h" | "24h";
            autoRefresh?: boolean;
            refreshInterval?: number;
        }) => {
            if (preferences.temperatureUnit)
                setTemperatureUnit(preferences.temperatureUnit);
            if (preferences.windSpeedUnit)
                setWindSpeedUnit(preferences.windSpeedUnit);
            if (preferences.pressureUnit)
                setPressureUnit(preferences.pressureUnit);
            if (preferences.timeFormat) setTimeFormat(preferences.timeFormat);
            if (preferences.autoRefresh !== undefined)
                setAutoRefresh(preferences.autoRefresh);
            if (preferences.refreshInterval)
                setRefreshInterval(preferences.refreshInterval);
        },
        [
            setTemperatureUnit,
            setWindSpeedUnit,
            setPressureUnit,
            setTimeFormat,
            setAutoRefresh,
            setRefreshInterval,
        ],
    );

    const getTimeSinceLastUpdate = useCallback(() => {
        if (!lastUpdated) return null;

        const now = new Date();
        const diffMs = now.getTime() - lastUpdated.getTime();
        const diffMinutes = Math.floor(diffMs / 60000);

        if (diffMinutes < 1) return "Just now";
        if (diffMinutes === 1) return "1 minute ago";
        if (diffMinutes < 60) return `${diffMinutes} minutes ago`;

        const diffHours = Math.floor(diffMinutes / 60);
        if (diffHours === 1) return "1 hour ago";
        if (diffHours < 24) return `${diffHours} hours ago`;

        const diffDays = Math.floor(diffHours / 24);
        if (diffDays === 1) return "1 day ago";
        return `${diffDays} days ago`;
    }, [lastUpdated]);

    return {
        // State
        weather: getFormattedWeather(),
        hourlyForecast: getFormattedHourlyForecast(),
        dailyForecast: getFormattedDailyForecast(),
        isLoading,
        error,
        selectedLocation,
        savedLocations,
        searchResults,
        isSearching,
        lastUpdated,
        timeSinceLastUpdate: getTimeSinceLastUpdate(),

        // Preferences
        preferences: {
            temperatureUnit,
            windSpeedUnit,
            pressureUnit,
            timeFormat,
            autoRefresh,
            refreshInterval,
        },

        // Actions
        selectLocation,
        searchLocation,
        refreshWeather,
        getCurrentLocationWeather,
        toggleSavedLocation,
        isLocationSaved,
        updatePreferences,
        clearError,

        // Utils
        hasWeatherData: !!currentWeather,
        hasError: !!error,
        isStale:
            lastUpdated &&
            new Date().getTime() - lastUpdated.getTime() >
                refreshInterval * 60 * 1000,
    };
};

export default useWeather;
