import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { WeatherLocation, ProcessedWeatherData } from '@/types/weather';
import WeatherAPI from '@/lib/weather-api';
import LocationStorage from '@/lib/location-storage';

interface WeatherState {
  // Current weather data
  currentWeather: ProcessedWeatherData | null;
  isLoading: boolean;
  error: string | null;
  
  // Location management
  selectedLocation: WeatherLocation | null;
  savedLocations: WeatherLocation[];
  searchResults: WeatherLocation[];
  isSearching: boolean;
  
  // Location permission dialog
  showLocationDialog: boolean;
  locationPermissionStatus: 'pending' | 'granted' | 'denied' | 'manual' | null;
  savedCityName: string | null;
  
  // User preferences
  temperatureUnit: 'celsius' | 'fahrenheit';
  windSpeedUnit: 'kmh' | 'mph';
  pressureUnit: 'hpa' | 'inHg';
  timeFormat: '12h' | '24h';
  autoRefresh: boolean;
  refreshInterval: number; // in minutes
  
  // UI state
  isWeatherWidgetExpanded: boolean;
  showHourlyForecast: boolean;
  showDailyForecast: boolean;
  lastUpdated: Date | null;
  
  // Actions
  setCurrentWeather: (weather: ProcessedWeatherData | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSelectedLocation: (location: WeatherLocation | null) => void;
  addSavedLocation: (location: WeatherLocation) => void;
  removeSavedLocation: (latitude: number, longitude: number) => void;
  setSearchResults: (results: WeatherLocation[]) => void;
  setSearching: (searching: boolean) => void;
  setTemperatureUnit: (unit: 'celsius' | 'fahrenheit') => void;
  setWindSpeedUnit: (unit: 'kmh' | 'mph') => void;
  setPressureUnit: (unit: 'hpa' | 'inHg') => void;
  setTimeFormat: (format: '12h' | '24h') => void;
  setAutoRefresh: (enabled: boolean) => void;
  setRefreshInterval: (interval: number) => void;
  toggleWeatherWidget: () => void;
  setShowHourlyForecast: (show: boolean) => void;
  setShowDailyForecast: (show: boolean) => void;
  
  // Location permission actions
  setShowLocationDialog: (show: boolean) => void;
  setLocationPermissionStatus: (status: 'pending' | 'granted' | 'denied' | 'manual' | null) => void;
  setSavedCityName: (city: string | null) => void;
  checkLocationPermission: () => void;
  handleLocationPermissionGranted: (city: string) => void;
  handleLocationPermissionDenied: () => void;
  
  // Async actions
  fetchWeatherForLocation: (location: WeatherLocation) => Promise<void>;
  fetchWeatherForCoordinates: (latitude: number, longitude: number) => Promise<void>;
  fetchCurrentLocationWeather: () => Promise<void>;
  searchLocations: (query: string) => Promise<void>;
  refreshWeatherData: () => Promise<void>;
  
  // Utility actions
  clearError: () => void;
  reset: () => void;
  getLocationKey: (location: WeatherLocation) => string;
}

const initialState = {
  currentWeather: null,
  isLoading: false,
  error: null,
  selectedLocation: null,
  savedLocations: [],
  searchResults: [],
  isSearching: false,
  showLocationDialog: false,
  locationPermissionStatus: null as 'pending' | 'granted' | 'denied' | 'manual' | null,
  savedCityName: null as string | null,
  temperatureUnit: 'celsius' as const,
  windSpeedUnit: 'kmh' as const,
  pressureUnit: 'hpa' as const,
  timeFormat: '24h' as const,
  autoRefresh: true,
  refreshInterval: 30,
  isWeatherWidgetExpanded: false,
  showHourlyForecast: true,
  showDailyForecast: true,
  lastUpdated: null,
};

export const useWeatherStore = create<WeatherState>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Basic setters
      setCurrentWeather: (weather) => set({ currentWeather: weather, lastUpdated: new Date() }),
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      setSelectedLocation: (location) => set({ selectedLocation: location }),
      setSearchResults: (results) => set({ searchResults: results }),
      setSearching: (searching) => set({ isSearching: searching }),
      setTemperatureUnit: (unit) => set({ temperatureUnit: unit }),
      setWindSpeedUnit: (unit) => set({ windSpeedUnit: unit }),
      setPressureUnit: (unit) => set({ pressureUnit: unit }),
      setTimeFormat: (format) => set({ timeFormat: format }),
      setAutoRefresh: (enabled) => set({ autoRefresh: enabled }),
      setRefreshInterval: (interval) => set({ refreshInterval: interval }),
      toggleWeatherWidget: () => set((state) => ({ isWeatherWidgetExpanded: !state.isWeatherWidgetExpanded })),
      setShowHourlyForecast: (show) => set({ showHourlyForecast: show }),
      setShowDailyForecast: (show) => set({ showDailyForecast: show }),

      // Location permission management
      setShowLocationDialog: (show) => set({ showLocationDialog: show }),
      setLocationPermissionStatus: (status) => set({ locationPermissionStatus: status }),
      setSavedCityName: (city) => set({ savedCityName: city }),
      
      checkLocationPermission: () => {
        const shouldShow = LocationStorage.shouldShowLocationDialog();
        const locationStatus = LocationStorage.getLocationStatus();
        
        set({ 
          showLocationDialog: shouldShow,
          locationPermissionStatus: locationStatus.preferenceType,
          savedCityName: locationStatus.city
        });
        
        if (!shouldShow && locationStatus.city) {
          // Load weather for saved city
          get().fetchCurrentLocationWeather();
        }
      },
      
      handleLocationPermissionGranted: (city) => {
        set({ 
          showLocationDialog: false,
          locationPermissionStatus: 'granted',
          savedCityName: city
        });
        LocationStorage.setSavedCity(city);
      },
      
      handleLocationPermissionDenied: () => {
        const defaultCity = LocationStorage.getDefaultCity();
        set({ 
          showLocationDialog: false,
          locationPermissionStatus: 'denied',
          savedCityName: defaultCity
        });
        LocationStorage.saveLocationPreference({
          type: 'denied',
          city: defaultCity,
          timestamp: Date.now()
        });
      },

      // Location management
      addSavedLocation: (location) => set((state) => {
        const exists = state.savedLocations.some(
          (saved) => Math.abs(saved.latitude - location.latitude) < 0.001 && 
                    Math.abs(saved.longitude - location.longitude) < 0.001
        );
        if (!exists) {
          // Also save to localStorage
          LocationStorage.addSavedLocation({
            city: location.city,
            country: location.country,
            latitude: location.latitude,
            longitude: location.longitude,
            countryCode: location.countryCode,
            timezone: location.timezone
          });
          return { savedLocations: [...state.savedLocations, location] };
        }
        return state;
      }),

      removeSavedLocation: (latitude, longitude) => set((state) => {
        // Also remove from localStorage
        LocationStorage.removeSavedLocation(latitude, longitude);
        return {
          savedLocations: state.savedLocations.filter(
            (location) => !(Math.abs(location.latitude - latitude) < 0.001 && 
                           Math.abs(location.longitude - longitude) < 0.001)
          )
        };
      }),

      getLocationKey: (location) => `${location.latitude.toFixed(3)},${location.longitude.toFixed(3)}`,

      // Async actions
      fetchWeatherForLocation: async (location) => {
        const { setLoading, setError, setCurrentWeather, setSelectedLocation } = get();
        
        try {
          setLoading(true);
          setError(null);
          setSelectedLocation(location);
          
          const weatherData = await WeatherAPI.getCurrentWeather(location.latitude, location.longitude);
          
          // Update location info from API response
          const updatedWeatherData = {
            ...weatherData,
            location: {
              ...weatherData.location,
              city: location.city,
              country: location.country,
              countryCode: location.countryCode,
              timezone: location.timezone
            }
          };
          
          setCurrentWeather(updatedWeatherData);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch weather data';
          setError(errorMessage);
          console.error('Error fetching weather:', error);
        } finally {
          setLoading(false);
        }
      },

      fetchWeatherForCoordinates: async (latitude, longitude) => {
        const { setLoading, setError, setCurrentWeather } = get();
        
        try {
          setLoading(true);
          setError(null);
          
          const weatherData = await WeatherAPI.getCurrentWeather(latitude, longitude);
          
          // Try to get location name from coordinates
          const locationInfo = await WeatherAPI.getLocationFromCoordinates(latitude, longitude);
          if (locationInfo) {
            weatherData.location = locationInfo;
          }
          
          // If location is still unknown, check if these are Jakarta coordinates or set Jakarta as default
          if (weatherData.location.city === 'Unknown' || !weatherData.location.city) {
            // Check if coordinates are close to Jakarta
            const isJakartaCoords = Math.abs(latitude - (-6.2088)) < 0.5 && Math.abs(longitude - 106.8456) < 0.5;
            
            if (isJakartaCoords) {
              weatherData.location = {
                ...weatherData.location,
                city: 'Jakarta',
                country: 'Indonesia',
                countryCode: 'ID',
                timezone: 'Asia/Jakarta'
              };
            }
          }
          
          setCurrentWeather(weatherData);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch weather data';
          setError(errorMessage);
          console.error('Error fetching weather:', error);
        } finally {
          setLoading(false);
        }
      },

      fetchCurrentLocationWeather: async () => {
        const { setLoading, setError, setCurrentWeather, setSavedCityName, setLocationPermissionStatus } = get();
        
        // Check if we have saved location preference
        const locationStatus = LocationStorage.getLocationStatus();
        if (locationStatus.hasPermission && !locationStatus.isExpired) {
          // Use saved location preference
          try {
            setLoading(true);
            setError(null);
            
            const savedPreference = LocationStorage.getLocationPreference();
            if (savedPreference?.coordinates) {
              // Use saved coordinates
              const weatherData = await WeatherAPI.getCurrentWeather(
                savedPreference.coordinates.latitude, 
                savedPreference.coordinates.longitude
              );
              
              // Update with saved city name
              weatherData.location = {
                ...weatherData.location,
                city: savedPreference.city
              };
              
              setCurrentWeather(weatherData);
              setSavedCityName(savedPreference.city);
              setLocationPermissionStatus('granted');
              return;
            } else {
              // Use saved city name to get coordinates (fallback)
              const jakartaWeather = await WeatherAPI.getCurrentWeather(-6.2088, 106.8456);
              jakartaWeather.location = {
                ...jakartaWeather.location,
                city: locationStatus.city,
                country: 'Indonesia',
                countryCode: 'ID',
                timezone: 'Asia/Jakarta'
              };
              setCurrentWeather(jakartaWeather);
              setSavedCityName(locationStatus.city);
              setLocationPermissionStatus('manual');
              return;
            }
          } catch (error) {
            console.warn('Failed to load saved location, trying current location...');
          } finally {
            setLoading(false);
          }
        }
        
        try {
          setLoading(true);
          setError(null);
          
          const position = await WeatherAPI.getCurrentLocation();
          const weatherData = await WeatherAPI.getCurrentWeather(position.latitude, position.longitude);
          
          // Save successful location access
          const cityName = weatherData.location.city === 'Unknown' ? 'Jakarta' : weatherData.location.city;
          LocationStorage.saveLocationPreference({
            type: 'granted',
            city: cityName,
            timestamp: Date.now(),
            coordinates: {
              latitude: position.latitude,
              longitude: position.longitude
            }
          });
          
          // If location comes back as unknown, set Jakarta as default
          if (weatherData.location.city === 'Unknown' || !weatherData.location.city) {
            weatherData.location = {
              ...weatherData.location,
              city: 'Jakarta',
              country: 'Indonesia',
              countryCode: 'ID',
              timezone: 'Asia/Jakarta'
            };
          }
          
          setCurrentWeather(weatherData);
          setSavedCityName(cityName);
          setLocationPermissionStatus('granted');
        } catch (error) {
          // On error, try to load Jakarta weather as fallback
          try {
            console.log('Failed to get user location, falling back to Jakarta...');
            const jakartaWeather = await WeatherAPI.getCurrentWeather(-6.2088, 106.8456);
            jakartaWeather.location = {
              ...jakartaWeather.location,
              city: 'Jakarta',
              country: 'Indonesia',
              countryCode: 'ID',
              timezone: 'Asia/Jakarta'
            };
            setCurrentWeather(jakartaWeather);
            
            // Save Jakarta as fallback
            LocationStorage.saveLocationPreference({
              type: 'manual',
              city: 'Jakarta',
              timestamp: Date.now()
            });
            
            setSavedCityName('Jakarta');
            setLocationPermissionStatus('denied');
            setError('Using Jakarta as default location. You can search for your city manually.');
          } catch (fallbackError) {
            let errorMessage = 'Unable to get weather for your location';
            
            if (error instanceof Error) {
              if (error.message.includes('denied') || error.message.includes('permission')) {
                errorMessage = 'Location permission denied. Please enable location access in your browser or search for a city manually.';
              } else if (error.message.includes('timeout') || error.message.includes('timed out')) {
                errorMessage = 'Location request timed out. You can search for your city manually.';
              } else if (error.message.includes('unavailable')) {
                errorMessage = 'Location unavailable. Please check your GPS or internet connection, or search for a city manually.';
              } else if (error.message.includes('not supported')) {
                errorMessage = 'Geolocation not supported by your browser. Please search for a city manually.';
              } else {
                errorMessage = `Location error: ${error.message}. You can search for your city manually.`;
              }
            }
            
            setError(errorMessage);
            setLocationPermissionStatus('denied');
            console.error('Error getting current location weather:', error);
            console.error('Error with Jakarta fallback:', fallbackError);
          }
        } finally {
          setLoading(false);
        }
      },

      searchLocations: async (query) => {
        const { setSearching, setSearchResults, setError } = get();
        
        if (!query.trim()) {
          setSearchResults([]);
          return;
        }
        
        try {
          setSearching(true);
          setError(null);
          
          const results = await WeatherAPI.searchLocations(query, 10);
          setSearchResults(results);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to search locations';
          setError(errorMessage);
          console.error('Error searching locations:', error);
          setSearchResults([]);
        } finally {
          setSearching(false);
        }
      },

      refreshWeatherData: async () => {
        const { selectedLocation, currentWeather, fetchWeatherForLocation, fetchWeatherForCoordinates } = get();
        
        if (selectedLocation) {
          await fetchWeatherForLocation(selectedLocation);
        } else if (currentWeather?.location) {
          await fetchWeatherForCoordinates(currentWeather.location.latitude, currentWeather.location.longitude);
        }
      },

      // Utility actions
      clearError: () => set({ error: null }),
      
      reset: () => set({
        ...initialState,
        // Keep user preferences and saved locations
        savedLocations: get().savedLocations,
        temperatureUnit: get().temperatureUnit,
        windSpeedUnit: get().windSpeedUnit,
        pressureUnit: get().pressureUnit,
        timeFormat: get().timeFormat,
        autoRefresh: get().autoRefresh,
        refreshInterval: get().refreshInterval,
      }),
    }),
    {
      name: 'weather-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // Persist user preferences and saved locations
        savedLocations: state.savedLocations,
        temperatureUnit: state.temperatureUnit,
        windSpeedUnit: state.windSpeedUnit,
        pressureUnit: state.pressureUnit,
        timeFormat: state.timeFormat,
        autoRefresh: state.autoRefresh,
        refreshInterval: state.refreshInterval,
        showHourlyForecast: state.showHourlyForecast,
        showDailyForecast: state.showDailyForecast,
      }),
    }
  )
);

// Selectors for better performance
export const useWeatherData = () => useWeatherStore((state) => state.currentWeather);
export const useWeatherLoading = () => useWeatherStore((state) => state.isLoading);
export const useWeatherError = () => useWeatherStore((state) => state.error);
export const useSelectedLocation = () => useWeatherStore((state) => state.selectedLocation);
export const useSavedLocations = () => useWeatherStore((state) => state.savedLocations);
export const useWeatherPreferences = () => useWeatherStore((state) => ({
  temperatureUnit: state.temperatureUnit,
  windSpeedUnit: state.windSpeedUnit,
  pressureUnit: state.pressureUnit,
  timeFormat: state.timeFormat,
  autoRefresh: state.autoRefresh,
  refreshInterval: state.refreshInterval,
}));