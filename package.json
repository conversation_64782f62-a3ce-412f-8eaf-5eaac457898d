{"private": true, "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix", "types": "tsc --noEmit"}, "devDependencies": {"@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.19.0", "@types/node": "^22.13.5", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.5.0", "axios": "^1.9.0", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "laravel-vite-plugin": "^1.2.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "resolve-url-loader": "^5.0.0", "sass": "^1.89.1", "sass-loader": "^12.6.0", "ts-loader": "^9.5.2", "tw-animate-css": "^1.3.3", "typescript": "^5.8.3", "typescript-eslint": "^8.23.0", "vite": "^5.4.19"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.0", "@inertiajs/progress": "^0.2.7", "@inertiajs/react": "^2.0.11", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/vite": "^4.1.8", "@types/react": "^19.0.3", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "fast-glob": "^3.3.3", "globals": "^15.14.0", "html-react-parser": "^5.2.5", "laravel-vite-plugin": "^1.0", "lucide-react": "^0.475.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-loading-indicators": "^1.0.1", "react-router-dom": "^7.6.1", "react-spinners": "^0.15.0", "swiper": "^11.2.8", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2", "vite": "^6.0", "zustand": "^5.0.5"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}