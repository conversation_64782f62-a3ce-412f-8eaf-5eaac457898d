<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\FE\LikeController;
use App\Http\Controllers\FE\NewsController;
use App\Http\Controllers\FE\UserController;
use App\Http\Controllers\FE\AdminController;
use App\Http\Controllers\FE\LoginController;
use App\Http\Controllers\FE\CategoryController;
use App\Http\Controllers\FE\NotificationController;
use App\Models\News;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Support\Facades\Schema;

// Inertia Test Route
Route::get("/inertia-welcome", function () {
    return Inertia::render("Welcome", [
        "appName" => config("app.name", "Lambe Turah News"),
        "user" => auth()->user()
            ? [
                "name" => auth()->user()->name,
                "email" => auth()->user()->email,
            ]
            : null,
    ]);
})->name("inertia.welcome");

// Inertia Dashboard Route
Route::get("/inertia-dashboard", function () {
    return Inertia::render("Dashboard", [
        "user" => auth()->user(),
        "stats" => [
            "totalNews" => 25,
            "totalUsers" => 150,
            "totalViews" => 1250,
            "totalLikes" => 340,
        ],
        "recentNews" => [
            [
                "id" => 1,
                "title" => "Breaking: Technology Advances in 2024",
                "status" => "published",
                "created_at" => now()->subDays(2)->toDateString(),
                "views_count" => 124,
            ],
            [
                "id" => 2,
                "title" => "Global Economic Update",
                "status" => "pending",
                "created_at" => now()->subDays(1)->toDateString(),
                "views_count" => 87,
            ],
        ],
    ]);
})
    ->middleware("auth")
    ->name("inertia.dashboard");


// Public Routes - Accessible to all users (authenticated and guest)
Route::get("/reactions", [\App\Http\Controllers\FE\ReactionController::class, "getReactions"])->name("reactions.get");
Route::get("/news/{news}/comments", [\App\Http\Controllers\FE\CommentController::class, "getComments"])->name("comments.get");

// Guest Comments and Reactions - Allow guests to comment and react
Route::post("/guest/news/{news}/comments", [\App\Http\Controllers\FE\CommentController::class, "store"])->name("guest.comments.store");
Route::post("/guest/reactions/toggle", [\App\Http\Controllers\FE\ReactionController::class, "toggleReaction"])->name("guest.reactions.toggle");
Route::post("/guest/news/{news}/react", [\App\Http\Controllers\FE\ReactionController::class, "reactToNews"])->name("guest.news.react");
Route::post("/guest/comments/{comment}/react", [\App\Http\Controllers\FE\ReactionController::class, "reactToComment"])->name("guest.comments.react");

// Demo route for testing NewsReactionBar
Route::get("/demo/reactions", function () {
    return inertia("Demo/ReactionsDemo", [
        'demoNews' => [
            'id' => 1,
            'title' => 'Demo News Article',
            'reactions' => [
                'counts' => [
                    'suka' => 12,
                    'benci' => 0,
                    'cinta' => 20,
                    'lucu' => 20,
                    'marah' => 20,
                    'sedih' => 20,
                    'wow' => 20,
                ],
                'total_count' => 112,
                'user_reaction' => null
            ]
        ]
    ]);
})->name("demo.reactions");

Route::get("/", function () {
    // Helper function to format news with reactions
    $formatNewsWithReactions = function($news) {
        $newsArray = $news->toArray();
        $newsArray['image_url'] = $news->getImageUrl();
        $newsArray['reactions'] = [
            'counts' => $news->getReactionsCounts(),
            'total_count' => $news->getTotalReactionsCount(),
            'user_reaction' => null, // Will be set if user is authenticated
        ];
        $newsArray['comments_count'] = $news->comments_count ?? 0;
        return $newsArray;
    };

    // Wrap all database queries in try-catch for better error handling
    try {
        $latestNews = News::with(['author', 'category', 'reactions'])
            ->where('status', 'Accept')
            ->latest()
            ->take(10)
            ->get()
            ->map($formatNewsWithReactions);
    } catch (\Exception $e) {
        $latestNews = collect();
    }

    try {
        $hotNews = News::with(['author', 'category', 'reactions'])
            ->where('status', 'Accept')
            ->orderBy('views', 'desc')
            ->take(5)
            ->get()
            ->map($formatNewsWithReactions);
    } catch (\Exception $e) {
        $hotNews = collect();
    }

    try {
        $popularNews = News::with(['author', 'category', 'reactions'])
            ->where('status', 'Accept')
            ->withCount('likes')
            ->orderBy('likes_count', 'desc')
            ->take(6)
            ->get()
            ->map($formatNewsWithReactions);
    } catch (\Exception $e) {
        $popularNews = collect();
    }

    try {
        $categories = Category::query()->orderBy('created_at', 'desc')->get();
    } catch (\Exception $e) {
        $categories = collect();
    }

    // Most See - Most viewed news
    try {
        $mostSeeNews = News::with(['author', 'category', 'reactions'])
            ->where('status', 'Accept')
            ->orderBy('views', 'desc')
            ->take(8)
            ->get()
            ->map($formatNewsWithReactions);
    } catch (\Exception $e) {
        $mostSeeNews = collect();
    }

    // Viral News - High engagement news
    try {
        $viralNews = News::with(['author', 'category', 'reactions'])
            ->where('status', 'Accept')
            ->withCount('likes')
            ->where('views', '>', 1000)
            ->orderByRaw('(views + likes_count * 10) DESC')
            ->take(8)
            ->get()
            ->map($formatNewsWithReactions);
    } catch (\Exception $e) {
        $viralNews = collect();
    }

    // Celebrity News - News from Celebrity category
    $celebrityNews = collect();
    try {
        $celebrityCategory = Category::query()->where('name', 'Selebriti')->first();
        if ($celebrityCategory) {
            $celebrityNews = News::with(['author', 'category', 'reactions'])
                ->where('status', 'Accept')
                ->where('category_id', $celebrityCategory->id)
                ->latest()
                ->take(8)
                ->get()
                ->map($formatNewsWithReactions);
        } else {
            // Fallback: get news from any entertainment-related category
            $entertainmentCategories = Category::query()
                ->whereIn('name', ['Hiburan', 'Selebriti', 'Film & Series', 'Music'])
                ->pluck('id');

            if ($entertainmentCategories->isNotEmpty()) {
                $celebrityNews = News::with(['author', 'category', 'reactions'])
                    ->where('status', 'Accept')
                    ->whereIn('category_id', $entertainmentCategories)
                    ->latest()
                    ->take(8)
                    ->get()
                    ->map($formatNewsWithReactions);
            }
        }
    } catch (\Exception $e) {
        $celebrityNews = collect();
    }

    // Tags - with fallback for missing table/data
    $tags = collect();
    try {
        if (Schema::hasTable('tags')) {
            $tags = Tag::query()->orderBy('count', 'desc')->take(20)->get();
        }
    } catch (\Exception $e) {
        $tags = collect();
    }

    return Inertia::render("Homepage", [
        "user" => auth()->user(),
        "latestNews" => $latestNews,
        "hotNews" => $hotNews,
        "popularNews" => $popularNews,
        "mostSeeNews" => $mostSeeNews,
        "viralNews" => $viralNews,
        "celebrityNews" => $celebrityNews,
        "categories" => $categories,
        "tags" => $tags,
    ]);
})->name("home");

Route::get("/news/{news}/show", [NewsController::class, "show"])->name(
    "news.show"
);

Route::get("/news/{categories}/category", [
    NewsController::class,
    "viewCategory",
])->name("news.viewCategory");

// Guest
Route::middleware(["guest"])->group(function () {
    Route::get("/login", [LoginController::class, "login"])->name("login");
    Route::post("/login/submit", [LoginController::class, "loginSubmit"])->name(
        "login.submit"
    );
    Route::get("/register", [LoginController::class, "register"])->name(
        "register"
    );
    Route::post("/register/submit", [
        LoginController::class,
        "registerSubmit",
    ])->name("register.submit");
});

// Authenticated Users Only
Route::middleware(["auth", "online.status"])->group(function () {
    Route::get("/dashboard", [AdminController::class, "dashboard"])->name(
        "dashboard"
    );
    Route::post("/logout", [LoginController::class, "logout"])->name("logout");

    // News interactions (like, view)
    Route::post("/news/{news}/like", [LikeController::class, "likeNews"])->name(
        "news.like"
    );
    Route::get("/news/{news}/view", [NewsController::class, "view"])->name(
        "news.view"
    );

    // Reactions
    Route::post("/reactions/toggle", [\App\Http\Controllers\FE\ReactionController::class, "toggleReaction"])->name("reactions.toggle");
    Route::post("/news/{news}/react", [\App\Http\Controllers\FE\ReactionController::class, "reactToNews"])->name("news.react");
    Route::post("/comments/{comment}/react", [\App\Http\Controllers\FE\ReactionController::class, "reactToComment"])->name("comments.react");

    // Comments
    Route::post("/news/{news}/comments", [\App\Http\Controllers\FE\CommentController::class, "store"])->name("comments.store");
    Route::patch("/comments/{comment}", [\App\Http\Controllers\FE\CommentController::class, "update"])->name("comments.update");
    Route::delete("/comments/{comment}", [\App\Http\Controllers\FE\CommentController::class, "destroy"])->name("comments.destroy");

    // Profile management
    Route::resource("profile", UserController::class)
        ->parameters([
            "profile" => "user",
        ])
        ->only(["edit", "update"]);

    // Notifications
    Route::prefix("notifications")
        ->name("notifications.")
        ->group(function () {
            Route::get("/count", [
                NotificationController::class,
                "unreadNotificationsCount",
            ])->name("count");
            Route::get("/fetch", [
                NotificationController::class,
                "fetchNotifications",
            ])->name("fetch");
            Route::post("/{id}/read", [
                NotificationController::class,
                "markAsRead",
            ])->name("markAsRead");
        });
});

// Comment Moderation
Route::middleware(["role:Super Admin|Editor"])->group(function () {
    Route::get("/admin/comments/moderate", [\App\Http\Controllers\FE\CommentController::class, "moderate"])->name("admin.comments.moderate");
    Route::patch("/comments/{comment}/toggle-approval", [\App\Http\Controllers\FE\CommentController::class, "toggleApproval"])->name("comments.toggle-approval");
});

// Super Admin
Route::middleware(["auth", "online.status", "role:Super Admin"])->group(function () {
    // News
    Route::resource("admin/news", NewsController::class)
        ->names("admin.news")
        ->only(["destroy"]);
    Route::get("/admin/news/manage", [NewsController::class, "manage"])->name(
        "admin.news.manage"
    );
    // Category
    Route::resource("admin/category", CategoryController::class)
        ->names("admin.category")
        ->only(["store", "update", "destroy"]);
    Route::get("/admin/category/manage", [
        CategoryController::class,
        "manage",
    ])->name("admin.category.manage");
    // Users
    Route::resource("admin/users", UserController::class)
        ->only(["index", "destroy"])
        ->names([
            "index" => "admin.users.manage",
            "destroy" => "admin.users.destroy",
        ]);

    Route::patch("/admin/users/{user}/assignRole", [
        UserController::class,
        "assignRole",
    ])->name("admin.users.assignRole");

    // Import WordPress Articles
    Route::get("/admin/import", [\App\Http\Controllers\FE\ImportController::class, "index"])->name("admin.import.index");
    Route::post("/admin/import/wordpress", [\App\Http\Controllers\FE\ImportController::class, "importWordPress"])->name("admin.import.wordpress");
    Route::post("/admin/import/preview", [\App\Http\Controllers\FE\ImportController::class, "previewImport"])->name("admin.import.preview");

    // Missing Categories Management
    Route::get("/admin/import/missing-categories", [\App\Http\Controllers\FE\ImportController::class, "getMissingCategories"])->name("admin.import.missing-categories");
    Route::post("/admin/import/create-categories", [\App\Http\Controllers\FE\ImportController::class, "createMissingCategories"])->name("admin.import.create-categories");
    Route::delete("/admin/import/clear-cache", [\App\Http\Controllers\FE\ImportController::class, "clearMissingCategoriesCache"])->name("admin.import.clear-cache");
});

// Admin News Edit - Accessible by Super Admin (anytime) and Writer (only rejected articles)
Route::middleware(["auth", "online.status"])->group(function () {
    Route::get("/admin/news/{news}/edit", [NewsController::class, "adminEdit"])->name(
        "admin.news.edit"
    );
    Route::put("/admin/news/{news}", [NewsController::class, "adminUpdate"])->name(
        "admin.news.update"
    );
});

// Editor
Route::group(
    ["middleware" => ["permission:Status News|Update Status News"]],
    function () {
        Route::get("/news/staged", [NewsController::class, "staged"])->name(
            "news.staged"
        );
        Route::patch("/news/{news}/updatestatus", [
            NewsController::class,
            "updateStatus",
        ])->name("news.updateStatus");
    }
);

// Writer
Route::group(
    [
        "middleware" => [
            "permission:Create News|Store News|Edit News|Update News|Draft",
        ],
    ],
    function () {
        Route::resource("news", NewsController::class)
            ->names("news")
            ->only(["create", "store", "edit", "update"]);
        Route::get("/news/draft", [NewsController::class, "draft"])->name(
            "news.draft"
        );
    }
);
