<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\CategoryApiController;
use App\Http\Controllers\API\NewsApiController;
use App\Http\Controllers\API\UserAPIController;
use App\Http\Controllers\API\WeatherController;
use Illuminate\Support\Facades\Route;

Route::middleware('auth:sanctum')->group(function () {
    Route::prefix('user')->group(function () {
        Route::get('/', [UserAPIController::class, 'index']);
        Route::get('/me', [UserAPIController::class, 'me']);
    });
});

Route::prefix('news')->group(function () {
    Route::get('/', [NewsApiController::class, 'index']);
});
Route::prefix('category')->group(function () {
    Route::get('/', [CategoryApiController::class, 'index']);
});

Route::prefix('weather')->group(function () {
    Route::get('/', [WeatherController::class, 'current']);
    Route::get('/current', [WeatherController::class, 'current']);
    Route::post('/location', [WeatherController::class, 'currentByLocation']);
    Route::post('/forecast', [WeatherController::class, 'forecast']);
    Route::post('/historical', [WeatherController::class, 'historical']);
    Route::get('/search', [WeatherController::class, 'searchLocations']);
    Route::post('/recommendations', [WeatherController::class, 'withRecommendations']);
    Route::post('/multiple', [WeatherController::class, 'multipleLocations']);
    Route::get('/status', [WeatherController::class, 'status']);
    Route::delete('/cache', [WeatherController::class, 'clearCache']);
});

Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
// Route::post('/logout', [App\Http\Controllers\API\AuthController::class, 'logout']);
