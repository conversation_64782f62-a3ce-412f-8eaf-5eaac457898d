# Missing Categories Detection & Management

## Overview
Fitur ini secara otomatis mendeteksi kategori WordPress yang tidak ada di sistem lokal dan menyediakan tools untuk mengelolanya dengan efisien.

## How It Works

### 1. Detection Process
Saat import WordPress, sistem akan:
1. **Parse wp_terms table** untuk extract semua kategori WordPress
2. **Filter by taxonomy** untuk hanya ambil kategori (bukan tags)
3. **Compare dengan kategori lokal** menggunakan exact match dan similarity scoring
4. **Identify missing categories** yang tidak ada di sistem
5. **Generate mapping suggestions** untuk kategori yang mirip

### 2. Similarity Algorithm
```php
// Menggunakan Levenshtein distance
$similarity = 1 - (levenshtein($wpCategory, $localCategory) / max(strlen($wpCategory), strlen($localCategory)));

// Threshold:
// ≥ 80% = High confidence mapping
// 70-79% = Medium confidence mapping  
// < 70% = Recommend for creation
```

### 3. Cache Storage
Data disimpan dalam Laravel cache dengan struktur:
- **Key Pattern**: `wordpress_import_missing_categories_{timestamp}`
- **TTL**: 24 hours
- **Index**: `wordpress_import_cache_keys` array

## Features

### Web Interface

#### Missing Categories Alert
```php
@if($missingCategoriesSummary['has_missing_categories'])
    <div class="alert alert-warning">
        <h5>Missing WordPress Categories Detected</h5>
        <p>{{ $missingCategoriesSummary['unique_count'] }} unique categories missing</p>
        <button id="viewMissingCategoriesBtn">View Missing Categories</button>
        <button id="createRecommendedCategoriesBtn">Create Recommended</button>
        <button id="clearCacheBtn">Clear Cache</button>
    </div>
@endif
```

#### Category Analysis in Preview
```javascript
// Preview menampilkan:
{
    "category_analysis": {
        "total_wp_categories": 15,
        "existing_matches": 8,
        "missing_categories": [
            {"name": "Technology", "id": 1, "slug": "technology"},
            {"name": "Sports", "id": 2, "slug": "sports"}
        ],
        "suggested_mappings": [
            {
                "wp_category": "Tech News",
                "suggested_local": {"id": 5, "name": "Teknologi"},
                "similarity": 85.5
            }
        ]
    }
}
```

#### Missing Categories Modal
- **Summary**: Total imports, unique missing categories
- **Categories to Create**: Checkbox list dengan select all
- **Mapping Suggestions**: Table dengan confidence score
- **Bulk Actions**: Create selected categories

### API Endpoints

#### Get Missing Categories
```http
GET /admin/import/missing-categories
```
Response:
```json
{
    "success": true,
    "data": {
        "summary": {
            "total_imports": 3,
            "unique_count": 5,
            "has_missing_categories": true,
            "latest_import": "2024-01-01T10:00:00Z"
        },
        "recommendations": {
            "categories_to_create": [...],
            "high_confidence_mappings": [...],
            "creation_recommended": 3,
            "mapping_recommended": 2
        }
    }
}
```

#### Create Categories
```http
POST /admin/import/create-categories
Content-Type: application/json

{
    "category_names": ["Technology", "Sports", "Entertainment"]
}
```

#### Clear Cache
```http
DELETE /admin/import/clear-cache
```

### Command Line Interface

#### List Missing Categories
```bash
php artisan import:missing-categories list
```
Output:
```
=== Missing WordPress Categories Summary ===
Total Import Sessions: 3
Unique Missing Categories: 5
Latest Import: 2024-01-01T10:00:00Z

=== Missing Categories ===
┌─────────────┬──────────────┬─────────────┐
│ Name        │ WordPress ID │ Slug        │
├─────────────┼──────────────┼─────────────┤
│ Technology  │ 1            │ technology  │
│ Sports      │ 2            │ sports      │
└─────────────┴──────────────┴─────────────┘

=== High-Confidence Mapping Suggestions ===
┌─────────────────┬─────────────────────────┬─────────────┐
│ WordPress Cat   │ Suggested Local Cat     │ Similarity  │
├─────────────────┼─────────────────────────┼─────────────┤
│ Tech News       │ Teknologi               │ 85.5%       │
└─────────────────┴─────────────────────────┴─────────────┘
```

#### Create Categories
```bash
# Create all recommended
php artisan import:missing-categories create --all

# Create specific categories
php artisan import:missing-categories create --categories="Technology,Sports"
```

#### Clear Cache
```bash
php artisan import:missing-categories clear
```

## Notification System

### Auto Notification
Setelah import, sistem otomatis mengirim notifikasi ke Super Admin:
```php
$message = sprintf(
    "WordPress Import Alert: %d unique categories are missing. " .
    "Please review and add them in the Import WordPress page. " .
    "Categories: %s",
    $uniqueCount,
    implode(', ', array_slice($categoryNames, 0, 5)) . ($uniqueCount > 5 ? '...' : '')
);
```

### Notification Prevention
- Cek existing notification untuk mencegah spam
- Hanya kirim jika ada kategori yang benar-benar hilang
- Update notification jika ada perubahan signifikan

## Configuration

### Cache Settings
```php
// config/cache.php
'default' => env('CACHE_STORE', 'database'),

// TTL untuk missing categories: 24 hours
Cache::put($key, $data, now()->addHours(24));
```

### Similarity Threshold
```php
// app/Services/CategoryMappingService.php
$threshold = 0.7; // 70% similarity threshold
$highConfidenceThreshold = 0.8; // 80% for high confidence
```

## Best Practices

### Performance
- Cache digunakan untuk menghindari re-parsing SQL berulang kali
- Batch operations untuk create multiple categories
- Lazy loading untuk large category lists

### User Experience
- Progressive disclosure: Alert → Modal → Details
- Clear action buttons dengan loading states
- Confirmation dialogs untuk destructive actions

### Data Integrity
- Validation untuk category names (max 255 chars)
- Slug generation otomatis
- Duplicate detection sebelum creation

## Troubleshooting

### Cache Issues
```bash
# Clear all cache
php artisan cache:clear

# Clear specific missing categories cache
php artisan import:missing-categories clear
```

### Memory Issues
```php
// Untuk file SQL besar, increase memory limit
ini_set('memory_limit', '512M');
```

### Similarity False Positives
```php
// Adjust threshold jika terlalu banyak false positive
$threshold = 0.8; // Increase for stricter matching
```

## Future Enhancements
- [ ] Machine learning untuk improve similarity scoring
- [ ] Category hierarchy support (parent-child)
- [ ] Bulk edit untuk mapping suggestions
- [ ] Export/import category mapping configuration
- [ ] Integration dengan WordPress REST API untuk real-time sync
