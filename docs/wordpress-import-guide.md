# WordPress Import Guide

## Overview
Fitur import WordPress memungkinkan Super Admin untuk mengimpor artikel dari database WordPress ke dalam sistem Lambe Turah News.

## Cara Menggunakan

### 1. Persiapan File SQL
1. Export database WordPress Anda menggunakan phpMyAdmin atau tools lainnya
2. Pastikan file SQL berisi tabel `wp_posts` dengan data artikel
3. File maksimal 50MB dengan ekstensi `.sql` atau `.txt`

### 2. Akses Halaman Import
1. Login sebagai Super Admin
2. Buka menu "Import WordPress" di sidebar dashboard
3. Anda akan melihat form upload dengan berbagai opsi

### 3. Konfigurasi Import
- **SQL File**: Upload file database WordPress
- **Default Category**: Pilih kategori default untuk artikel yang tidak memiliki kategori
- **Default Author**: Pilih penulis default untuk artikel
- **Skip Duplicates**: Centang untuk melewati artikel yang sudah ada
- **Import Images**: (Experimental) Centang untuk mencoba import gambar

### 4. Status Mapping
Tentukan bagaimana status WordPress akan dipetakan:
- **Published Posts** → Accept/Pending/Reject
- **Draft Posts** → Accept/Pending/Reject  
- **Private Posts** → Accept/Pending/Reject

### 5. Preview Import
1. Klik "Preview Import" untuk melihat data yang akan diimpor
2. Review jumlah artikel, rentang tanggal, dan contoh artikel
3. Pastikan data sesuai dengan yang diharapkan

### 6. Mulai Import
1. Setelah preview terlihat benar, klik "Start Import"
2. Tunggu proses selesai
3. Review hasil import (berhasil, dilewati, error)

## Struktur Data WordPress yang Didukung

### Tabel wp_posts
Import akan membaca kolom berikut dari tabel `wp_posts`:
- `post_title` → `title`
- `post_content` → `content`
- `post_date` → `created_at`
- `post_modified` → `updated_at`
- `post_status` → `status` (dengan mapping)
- `post_type` → filter untuk 'post' dan 'page'

### Mapping Data
| WordPress | Lambe Turah News |
|-----------|------------------|
| post_title | title |
| post_content | content |
| post_date | created_at |
| post_modified | updated_at |
| post_status | status (dengan mapping) |
| post_author | user_id (default) |

## Fitur Keamanan

### Validasi File
- Hanya file .sql dan .txt yang diterima
- Maksimal ukuran 50MB
- Validasi struktur SQL dasar

### Kontrol Akses
- Hanya Super Admin yang dapat mengakses fitur ini
- Semua operasi dicatat dalam log sistem

### Backup & Rollback
- Gunakan database transaction untuk konsistensi
- Rollback otomatis jika terjadi error
- Disarankan backup database sebelum import besar

## Troubleshooting

### Error "Failed to parse SQL"
- Pastikan file SQL valid dan berisi tabel wp_posts
- Coba export ulang database dengan format yang berbeda
- Periksa encoding file (harus UTF-8)

### Error "Category not found"
- Pastikan kategori default sudah dipilih
- Periksa apakah kategori masih ada di database

### Error "User not found"
- Pastikan user default sudah dipilih
- Periksa apakah user masih aktif

### Import Lambat
- File besar akan memakan waktu lama
- Gunakan fitur "Skip Duplicates" untuk import ulang
- Pertimbangkan split file jika terlalu besar

## Batasan

### Fitur yang Belum Didukung
- Import kategori WordPress (menggunakan default)
- Import gambar featured (dalam pengembangan)
- Import komentar
- Import meta data custom

### Rekomendasi
- Test dengan file kecil terlebih dahulu
- Backup database sebelum import besar
- Import di luar jam sibuk untuk performa optimal
- Monitor log sistem untuk error

## Contoh SQL yang Didukung

```sql
INSERT INTO `wp_posts` (`ID`, `post_author`, `post_date`, `post_date_gmt`, `post_content`, `post_title`, `post_excerpt`, `post_status`, `comment_status`, `ping_status`, `post_password`, `post_name`, `to_ping`, `pinged`, `post_modified`, `post_modified_gmt`, `post_content_filtered`, `post_parent`, `guid`, `menu_order`, `post_type`, `post_mime_type`, `comment_count`) VALUES
(1, 1, '2024-01-01 10:00:00', '2024-01-01 10:00:00', 'Konten artikel...', 'Judul Artikel', '', 'publish', 'open', 'open', '', 'judul-artikel', '', '', '2024-01-01 10:00:00', '2024-01-01 10:00:00', '', 0, 'http://example.com/?p=1', 0, 'post', '', 0);
```

## Support
Jika mengalami masalah, periksa:
1. Log aplikasi di `storage/logs/laravel.log`
2. Error message di halaman import
3. Konsultasi dengan developer jika diperlukan
