<?php

namespace App\Helpers;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Facades\Auth;

class AuthHelper
{
    /**
     * Get the authenticated user
     *
     * @return Authenticatable|null
     */
    public static function user(): ?Authenticatable
    {
        return Auth::user();
    }

    /**
     * Check if user is authenticated
     *
     * @return bool
     */
    public static function check(): bool
    {
        return Auth::check();
    }

    /**
     * Check if user is a guest
     *
     * @return bool
     */
    public static function guest(): bool
    {
        return Auth::guest();
    }

    /**
     * Get user ID if authenticated
     *
     * @return int|string|null
     */
    public static function id(): int|string|null
    {
        return Auth::id();
    }

    /**
     * Get user data as array for Inertia sharing
     *
     * @return array|null
     */
    public static function userData(): ?array
    {
        $user = self::user();
        
        if (!$user) {
            return null;
        }

        return [
            'id' => $user->getAuthIdentifier(),
            'name' => $user->name ?? '',
            'email' => $user->email ?? '',
            'created_at' => $user->created_at ?? null,
        ];
    }

    /**
     * Check if user has specific role
     *
     * @param string $role
     * @return bool
     */
    public static function hasRole(string $role): bool
    {
        $user = self::user();
        
        if (!$user) {
            return false;
        }

        // Check if user has hasRole method (Spatie permission package)
        if (method_exists($user, 'hasRole')) {
            return $user->hasRole($role);
        }

        return false;
    }

    /**
     * Check if user has specific permission
     *
     * @param string $permission
     * @return bool
     */
    public static function hasPermission(string $permission): bool
    {
        $user = self::user();
        
        if (!$user) {
            return false;
        }

        // Check if user has hasPermissionTo method (Spatie permission package)
        if (method_exists($user, 'hasPermissionTo')) {
            return $user->hasPermissionTo($permission);
        }

        return false;
    }

    /**
     * Get user's roles as array
     *
     * @return array
     */
    public static function roles(): array
    {
        $user = self::user();
        
        if (!$user) {
            return [];
        }

        // Check if user has getRoleNames method (Spatie permission package)
        if (method_exists($user, 'getRoleNames')) {
            $roleNames = $user->getRoleNames();
            return $roleNames ? $roleNames->toArray() : [];
        }

        return [];
    }

    /**
     * Get user's permissions as array
     *
     * @return array
     */
    public static function permissions(): array
    {
        $user = self::user();
        
        if (!$user) {
            return [];
        }

        // Check if user has getAllPermissions method (Spatie permission package)
        if (method_exists($user, 'getAllPermissions')) {
            $permissions = $user->getAllPermissions();
            return $permissions ? $permissions->pluck('name')->toArray() : [];
        }

        return [];
    }

    /**
     * Logout the user
     *
     * @return void
     */
    public static function logout(): void
    {
        Auth::logout();
    }

    /**
     * Login the user
     *
     * @param Authenticatable $user
     * @param bool $remember
     * @return void
     */
    public static function login(Authenticatable $user, bool $remember = false): void
    {
        Auth::login($user, $remember);
    }

    /**
     * Get user's name safely
     *
     * @return string
     */
    public static function userName(): string
    {
        $user = self::user();
        return $user->name ?? 'Guest';
    }

    /**
     * Get user's email safely
     *
     * @return string
     */
    public static function userEmail(): string
    {
        $user = self::user();
        return $user->email ?? '';
    }

    /**
     * Check if current user is super admin
     *
     * @return bool
     */
    public static function isSuperAdmin(): bool
    {
        return self::hasRole('Super Admin');
    }

    /**
     * Check if current user is admin
     *
     * @return bool
     */
    public static function isAdmin(): bool
    {
        return self::hasRole('Admin') || self::isSuperAdmin();
    }

    /**
     * Check if current user is editor
     *
     * @return bool
     */
    public static function isEditor(): bool
    {
        return self::hasRole('Editor') || self::isAdmin();
    }

    /**
     * Check if current user is writer
     *
     * @return bool
     */
    public static function isWriter(): bool
    {
        return self::hasRole('Writer') || self::isEditor();
    }

    /**
     * Get user's profile data for frontend
     *
     * @return array
     */
    public static function profileData(): array
    {
        $user = self::user();
        
        if (!$user) {
            return [];
        }

        return [
            'id' => $user->getAuthIdentifier(),
            'name' => self::userName(),
            'email' => self::userEmail(),
            'roles' => self::roles(),
            'permissions' => self::permissions(),
            'is_super_admin' => self::isSuperAdmin(),
            'is_admin' => self::isAdmin(),
            'is_editor' => self::isEditor(),
            'is_writer' => self::isWriter(),
            'created_at' => $user->created_at ?? null,
            'updated_at' => $user->updated_at ?? null,
        ];
    }
}