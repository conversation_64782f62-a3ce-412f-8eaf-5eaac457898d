<?php

namespace App\Helpers;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class ImageHelper
{
    /**
     * Upload an image with proper permissions
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param array $options
     * @return string|false
     */
    public static function uploadImage(UploadedFile $file, string $directory = 'images', array $options = []): string|false
    {
        // Validate image
        if (!self::isValidImage($file)) {
            return false;
        }

        // Generate unique filename
        $filename = self::generateFilename($file, $options['prefix'] ?? null);
        
        // Ensure directory exists with proper permissions
        self::ensureDirectoryExists($directory);
        
        // Store the file
        $path = $file->storeAs($directory, $filename, 'public');
        
        if ($path) {
            // Set proper file permissions
            self::setFilePermissions($path);
            
            // Resize image if needed
            if (isset($options['resize'])) {
                self::resizeImage($path, $options['resize']);
            }
            
            return $path;
        }
        
        return false;
    }

    /**
     * Validate if the uploaded file is a valid image
     *
     * @param UploadedFile $file
     * @return bool
     */
    public static function isValidImage(UploadedFile $file): bool
    {
        $allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        
        return in_array($file->getMimeType(), $allowedMimes) && 
               in_array(strtolower($file->getClientOriginalExtension()), $allowedExtensions) &&
               $file->getSize() <= 10 * 1024 * 1024; // 10MB max
    }

    /**
     * Generate a unique filename for the uploaded image
     *
     * @param UploadedFile $file
     * @param string|null $prefix
     * @return string
     */
    public static function generateFilename(UploadedFile $file, ?string $prefix = null): string
    {
        $extension = $file->getClientOriginalExtension();
        $hash = Str::random(40);
        
        return ($prefix ? $prefix . '_' : '') . $hash . '.' . $extension;
    }

    /**
     * Ensure directory exists with proper permissions
     *
     * @param string $directory
     * @return void
     */
    public static function ensureDirectoryExists(string $directory): void
    {
        $fullPath = storage_path("app/public/{$directory}");
        
        if (!file_exists($fullPath)) {
            mkdir($fullPath, 0755, true);
        } else {
            chmod($fullPath, 0755);
        }
    }

    /**
     * Set proper file permissions for uploaded image
     *
     * @param string $path
     * @return void
     */
    public static function setFilePermissions(string $path): void
    {
        $fullPath = storage_path("app/public/{$path}");
        
        if (file_exists($fullPath)) {
            chmod($fullPath, 0644);
        }
    }

    /**
     * Resize image to specified dimensions
     *
     * @param string $path
     * @param array $dimensions
     * @return void
     */
    public static function resizeImage(string $path, array $dimensions): void
    {
        $fullPath = storage_path("app/public/{$path}");
        
        if (file_exists($fullPath) && class_exists('Intervention\Image\Facades\Image')) {
            $image = \Intervention\Image\Facades\Image::make($fullPath);
            
            if (isset($dimensions['width']) && isset($dimensions['height'])) {
                $image->resize($dimensions['width'], $dimensions['height'], function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            } elseif (isset($dimensions['width'])) {
                $image->widen($dimensions['width']);
            } elseif (isset($dimensions['height'])) {
                $image->heighten($dimensions['height']);
            }
            
            $image->save($fullPath, $dimensions['quality'] ?? 85);
        }
    }

    /**
     * Get the full URL for an image
     *
     * @param string|null $path
     * @return string|null
     */
    public static function getImageUrl(?string $path): ?string
    {
        if (!$path) {
            return null;
        }
        
        return Storage::url($path);
    }

    /**
     * Delete an image file
     *
     * @param string $path
     * @return bool
     */
    public static function deleteImage(string $path): bool
    {
        return Storage::disk('public')->delete($path);
    }

    /**
     * Fix permissions for all images in storage
     *
     * @return void
     */
    public static function fixAllImagePermissions(): void
    {
        $publicPath = storage_path('app/public');
        
        // Fix main directory permissions
        if (file_exists($publicPath)) {
            chmod($publicPath, 0755);
        }
        
        // Fix images directory permissions
        $imagesPath = $publicPath . '/images';
        if (file_exists($imagesPath)) {
            chmod($imagesPath, 0755);
            
            // Fix all image file permissions
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($imagesPath)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    chmod($file->getPathname(), 0644);
                } elseif ($file->isDir() && !in_array($file->getFilename(), ['.', '..'])) {
                    chmod($file->getPathname(), 0755);
                }
            }
        }
    }

    /**
     * Get image dimensions
     *
     * @param string $path
     * @return array|null
     */
    public static function getImageDimensions(string $path): ?array
    {
        $fullPath = storage_path("app/public/{$path}");
        
        if (!file_exists($fullPath)) {
            return null;
        }
        
        $imageInfo = getimagesize($fullPath);
        
        if ($imageInfo === false) {
            return null;
        }
        
        return [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'mime' => $imageInfo['mime']
        ];
    }

    /**
     * Create thumbnail for an image
     *
     * @param string $originalPath
     * @param int $width
     * @param int $height
     * @return string|false
     */
    public static function createThumbnail(string $originalPath, int $width = 150, int $height = 150): string|false
    {
        if (!class_exists('Intervention\Image\Facades\Image')) {
            return false;
        }
        
        $fullPath = storage_path("app/public/{$originalPath}");
        
        if (!file_exists($fullPath)) {
            return false;
        }
        
        $pathInfo = pathinfo($originalPath);
        $thumbnailPath = $pathInfo['dirname'] . '/thumb_' . $pathInfo['basename'];
        $thumbnailFullPath = storage_path("app/public/{$thumbnailPath}");
        
        $image = \Intervention\Image\Facades\Image::make($fullPath);
        $image->fit($width, $height);
        $image->save($thumbnailFullPath, 85);
        
        // Set proper permissions
        chmod($thumbnailFullPath, 0644);
        
        return $thumbnailPath;
    }
}