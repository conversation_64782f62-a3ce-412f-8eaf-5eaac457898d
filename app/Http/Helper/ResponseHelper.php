<?php

namespace App\Http\Helper;

use App\Enums\NetworkCode;
use App\Enums\NetworkStatus;
use App\Models\Response\ErrorResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;

class ResponseHelper
{
    public static function formatPagination(LengthAwarePaginator $result): JsonResponse
    {
        $response = [
            'status'        => true,
            'message'       => "Success",
            'total'         => $result->total(),
            'perPage'       => $result->perPage(),
            'currentPage'   => $result->currentPage(),
            'data'          => $result->getCollection(),
        ];

        return response()->json($response, 200);
    }

    public static function successResponse($data, String $msg = 'Success'): JsonResponse
    {
        $response = [
            'status'        => true,
            'message'       => $msg,
            'data'          => $data,
        ];
        return response()->json($response, 200);
    }

    public static function failedResponse(
        $data,
        String $msg = 'Failed',
        ?int $errorCode = null,
        NetworkCode $code = NetworkCode::ERROR,
        $line = 0
    ): JsonResponse {
        $error = new ErrorResponse($code, $msg, $errorCode);
        $response = [
            'status'        => false,
            'message'       => $error->message,
            'errorCode'     => $error->errorCode,
        ];
        if (env('APP_DEBUG')) {
            $response['data']    = $data;
            $response['line']    = $line;
        }
        return response()->json($response, $error->networkCode->value);
    }

    public static function unauthorizedResponse($data, String $msg = NetworkStatus::UNAUTHORIZED->value): JsonResponse
    {
        $error = new ErrorResponse(NetworkCode::UNAUTHORIZED, $msg, null, null);
        $response = [
            'status'        => false,
            'message'       => $error->message,
            'errorCode'     => $error->errorCode,
        ];
        return response()->json($response, $error->networkCode->value);
    }
}
