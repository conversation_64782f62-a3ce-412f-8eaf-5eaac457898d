<?php

namespace App\Http\Helper;

use Illuminate\Support\Facades\Log;

class RequestHelper
{
    public static function sendCallback($token, $params, $urlCallback, bool $isEncode = true)
    {
        // dd($isEncode ? json_encode($params) : $params);
        // dd($urlCallback);
        $curl = curl_init();
        $header = array(
            "Token: $token",
            'Content-Type: ' .  $isEncode ? 'application/json' : 'application/x-www-form-urlencoded'
        );

        curl_setopt_array($curl, array(
            CURLOPT_URL => $urlCallback,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $isEncode ? json_encode($params) : $params,
            CURLOPT_HTTPHEADER => $header,
        ));
        Log::info("Sending Request", [$urlCallback, $params, $token]);
        $response = curl_exec($curl);

        curl_close($curl);

        Log::info("Result Callback", [$response]);
        return $response;
        // echo $response;

    }
}
