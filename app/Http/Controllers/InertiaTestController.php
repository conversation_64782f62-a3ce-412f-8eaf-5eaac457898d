<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Helpers\AuthHelper;
use Inertia\Inertia;
use Inertia\Response;

class InertiaTestController extends Controller
{
    public function welcome(): Response
    {
        $user = AuthHelper::user();
        
        return Inertia::render('Welcome', [
            'appName' => config('app.name', 'Lambe Turah News'),
            'user' => $user ? [
                'name' => $user->name,
                'email' => $user->email,
            ] : null
        ]);
    }

    public function dashboard(): Response
    {
        return Inertia::render('Dashboard', [
            'user' => AuthHelper::user(),
            'stats' => [
                'totalNews' => 25,
                'totalUsers' => 150,
                'totalViews' => 1250,
                'totalLikes' => 340,
            ],
            'recentNews' => [
                [
                    'id' => 1,
                    'title' => 'Breaking: Technology Advances in 2024',
                    'status' => 'published',
                    'created_at' => now()->subDays(2)->toDateString(),
                    'views_count' => 124,
                ],
                [
                    'id' => 2,
                    'title' => 'Global Economic Update',
                    'status' => 'pending',
                    'created_at' => now()->subDays(1)->toDateString(),
                    'views_count' => 87,
                ],
            ]
        ]);
    }

    public function newsIndex(Request $request): Response
    {
        $sampleNews = collect([
            [
                'id' => 1,
                'title' => 'Breaking: Technology Advances in 2024',
                'excerpt' => 'Discover the latest technological breakthroughs that are shaping our future.',
                'content' => 'Full content here...',
                'slug' => 'technology-advances-2024',
                'featured_image' => 'https://via.placeholder.com/400x300',
                'author' => ['id' => 1, 'name' => 'John Doe'],
                'category' => ['id' => 1, 'name' => 'Technology', 'slug' => 'technology'],
                'status' => 'published',
                'views_count' => 124,
                'likes_count' => 15,
                'created_at' => now()->subDays(2)->toISOString(),
                'updated_at' => now()->subDays(2)->toISOString(),
            ],
            [
                'id' => 2,
                'title' => 'Global Economic Update',
                'excerpt' => 'Latest updates on global economic trends and market analysis.',
                'content' => 'Full content here...',
                'slug' => 'global-economic-update',
                'featured_image' => 'https://via.placeholder.com/400x300',
                'author' => ['id' => 2, 'name' => 'Jane Smith'],
                'category' => ['id' => 2, 'name' => 'Economics', 'slug' => 'economics'],
                'status' => 'published',
                'views_count' => 87,
                'likes_count' => 12,
                'created_at' => now()->subDays(1)->toISOString(),
                'updated_at' => now()->subDays(1)->toISOString(),
            ],
            [
                'id' => 3,
                'title' => 'Climate Change Solutions',
                'excerpt' => 'Innovative approaches to combat climate change around the world.',
                'content' => 'Full content here...',
                'slug' => 'climate-change-solutions',
                'featured_image' => 'https://via.placeholder.com/400x300',
                'author' => ['id' => 3, 'name' => 'Mike Johnson'],
                'category' => ['id' => 3, 'name' => 'Environment', 'slug' => 'environment'],
                'status' => 'published',
                'views_count' => 203,
                'likes_count' => 45,
                'created_at' => now()->subDays(3)->toISOString(),
                'updated_at' => now()->subDays(3)->toISOString(),
            ]
        ]);

        $categories = [
            ['id' => 1, 'name' => 'Technology', 'slug' => 'technology'],
            ['id' => 2, 'name' => 'Economics', 'slug' => 'economics'],
            ['id' => 3, 'name' => 'Environment', 'slug' => 'environment'],
            ['id' => 4, 'name' => 'Politics', 'slug' => 'politics'],
            ['id' => 5, 'name' => 'Sports', 'slug' => 'sports'],
        ];

        // Filter by search
        if (request('search')) {
            $searchTerm = request('search');
            $sampleNews = $sampleNews->filter(function ($article) use ($searchTerm) {
                return str_contains(strtolower($article['title']), strtolower($searchTerm)) ||
                       str_contains(strtolower($article['excerpt']), strtolower($searchTerm));
            });
        }

        // Filter by category
        if (request('category')) {
            $categorySlug = request('category');
            $sampleNews = $sampleNews->filter(function ($article) use ($categorySlug) {
                return $article['category']['slug'] === $categorySlug;
            });
        }

        // Simulate pagination
        $perPage = 10;
        $currentPage = request('page', 1);
        $total = $sampleNews->count();
        $items = $sampleNews->slice(($currentPage - 1) * $perPage, $perPage)->values();

        $currentUrl = url()->current();
        
        $pagination = [
            'current_page' => $currentPage,
            'data' => $items,
            'first_page_url' => $currentUrl . '?page=1',
            'from' => ($currentPage - 1) * $perPage + 1,
            'last_page' => ceil($total / $perPage),
            'last_page_url' => $currentUrl . '?page=' . ceil($total / $perPage),
            'links' => [],
            'next_page_url' => $currentPage < ceil($total / $perPage) ? $currentUrl . '?page=' . ($currentPage + 1) : null,
            'path' => $currentUrl,
            'per_page' => $perPage,
            'prev_page_url' => $currentPage > 1 ? $currentUrl . '?page=' . ($currentPage - 1) : null,
            'to' => min($currentPage * $perPage, $total),
            'total' => $total,
        ];

        return Inertia::render('News/Index', [
            'news' => $pagination,
            'filters' => [
                'search' => request('search'),
                'category' => request('category'),
            ],
            'categories' => $categories,
        ]);
    }
}
