<?php

namespace App\Http\Controllers\FE;
use App\Http\Controllers\Controller;

use App\Models\News;
use App\Models\Category;
use App\Models\Comment;
use App\Events\NewsCreated;
use Illuminate\Http\Request;
use App\Events\NewsStatusUpdated;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class NewsController extends Controller
{
    /**
     * Helper method to format news with reaction data
     */
    private function formatNewsWithReactions($news)
    {
        $newsArray = $news->toArray();
        $newsArray['reactions'] = [
            'counts' => $news->getReactionsCounts(),
            'total_count' => $news->getTotalReactionsCount(),
            'user_reaction' => null, // Will be set if user is authenticated
        ];
        $newsArray['comments_count'] = $news->comments_count ?? 0;
        return $newsArray;
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $latestNews = News::where('status', 'Accept')
            ->with(['author', 'category', 'reactions'])
            ->latest()
            ->take(10)
            ->get()
            ->map(function($news) {
                return $this->formatNewsWithReactions($news);
            });

        $topNews = News::where('status', 'Accept')
            ->with(['author', 'category', 'reactions'])
            ->orderBy('views', 'desc')
            ->take(10)
            ->get()
            ->map(function($news) {
                return $this->formatNewsWithReactions($news);
            });

        $popularNews = News::where('status', 'Accept')
            ->with(['author', 'category', 'reactions'])
            ->withCount('likes')
            ->orderBy('likes_count', 'desc')
            ->take(10)
            ->get()
            ->map(function($news) {
                return $this->formatNewsWithReactions($news);
            });
        $topCategory = Category::orderBy('views', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get reaction types from Reaction model
        $reactionTypes = \App\Models\Reaction::REACTION_TYPES;

        return \Inertia\Inertia::render('Homepage', [
            'user' => auth()->user(),
            'latestNews' => $latestNews,
            'topNews' => $topNews,
            'popularNews' => $popularNews,
            'categories' => $topCategory,
            'reactionTypes' => $reactionTypes,
            'pageTitle' => 'Lambe Turah News - Berita Terkini',
        ]);
    }

    public function manage()
    {
        $allNews = News::with('category')->get();
        return view('admin.news.manage', compact('allNews'));
    }

    /**
     * Show the form for editing news from admin panel with role-based access
     */
    public function adminEdit(News $news)
    {
        // Check access permissions
        if (!$this->canEditNews($news)) {
            abort(403, 'You do not have permission to edit this article.');
        }

        $allCategory = Category::all();
        return view('admin.news.edit', compact('news', 'allCategory'));
    }

    /**
     * Update news from admin panel with role-based access
     */
    public function adminUpdate(Request $request, News $news)
    {
        // Check access permissions
        if (!$this->canEditNews($news)) {
            abort(403, 'You do not have permission to edit this article.');
        }

        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'image' => 'nullable|image|mimes:jpeg,jpg,png|max:2048',
                'image_alt' => 'nullable|string|max:255',
                'category_id' => 'required|exists:category,id'
            ]);

            $data = [
                'title' => $request->title,
                'content' => $request->content,
                'category_id' => $request->category_id,
                'image_alt' => $request->image_alt,
            ];

            // Handle image upload
            if ($request->hasFile('image')) {
                $image = $request->file('image');
                $image->storeAs('public/images/', $image->hashName());

                // Delete old image if exists
                if ($news->image) {
                    Storage::delete('public/images/' . $news->image);
                }

                $data['image'] = $image->hashName();
            }

            // If user is Writer and article was rejected, reset status to Pending
            if (auth()->user()->hasRole('Writer') && $news->status === 'Reject') {
                $data['status'] = 'Pending';
            }

            $news->update($data);

            return redirect()->route('admin.news.manage')->with('success', 'News updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update news: ' . $e->getMessage());
        }
    }

    /**
     * Check if current user can edit the given news article
     */
    private function canEditNews(News $news): bool
    {
        $user = auth()->user();

        // Super Admin can edit anytime
        if ($user->hasRole('Super Admin')) {
            return true;
        }

        // Writer can only edit rejected articles
        if ($user->hasRole('Writer') && $news->status === 'Reject') {
            return true;
        }

        return false;
    }

    public function viewCategory(Category $categories)
    {
        $latestNews = $categories->news()
            ->with(['author', 'category'])
            ->where('status', 'Accept')
            ->latest()
            ->get()
            ->map(function($news) {
                $newsArray = $news->toArray();
                $newsArray['image_url'] = $news->getImageUrl();
                return $newsArray;
            });
            
        $topNews = $categories->news()
            ->with(['author', 'category'])
            ->where('status', 'Accept')
            ->orderBy('views', 'desc')
            ->get()
            ->map(function($news) {
                $newsArray = $news->toArray();
                $newsArray['image_url'] = $news->getImageUrl();
                return $newsArray;
            });
            
        $popularNews = $categories->news()
            ->with(['author', 'category'])
            ->where('status', 'Accept')
            ->withCount('likes')
            ->orderBy('likes_count', 'desc')
            ->get()
            ->map(function($news) {
                $newsArray = $news->toArray();
                $newsArray['image_url'] = $news->getImageUrl();
                return $newsArray;
            });

        $categories->increment('views');

        return Inertia::render('CategoryNews', [
            'user' => auth()->user(),
            'categories' => $categories,
            'latestNews' => $latestNews,
            'topNews' => $topNews,
            'popularNews' => $popularNews,
            
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $allCategory = Category::all();
        return view('news.create', compact('allCategory'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'title' => 'required|string|min:1',
                'content' => 'required|string|min:1',
                'image' => 'nullable|image|mimes:jpeg,jpg,png|max:2048',
                'image_alt' => 'nullable|string|max:255',
                'category_id' => 'required|exists:category,id',
            ]);

            $imageHashName = null;

            if ($request->hasFile('image')) {
                $image = $request->file('image');
                $imageHashName = $image->hashName();
                $image->storeAs('public/images', $imageHashName);
            }

            $news = News::create([
                'title' => $request->title,
                'content' => $request->content,
                'user_id' => Auth::id(),
                'category_id' => $request->category_id,
                'image' => $imageHashName,
                'image_alt' => $request->image_alt,
            ]);

            event(new NewsCreated($news));

            return response()->json([
                'success' => true,
                'message' => 'Successfully saved the data.',
                'redirect_url' => route('dashboard')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, News $news)
    {
        // Get or create device ID for guest users
        if (!$request->session()->has('device_id')) {
            $deviceId = Str::uuid()->toString();
            $request->session()->put('device_id', $deviceId);
        } else {
            $deviceId = $request->session()->get('device_id');
        }

        $userId = auth()->id();

        $randomNews = News::with(['author', 'category'])
            ->where('status', 'Accept')
            ->where('id', '!=', $news->id)
            ->inRandomOrder()
            ->take(2)
            ->get();
        
        $news->increment('views');
        
        // Load relationships for the main news
        $news->load(['author', 'category', 'reactions']);

        // Get user's reaction to this news
        $userReaction = $news->reactions()
            ->where(function($q) use ($userId, $deviceId) {
                if ($userId) {
                    $q->where('user_id', $userId);
                } elseif ($deviceId) {
                    $q->where('device_id', $deviceId);
                }
            })
            ->first();

        // Get reaction counts using the Reactable trait method
        $reactionCounts = $news->getReactionsCounts();

        // Get comments count from database
        $commentsCount = Comment::where('news_id', $news->id)
            ->where('is_approved', true)
            ->count();

        return Inertia::render('NewsDetail', [
            'user' => auth()->user(),
            'news' => array_merge($news->toArray(), [
                'reactions' => [
                    'counts' => $reactionCounts,
                    'total_count' => array_sum($reactionCounts),
                    'user_reaction' => $userReaction ? $userReaction->reaction_type : null,
                ],
                'comments_count' => $commentsCount,
            ]),
            'randomNews' => $randomNews->map(function($item) {
                return $item->toArray();
            }),
            'deviceId' => $deviceId,
            'reactionTypes' => \App\Models\Reaction::REACTION_TYPES,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(News $news)
    {
        $allCategory = Category::all();

        if ($news->status != 'Reject') {
            return redirect()->back()->with('error', 'News can only be edited if it is rejected.');
        }

        return view('news.edit', compact('news', 'allCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, News $news)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'content' => 'required|string|max:255',
                'image' => 'nullable|image|mimes:jpeg,jpg,png|max:2048',
                'image_alt' => 'nullable|string|max:255',
                'category_id' => 'required|exists:category,id'
            ]);

            $data = [
                'title' => $request->title,
                'content' => $request->content,
                'user_id' => Auth::id(),
                'category_id' => $request->category_id,
                'image_alt' => $request->image_alt,
            ];

            if ($request->hasFile('image')) {
                $image = $request->file('image');
                $image->storeAs('public/images/', $image->hashName());

                Storage::delete('public/images/' . $news->image);

                $data['image'] = $image->hashName();
            }

            $news->update($data);

            event(new NewsCreated($news));

            return response()->json([
                'success' => true,
                'message' => 'Successfully update the data.',
                'redirect_url' => route('dashboard')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(News $news)
    {
        try {
            Storage::delete('public/images/' . $news->image);
            $news->delete();

            return response()->json([
                'success' => true,
                'message' => 'Successfully delete the data.',
                'redirect_url' => route('admin.news.manage')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function staged(Request $request, News $news)
    {
        // Show articles that are staged (pending or rejected) for review
        $stagedNews = News::with(['category', 'author'])
            ->whereIn('status', ['Pending', 'Reject'])
            ->orderBy('created_at', 'desc')
            ->get();

        return view('news.staged', compact('stagedNews'));
    }

    public function view(Request $request, News $news)
    {
        return view('news.view', compact('news'));
    }

    public function updateStatus(Request $request, News $news)
    {
        try {
            $request->validate([
                'status' => 'required'
            ]);

            $news->status = $request->status;
            $news->save();

            event(new NewsStatusUpdated($news));

            return response()->json([
                'success' => true,
                'message' => 'Successfully updated status the news.',
                'redirect_url' => route('news.staged')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function draft()
    {
        $userId = auth()->id();

        $acceptedNews = News::with('category')
            ->where('status', 'Accept')
            ->where('user_id', $userId)
            ->get();

        $notAcceptedNews = News::with('category')
            ->whereIn('status', ['Pending', 'Reject'])
            ->where('user_id', $userId)
            ->get();

        return view('admin.users.draft', compact('acceptedNews', 'notAcceptedNews'));
    }
}
