<?php

namespace App\Http\Controllers\FE;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\Comment;
use App\Models\Reaction;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ReactionController extends Controller
{
    public function toggleReaction(Request $request)
    {
        $request->validate([
            'reactable_type' => 'required|in:news,comment',
            'reactable_id' => 'required|integer',
            'reaction_type' => 'required|in:like,love,wow,haha,sad,angry,suka,benci,cinta,lucu,marah,sedih',
        ]);

        // Get or create device ID for guest users
        if (!$request->session()->has('device_id')) {
            $deviceId = Str::uuid()->toString();
            $request->session()->put('device_id', $deviceId);
        } else {
            $deviceId = $request->session()->get('device_id');
        }

        $userId = auth()->id();
        $reactableType = $request->reactable_type;
        $reactableId = $request->reactable_id;
        $reactionType = $request->reaction_type;

        // Get the reactable model
        $reactable = $this->getReactableModel($reactableType, $reactableId);
        
        if (!$reactable) {
            return response()->json([
                'success' => false,
                'message' => 'Resource not found'
            ], 404);
        }

        // Toggle the reaction
        $result = $reactable->toggleReaction($reactionType, $userId, $deviceId);

        // Return JSON response for AJAX requests
        return response()->json([
            'success' => true,
            'action' => $result['action'],
            'reaction_type' => $result['reaction_type'],
            'counts' => $result['counts'],
            'total_count' => array_sum($result['counts']),
            'user_reaction' => $result['action'] === 'removed' ? null : $result['reaction_type']
        ]);
    }

    public function getReactions(Request $request)
    {
        $request->validate([
            'reactable_type' => 'required|in:news,comment',
            'reactable_id' => 'required|integer',
        ]);

        $deviceId = $request->session()->get('device_id');
        $userId = auth()->id();
        $reactableType = $request->reactable_type;
        $reactableId = $request->reactable_id;

        $reactable = $this->getReactableModel($reactableType, $reactableId);
        
        if (!$reactable) {
            return response()->json([
                'success' => false,
                'message' => 'Resource not found'
            ], 404);
        }

        $counts = $reactable->getReactionsCounts();
        $userReaction = $reactable->hasUserReacted($userId, $deviceId);

        return response()->json([
            'success' => true,
            'counts' => $counts,
            'total_count' => array_sum($counts),
            'user_reaction' => $userReaction ? $userReaction->reaction_type : null,
            'reaction_types' => Reaction::REACTION_TYPES
        ]);
    }

    public function reactToNews(Request $request, News $news)
    {
        $request->validate([
            'reaction_type' => 'required|in:like,love,wow,haha,sad,angry,suka,benci,cinta,lucu,marah,sedih',
        ]);

        // Get or create device ID for guest users
        if (!$request->session()->has('device_id')) {
            $deviceId = Str::uuid()->toString();
            $request->session()->put('device_id', $deviceId);
        } else {
            $deviceId = $request->session()->get('device_id');
        }

        $userId = auth()->id();
        $reactionType = $request->reaction_type;

        $result = $news->toggleReaction($reactionType, $userId, $deviceId);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'action' => $result['action'],
                'reaction_type' => $result['reaction_type'],
                'counts' => $result['counts'],
                'total_count' => array_sum($result['counts']),
                'user_reaction' => $result['action'] === 'removed' ? null : $result['reaction_type']
            ]);
        }

        return back()->with('success', 'Reaction updated successfully');
    }

    public function reactToComment(Request $request, Comment $comment)
    {
        $request->validate([
            'reaction_type' => 'required|in:like,love,wow,haha,sad,angry,suka,benci,cinta,lucu,marah,sedih',
        ]);

        // Get or create device ID for guest users
        if (!$request->session()->has('device_id')) {
            $deviceId = Str::uuid()->toString();
            $request->session()->put('device_id', $deviceId);
        } else {
            $deviceId = $request->session()->get('device_id');
        }

        $userId = auth()->id();
        $reactionType = $request->reaction_type;

        $result = $comment->toggleReaction($reactionType, $userId, $deviceId);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'action' => $result['action'],
                'reaction_type' => $result['reaction_type'],
                'counts' => $result['counts'],
                'total_count' => array_sum($result['counts']),
                'user_reaction' => $result['action'] === 'removed' ? null : $result['reaction_type']
            ]);
        }

        return back()->with('success', 'Reaction updated successfully');
    }

    private function getReactableModel(string $type, int $id)
    {
        switch ($type) {
            case 'news':
                return News::find($id);
            case 'comment':
                return Comment::find($id);
            default:
                return null;
        }
    }
}