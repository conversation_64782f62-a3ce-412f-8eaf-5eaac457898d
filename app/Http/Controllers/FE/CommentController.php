<?php

namespace App\Http\Controllers\FE;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\Comment;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class CommentController extends Controller
{
    public function store(Request $request, News $news)
    {
        $request->validate([
            'content' => 'required|string|min:1|max:1000',
            'parent_id' => 'nullable|exists:comments,id',
            'guest_name' => 'required_if:user_id,null|string|max:100',
            'guest_email' => 'required_if:user_id,null|email|max:255',
        ]);

        // Get or create device ID for guest users
        if (!$request->session()->has('device_id')) {
            $deviceId = Str::uuid()->toString();
            $request->session()->put('device_id', $deviceId);
        } else {
            $deviceId = $request->session()->get('device_id');
        }

        $userId = auth()->id();
        $parentId = $request->parent_id;

        // Validate parent comment exists and belongs to the same news
        if ($parentId) {
            $parentComment = Comment::where('id', $parentId)
                ->where('news_id', $news->id)
                ->first();
            
            if (!$parentComment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Parent comment not found'
                ], 404);
            }

            // Only allow replies to top-level comments (no nested replies)
            if ($parentComment->parent_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot reply to a reply'
                ], 422);
            }
        }

        $commentData = [
            'user_id' => $userId,
            'device_id' => $userId ? null : $deviceId,
            'news_id' => $news->id,
            'parent_id' => $parentId,
            'content' => $request->content,
            'is_approved' => true, // Auto-approve for now
        ];

        // Add guest information if not authenticated
        if (!$userId) {
            $commentData['guest_name'] = $request->guest_name;
            $commentData['guest_email'] = $request->guest_email;
        }

        $comment = Comment::create($commentData);
        $comment->load(['user', 'reactions', 'replies']);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Comment posted successfully',
                'comment' => $this->formatComment($comment)
            ], 201);
        }

        return back()->with('success', 'Comment posted successfully');
    }

    public function update(Request $request, Comment $comment)
    {
        // Check if user can edit this comment
        if (!$this->canUserEditComment($comment, auth()->id(), $request->session()->get('device_id'))) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to edit this comment'
            ], 403);
        }

        $request->validate([
            'content' => 'required|string|min:1|max:1000',
        ]);

        $comment->update([
            'content' => $request->content,
        ]);

        $comment->load(['user', 'reactions', 'replies']);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Comment updated successfully',
                'comment' => $this->formatComment($comment)
            ]);
        }

        return back()->with('success', 'Comment updated successfully');
    }

    public function destroy(Request $request, Comment $comment)
    {
        // Check if user can delete this comment
        if (!$this->canUserEditComment($comment, auth()->id(), $request->session()->get('device_id'))) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to delete this comment'
            ], 403);
        }

        // Delete the comment and its replies
        $comment->delete();

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Comment deleted successfully'
            ]);
        }

        return back()->with('success', 'Comment deleted successfully');
    }

    public function getComments(Request $request, News $news)
    {
        $perPage = $request->get('per_page', 10);
        $page = $request->get('page', 1);

        $comments = $news->comments()
            ->with(['user', 'reactions', 'replies.user', 'replies.reactions'])
            ->paginate($perPage);

        $formattedComments = $comments->map(function ($comment) {
            return $this->formatComment($comment);
        });

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'comments' => $formattedComments,
                'pagination' => [
                    'current_page' => $comments->currentPage(),
                    'last_page' => $comments->lastPage(),
                    'per_page' => $comments->perPage(),
                    'total' => $comments->total(),
                    'has_more' => $comments->hasMorePages(),
                ]
            ]);
        }

        return view('comments.index', compact('comments', 'news'));
    }

    public function toggleApproval(Request $request, Comment $comment)
    {
        // Only admins can toggle approval
        if (!auth()->user() || !auth()->user()->hasRole('admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $comment->update([
            'is_approved' => !$comment->is_approved
        ]);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Comment approval status updated',
                'is_approved' => $comment->is_approved
            ]);
        }

        return back()->with('success', 'Comment approval status updated');
    }

    public function moderate(Request $request)
    {
        // Only admins can moderate
        if (!auth()->user() || !auth()->user()->hasRole('admin')) {
            return redirect()->back()->with('error', 'Unauthorized');
        }

        $comments = Comment::with(['user', 'news'])
            ->where('is_approved', false)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.comments.moderate', compact('comments'));
    }

    private function canUserEditComment(Comment $comment, $userId = null, $deviceId = null): bool
    {
        // Admins can edit any comment
        if ($userId && auth()->user()->hasRole('admin')) {
            return true;
        }

        // Users can edit their own comments
        if ($userId && $comment->user_id == $userId) {
            return true;
        }

        // Guests can edit comments made with their device ID
        if (!$userId && $deviceId && $comment->device_id == $deviceId) {
            return true;
        }

        return false;
    }

    private function formatComment(Comment $comment): array
    {
        $deviceId = session('device_id');
        $userId = auth()->id();

        return [
            'id' => $comment->id,
            'content' => $comment->content,
            'author_name' => $comment->author_name,
            'author_email' => $comment->author_email,
            'is_approved' => $comment->is_approved,
            'is_reply' => $comment->isReply(),
            'can_be_replied_to' => $comment->canBeRepliedTo(),
            'can_edit' => $this->canUserEditComment($comment, $userId, $deviceId),
            'created_at' => $comment->created_at->format('Y-m-d H:i:s'),
            'created_at_human' => $comment->created_at->diffForHumans(),
            'reactions' => [
                'counts' => $comment->getReactionsCounts(),
                'total_count' => $comment->getTotalReactionsCount(),
                'user_reaction' => $comment->hasUserReacted($userId, $deviceId)?->reaction_type,
            ],
            'replies' => $comment->replies->map(function ($reply) use ($userId, $deviceId) {
                return [
                    'id' => $reply->id,
                    'content' => $reply->content,
                    'author_name' => $reply->author_name,
                    'author_email' => $reply->author_email,
                    'is_approved' => $reply->is_approved,
                    'can_edit' => $this->canUserEditComment($reply, $userId, $deviceId),
                    'created_at' => $reply->created_at->format('Y-m-d H:i:s'),
                    'created_at_human' => $reply->created_at->diffForHumans(),
                    'reactions' => [
                        'counts' => $reply->getReactionsCounts(),
                        'total_count' => $reply->getTotalReactionsCount(),
                        'user_reaction' => $reply->hasUserReacted($userId, $deviceId)?->reaction_type,
                    ],
                ];
            }),
        ];
    }
}