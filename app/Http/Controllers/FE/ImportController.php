<?php

namespace App\Http\Controllers\FE;

use App\Http\Controllers\Controller;
use App\Http\Requests\ImportWordPressRequest;
use App\Services\AuthorMappingService;
use App\Services\CategoryMappingService;
use App\Services\WordPressImportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ImportController extends Controller
{
    protected WordPressImportService $importService;
    protected CategoryMappingService $categoryMappingService;
    protected AuthorMappingService $authorMappingService;

    public function __construct(
        WordPressImportService $importService,
        CategoryMappingService $categoryMappingService,
        AuthorMappingService $authorMappingService
    ) {
        $this->importService = $importService;
        $this->categoryMappingService = $categoryMappingService;
        $this->authorMappingService = $authorMappingService;
    }

    /**
     * Show the import form
     */
    public function index()
    {
        $missingCategoriesSummary = $this->categoryMappingService->getMissingCategoriesSummary();
        $categoryRecommendations = $this->categoryMappingService->generateCategoryRecommendations();
        $articlesNeedingAuthors = $this->authorMappingService->getArticlesNeedingAuthorAssignment();

        return view('admin.import.index', [
            'categories' => \App\Models\Category::all(),
            'missingCategoriesSummary' => $missingCategoriesSummary,
            'categoryRecommendations' => $categoryRecommendations,
            'articlesNeedingAuthors' => $articlesNeedingAuthors
        ]);
    }

    /**
     * Preview import data before actual import
     */
    public function previewImport(ImportWordPressRequest $request)
    {
        try {
            $file = $request->file('sql_file');
            $tempPath = $file->store('temp');

            // Parse SQL file and extract preview data
            $previewData = $this->importService->previewImport(
                Storage::path($tempPath),
                $request->validated()
            );

            // Clean up temp file
            Storage::delete($tempPath);

            return response()->json([
                'success' => true,
                'data' => $previewData
            ]);

        } catch (\Exception $e) {
            Log::error('Import preview failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to preview import: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Import WordPress articles from SQL file
     */
    public function importWordPress(ImportWordPressRequest $request)
    {
        try {
            DB::beginTransaction();

            $file = $request->file('sql_file');
            $tempPath = $file->store('temp');

            // Perform the import
            $result = $this->importService->importFromSql(
                Storage::path($tempPath),
                $request->validated()
            );

            // Clean up temp file
            Storage::delete($tempPath);

            // Send notifications about missing categories and authors if any
            $this->categoryMappingService->notifyAdminsAboutMissingCategories();
            $this->authorMappingService->notifyAdminsAboutMissingAuthors();

            // Send notification about missing images if any
            if (isset($result['images_not_found']) && $result['images_not_found'] > 0) {
                $this->notifyAdminsAboutMissingImages($result['images_not_found']);
            }

            DB::commit();

            $message = "Successfully imported {$result['imported']} articles";
            if (isset($result['images_not_found']) && $result['images_not_found'] > 0) {
                $message .= ". {$result['images_not_found']} articles are using placeholder images.";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('WordPress import failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Import failed: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get missing categories summary
     */
    public function getMissingCategories()
    {
        try {
            $summary = $this->categoryMappingService->getMissingCategoriesSummary();
            $recommendations = $this->categoryMappingService->generateCategoryRecommendations();

            return response()->json([
                'success' => true,
                'data' => [
                    'summary' => $summary,
                    'recommendations' => $recommendations
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get missing categories: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to get missing categories: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Create categories from WordPress import suggestions
     */
    public function createMissingCategories(Request $request)
    {
        try {
            $request->validate([
                'category_names' => 'required|array',
                'category_names.*' => 'required|string|max:255'
            ]);

            $result = $this->categoryMappingService->createCategoriesFromSuggestions(
                $request->category_names
            );

            if ($result['created_count'] > 0) {
                return response()->json([
                    'success' => true,
                    'message' => "Successfully created {$result['created_count']} categories",
                    'data' => $result
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'No categories were created',
                    'data' => $result
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('Failed to create missing categories: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to create categories: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Clear cached missing categories
     */
    public function clearMissingCategoriesCache()
    {
        try {
            $cleared = $this->categoryMappingService->clearCachedMissingCategories();

            if ($cleared) {
                return response()->json([
                    'success' => true,
                    'message' => 'Missing categories cache cleared successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to clear cache'
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('Failed to clear missing categories cache: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Send notification about missing images
     */
    protected function notifyAdminsAboutMissingImages(int $count): void
    {
        try {
            $superAdmins = \App\Models\User::role('Super Admin')->get();

            $message = sprintf(
                "WordPress Import Alert: %d articles are using placeholder images because the original images could not be imported. " .
                "Please review these articles and upload appropriate images manually.",
                $count
            );

            foreach ($superAdmins as $admin) {
                \App\Models\Notification::create([
                    'user_id' => $admin->id,
                    'data' => $message,
                    'read_at' => null,
                ]);
            }

            Log::info('WordPress import missing images notification sent', [
                'missing_images_count' => $count,
                'notified_admins' => $superAdmins->count()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send missing images notification', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
