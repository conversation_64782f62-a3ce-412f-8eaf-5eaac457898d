<?php

namespace App\Http\Controllers\API;

use App\Enums\NewsType;
use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class NewsApiController extends BaseController
{
    function index(Request $request): JsonResponse
    {
        $type = NewsType::fromName($request->type);
        $news = new News();
        switch ($type) {
            case NewsType::latest:
                $news = $news->where('status', 'Accept')->latest();
                break;
            case NewsType::top:
                $news = $news->where('status', 'Accept')->orderBy('views', 'desc');
                break;
            case NewsType::popular:
                $news = $news->where('status', 'Accept')->withCount('likes')->orderBy('likes_count', 'desc');
                break;
            default:
                $news = $news->where('status', 'Accept')->latest();
                break;
        }
        return $this->sendPaginationResponse($news->paginate($request->perPage));
    }
}
