<?php

namespace App\Http\Controllers\API;

use App\Enums\NewsType;
use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class NewsApiController extends BaseController
{
    function index(Request $request): JsonResponse
    {
        $type = NewsType::fromName($request->type);
        $news = News::with(['author', 'category']);

        // Base filter - only accepted news
        $news = $news->where('status', 'Accept');

        // Category filter
        if ($request->filled('category_id')) {
            $news = $news->where('category_id', $request->category_id);
        }

        // Search filter
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $news = $news->where(function($query) use ($searchTerm) {
                $query->where('title', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('content', 'LIKE', "%{$searchTerm}%");
            });
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $news = $news->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $news = $news->whereDate('created_at', '<=', $request->date_to);
        }

        // Apply sorting based on type
        switch ($type) {
            case NewsType::latest:
                $news = $news->latest();
                break;
            case NewsType::top:
                $news = $news->orderBy('views', 'desc');
                break;
            case NewsType::popular:
                $news = $news->withCount('likes')->orderBy('likes_count', 'desc');
                break;
            default:
                $news = $news->latest();
                break;
        }

        $paginatedNews = $news->paginate($request->perPage ?? 10);

        // Transform the data to include image_url and other computed fields
        $paginatedNews->getCollection()->transform(function ($newsItem) {
            $newsArray = $newsItem->toArray();
            $newsArray['image_url'] = $newsItem->getImageUrl();
            return $newsArray;
        });

        return $this->sendPaginationResponse($paginatedNews);
    }
}
