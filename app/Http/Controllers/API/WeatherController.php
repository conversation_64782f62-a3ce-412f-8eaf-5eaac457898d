<?php

namespace App\Http\Controllers\API;

use App\Domain\Weather\Models\Location;
use App\Domain\Weather\Services\WeatherService;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class WeatherController extends Controller
{
    public function __construct(
        private readonly WeatherService $weatherService
    ) {}

    /**
     * Get current weather for default location (Jakarta)
     */
    public function current(): JsonResponse
    {
        try {
            $weather = $this->weatherService->getDefaultLocationWeather();
            
            return response()->json([
                'success' => true,
                'data' => $weather->toArray(),
                'message' => 'Current weather retrieved successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get current weather', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve current weather',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current weather for specific location
     */
    public function currentByLocation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'city' => 'required_without_all:latitude,longitude|string|max:100',
            'country' => 'string|max:100',
            'latitude' => 'required_with:longitude|numeric|between:-90,90',
            'longitude' => 'required_with:latitude|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            if ($request->has(['latitude', 'longitude'])) {
                $location = Location::fromCoordinates(
                    $request->latitude,
                    $request->longitude
                );
            } else {
                $location = Location::fromCityCountry(
                    $request->city,
                    $request->country ?? 'ID'
                );
            }

            $weather = $this->weatherService->getCurrentWeather($location);
            
            return response()->json([
                'success' => true,
                'data' => $weather->toArray(),
                'message' => 'Weather retrieved successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get weather by location', [
                'request' => $request->all(),
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve weather for location',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get weather forecast
     */
    public function forecast(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'city' => 'required_without_all:latitude,longitude|string|max:100',
            'country' => 'string|max:100',
            'latitude' => 'required_with:longitude|numeric|between:-90,90',
            'longitude' => 'required_with:latitude|numeric|between:-180,180',
            'days' => 'integer|min:1|max:7'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            if ($request->has(['latitude', 'longitude'])) {
                $location = Location::fromCoordinates(
                    $request->latitude,
                    $request->longitude
                );
            } else {
                $location = Location::fromCityCountry(
                    $request->city ?? 'Jakarta',
                    $request->country ?? 'ID'
                );
            }

            $days = $request->days ?? 5;
            $forecast = $this->weatherService->getForecast($location, $days);
            
            return response()->json([
                'success' => true,
                'data' => array_map(fn($weather) => $weather->toArray(), $forecast),
                'meta' => [
                    'days' => $days,
                    'location' => $location->toArray(),
                    'count' => count($forecast)
                ],
                'message' => 'Weather forecast retrieved successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get weather forecast', [
                'request' => $request->all(),
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve weather forecast',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get historical weather
     */
    public function historical(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'city' => 'required_without_all:latitude,longitude|string|max:100',
            'country' => 'string|max:100',
            'latitude' => 'required_with:longitude|numeric|between:-90,90',
            'longitude' => 'required_with:latitude|numeric|between:-180,180',
            'date' => 'required|date|before:today'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            if ($request->has(['latitude', 'longitude'])) {
                $location = Location::fromCoordinates(
                    $request->latitude,
                    $request->longitude
                );
            } else {
                $location = Location::fromCityCountry(
                    $request->city,
                    $request->country ?? 'ID'
                );
            }

            $date = new \DateTime($request->date);
            $weather = $this->weatherService->getHistoricalWeather($location, $date);
            
            return response()->json([
                'success' => true,
                'data' => $weather->toArray(),
                'meta' => [
                    'date' => $date->format('Y-m-d'),
                    'location' => $location->toArray()
                ],
                'message' => 'Historical weather retrieved successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get historical weather', [
                'request' => $request->all(),
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve historical weather',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search locations
     */
    public function searchLocations(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:2|max:100'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $locations = $this->weatherService->searchLocations($request->query);
            
            return response()->json([
                'success' => true,
                'data' => array_map(fn($location) => $location->toArray(), $locations),
                'meta' => [
                    'query' => $request->query,
                    'count' => count($locations)
                ],
                'message' => 'Locations retrieved successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to search locations', [
                'query' => $request->query,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to search locations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get weather with recommendations
     */
    public function withRecommendations(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'city' => 'required_without_all:latitude,longitude|string|max:100',
            'country' => 'string|max:100',
            'latitude' => 'required_with:longitude|numeric|between:-90,90',
            'longitude' => 'required_with:latitude|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            if ($request->has(['latitude', 'longitude'])) {
                $location = Location::fromCoordinates(
                    $request->latitude,
                    $request->longitude
                );
            } else {
                $location = Location::fromCityCountry(
                    $request->city ?? 'Jakarta',
                    $request->country ?? 'ID'
                );
            }

            $weather = $this->weatherService->getCurrentWeather($location);
            $recommendations = $this->weatherService->getWeatherRecommendations($weather);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'weather' => $weather->toArray(),
                    'recommendations' => $recommendations
                ],
                'message' => 'Weather with recommendations retrieved successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get weather with recommendations', [
                'request' => $request->all(),
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve weather with recommendations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get multiple locations weather
     */
    public function multipleLocations(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'locations' => 'required|array|min:1|max:10',
            'locations.*.city' => 'required|string|max:100',
            'locations.*.country' => 'string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $locations = [];
            foreach ($request->locations as $locationData) {
                $locations[] = Location::fromCityCountry(
                    $locationData['city'],
                    $locationData['country'] ?? 'ID'
                );
            }

            $results = $this->weatherService->getMultiLocationWeather($locations);
            
            return response()->json([
                'success' => true,
                'data' => array_map(function ($result) {
                    return [
                        'location' => $result['location']->toArray(),
                        'weather' => $result['weather']?->toArray(),
                        'status' => $result['status'],
                        'message' => $result['message'] ?? null
                    ];
                }, $results),
                'meta' => [
                    'total_locations' => count($results),
                    'successful' => count(array_filter($results, fn($r) => $r['status'] === 'success')),
                    'failed' => count(array_filter($results, fn($r) => $r['status'] === 'error'))
                ],
                'message' => 'Multiple locations weather retrieved'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get multiple locations weather', [
                'request' => $request->all(),
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve weather for multiple locations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get weather service status
     */
    public function status(): JsonResponse
    {
        try {
            $providerInfo = $this->weatherService->getProviderInfo();
            
            return response()->json([
                'success' => true,
                'data' => $providerInfo,
                'message' => 'Weather service status retrieved successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get weather service status', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve weather service status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear weather cache
     */
    public function clearCache(): JsonResponse
    {
        try {
            $result = $this->weatherService->clearCache();
            
            return response()->json([
                'success' => $result,
                'message' => $result ? 'Weather cache cleared successfully' : 'Failed to clear weather cache'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to clear weather cache', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear weather cache',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}