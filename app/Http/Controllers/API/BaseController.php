<?php


namespace App\Http\Controllers\API;

use App\Enums\NetworkCode;
use App\Http\Controllers\Controller as Controller;
use App\Http\Helper\ResponseHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;

class BaseController extends Controller
{
    public function sendResponse($result, $message = 'Success'): JsonResponse
    {
        return ResponseHelper::successResponse($result, $message);
    }
    public function sendPaginationResponse(LengthAwarePaginator $paginateData): JsonResponse
    {
        return ResponseHelper::formatPagination($paginateData);
    }

    public function sendError($error, $message = 'Failed', int $code = 400, $line = 0): JsonResponse
    {
        return ResponseHelper::failedResponse($error, $message, $code, NetworkCode::fromCode($code), $line);
    }
}
