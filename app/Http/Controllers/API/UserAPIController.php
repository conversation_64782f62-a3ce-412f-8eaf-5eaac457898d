<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserAPIController extends BaseController
{
    function index(Request $request): JsonResponse
    {
        $result = User::with('roles')->paginate($request->perPage);
        return $this->sendPaginationResponse($result);
    }

    function me(): JsonResponse
    {
        $result = User::find(Auth::id())->with('roles')->first();
        return $this->sendResponse($result);
    }
}
