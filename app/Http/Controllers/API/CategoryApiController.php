<?php

namespace App\Http\Controllers\API;

use App\Enums\CategoryType;
use App\Models\Category;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CategoryApiController extends BaseController
{
    function index(Request $request): JsonResponse
    {
        $type = CategoryType::fromName($request->type);
        $category = new Category();
        switch ($type) {
            case CategoryType::top:
                $category = $category->orderBy('views', 'desc')->orderBy('created_at', 'desc');
                break;
            default:
                $category = $category->orderBy('views', 'desc')->orderBy('created_at', 'desc');
                break;
        }
        return $this->sendPaginationResponse($category->paginate($request->perPage));
    }
}
