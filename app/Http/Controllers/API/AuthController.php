<?php

namespace App\Http\Controllers\API;

use App\Enums\NetworkCode;
use App\Http\Helper\FormatHelper;
use App\Http\Helper\LogHelper;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;

class AuthController extends BaseController
{
    public function register(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'password' => 'required|string|min:4',
                'confirm_password' => 'required|string|min:4',
            ]);

            if ($validator->fails()) {
                return $this->sendError($validator->errors(), 'Validation Error.');
            }
            if ($request->password != $request->confirm_password) {
                throw new Exception("Password Mismatch");
            }

            // $verificationCode = rand(100000, 999999);
            DB::beginTransaction();

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
            ]);
            $memberRole = Role::where('name', 'Writer')->first();
            if (!FormatHelper::isNotEmpty($memberRole)) {
                throw new Exception("Role Member not found");
            }
            $user->assignRole($memberRole);
            DB::commit();
            // if (!FormatHelper::isNotEmpty($request->project_code)) {
            //     MailJobs::dispatch($request->email, new VerificationEmail($verificationCode));
            //     return ResponseHelper::successResponse($user, 'User Created Successfully, please verify your email');
            // }
            // $user->email_verified_at = Carbon::now()->format('d-m-Y H:i:s');
            // $user->save();
            return $this->sendResponse($user, 'Success');
        } catch (Exception $ex) {
            LogHelper::sendErrorLog($ex);
            DB::rollBack();
            return $this->sendError($ex->getFile(), $ex->getMessage(),  NetworkCode::ERROR->value, $ex->getLine());
        }
    }

    public function login(Request $request)
    {
        try {
            $user = User::where('email', $request->email)->first();
            if (!$user || !Hash::check($request->password, $user->password)) {
                throw new Exception("The provided credentials are incorrect.");
            }
            $success['token'] =  $user->createToken('MyApp')->plainTextToken;
            $success['user'] =  $user;
            return $this->sendResponse($success, 'Login successful');
        } catch (Exception $ex) {
            return $this->sendError($ex->getFile(), $ex->getMessage(),  NetworkCode::ERROR->value, $ex->getLine());
        }
    }
}
