<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ImportWordPressRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user()->hasRole('Super Admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'sql_file' => [
                'required',
                'file',
                'mimes:sql,txt',
                'max:51200' // 50MB max
            ],
            'wordpress_domain' => [
                'nullable',
                'string',
                'max:255'
            ],
            'fallback_category_id' => [
                'nullable',
                'exists:category,id'
            ],
            'import_images' => [
                'boolean'
            ],
            'skip_duplicates' => [
                'boolean'
            ],
            'post_status_mapping' => [
                'array'
            ],
            'post_status_mapping.publish' => [
                'string',
                'in:Accept,Pending,Reject'
            ],
            'post_status_mapping.draft' => [
                'string',
                'in:Accept,Pending,Reject'
            ],
            'post_status_mapping.private' => [
                'string',
                'in:Accept,Pending,Reject'
            ],
            'category_mapping' => [
                'array'
            ],
            'category_mapping.*' => [
                'exists:category,id'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'sql_file.required' => 'Please select a SQL file to import.',
            'sql_file.mimes' => 'The file must be a SQL file (.sql or .txt).',
            'sql_file.max' => 'The file size must not exceed 50MB.',
            'wordpress_domain.string' => 'WordPress domain must be a valid string.',
            'fallback_category_id.exists' => 'The selected fallback category is invalid.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'import_images' => $this->boolean('import_images'),
            'skip_duplicates' => $this->boolean('skip_duplicates'),
            'auto_assign_authors' => $this->boolean('auto_assign_authors', true),
            'create_missing_categories' => $this->boolean('create_missing_categories', true),
            'post_status_mapping' => $this->input('post_status_mapping', [
                'publish' => 'Accept',
                'draft' => 'Pending',
                'private' => 'Pending'
            ])
        ]);
    }
}
