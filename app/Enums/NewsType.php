<?php

namespace App\Enums;

enum NewsType: string
{
    case latest = 'latest';
    case top = 'top';
    case popular = 'popular';

    static function fromName(string $name): NewsType
    {
        switch ($name) {
            case 'latest':
                return self::latest;
            case 'top':
                return self::top;
            case 'popular':
                return self::popular;
            default:
                return self::latest;
        }
    }
}
