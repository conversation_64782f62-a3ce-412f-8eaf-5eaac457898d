<?php

namespace App\Models\Response;

use App\Enums\NetworkCode;
use App\Enums\NetworkStatus;
use App\Http\Helper\FormatHelper;
use Illuminate\Database\Eloquent\Model;

class ErrorResponse extends Model
{
    // Tidak perlu menggunakan table name atau timestamps jika hanya model biasa

    public NetworkCode $networkCode;
    public string $message;
    public int $errorCode;

    // Constructor untuk menerima parameter code dan msg
    public function __construct(NetworkCode $networkCode, string $message, ?int $errorCode = null)
    {
        $this->networkCode = $networkCode;
        if (!FormatHelper::isNotEmpty($message)) {
            $this->message = NetworkStatus::getNetworkStatusFromNetworkCode($networkCode)->value;
        } else {
            $this->message = $message;
        }
        if (empty($errorCode)) {
            $this->errorCode = $this->networkCode->value;
        } else {
            $this->errorCode = $errorCode;
        }
    }
}
