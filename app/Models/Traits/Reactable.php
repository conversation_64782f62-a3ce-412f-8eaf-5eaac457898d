<?php

namespace App\Models\Traits;

use App\Models\Reaction;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait Reactable
{
    public function reactions(): MorphMany
    {
        return $this->morphMany(Reaction::class, 'reactable');
    }

    public function getReactionCountByType(string $type): int
    {
        return $this->reactions()->where('reaction_type', $type)->count();
    }

    public function getTotalReactionsCount(): int
    {
        return $this->reactions()->count();
    }

    public function getReactionsCounts(): array
    {
        $counts = [];
        foreach (Reaction::REACTION_TYPES as $type => $emoji) {
            $counts[$type] = $this->getReactionCountByType($type);
        }
        return $counts;
    }

    public function hasUserReacted($userId = null, $deviceId = null): ?Reaction
    {
        return $this->reactions()
            ->byUserOrDevice($userId, $deviceId)
            ->first();
    }

    public function toggleReaction(string $reactionType, $userId = null, $deviceId = null): array
    {
        $existingReaction = $this->hasUserReacted($userId, $deviceId);

        if ($existingReaction) {
            if ($existingReaction->reaction_type === $reactionType) {
                // Same reaction type - remove it
                $existingReaction->delete();
                return [
                    'action' => 'removed',
                    'reaction_type' => $reactionType,
                    'counts' => $this->getReactionsCounts()
                ];
            } else {
                // Different reaction type - update it
                $existingReaction->update(['reaction_type' => $reactionType]);
                return [
                    'action' => 'updated',
                    'reaction_type' => $reactionType,
                    'previous_type' => $existingReaction->reaction_type,
                    'counts' => $this->getReactionsCounts()
                ];
            }
        } else {
            // No existing reaction - create new one
            $this->reactions()->create([
                'user_id' => $userId,
                'device_id' => $deviceId,
                'reaction_type' => $reactionType,
            ]);
            
            return [
                'action' => 'added',
                'reaction_type' => $reactionType,
                'counts' => $this->getReactionsCounts()
            ];
        }
    }

    public function getMostPopularReaction(): ?string
    {
        $reaction = $this->reactions()
            ->selectRaw('reaction_type, COUNT(*) as count')
            ->groupBy('reaction_type')
            ->orderByDesc('count')
            ->first();

        return $reaction ? $reaction->reaction_type : null;
    }

    public function getReactionEmoji(string $type): string
    {
        return Reaction::REACTION_TYPES[$type] ?? '👍';
    }
}