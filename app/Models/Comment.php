<?php

namespace App\Models;

use App\Models\Traits\Reactable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Comment extends Model
{
    use HasFactory, Reactable;

    protected $fillable = [
        'user_id',
        'device_id',
        'guest_name',
        'guest_email',
        'news_id',
        'parent_id',
        'content',
        'is_approved',
    ];

    protected $casts = [
        'is_approved' => 'boolean',
    ];

    protected $with = ['user', 'reactions'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function news(): BelongsTo
    {
        return $this->belongsTo(News::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Comment::class, 'parent_id');
    }

    public function replies(): HasMany
    {
        return $this->hasMany(Comment::class, 'parent_id')
            ->where('is_approved', true)
            ->orderBy('created_at', 'asc');
    }

    public function allReplies(): HasMany
    {
        return $this->hasMany(Comment::class, 'parent_id')
            ->orderBy('created_at', 'asc');
    }



    public function getAuthorNameAttribute(): string
    {
        return $this->user ? $this->user->name : ($this->guest_name ?? 'Anonymous');
    }

    public function getAuthorEmailAttribute(): ?string
    {
        return $this->user ? $this->user->email : $this->guest_email;
    }

    public function isReply(): bool
    {
        return !is_null($this->parent_id);
    }

    public function isApproved(): bool
    {
        return $this->is_approved;
    }

    public function canBeRepliedTo(): bool
    {
        return !$this->isReply(); // Only allow replies to top-level comments
    }

    public function getReactionCountByType(string $type): int
    {
        return $this->reactions()->where('reaction_type', $type)->count();
    }

    public function getTotalReactionsCount(): int
    {
        return $this->reactions()->count();
    }

    public function hasUserReacted($userId = null, $deviceId = null): ?Reaction
    {
        return $this->reactions()
            ->byUserOrDevice($userId, $deviceId)
            ->first();
    }

    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    public function scopeReplies($query)
    {
        return $query->whereNotNull('parent_id');
    }

    public function scopeForNews($query, $newsId)
    {
        return $query->where('news_id', $newsId);
    }

    public function scopeByUserOrDevice($query, $userId = null, $deviceId = null)
    {
        return $query->where(function ($q) use ($userId, $deviceId) {
            if ($userId) {
                $q->where('user_id', $userId);
            } elseif ($deviceId) {
                $q->where('device_id', $deviceId);
            }
        });
    }
}