<?php

namespace App\Models;

use App\Models\Traits\Reactable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class News extends Model
{
    use HasFactory, Reactable;

    protected $guarded = ['id'];
    protected $table = 'news';
    protected $with = ['author'];

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function likes()
    {
        return $this->hasMany(Like::class);
    }

    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class)
            ->whereNull('parent_id')
            ->where('is_approved', true)
            ->orderBy('created_at', 'desc');
    }

    public function allComments(): HasMany
    {
        return $this->hasMany(Comment::class)
            ->orderBy('created_at', 'desc');
    }

    public function getCommentsCountAttribute(): int
    {
        return $this->allComments()->where('is_approved', true)->count();
    }

    public function getImageUrlAttribute(): ?string
    {
        return $this->getImageUrl();
    }

    public function getImageUrl(): string
    {
        if ($this->image && file_exists(public_path('storage/images/' . $this->image))) {
            return asset('storage/images/' . $this->image);
        }

        // Fallback to default no-image placeholder
        return asset('img/noimg.jpg');
    }

    /**
     * Add image URL to array representation
     */
    public function toArray()
    {
        $array = parent::toArray();
        $array['image_url'] = $this->getImageUrl();
        return $array;
    }

    /**
     * Static helper method for getting image URL from image filename
     */
    public static function getImageUrlFromFilename(?string $imageFilename, int $newsId = 1): string
    {
        if ($imageFilename && file_exists(public_path('storage/images/' . $imageFilename))) {
            return asset('storage/images/' . $imageFilename);
        }

        // Fallback to default no-image placeholder
        return asset('img/noimg.jpg');
    }
}
