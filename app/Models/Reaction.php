<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Reaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'device_id',
        'reactable_id',
        'reactable_type',
        'reaction_type',
    ];

    public const REACTION_TYPES = [
        // New reaction types (primary)
        'suka' => '👍',
        'benci' => '👎',
        'cinta' => '❤️',
        'lucu' => '😂',
        'marah' => '😡',
        'sedih' => '😢',
        'wow' => '😮',
        // Old reaction types (for backward compatibility)
        'like' => '👍',
        'love' => '❤️',
        'haha' => '😂',
        'sad' => '😢',
        'angry' => '😡',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function reactable(): MorphTo
    {
        return $this->morphTo();
    }

    public function getReactionEmojiAttribute(): string
    {
        return self::REACTION_TYPES[$this->reaction_type] ?? '👍';
    }

    public function scopeByUserOrDevice($query, $userId = null, $deviceId = null)
    {
        return $query->where(function ($q) use ($userId, $deviceId) {
            if ($userId) {
                $q->where('user_id', $userId);
            } elseif ($deviceId) {
                $q->where('device_id', $deviceId);
            }
        });
    }

    public function scopeOfType($query, string $type)
    {
        return $query->where('reaction_type', $type);
    }
}