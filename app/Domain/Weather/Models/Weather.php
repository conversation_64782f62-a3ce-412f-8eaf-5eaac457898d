<?php

namespace App\Domain\Weather\Models;

use Carbon\Carbon;

class Weather
{
    public function __construct(
        private readonly string $location,
        private readonly float $temperature,
        private readonly float $feelsLike,
        private readonly string $condition,
        private readonly string $description,
        private readonly int $humidity,
        private readonly float $windSpeed,
        private readonly int $windDirection,
        private readonly float $pressure,
        private readonly int $visibility,
        private readonly float $uvIndex,
        private readonly Carbon $observedAt,
        private readonly ?string $icon = null,
        private readonly ?float $temperatureMin = null,
        private readonly ?float $temperatureMax = null,
        private readonly ?int $cloudiness = null,
        private readonly ?Carbon $sunrise = null,
        private readonly ?Carbon $sunset = null
    ) {}

    public static function create(array $data): self
    {
        return new self(
            location: $data['location'],
            temperature: (float) $data['temperature'],
            feelsLike: (float) $data['feels_like'],
            condition: $data['condition'],
            description: $data['description'],
            humidity: (int) $data['humidity'],
            windSpeed: (float) $data['wind_speed'],
            windDirection: (int) $data['wind_direction'],
            pressure: (float) $data['pressure'],
            visibility: (int) $data['visibility'],
            uvIndex: (float) $data['uv_index'],
            observedAt: $data['observed_at'] instanceof Carbon 
                ? $data['observed_at'] 
                : Carbon::parse($data['observed_at']),
            icon: $data['icon'] ?? null,
            temperatureMin: isset($data['temperature_min']) ? (float) $data['temperature_min'] : null,
            temperatureMax: isset($data['temperature_max']) ? (float) $data['temperature_max'] : null,
            cloudiness: isset($data['cloudiness']) ? (int) $data['cloudiness'] : null,
            sunrise: isset($data['sunrise']) 
                ? ($data['sunrise'] instanceof Carbon ? $data['sunrise'] : Carbon::parse($data['sunrise']))
                : null,
            sunset: isset($data['sunset']) 
                ? ($data['sunset'] instanceof Carbon ? $data['sunset'] : Carbon::parse($data['sunset']))
                : null
        );
    }

    public function getLocation(): string
    {
        return $this->location;
    }

    public function getTemperature(): float
    {
        return $this->temperature;
    }

    public function getFeelsLike(): float
    {
        return $this->feelsLike;
    }

    public function getCondition(): string
    {
        return $this->condition;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getHumidity(): int
    {
        return $this->humidity;
    }

    public function getWindSpeed(): float
    {
        return $this->windSpeed;
    }

    public function getWindDirection(): int
    {
        return $this->windDirection;
    }

    public function getPressure(): float
    {
        return $this->pressure;
    }

    public function getVisibility(): int
    {
        return $this->visibility;
    }

    public function getUvIndex(): float
    {
        return $this->uvIndex;
    }

    public function getObservedAt(): Carbon
    {
        return $this->observedAt;
    }

    public function getIcon(): ?string
    {
        return $this->icon;
    }

    public function getTemperatureMin(): ?float
    {
        return $this->temperatureMin;
    }

    public function getTemperatureMax(): ?float
    {
        return $this->temperatureMax;
    }

    public function getCloudiness(): ?int
    {
        return $this->cloudiness;
    }

    public function getSunrise(): ?Carbon
    {
        return $this->sunrise;
    }

    public function getSunset(): ?Carbon
    {
        return $this->sunset;
    }

    public function isHot(): bool
    {
        return $this->temperature > 30;
    }

    public function isCold(): bool
    {
        return $this->temperature < 15;
    }

    public function isWindy(): bool
    {
        return $this->windSpeed > 20;
    }

    public function isHighHumidity(): bool
    {
        return $this->humidity > 80;
    }

    public function getWindDirectionText(): string
    {
        $directions = [
            'N' => [0, 11.25, 348.75, 360],
            'NNE' => [11.25, 33.75],
            'NE' => [33.75, 56.25],
            'ENE' => [56.25, 78.75],
            'E' => [78.75, 101.25],
            'ESE' => [101.25, 123.75],
            'SE' => [123.75, 146.25],
            'SSE' => [146.25, 168.75],
            'S' => [168.75, 191.25],
            'SSW' => [191.25, 213.75],
            'SW' => [213.75, 236.25],
            'WSW' => [236.25, 258.75],
            'W' => [258.75, 281.25],
            'WNW' => [281.25, 303.75],
            'NW' => [303.75, 326.25],
            'NNW' => [326.25, 348.75],
        ];

        foreach ($directions as $direction => $ranges) {
            if (count($ranges) === 4) {
                // Special case for North (0-11.25 and 348.75-360)
                if (($this->windDirection >= $ranges[0] && $this->windDirection <= $ranges[1]) ||
                    ($this->windDirection >= $ranges[2] && $this->windDirection <= $ranges[3])) {
                    return $direction;
                }
            } else {
                if ($this->windDirection >= $ranges[0] && $this->windDirection < $ranges[1]) {
                    return $direction;
                }
            }
        }

        return 'N'; // fallback
    }

    public function toArray(): array
    {
        return [
            'location' => $this->location,
            'temperature' => $this->temperature,
            'feels_like' => $this->feelsLike,
            'condition' => $this->condition,
            'description' => $this->description,
            'humidity' => $this->humidity,
            'wind_speed' => $this->windSpeed,
            'wind_direction' => $this->windDirection,
            'wind_direction_text' => $this->getWindDirectionText(),
            'pressure' => $this->pressure,
            'visibility' => $this->visibility,
            'uv_index' => $this->uvIndex,
            'observed_at' => $this->observedAt->toISOString(),
            'icon' => $this->icon,
            'temperature_min' => $this->temperatureMin,
            'temperature_max' => $this->temperatureMax,
            'cloudiness' => $this->cloudiness,
            'sunrise' => $this->sunrise?->toISOString(),
            'sunset' => $this->sunset?->toISOString(),
            'is_hot' => $this->isHot(),
            'is_cold' => $this->isCold(),
            'is_windy' => $this->isWindy(),
            'is_high_humidity' => $this->isHighHumidity(),
        ];
    }
}