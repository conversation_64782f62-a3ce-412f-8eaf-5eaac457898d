<?php

namespace App\Domain\Weather\Models;

class Location
{
    public function __construct(
        private readonly string $city,
        private readonly string $country,
        private readonly float $latitude,
        private readonly float $longitude,
        private readonly ?string $state = null,
        private readonly ?string $countryCode = null,
        private readonly ?string $timezone = null
    ) {}

    public static function create(array $data): self
    {
        return new self(
            city: $data['city'],
            country: $data['country'],
            latitude: (float) $data['latitude'],
            longitude: (float) $data['longitude'],
            state: $data['state'] ?? null,
            countryCode: $data['country_code'] ?? null,
            timezone: $data['timezone'] ?? null
        );
    }

    public static function fromCoordinates(float $latitude, float $longitude, string $city = 'Unknown', string $country = 'Unknown'): self
    {
        return new self(
            city: $city,
            country: $country,
            latitude: $latitude,
            longitude: $longitude
        );
    }

    public static function fromCityCountry(string $city, string $country, ?string $countryCode = null): self
    {
        return new self(
            city: $city,
            country: $country,
            latitude: 0.0, // Will be resolved by geocoding service
            longitude: 0.0, // Will be resolved by geocoding service
            countryCode: $countryCode
        );
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getLatitude(): float
    {
        return $this->latitude;
    }

    public function getLongitude(): float
    {
        return $this->longitude;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    public function getTimezone(): ?string
    {
        return $this->timezone;
    }

    public function getCoordinates(): array
    {
        return [
            'latitude' => $this->latitude,
            'longitude' => $this->longitude
        ];
    }

    public function getFullName(): string
    {
        $parts = [$this->city];
        
        if ($this->state) {
            $parts[] = $this->state;
        }
        
        $parts[] = $this->country;
        
        return implode(', ', $parts);
    }

    public function toString(): string
    {
        return $this->getFullName();
    }

    public function hasCoordinates(): bool
    {
        return $this->latitude !== 0.0 && $this->longitude !== 0.0;
    }

    public function distanceTo(Location $other): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $lat1 = deg2rad($this->latitude);
        $lon1 = deg2rad($this->longitude);
        $lat2 = deg2rad($other->getLatitude());
        $lon2 = deg2rad($other->getLongitude());

        $deltaLat = $lat2 - $lat1;
        $deltaLon = $lon2 - $lon1;

        $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
             cos($lat1) * cos($lat2) *
             sin($deltaLon / 2) * sin($deltaLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    public function equals(Location $other): bool
    {
        return $this->latitude === $other->getLatitude() &&
               $this->longitude === $other->getLongitude();
    }

    public function toArray(): array
    {
        return [
            'city' => $this->city,
            'country' => $this->country,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'state' => $this->state,
            'country_code' => $this->countryCode,
            'timezone' => $this->timezone,
            'full_name' => $this->getFullName(),
            'coordinates' => $this->getCoordinates()
        ];
    }
}