<?php

namespace App\Domain\Weather\Services;

use App\Domain\Weather\Contracts\WeatherRepositoryInterface;
use App\Domain\Weather\Contracts\WeatherCacheInterface;
use App\Domain\Weather\Models\Location;
use App\Domain\Weather\Models\Weather;
use Illuminate\Support\Facades\Log;

class WeatherService
{
    public function __construct(
        private readonly WeatherRepositoryInterface $weatherRepository,
        private readonly WeatherCacheInterface $weatherCache
    ) {}

    /**
     * Get current weather with caching
     */
    public function getCurrentWeather(Location $location): Weather
    {
        if ($this->weatherCache->isEnabled()) {
            $cacheKey = $this->weatherCache->generateCurrentWeatherKey($location);
            
            if ($this->weatherCache->has($cacheKey)) {
                Log::info('Weather data retrieved from cache', ['location' => $location->getFullName()]);
                return $this->weatherCache->get($cacheKey);
            }
        }

        try {
            $weather = $this->weatherRepository->getCurrentWeather($location);
            
            if ($this->weatherCache->isEnabled()) {
                $this->weatherCache->put(
                    $this->weatherCache->generateCurrentWeatherKey($location),
                    $weather,
                    $this->weatherCache->getTtl()
                );
                Log::info('Weather data cached', ['location' => $location->getFullName()]);
            }

            return $weather;
        } catch (\Exception $e) {
            Log::error('Failed to fetch current weather', [
                'location' => $location->getFullName(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get weather forecast with caching
     */
    public function getForecast(Location $location, int $days = 5): array
    {
        if ($this->weatherCache->isEnabled()) {
            $cacheKey = $this->weatherCache->generateForecastKey($location, $days);
            
            if ($this->weatherCache->has($cacheKey)) {
                Log::info('Forecast data retrieved from cache', [
                    'location' => $location->getFullName(),
                    'days' => $days
                ]);
                return $this->weatherCache->get($cacheKey);
            }
        }

        try {
            $forecast = $this->weatherRepository->getForecast($location, $days);
            
            if ($this->weatherCache->isEnabled()) {
                $this->weatherCache->put(
                    $this->weatherCache->generateForecastKey($location, $days),
                    $forecast,
                    $this->weatherCache->getTtl()
                );
                Log::info('Forecast data cached', [
                    'location' => $location->getFullName(),
                    'days' => $days
                ]);
            }

            return $forecast;
        } catch (\Exception $e) {
            Log::error('Failed to fetch weather forecast', [
                'location' => $location->getFullName(),
                'days' => $days,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get historical weather data
     */
    public function getHistoricalWeather(Location $location, \DateTimeInterface $date): Weather
    {
        if ($this->weatherCache->isEnabled()) {
            $cacheKey = $this->weatherCache->generateHistoricalKey($location, $date);
            
            if ($this->weatherCache->has($cacheKey)) {
                Log::info('Historical weather data retrieved from cache', [
                    'location' => $location->getFullName(),
                    'date' => $date->format('Y-m-d')
                ]);
                return $this->weatherCache->get($cacheKey);
            }
        }

        try {
            $weather = $this->weatherRepository->getHistoricalWeather($location, $date);
            
            if ($this->weatherCache->isEnabled()) {
                // Cache historical data for longer since it doesn't change
                $this->weatherCache->put(
                    $this->weatherCache->generateHistoricalKey($location, $date),
                    $weather,
                    $this->weatherCache->getTtl() * 24 // 24x longer cache for historical data
                );
                Log::info('Historical weather data cached', [
                    'location' => $location->getFullName(),
                    'date' => $date->format('Y-m-d')
                ]);
            }

            return $weather;
        } catch (\Exception $e) {
            Log::error('Failed to fetch historical weather', [
                'location' => $location->getFullName(),
                'date' => $date->format('Y-m-d'),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Search locations
     */
    public function searchLocations(string $query): array
    {
        try {
            return $this->weatherRepository->searchLocations($query);
        } catch (\Exception $e) {
            Log::error('Failed to search locations', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get location from coordinates
     */
    public function getLocationFromCoordinates(float $latitude, float $longitude): Location
    {
        try {
            return $this->weatherRepository->getLocationFromCoordinates($latitude, $longitude);
        } catch (\Exception $e) {
            Log::error('Failed to get location from coordinates', [
                'latitude' => $latitude,
                'longitude' => $longitude,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get weather for default location (Jakarta)
     */
    public function getDefaultLocationWeather(): Weather
    {
        $defaultLocation = Location::create(config('weather.default_location'));
        return $this->getCurrentWeather($defaultLocation);
    }

    /**
     * Get weather summary for multiple locations
     */
    public function getMultiLocationWeather(array $locations): array
    {
        $results = [];
        
        foreach ($locations as $location) {
            try {
                $results[] = [
                    'location' => $location,
                    'weather' => $this->getCurrentWeather($location),
                    'status' => 'success'
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'location' => $location,
                    'weather' => null,
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
                Log::warning('Failed to get weather for location', [
                    'location' => $location->getFullName(),
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Check if weather service is available
     */
    public function isServiceAvailable(): bool
    {
        try {
            return $this->weatherRepository->isAvailable();
        } catch (\Exception $e) {
            Log::error('Weather service availability check failed', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get weather provider information
     */
    public function getProviderInfo(): array
    {
        return [
            'provider' => $this->weatherRepository->getProviderName(),
            'available' => $this->isServiceAvailable(),
            'cache_enabled' => $this->weatherCache->isEnabled(),
            'cache_ttl' => $this->weatherCache->getTtl()
        ];
    }

    /**
     * Clear weather cache
     */
    public function clearCache(): bool
    {
        try {
            $result = $this->weatherCache->flush();
            Log::info('Weather cache cleared');
            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to clear weather cache', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get weather recommendations based on conditions
     */
    public function getWeatherRecommendations(Weather $weather): array
    {
        $recommendations = [];

        if ($weather->isHot()) {
            $recommendations[] = [
                'type' => 'clothing',
                'message' => 'Gunakan pakaian ringan dan berwarna terang',
                'icon' => '👕'
            ];
            $recommendations[] = [
                'type' => 'activity',
                'message' => 'Hindari aktivitas outdoor pada siang hari',
                'icon' => '🌞'
            ];
        }

        if ($weather->isCold()) {
            $recommendations[] = [
                'type' => 'clothing',
                'message' => 'Gunakan jaket atau pakaian hangat',
                'icon' => '🧥'
            ];
        }

        if ($weather->isWindy()) {
            $recommendations[] = [
                'type' => 'activity',
                'message' => 'Hati-hati dengan angin kencang',
                'icon' => '💨'
            ];
        }

        if ($weather->isHighHumidity()) {
            $recommendations[] = [
                'type' => 'comfort',
                'message' => 'Kelembaban tinggi, gunakan AC atau kipas',
                'icon' => '💧'
            ];
        }

        if ($weather->getUvIndex() > 7) {
            $recommendations[] = [
                'type' => 'health',
                'message' => 'UV Index tinggi, gunakan sunscreen',
                'icon' => '☀️'
            ];
        }

        return $recommendations;
    }
}