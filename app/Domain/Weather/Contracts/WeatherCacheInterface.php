<?php

namespace App\Domain\Weather\Contracts;

use App\Domain\Weather\Models\Location;
use App\Domain\Weather\Models\Weather;

interface WeatherCacheInterface
{
    /**
     * Store weather data in cache
     */
    public function put(string $key, Weather $weather, int $ttl = null): bool;

    /**
     * Retrieve weather data from cache
     */
    public function get(string $key): ?Weather;

    /**
     * Check if weather data exists in cache
     */
    public function has(string $key): bool;

    /**
     * Remove weather data from cache
     */
    public function forget(string $key): bool;

    /**
     * Clear all weather cache
     */
    public function flush(): bool;

    /**
     * Generate cache key for current weather
     */
    public function generateCurrentWeatherKey(Location $location): string;

    /**
     * Generate cache key for forecast weather
     */
    public function generateForecastKey(Location $location, int $days): string;

    /**
     * Generate cache key for historical weather
     */
    public function generateHistoricalKey(Location $location, \DateTimeInterface $date): string;

    /**
     * Get cache TTL in minutes
     */
    public function getTtl(): int;

    /**
     * Check if cache is enabled
     */
    public function isEnabled(): bool;
}