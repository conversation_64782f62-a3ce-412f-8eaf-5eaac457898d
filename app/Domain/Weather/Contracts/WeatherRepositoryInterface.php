<?php

namespace App\Domain\Weather\Contracts;

use App\Domain\Weather\Models\Location;
use App\Domain\Weather\Models\Weather;

interface WeatherRepositoryInterface
{
    /**
     * Get current weather for a specific location
     */
    public function getCurrentWeather(Location $location): Weather;

    /**
     * Get weather forecast for multiple days
     */
    public function getForecast(Location $location, int $days = 5): array;

    /**
     * Get historical weather data
     */
    public function getHistoricalWeather(Location $location, \DateTimeInterface $date): Weather;

    /**
     * Search locations by query string
     */
    public function searchLocations(string $query): array;

    /**
     * Get location details from coordinates
     */
    public function getLocationFromCoordinates(float $latitude, float $longitude): Location;

    /**
     * Check if the weather service is available
     */
    public function isAvailable(): bool;

    /**
     * Get service provider name
     */
    public function getProviderName(): string;
}