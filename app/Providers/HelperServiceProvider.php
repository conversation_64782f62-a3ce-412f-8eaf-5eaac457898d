<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Helpers\AuthHelper;
use App\Helpers\ImageHelper;

class HelperServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register AuthHelper as singleton
        $this->app->singleton('auth.helper', function ($app) {
            return new AuthHelper();
        });

        // Register ImageHelper as singleton
        $this->app->singleton('image.helper', function ($app) {
            return new ImageHelper();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load helper functions
        $this->loadHelperFunctions();
    }

    /**
     * Load global helper functions
     *
     * @return void
     */
    private function loadHelperFunctions(): void
    {
        if (!function_exists('auth_helper')) {
            /**
             * Get AuthHelper instance
             *
             * @return AuthHelper
             */
            function auth_helper(): AuthHelper
            {
                return app('auth.helper');
            }
        }

        if (!function_exists('image_helper')) {
            /**
             * Get ImageHelper instance
             *
             * @return ImageHelper
             */
            function image_helper(): ImageHelper
            {
                return app('image.helper');
            }
        }

        if (!function_exists('current_user')) {
            /**
             * Get current authenticated user
             *
             * @return \Illuminate\Contracts\Auth\Authenticatable|null
             */
            function current_user()
            {
                return AuthHelper::user();
            }
        }

        if (!function_exists('user_data')) {
            /**
             * Get current user data as array
             *
             * @return array|null
             */
            function user_data(): ?array
            {
                return AuthHelper::userData();
            }
        }

        if (!function_exists('is_authenticated')) {
            /**
             * Check if user is authenticated
             *
             * @return bool
             */
            function is_authenticated(): bool
            {
                return AuthHelper::check();
            }
        }
    }
}
