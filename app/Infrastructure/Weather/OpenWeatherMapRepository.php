<?php

namespace App\Infrastructure\Weather;

use App\Domain\Weather\Contracts\WeatherRepositoryInterface;
use App\Domain\Weather\Models\Location;
use App\Domain\Weather\Models\Weather;
use Carbon\Carbon;
use Illuminate\Http\Client\Factory as HttpClient;
use Illuminate\Support\Facades\Log;

class OpenWeatherMapRepository implements WeatherRepositoryInterface
{
    private const BASE_URL = 'https://api.openweathermap.org/data/2.5';
    private const GEO_URL = 'https://api.openweathermap.org/geo/1.0';

    public function __construct(
        private readonly HttpClient $httpClient,
        private readonly string $apiKey,
        private readonly string $baseUrl,
        private readonly int $timeout,
        private readonly string $units,
        private readonly string $lang
    ) {}

    public function getCurrentWeather(Location $location): Weather
    {
        $url = $this->baseUrl . '/weather';
        
        $params = [
            'appid' => $this->apiKey,
            'units' => $this->units,
            'lang' => $this->lang,
        ];

        if ($location->hasCoordinates()) {
            $params['lat'] = $location->getLatitude();
            $params['lon'] = $location->getLongitude();
        } else {
            $params['q'] = $location->getCity() . ',' . $location->getCountryCode();
        }

        try {
            $response = $this->httpClient
                ->timeout($this->timeout)
                ->get($url, $params);

            if (!$response->successful()) {
                throw new \Exception("OpenWeatherMap API error: " . $response->body());
            }

            $data = $response->json();
            return $this->transformCurrentWeatherData($data, $location);
        } catch (\Exception $e) {
            Log::error('OpenWeatherMap API error', [
                'url' => $url,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function getForecast(Location $location, int $days = 5): array
    {
        $url = $this->baseUrl . '/forecast';
        
        $params = [
            'appid' => $this->apiKey,
            'units' => $this->units,
            'lang' => $this->lang,
            'cnt' => $days * 8, // 8 forecasts per day (every 3 hours)
        ];

        if ($location->hasCoordinates()) {
            $params['lat'] = $location->getLatitude();
            $params['lon'] = $location->getLongitude();
        } else {
            $params['q'] = $location->getCity() . ',' . $location->getCountryCode();
        }

        try {
            $response = $this->httpClient
                ->timeout($this->timeout)
                ->get($url, $params);

            if (!$response->successful()) {
                throw new \Exception("OpenWeatherMap API error: " . $response->body());
            }

            $data = $response->json();
            return $this->transformForecastData($data, $location);
        } catch (\Exception $e) {
            Log::error('OpenWeatherMap forecast API error', [
                'url' => $url,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function getHistoricalWeather(Location $location, \DateTimeInterface $date): Weather
    {
        $url = $this->baseUrl . '/onecall/timemachine';
        
        $params = [
            'appid' => $this->apiKey,
            'lat' => $location->getLatitude(),
            'lon' => $location->getLongitude(),
            'dt' => $date->getTimestamp(),
            'units' => $this->units,
            'lang' => $this->lang,
        ];

        try {
            $response = $this->httpClient
                ->timeout($this->timeout)
                ->get($url, $params);

            if (!$response->successful()) {
                throw new \Exception("OpenWeatherMap historical API error: " . $response->body());
            }

            $data = $response->json();
            return $this->transformHistoricalWeatherData($data, $location);
        } catch (\Exception $e) {
            Log::error('OpenWeatherMap historical API error', [
                'url' => $url,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function searchLocations(string $query): array
    {
        $url = self::GEO_URL . '/direct';
        
        $params = [
            'q' => $query,
            'limit' => 5,
            'appid' => $this->apiKey,
        ];

        try {
            $response = $this->httpClient
                ->timeout($this->timeout)
                ->get($url, $params);

            if (!$response->successful()) {
                throw new \Exception("OpenWeatherMap geocoding API error: " . $response->body());
            }

            $data = $response->json();
            return $this->transformLocationData($data);
        } catch (\Exception $e) {
            Log::error('OpenWeatherMap geocoding API error', [
                'url' => $url,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function getLocationFromCoordinates(float $latitude, float $longitude): Location
    {
        $url = self::GEO_URL . '/reverse';
        
        $params = [
            'lat' => $latitude,
            'lon' => $longitude,
            'limit' => 1,
            'appid' => $this->apiKey,
        ];

        try {
            $response = $this->httpClient
                ->timeout($this->timeout)
                ->get($url, $params);

            if (!$response->successful()) {
                throw new \Exception("OpenWeatherMap reverse geocoding API error: " . $response->body());
            }

            $data = $response->json();
            
            if (empty($data)) {
                return Location::fromCoordinates($latitude, $longitude);
            }

            return $this->transformSingleLocationData($data[0]);
        } catch (\Exception $e) {
            Log::error('OpenWeatherMap reverse geocoding API error', [
                'url' => $url,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            
            // Return basic location on error
            return Location::fromCoordinates($latitude, $longitude);
        }
    }

    public function isAvailable(): bool
    {
        try {
            $response = $this->httpClient
                ->timeout(5)
                ->get($this->baseUrl . '/weather', [
                    'appid' => $this->apiKey,
                    'q' => 'Jakarta,ID',
                    'units' => $this->units,
                ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::warning('OpenWeatherMap availability check failed', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function getProviderName(): string
    {
        return 'OpenWeatherMap';
    }

    private function transformCurrentWeatherData(array $data, Location $location): Weather
    {
        return Weather::create([
            'location' => $location->getFullName(),
            'temperature' => $data['main']['temp'],
            'feels_like' => $data['main']['feels_like'],
            'condition' => $data['weather'][0]['main'],
            'description' => ucfirst($data['weather'][0]['description']),
            'humidity' => $data['main']['humidity'],
            'wind_speed' => $data['wind']['speed'] ?? 0,
            'wind_direction' => $data['wind']['deg'] ?? 0,
            'pressure' => $data['main']['pressure'],
            'visibility' => isset($data['visibility']) ? $data['visibility'] / 1000 : 10, // Convert to km
            'uv_index' => 0, // Not available in current weather endpoint
            'observed_at' => Carbon::createFromTimestamp($data['dt']),
            'icon' => $data['weather'][0]['icon'],
            'temperature_min' => $data['main']['temp_min'],
            'temperature_max' => $data['main']['temp_max'],
            'cloudiness' => $data['clouds']['all'] ?? 0,
            'sunrise' => isset($data['sys']['sunrise']) ? Carbon::createFromTimestamp($data['sys']['sunrise']) : null,
            'sunset' => isset($data['sys']['sunset']) ? Carbon::createFromTimestamp($data['sys']['sunset']) : null,
        ]);
    }

    private function transformForecastData(array $data, Location $location): array
    {
        $forecasts = [];
        
        foreach ($data['list'] as $item) {
            $forecasts[] = Weather::create([
                'location' => $location->getFullName(),
                'temperature' => $item['main']['temp'],
                'feels_like' => $item['main']['feels_like'],
                'condition' => $item['weather'][0]['main'],
                'description' => ucfirst($item['weather'][0]['description']),
                'humidity' => $item['main']['humidity'],
                'wind_speed' => $item['wind']['speed'] ?? 0,
                'wind_direction' => $item['wind']['deg'] ?? 0,
                'pressure' => $item['main']['pressure'],
                'visibility' => isset($item['visibility']) ? $item['visibility'] / 1000 : 10,
                'uv_index' => 0,
                'observed_at' => Carbon::createFromTimestamp($item['dt']),
                'icon' => $item['weather'][0]['icon'],
                'temperature_min' => $item['main']['temp_min'],
                'temperature_max' => $item['main']['temp_max'],
                'cloudiness' => $item['clouds']['all'] ?? 0,
            ]);
        }

        return $forecasts;
    }

    private function transformHistoricalWeatherData(array $data, Location $location): Weather
    {
        $current = $data['current'];
        
        return Weather::create([
            'location' => $location->getFullName(),
            'temperature' => $current['temp'],
            'feels_like' => $current['feels_like'],
            'condition' => $current['weather'][0]['main'],
            'description' => ucfirst($current['weather'][0]['description']),
            'humidity' => $current['humidity'],
            'wind_speed' => $current['wind_speed'] ?? 0,
            'wind_direction' => $current['wind_deg'] ?? 0,
            'pressure' => $current['pressure'],
            'visibility' => isset($current['visibility']) ? $current['visibility'] / 1000 : 10,
            'uv_index' => $current['uvi'] ?? 0,
            'observed_at' => Carbon::createFromTimestamp($current['dt']),
            'icon' => $current['weather'][0]['icon'],
            'cloudiness' => $current['clouds'] ?? 0,
            'sunrise' => isset($current['sunrise']) ? Carbon::createFromTimestamp($current['sunrise']) : null,
            'sunset' => isset($current['sunset']) ? Carbon::createFromTimestamp($current['sunset']) : null,
        ]);
    }

    private function transformLocationData(array $data): array
    {
        $locations = [];
        
        foreach ($data as $item) {
            $locations[] = $this->transformSingleLocationData($item);
        }

        return $locations;
    }

    private function transformSingleLocationData(array $item): Location
    {
        return Location::create([
            'city' => $item['name'],
            'country' => $item['country'],
            'latitude' => $item['lat'],
            'longitude' => $item['lon'],
            'state' => $item['state'] ?? null,
            'country_code' => $item['country'],
        ]);
    }
}