<?php

namespace App\Infrastructure\Weather;

use App\Domain\Weather\Contracts\WeatherCacheInterface;
use App\Domain\Weather\Models\Location;
use App\Domain\Weather\Models\Weather;
use Illuminate\Cache\CacheManager;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class WeatherCache implements WeatherCacheInterface
{
    private readonly string $prefix;
    private readonly int $ttl;
    private readonly bool $enabled;

    public function __construct(
        private readonly CacheManager $cache
    ) {
        $this->prefix = config('weather.cache.prefix', 'weather');
        $this->ttl = config('weather.cache.ttl', 10);
        $this->enabled = config('weather.cache.enabled', true);
    }

    public function put(string $key, Weather $weather, int $ttl = null): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            $cacheKey = $this->prefix . ':' . $key;
            $ttlMinutes = $ttl ?? $this->ttl;
            
            return $this->cache->put($cacheKey, $this->serializeWeather($weather), now()->addMinutes($ttlMinutes));
        } catch (\Exception $e) {
            Log::error('Failed to cache weather data', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function get(string $key): ?Weather
    {
        if (!$this->enabled) {
            return null;
        }

        try {
            $cacheKey = $this->prefix . ':' . $key;
            $data = $this->cache->get($cacheKey);
            
            if ($data === null) {
                return null;
            }

            return $this->unserializeWeather($data);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve weather data from cache', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    public function has(string $key): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            $cacheKey = $this->prefix . ':' . $key;
            return $this->cache->has($cacheKey);
        } catch (\Exception $e) {
            Log::error('Failed to check weather cache existence', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function forget(string $key): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            $cacheKey = $this->prefix . ':' . $key;
            return $this->cache->forget($cacheKey);
        } catch (\Exception $e) {
            Log::error('Failed to remove weather data from cache', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function flush(): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            // Get all cache keys with our prefix
            $pattern = $this->prefix . ':*';
            
            // This is cache store dependent - for Redis/Memcached
            if (method_exists($this->cache->getStore(), 'flush')) {
                // For stores that support flushing by pattern
                return $this->cache->getStore()->flush();
            }
            
            // For file cache or others, we'll need to implement differently
            // This is a simplified version - in production you might want to track keys
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to flush weather cache', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function generateCurrentWeatherKey(Location $location): string
    {
        return sprintf(
            'current:%s:%s',
            $this->sanitizeKeyComponent($location->getCity()),
            $this->sanitizeKeyComponent($location->getCountry())
        );
    }

    public function generateForecastKey(Location $location, int $days): string
    {
        return sprintf(
            'forecast:%s:%s:%d',
            $this->sanitizeKeyComponent($location->getCity()),
            $this->sanitizeKeyComponent($location->getCountry()),
            $days
        );
    }

    public function generateHistoricalKey(Location $location, \DateTimeInterface $date): string
    {
        return sprintf(
            'historical:%s:%s:%s',
            $this->sanitizeKeyComponent($location->getCity()),
            $this->sanitizeKeyComponent($location->getCountry()),
            $date->format('Y-m-d')
        );
    }

    public function getTtl(): int
    {
        return $this->ttl;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    private function serializeWeather(Weather $weather): array
    {
        return $weather->toArray();
    }

    /**
     * @param array $data
     */
    private function unserializeWeather(array $data): Weather
    {
        // Convert string dates back to Carbon instances
        $data['observed_at'] = Carbon::parse($data['observed_at']);
        
        if (isset($data['sunrise']) && $data['sunrise']) {
            $data['sunrise'] = Carbon::parse($data['sunrise']);
        }
        
        if (isset($data['sunset']) && $data['sunset']) {
            $data['sunset'] = Carbon::parse($data['sunset']);
        }

        return Weather::create($data);
    }

    private function sanitizeKeyComponent(string $component): string
    {
        // Remove special characters that might cause issues in cache keys
        return preg_replace('/[^a-zA-Z0-9\-_]/', '', strtolower($component));
    }

    /**
     * Generate cache key for coordinates-based lookup
     */
    public function generateCoordinatesKey(float $latitude, float $longitude): string
    {
        return sprintf(
            'coords:%.4f:%.4f',
            $latitude,
            $longitude
        );
    }

    /**
     * Cache weather data with coordinates key
     */
    public function putByCoordinates(float $latitude, float $longitude, Weather $weather, int $ttl = null): bool
    {
        $key = $this->generateCoordinatesKey($latitude, $longitude);
        return $this->put($key, $weather, $ttl);
    }

    /**
     * Get weather data by coordinates
     */
    public function getByCoordinates(float $latitude, float $longitude): ?Weather
    {
        $key = $this->generateCoordinatesKey($latitude, $longitude);
        return $this->get($key);
    }

    /**
     * Check if coordinates have cached data
     */
    public function hasByCoordinates(float $latitude, float $longitude): bool
    {
        $key = $this->generateCoordinatesKey($latitude, $longitude);
        return $this->has($key);
    }

    /**
     * Get cache statistics
     * @return array<string,mixed>
     */
    public function getStats(): array
    {
        return [
            'enabled' => $this->enabled,
            'prefix' => $this->prefix,
            'ttl_minutes' => $this->ttl,
            'store' => get_class($this->cache->getStore()),
        ];
    }

    /**
     * Warm up cache with popular locations
     * @param array $locations
     * @param callable $weatherFetcher
     * @return array<int,array<string,mixed>>
     */
    public function warmUp(array $locations, callable $weatherFetcher): array
    {
        $results = [];
        
        foreach ($locations as $location) {
            try {
                $weather = $weatherFetcher($location);
                $key = $this->generateCurrentWeatherKey($location);
                $success = $this->put($key, $weather);
                
                $results[] = [
                    'location' => $location->getFullName(),
                    'cached' => $success,
                    'key' => $key
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'location' => $location->getFullName(),
                    'cached' => false,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
}