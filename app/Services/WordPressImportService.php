<?php

namespace App\Services;

use App\Models\Category;
use App\Models\News;
use App\Models\User;
use App\Services\AuthorMappingService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class WordPressImportService
{
    protected AuthorMappingService $authorMappingService;

    public function __construct(AuthorMappingService $authorMappingService)
    {
        $this->authorMappingService = $authorMappingService;
    }
    /**
     * Preview import data from SQL file
     */
    public function previewImport(string $filePath, array $options): array
    {
        $sqlContent = file_get_contents($filePath);
        $posts = $this->parseWordPressPosts($sqlContent);

        // Extract and analyze WordPress categories and authors
        $wpCategories = $this->extractWordPressCategories($sqlContent);
        $categoryAnalysis = $this->analyzeCategoryMapping($wpCategories);

        $wpAuthors = $this->authorMappingService->extractWordPressAuthors($sqlContent);
        $authorAnalysis = $this->authorMappingService->analyzeAuthorMapping($wpAuthors);

        // Extract WordPress domain from SQL
        $wordpressDomain = $this->extractWordPressDomain($sqlContent);

        // Extract and analyze images
        $imageAnalysis = $this->analyzeImages($sqlContent, $posts);

        $preview = [
            'total_posts' => count($posts),
            'sample_posts' => array_slice($posts, 0, 5),
            'post_statuses' => array_count_values(array_column($posts, 'post_status')),
            'categories_found' => $wpCategories,
            'category_analysis' => $categoryAnalysis,
            'authors_found' => $wpAuthors,
            'author_analysis' => $authorAnalysis,
            'image_analysis' => $imageAnalysis,
            'wordpress_domain' => $wordpressDomain,
            'date_range' => $this->getDateRange($posts)
        ];

        return $preview;
    }

    /**
     * Import WordPress posts from SQL file
     */
    public function importFromSql(string $filePath, array $options): array
    {
        $sqlContent = file_get_contents($filePath);
        $posts = $this->parseWordPressPosts($sqlContent);
        
        $imported = 0;
        $skipped = 0;
        $errors = [];
        
        // Analyze categories and authors before import
        $sqlContent = file_get_contents($filePath);
        $wpCategories = $this->extractWordPressCategories($sqlContent);
        $wpAuthors = $this->authorMappingService->extractWordPressAuthors($sqlContent);

        $this->cacheMissingCategories($wpCategories);
        $this->authorMappingService->cacheMissingAuthors($wpAuthors);

        // Auto-create missing categories if enabled
        if ($options['create_missing_categories'] ?? true) {
            $this->autoCreateMissingCategories($wpCategories);
        }

        foreach ($posts as $post) {
            try {
                if ($this->shouldSkipPost($post, $options)) {
                    $skipped++;
                    continue;
                }

                $newsData = $this->mapWordPressToNews($post, $options);

                if ($options['skip_duplicates'] && $this->isDuplicate($newsData)) {
                    $skipped++;
                    continue;
                }

                $news = News::create($newsData);

                // Import featured image if enabled
                if ($options['import_images'] ?? false) {
                    $imageImported = $this->importFeaturedImage($news, $post, $sqlContent, $options['wordpress_domain'] ?? null);
                    if (!$imageImported) {
                        $result['images_not_found'] = ($result['images_not_found'] ?? 0) + 1;
                    }
                }

                $imported++;

            } catch (\Exception $e) {
                $errors[] = "Failed to import post '{$post['post_title']}': " . $e->getMessage();
                Log::error("WordPress import error for post {$post['ID']}: " . $e->getMessage());
            }
        }
        
        return [
            'imported' => $imported,
            'skipped' => $skipped,
            'errors' => $errors,
            'total_processed' => count($posts)
        ];
    }

    /**
     * Parse WordPress posts from SQL content
     */
    protected function parseWordPressPosts(string $sqlContent): array
    {
        $posts = [];
        
        // Extract INSERT statements for wp_posts table
        $pattern = '/INSERT INTO `?wp_posts`?\s*\([^)]+\)\s*VALUES\s*(.+?);/is';
        
        if (preg_match_all($pattern, $sqlContent, $matches)) {
            foreach ($matches[1] as $valuesString) {
                $postRows = $this->parseInsertValues($valuesString);
                $posts = array_merge($posts, $postRows);
            }
        }
        
        // Filter only published posts and pages
        return array_filter($posts, function($post) {
            return in_array($post['post_type'], ['post', 'page']) && 
                   in_array($post['post_status'], ['publish', 'draft', 'private']);
        });
    }

    /**
     * Parse INSERT VALUES string into array of posts
     */
    protected function parseInsertValues(string $valuesString): array
    {
        $posts = [];

        // WordPress wp_posts table structure
        $columns = [
            'ID', 'post_author', 'post_date', 'post_date_gmt', 'post_content',
            'post_title', 'post_excerpt', 'post_status', 'comment_status',
            'ping_status', 'post_password', 'post_name', 'to_ping', 'pinged',
            'post_modified', 'post_modified_gmt', 'post_content_filtered',
            'post_parent', 'guid', 'menu_order', 'post_type', 'post_mime_type',
            'comment_count'
        ];

        // More robust parsing for SQL VALUES
        $pattern = '/\(([^)]*(?:\([^)]*\)[^)]*)*)\)/s';
        preg_match_all($pattern, $valuesString, $matches);

        foreach ($matches[1] as $row) {
            $values = $this->parseRowValues($row);

            if (count($values) >= count($columns)) {
                $post = array_combine($columns, array_slice($values, 0, count($columns)));
                // Clean up the values
                $post = array_map(function($value) {
                    return is_string($value) ? trim($value, "'\"") : $value;
                }, $post);
                $posts[] = $post;
            }
        }

        return $posts;
    }

    /**
     * Parse individual row values handling nested quotes and commas
     */
    protected function parseRowValues(string $row): array
    {
        $values = [];
        $current = '';
        $inQuotes = false;
        $quoteChar = '';
        $i = 0;

        while ($i < strlen($row)) {
            $char = $row[$i];

            if (!$inQuotes && ($char === "'" || $char === '"')) {
                $inQuotes = true;
                $quoteChar = $char;
                $current .= $char;
            } elseif ($inQuotes && $char === $quoteChar) {
                // Check for escaped quote
                if ($i + 1 < strlen($row) && $row[$i + 1] === $quoteChar) {
                    $current .= $char . $char;
                    $i++; // Skip next character
                } else {
                    $inQuotes = false;
                    $current .= $char;
                }
            } elseif (!$inQuotes && $char === ',') {
                $values[] = trim($current);
                $current = '';
            } else {
                $current .= $char;
            }

            $i++;
        }

        // Add the last value
        if ($current !== '') {
            $values[] = trim($current);
        }

        return $values;
    }

    /**
     * Map WordPress post data to News model structure
     */
    protected function mapWordPressToNews(array $post, array $options): array
    {
        $statusMapping = $options['post_status_mapping'] ?? [
            'publish' => 'Accept',
            'draft' => 'Pending',
            'private' => 'Pending'
        ];

        return [
            'title' => $this->cleanText($post['post_title']),
            'content' => $this->cleanContent($post['post_content']),
            'status' => $statusMapping[$post['post_status']] ?? 'Pending',
            'user_id' => $this->mapAuthor($post, $options),
            'category_id' => $this->mapCategory($post, $options),
            'views' => 0,
            'created_at' => $post['post_date'],
            'updated_at' => $post['post_modified'] ?? $post['post_date']
        ];
    }

    /**
     * Map WordPress author to local user
     */
    protected function mapAuthor(array $post, array $options): int
    {
        // For now, use placeholder author
        // In future, could implement author mapping based on post_author field
        $placeholderAuthor = $this->authorMappingService->getPlaceholderAuthor();
        return $placeholderAuthor->id;
    }

    /**
     * Map WordPress category to local category
     */
    protected function mapCategory(array $post, array $options): int
    {
        // Try to find existing category first
        $fallbackCategoryId = $options['fallback_category_id'] ?? null;

        // If no fallback category specified, create or find a default one
        if (!$fallbackCategoryId) {
            $defaultCategory = Category::firstOrCreate(
                ['name' => 'WordPress Import'],
                [
                    'name' => 'WordPress Import',
                    'slug' => Str::slug('WordPress Import'),
                    'views' => 0
                ]
            );
            $fallbackCategoryId = $defaultCategory->id;
        }

        return $fallbackCategoryId;
    }

    /**
     * Check if post should be skipped
     */
    protected function shouldSkipPost(array $post, array $options): bool
    {
        // Skip if title or content is empty
        if (empty(trim($post['post_title'])) || empty(trim($post['post_content']))) {
            return true;
        }
        
        // Skip if post is too old (optional filter)
        if (isset($options['min_date'])) {
            $postDate = strtotime($post['post_date']);
            $minDate = strtotime($options['min_date']);
            if ($postDate < $minDate) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if news already exists (duplicate detection)
     */
    protected function isDuplicate(array $newsData): bool
    {
        return News::where('title', $newsData['title'])
                  ->where('created_at', $newsData['created_at'])
                  ->exists();
    }

    /**
     * Clean text content
     */
    protected function cleanText(string $text): string
    {
        return trim(html_entity_decode($text, ENT_QUOTES, 'UTF-8'));
    }

    /**
     * Clean HTML content
     */
    protected function cleanContent(string $content): string
    {
        // Remove WordPress shortcodes
        $content = preg_replace('/\[([^\]]*)\]/', '', $content);
        
        // Clean up HTML
        $content = html_entity_decode($content, ENT_QUOTES, 'UTF-8');
        
        return trim($content);
    }

    /**
     * Extract WordPress categories from SQL content
     */
    protected function extractWordPressCategories(string $sqlContent): array
    {
        $categories = [];

        // Extract from wp_terms table (categories)
        $termsPattern = '/INSERT INTO `?wp_terms`?\s*\([^)]+\)\s*VALUES\s*(.+?);/is';
        if (preg_match_all($termsPattern, $sqlContent, $matches)) {
            foreach ($matches[1] as $valuesString) {
                $termRows = $this->parseTermsValues($valuesString);
                foreach ($termRows as $term) {
                    if (isset($term['name']) && !empty(trim($term['name']))) {
                        $categories[] = [
                            'id' => $term['term_id'] ?? null,
                            'name' => trim($term['name'], "'\""),
                            'slug' => $term['slug'] ?? null
                        ];
                    }
                }
            }
        }

        // Extract from wp_term_taxonomy table to filter only categories
        $taxonomyPattern = '/INSERT INTO `?wp_term_taxonomy`?\s*\([^)]+\)\s*VALUES\s*(.+?);/is';
        $categoryTermIds = [];
        if (preg_match_all($taxonomyPattern, $sqlContent, $matches)) {
            foreach ($matches[1] as $valuesString) {
                $taxonomyRows = $this->parseTermTaxonomyValues($valuesString);
                foreach ($taxonomyRows as $taxonomy) {
                    if (isset($taxonomy['taxonomy']) && trim($taxonomy['taxonomy'], "'\"") === 'category') {
                        $categoryTermIds[] = $taxonomy['term_id'];
                    }
                }
            }
        }

        // Filter categories to only include actual categories (not tags, etc.)
        if (!empty($categoryTermIds)) {
            $categories = array_filter($categories, function($cat) use ($categoryTermIds) {
                return in_array($cat['id'], $categoryTermIds);
            });
        }

        return array_values($categories);
    }

    /**
     * Parse wp_terms table values
     */
    protected function parseTermsValues(string $valuesString): array
    {
        $terms = [];
        $pattern = '/\(([^)]*(?:\([^)]*\)[^)]*)*)\)/s';
        preg_match_all($pattern, $valuesString, $matches);

        foreach ($matches[1] as $row) {
            $values = $this->parseRowValues($row);
            if (count($values) >= 3) {
                $terms[] = [
                    'term_id' => trim($values[0], "'\""),
                    'name' => trim($values[1], "'\""),
                    'slug' => trim($values[2], "'\"")
                ];
            }
        }

        return $terms;
    }

    /**
     * Parse wp_term_taxonomy table values
     */
    protected function parseTermTaxonomyValues(string $valuesString): array
    {
        $taxonomies = [];
        $pattern = '/\(([^)]*(?:\([^)]*\)[^)]*)*)\)/s';
        preg_match_all($pattern, $valuesString, $matches);

        foreach ($matches[1] as $row) {
            $values = $this->parseRowValues($row);
            if (count($values) >= 3) {
                $taxonomies[] = [
                    'term_taxonomy_id' => trim($values[0], "'\""),
                    'term_id' => trim($values[1], "'\""),
                    'taxonomy' => trim($values[2], "'\"")
                ];
            }
        }

        return $taxonomies;
    }

    /**
     * Analyze category mapping and identify missing categories
     */
    protected function analyzeCategoryMapping(array $wpCategories): array
    {
        $localCategories = Category::pluck('name', 'id')->toArray();
        $localCategoryNames = array_map('strtolower', array_values($localCategories));

        $missing = [];
        $existing = [];
        $suggestions = [];

        foreach ($wpCategories as $wpCategory) {
            $wpCategoryName = strtolower($wpCategory['name']);

            // Check exact match
            if (in_array($wpCategoryName, $localCategoryNames)) {
                $existing[] = $wpCategory;
                continue;
            }

            // Check similar matches
            $similarCategory = $this->findSimilarCategory($wpCategory['name'], $localCategories);
            if ($similarCategory) {
                $suggestions[] = [
                    'wp_category' => $wpCategory,
                    'suggested_local' => $similarCategory,
                    'similarity' => $this->calculateSimilarity($wpCategory['name'], $similarCategory['name'])
                ];
            } else {
                $missing[] = $wpCategory;
            }
        }

        return [
            'total_wp_categories' => count($wpCategories),
            'existing_matches' => count($existing),
            'missing_categories' => $missing,
            'suggested_mappings' => $suggestions,
            'existing_categories' => $existing
        ];
    }

    /**
     * Find similar category in local database
     */
    protected function findSimilarCategory(string $wpCategoryName, array $localCategories): ?array
    {
        $threshold = 0.7; // 70% similarity threshold
        $bestMatch = null;
        $bestSimilarity = 0;

        foreach ($localCategories as $id => $name) {
            $similarity = $this->calculateSimilarity($wpCategoryName, $name);
            if ($similarity > $threshold && $similarity > $bestSimilarity) {
                $bestSimilarity = $similarity;
                $bestMatch = ['id' => $id, 'name' => $name];
            }
        }

        return $bestMatch;
    }

    /**
     * Calculate similarity between two strings
     */
    protected function calculateSimilarity(string $str1, string $str2): float
    {
        $str1 = strtolower(trim($str1));
        $str2 = strtolower(trim($str2));

        // Use Levenshtein distance for similarity
        $maxLen = max(strlen($str1), strlen($str2));
        if ($maxLen === 0) return 1.0;

        $distance = levenshtein($str1, $str2);
        return 1 - ($distance / $maxLen);
    }

    /**
     * Cache missing categories for admin notification
     */
    protected function cacheMissingCategories(array $wpCategories): void
    {
        $analysis = $this->analyzeCategoryMapping($wpCategories);

        if (!empty($analysis['missing_categories']) || !empty($analysis['suggested_mappings'])) {
            $cacheKey = 'wordpress_import_missing_categories_' . now()->format('Y_m_d_H_i_s');

            $cacheData = [
                'timestamp' => now()->toISOString(),
                'missing_categories' => $analysis['missing_categories'],
                'suggested_mappings' => $analysis['suggested_mappings'],
                'total_missing' => count($analysis['missing_categories']),
                'total_suggestions' => count($analysis['suggested_mappings'])
            ];

            // Cache for 24 hours
            Cache::put($cacheKey, $cacheData, now()->addHours(24));

            // Store the cache key in a list for easy retrieval
            $cacheKeys = Cache::get('wordpress_import_cache_keys', []);
            $cacheKeys[] = $cacheKey;
            Cache::put('wordpress_import_cache_keys', $cacheKeys, now()->addHours(24));

            Log::info('WordPress import: Missing categories cached', [
                'cache_key' => $cacheKey,
                'missing_count' => count($analysis['missing_categories']),
                'suggestions_count' => count($analysis['suggested_mappings'])
            ]);
        }
    }

    /**
     * Auto-create missing categories that don't have good mapping suggestions
     */
    protected function autoCreateMissingCategories(array $wpCategories): void
    {
        $analysis = $this->analyzeCategoryMapping($wpCategories);
        $created = 0;

        foreach ($analysis['missing_categories'] as $missingCategory) {
            // Check if this category has a high-confidence mapping suggestion
            $hasGoodMapping = false;
            foreach ($analysis['suggested_mappings'] as $mapping) {
                if (strtolower($mapping['wp_category']['name']) === strtolower($missingCategory['name']) &&
                    $mapping['similarity'] >= 0.8) {
                    $hasGoodMapping = true;
                    break;
                }
            }

            // Only create if no good mapping exists
            if (!$hasGoodMapping) {
                try {
                    $categoryName = trim($missingCategory['name']);

                    // Check if category already exists (double-check)
                    if (!Category::where('name', $categoryName)->exists()) {
                        Category::create([
                            'name' => $categoryName,
                            'slug' => Str::slug($categoryName),
                            'views' => 0
                        ]);
                        $created++;

                        Log::info('Auto-created missing WordPress category', [
                            'category_name' => $categoryName
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to auto-create WordPress category', [
                        'category_name' => $missingCategory['name'],
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        if ($created > 0) {
            Log::info('WordPress import: Auto-created missing categories', [
                'created_count' => $created
            ]);
        }
    }

    /**
     * Get date range from posts
     */
    protected function getDateRange(array $posts): array
    {
        if (empty($posts)) {
            return ['min' => null, 'max' => null];
        }

        $dates = array_column($posts, 'post_date');

        return [
            'min' => min($dates),
            'max' => max($dates)
        ];
    }

    /**
     * Extract WordPress domain from SQL content
     */
    protected function extractWordPressDomain(string $sqlContent): ?string
    {
        // Try to extract from wp_options table (siteurl or home)
        $optionsPattern = '/INSERT INTO `?wp_options`?\s*\([^)]+\)\s*VALUES\s*(.+?);/is';
        if (preg_match_all($optionsPattern, $sqlContent, $matches)) {
            foreach ($matches[1] as $valuesString) {
                $optionRows = $this->parseOptionsValues($valuesString);
                foreach ($optionRows as $option) {
                    if (in_array($option['option_name'], ['siteurl', 'home'])) {
                        $url = trim($option['option_value'], "'\"");
                        if (filter_var($url, FILTER_VALIDATE_URL)) {
                            return parse_url($url, PHP_URL_HOST);
                        }
                    }
                }
            }
        }

        // Fallback: try to extract from post guid URLs
        $posts = $this->parseWordPressPosts($sqlContent);
        foreach ($posts as $post) {
            if (!empty($post['guid'])) {
                $url = trim($post['guid'], "'\"");
                if (filter_var($url, FILTER_VALIDATE_URL)) {
                    return parse_url($url, PHP_URL_HOST);
                }
            }
        }

        // Default fallback
        return 'lambeturah.com';
    }

    /**
     * Parse wp_options table values
     */
    protected function parseOptionsValues(string $valuesString): array
    {
        $options = [];
        $pattern = '/\(([^)]*(?:\([^)]*\)[^)]*)*)\)/s';
        preg_match_all($pattern, $valuesString, $matches);

        foreach ($matches[1] as $row) {
            $values = $this->parseRowValues($row);
            if (count($values) >= 3) {
                $options[] = [
                    'option_id' => trim($values[0], "'\""),
                    'option_name' => trim($values[1], "'\""),
                    'option_value' => trim($values[2], "'\"")
                ];
            }
        }

        return $options;
    }

    /**
     * Analyze images in WordPress SQL content
     */
    protected function analyzeImages(string $sqlContent, array $posts): array
    {
        $attachments = $this->extractAttachments($sqlContent);
        $featuredImages = $this->extractFeaturedImages($sqlContent);
        $postImageMapping = $this->mapPostImages($posts, $featuredImages, $attachments);

        $totalImages = count($attachments);
        $featuredImagesCount = count($featuredImages);
        $postsWithImages = count(array_filter($postImageMapping, fn($img) => !empty($img)));

        return [
            'total_attachments' => $totalImages,
            'featured_images_found' => $featuredImagesCount,
            'posts_with_images' => $postsWithImages,
            'posts_without_images' => count($posts) - $postsWithImages,
            'sample_images' => array_slice($attachments, 0, 3),
            'image_extensions' => $this->getImageExtensions($attachments)
        ];
    }

    /**
     * Extract WordPress attachments (images) from SQL content
     */
    protected function extractAttachments(string $sqlContent): array
    {
        $attachments = [];
        $posts = $this->parseWordPressPosts($sqlContent);

        foreach ($posts as $post) {
            if ($post['post_type'] === 'attachment' &&
                strpos($post['post_mime_type'], 'image/') === 0) {

                $attachments[$post['ID']] = [
                    'id' => $post['ID'],
                    'title' => $post['post_title'],
                    'filename' => basename($post['guid']),
                    'url' => $post['guid'],
                    'mime_type' => $post['post_mime_type'],
                    'date' => $post['post_date']
                ];
            }
        }

        return $attachments;
    }

    /**
     * Extract featured image relationships from wp_postmeta
     */
    protected function extractFeaturedImages(string $sqlContent): array
    {
        $featuredImages = [];

        // Extract from wp_postmeta table
        $metaPattern = '/INSERT INTO `?wp_postmeta`?\s*\([^)]+\)\s*VALUES\s*(.+?);/is';
        if (preg_match_all($metaPattern, $sqlContent, $matches)) {
            foreach ($matches[1] as $valuesString) {
                $metaRows = $this->parsePostMetaValues($valuesString);
                foreach ($metaRows as $meta) {
                    if ($meta['meta_key'] === '_thumbnail_id') {
                        $featuredImages[$meta['post_id']] = $meta['meta_value'];
                    }
                }
            }
        }

        return $featuredImages;
    }

    /**
     * Import featured image for a news article
     */
    protected function importFeaturedImage(News $news, array $post, string $sqlContent, ?string $wordpressDomain = null): bool
    {
        try {
            $attachments = $this->extractAttachments($sqlContent);
            $featuredImages = $this->extractFeaturedImages($sqlContent);

            $postId = $post['ID'];

            // Check if post has featured image
            if (!isset($featuredImages[$postId])) {
                $this->setPlaceholderImage($news);
                return false;
            }

            $attachmentId = $featuredImages[$postId];
            if (!isset($attachments[$attachmentId])) {
                $this->setPlaceholderImage($news);
                return false;
            }

            $attachment = $attachments[$attachmentId];
            $originalUrl = $attachment['url'];

            // Replace domain if provided
            if ($wordpressDomain) {
                $parsedUrl = parse_url($originalUrl);
                if ($parsedUrl) {
                    $imageUrl = 'https://' . $wordpressDomain . ($parsedUrl['path'] ?? '');
                } else {
                    $imageUrl = $originalUrl;
                }
            } else {
                $imageUrl = $originalUrl;
            }

            // Validate URL
            if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                Log::warning('Invalid image URL for post', [
                    'post_id' => $postId,
                    'original_url' => $originalUrl,
                    'modified_url' => $imageUrl
                ]);
                $this->setPlaceholderImage($news);
                return false;
            }

            // Download image
            $response = Http::timeout(30)->get($imageUrl);

            if (!$response->successful()) {
                Log::warning('Failed to download image', [
                    'post_id' => $postId,
                    'url' => $imageUrl,
                    'status' => $response->status()
                ]);
                $this->setPlaceholderImage($news);
                return false;
            }

            // Generate unique filename
            $originalFilename = $attachment['filename'];
            $extension = strtolower(pathinfo($originalFilename, PATHINFO_EXTENSION));
            $filename = Str::slug($news->title) . '-' . time() . '.' . $extension;

            // Ensure directory exists
            $directory = 'news-images';
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }

            // Save image
            $path = $directory . '/' . $filename;
            Storage::disk('public')->put($path, $response->body());

            // Update news record
            $news->update([
                'image' => $path,
                'image_alt' => $attachment['title'] ?: $news->title
            ]);

            Log::info('Successfully imported featured image', [
                'news_id' => $news->id,
                'original_url' => $originalUrl,
                'final_url' => $imageUrl,
                'saved_path' => $path
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to import featured image', [
                'news_id' => $news->id,
                'post_id' => $post['ID'],
                'error' => $e->getMessage()
            ]);

            $this->setPlaceholderImage($news);
            return false;
        }
    }

    /**
     * Set placeholder image for news
     */
    protected function setPlaceholderImage(News $news): void
    {
        $news->update([
            'image' => 'img/noimg.jpg',
            'image_alt' => 'Image not available - Please upload manually'
        ]);
    }
}
