<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use App\Models\News;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AuthorMappingService
{
    /**
     * Get or create placeholder author for WordPress imports
     */
    public function getPlaceholderAuthor(): User
    {
        $placeholderAuthor = User::where('email', '<EMAIL>')->first();
        
        if (!$placeholderAuthor) {
            $placeholderAuthor = User::create([
                'name' => 'WordPress Import Placeholder',
                'email' => '<EMAIL>',
                'password' => bcrypt(Str::random(32)),
                'email_verified_at' => now(),
            ]);
            
            // Assign Writer role if exists
            if (class_exists(\Spatie\Permission\Models\Role::class)) {
                $writerRole = \Spatie\Permission\Models\Role::where('name', 'Writer')->first();
                if ($writerRole) {
                    $placeholderAuthor->assignRole($writerRole);
                }
            }
            
            Log::info('Created WordPress import placeholder author', [
                'user_id' => $placeholderAuthor->id,
                'email' => $placeholderAuthor->email
            ]);
        }
        
        return $placeholderAuthor;
    }

    /**
     * Extract WordPress authors from SQL content
     */
    public function extractWordPressAuthors(string $sqlContent): array
    {
        $authors = [];
        
        // Extract from wp_users table
        $usersPattern = '/INSERT INTO `?wp_users`?\s*\([^)]+\)\s*VALUES\s*(.+?);/is';
        if (preg_match_all($usersPattern, $sqlContent, $matches)) {
            foreach ($matches[1] as $valuesString) {
                $userRows = $this->parseUsersValues($valuesString);
                foreach ($userRows as $user) {
                    if (isset($user['user_login']) && !empty(trim($user['user_login']))) {
                        $authors[] = [
                            'id' => $user['ID'] ?? null,
                            'login' => trim($user['user_login'], "'\""),
                            'email' => trim($user['user_email'] ?? '', "'\""),
                            'display_name' => trim($user['display_name'] ?? $user['user_login'], "'\""),
                            'registered' => $user['user_registered'] ?? null
                        ];
                    }
                }
            }
        }
        
        return array_values($authors);
    }

    /**
     * Parse wp_users table values
     */
    protected function parseUsersValues(string $valuesString): array
    {
        $users = [];
        $pattern = '/\(([^)]*(?:\([^)]*\)[^)]*)*)\)/s';
        preg_match_all($pattern, $valuesString, $matches);
        
        foreach ($matches[1] as $row) {
            $values = $this->parseRowValues($row);
            if (count($values) >= 4) {
                $users[] = [
                    'ID' => trim($values[0], "'\""),
                    'user_login' => trim($values[1], "'\""),
                    'user_pass' => trim($values[2], "'\""),
                    'user_nicename' => trim($values[3], "'\""),
                    'user_email' => isset($values[4]) ? trim($values[4], "'\"") : '',
                    'user_url' => isset($values[5]) ? trim($values[5], "'\"") : '',
                    'user_registered' => isset($values[6]) ? trim($values[6], "'\"") : '',
                    'user_activation_key' => isset($values[7]) ? trim($values[7], "'\"") : '',
                    'user_status' => isset($values[8]) ? trim($values[8], "'\"") : '',
                    'display_name' => isset($values[9]) ? trim($values[9], "'\"") : ''
                ];
            }
        }
        
        return $users;
    }

    /**
     * Parse individual row values handling nested quotes and commas
     */
    protected function parseRowValues(string $row): array
    {
        $values = [];
        $current = '';
        $inQuotes = false;
        $quoteChar = '';
        $i = 0;
        
        while ($i < strlen($row)) {
            $char = $row[$i];
            
            if (!$inQuotes && ($char === "'" || $char === '"')) {
                $inQuotes = true;
                $quoteChar = $char;
                $current .= $char;
            } elseif ($inQuotes && $char === $quoteChar) {
                if ($i + 1 < strlen($row) && $row[$i + 1] === $quoteChar) {
                    $current .= $char . $char;
                    $i++;
                } else {
                    $inQuotes = false;
                    $current .= $char;
                }
            } elseif (!$inQuotes && $char === ',') {
                $values[] = trim($current);
                $current = '';
            } else {
                $current .= $char;
            }
            
            $i++;
        }
        
        if ($current !== '') {
            $values[] = trim($current);
        }
        
        return $values;
    }

    /**
     * Analyze author mapping and identify missing authors
     */
    public function analyzeAuthorMapping(array $wpAuthors): array
    {
        $localUsers = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['Super Admin', 'Writer', 'Editor']);
        })->pluck('email', 'id')->toArray();
        
        $localEmails = array_map('strtolower', array_values($localUsers));
        
        $missing = [];
        $existing = [];
        $suggestions = [];
        
        foreach ($wpAuthors as $wpAuthor) {
            $wpEmail = strtolower($wpAuthor['email']);
            
            // Check exact email match
            if (in_array($wpEmail, $localEmails)) {
                $existing[] = $wpAuthor;
                continue;
            }
            
            // Check similar email or display name
            $similarUser = $this->findSimilarUser($wpAuthor, $localUsers);
            if ($similarUser) {
                $suggestions[] = [
                    'wp_author' => $wpAuthor,
                    'suggested_local' => $similarUser,
                    'similarity' => $this->calculateSimilarity($wpAuthor['display_name'], $similarUser['name'])
                ];
            } else {
                $missing[] = $wpAuthor;
            }
        }
        
        return [
            'total_wp_authors' => count($wpAuthors),
            'existing_matches' => count($existing),
            'missing_authors' => $missing,
            'suggested_mappings' => $suggestions,
            'existing_authors' => $existing
        ];
    }

    /**
     * Find similar user in local database
     */
    protected function findSimilarUser(array $wpAuthor, array $localUsers): ?array
    {
        $threshold = 0.6; // 60% similarity threshold for names
        $bestMatch = null;
        $bestSimilarity = 0;
        
        foreach ($localUsers as $id => $email) {
            $localUser = User::find($id);
            if (!$localUser) continue;
            
            // Check email similarity
            $emailSimilarity = $this->calculateSimilarity($wpAuthor['email'], $localUser->email);
            
            // Check name similarity
            $nameSimilarity = $this->calculateSimilarity($wpAuthor['display_name'], $localUser->name);
            
            $maxSimilarity = max($emailSimilarity, $nameSimilarity);
            
            if ($maxSimilarity > $threshold && $maxSimilarity > $bestSimilarity) {
                $bestSimilarity = $maxSimilarity;
                $bestMatch = [
                    'id' => $id,
                    'name' => $localUser->name,
                    'email' => $localUser->email
                ];
            }
        }
        
        return $bestMatch;
    }

    /**
     * Calculate similarity between two strings
     */
    protected function calculateSimilarity(string $str1, string $str2): float
    {
        $str1 = strtolower(trim($str1));
        $str2 = strtolower(trim($str2));
        
        if (empty($str1) || empty($str2)) return 0.0;
        
        $maxLen = max(strlen($str1), strlen($str2));
        if ($maxLen === 0) return 1.0;
        
        $distance = levenshtein($str1, $str2);
        return 1 - ($distance / $maxLen);
    }

    /**
     * Cache missing authors for admin notification
     */
    public function cacheMissingAuthors(array $wpAuthors): void
    {
        $analysis = $this->analyzeAuthorMapping($wpAuthors);
        
        if (!empty($analysis['missing_authors']) || !empty($analysis['suggested_mappings'])) {
            $cacheKey = 'wordpress_import_missing_authors_' . now()->format('Y_m_d_H_i_s');
            
            $cacheData = [
                'timestamp' => now()->toISOString(),
                'missing_authors' => $analysis['missing_authors'],
                'suggested_mappings' => $analysis['suggested_mappings'],
                'total_missing' => count($analysis['missing_authors']),
                'total_suggestions' => count($analysis['suggested_mappings'])
            ];
            
            // Cache for 24 hours
            Cache::put($cacheKey, $cacheData, now()->addHours(24));
            
            // Store the cache key in a list for easy retrieval
            $cacheKeys = Cache::get('wordpress_import_author_cache_keys', []);
            $cacheKeys[] = $cacheKey;
            Cache::put('wordpress_import_author_cache_keys', $cacheKeys, now()->addHours(24));
            
            Log::info('WordPress import: Missing authors cached', [
                'cache_key' => $cacheKey,
                'missing_count' => count($analysis['missing_authors']),
                'suggestions_count' => count($analysis['suggested_mappings'])
            ]);
        }
    }

    /**
     * Get articles that need author assignment
     */
    public function getArticlesNeedingAuthorAssignment(): array
    {
        $placeholderAuthor = $this->getPlaceholderAuthor();
        
        $articles = News::where('user_id', $placeholderAuthor->id)
            ->with(['category'])
            ->orderBy('created_at', 'desc')
            ->get();
            
        return $articles->map(function($article) {
            return [
                'id' => $article->id,
                'title' => $article->title,
                'category' => $article->category->name ?? 'Uncategorized',
                'created_at' => $article->created_at->format('Y-m-d H:i:s'),
                'status' => $article->status
            ];
        })->toArray();
    }

    /**
     * Send notification to admins about articles needing author assignment
     */
    public function notifyAdminsAboutMissingAuthors(): void
    {
        $articlesNeedingAssignment = $this->getArticlesNeedingAuthorAssignment();
        
        if (empty($articlesNeedingAssignment)) {
            return;
        }
        
        $superAdmins = User::role('Super Admin')->get();
        
        $message = sprintf(
            "WordPress Import Alert: %d articles need author assignment. " .
            "These articles are currently assigned to a placeholder author. " .
            "Please review and assign proper authors in the news management section.",
            count($articlesNeedingAssignment)
        );
        
        foreach ($superAdmins as $admin) {
            // Check if notification already exists for this admin
            $existingNotification = Notification::where('user_id', $admin->id)
                ->where('data', 'LIKE', '%articles need author assignment%')
                ->whereNull('read_at')
                ->first();
                
            if (!$existingNotification) {
                Notification::create([
                    'user_id' => $admin->id,
                    'data' => $message,
                    'read_at' => null,
                ]);
            }
        }
        
        Log::info('WordPress import missing authors notification sent', [
            'articles_count' => count($articlesNeedingAssignment),
            'notified_admins' => $superAdmins->count()
        ]);
    }
}
