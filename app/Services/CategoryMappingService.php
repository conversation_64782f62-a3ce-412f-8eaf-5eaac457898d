<?php

namespace App\Services;

use App\Models\Category;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CategoryMappingService
{
    /**
     * Get all cached missing categories from WordPress imports
     */
    public function getCachedMissingCategories(): array
    {
        $cacheKeys = Cache::get('wordpress_import_cache_keys', []);
        $allMissingCategories = [];
        
        foreach ($cacheKeys as $key) {
            $data = Cache::get($key);
            if ($data) {
                $allMissingCategories[] = $data;
            }
        }
        
        return $allMissingCategories;
    }

    /**
     * Get summary of missing categories
     */
    public function getMissingCategoriesSummary(): array
    {
        $cachedData = $this->getCachedMissingCategories();
        
        $totalMissing = 0;
        $totalSuggestions = 0;
        $uniqueCategories = [];
        $latestImport = null;
        
        foreach ($cachedData as $data) {
            $totalMissing += $data['total_missing'];
            $totalSuggestions += $data['total_suggestions'];
            
            // Collect unique missing categories
            foreach ($data['missing_categories'] as $category) {
                $categoryName = strtolower($category['name']);
                if (!isset($uniqueCategories[$categoryName])) {
                    $uniqueCategories[$categoryName] = $category;
                }
            }
            
            // Track latest import
            if (!$latestImport || $data['timestamp'] > $latestImport) {
                $latestImport = $data['timestamp'];
            }
        }
        
        return [
            'total_imports' => count($cachedData),
            'total_missing' => $totalMissing,
            'total_suggestions' => $totalSuggestions,
            'unique_missing_categories' => array_values($uniqueCategories),
            'unique_count' => count($uniqueCategories),
            'latest_import' => $latestImport,
            'has_missing_categories' => count($uniqueCategories) > 0
        ];
    }

    /**
     * Create categories from WordPress import suggestions
     */
    public function createCategoriesFromSuggestions(array $categoryNames): array
    {
        $created = [];
        $errors = [];
        
        foreach ($categoryNames as $categoryName) {
            try {
                $categoryName = trim($categoryName);
                
                // Check if category already exists
                if (Category::where('name', $categoryName)->exists()) {
                    $errors[] = "Category '{$categoryName}' already exists";
                    continue;
                }
                
                $category = Category::create([
                    'name' => $categoryName,
                    'slug' => Str::slug($categoryName),
                    'views' => 0
                ]);
                
                $created[] = $category;
                
            } catch (\Exception $e) {
                $errors[] = "Failed to create category '{$categoryName}': " . $e->getMessage();
                Log::error("Failed to create category from WordPress import", [
                    'category_name' => $categoryName,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        return [
            'created' => $created,
            'errors' => $errors,
            'created_count' => count($created),
            'error_count' => count($errors)
        ];
    }

    /**
     * Send notification to Super Admins about missing categories
     */
    public function notifyAdminsAboutMissingCategories(): void
    {
        $summary = $this->getMissingCategoriesSummary();
        
        if (!$summary['has_missing_categories']) {
            return;
        }
        
        $superAdmins = User::role('Super Admin')->get();
        
        $message = sprintf(
            "WordPress Import Alert: %d unique categories are missing from the system. " .
            "Please review and add them in the Import WordPress page. " .
            "Categories: %s",
            $summary['unique_count'],
            implode(', ', array_slice(array_column($summary['unique_missing_categories'], 'name'), 0, 5)) .
            ($summary['unique_count'] > 5 ? '...' : '')
        );
        
        foreach ($superAdmins as $admin) {
            // Check if notification already exists for this admin
            $existingNotification = Notification::where('user_id', $admin->id)
                ->where('data', 'LIKE', '%WordPress Import Alert%')
                ->whereNull('read_at')
                ->first();
                
            if (!$existingNotification) {
                Notification::create([
                    'user_id' => $admin->id,
                    'data' => $message,
                    'read_at' => null,
                ]);
            }
        }
        
        Log::info('WordPress import missing categories notification sent', [
            'unique_categories_count' => $summary['unique_count'],
            'notified_admins' => $superAdmins->count()
        ]);
    }

    /**
     * Clear cached missing categories data
     */
    public function clearCachedMissingCategories(): bool
    {
        try {
            $cacheKeys = Cache::get('wordpress_import_cache_keys', []);
            
            foreach ($cacheKeys as $key) {
                Cache::forget($key);
            }
            
            Cache::forget('wordpress_import_cache_keys');
            
            Log::info('WordPress import cached missing categories cleared', [
                'cleared_keys_count' => count($cacheKeys)
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('Failed to clear WordPress import cache', [
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Get category mapping suggestions for WordPress categories
     */
    public function getCategoryMappingSuggestions(): array
    {
        $cachedData = $this->getCachedMissingCategories();
        $suggestions = [];
        
        foreach ($cachedData as $data) {
            foreach ($data['suggested_mappings'] as $mapping) {
                $wpCategoryName = $mapping['wp_category']['name'];
                $localCategoryName = $mapping['suggested_local']['name'];
                $similarity = $mapping['similarity'];
                
                $suggestions[] = [
                    'wp_category' => $wpCategoryName,
                    'suggested_local_category' => $localCategoryName,
                    'suggested_local_id' => $mapping['suggested_local']['id'],
                    'similarity_score' => round($similarity * 100, 1),
                    'import_timestamp' => $data['timestamp']
                ];
            }
        }
        
        // Sort by similarity score (highest first)
        usort($suggestions, function($a, $b) {
            return $b['similarity_score'] <=> $a['similarity_score'];
        });
        
        return $suggestions;
    }

    /**
     * Generate category creation recommendations
     */
    public function generateCategoryRecommendations(): array
    {
        $summary = $this->getMissingCategoriesSummary();
        $suggestions = $this->getCategoryMappingSuggestions();
        
        $recommendations = [];
        
        // High-confidence mapping suggestions
        $highConfidenceMappings = array_filter($suggestions, function($suggestion) {
            return $suggestion['similarity_score'] >= 80;
        });
        
        // Categories that should be created
        $categoriesToCreate = array_filter($summary['unique_missing_categories'], function($category) use ($suggestions) {
            // Don't recommend creation if there's a high-confidence mapping suggestion
            foreach ($suggestions as $suggestion) {
                if (strtolower($suggestion['wp_category']) === strtolower($category['name']) && 
                    $suggestion['similarity_score'] >= 70) {
                    return false;
                }
            }
            return true;
        });
        
        return [
            'high_confidence_mappings' => $highConfidenceMappings,
            'categories_to_create' => $categoriesToCreate,
            'total_missing' => $summary['unique_count'],
            'creation_recommended' => count($categoriesToCreate),
            'mapping_recommended' => count($highConfidenceMappings)
        ];
    }
}
