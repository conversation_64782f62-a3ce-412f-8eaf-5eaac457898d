<?php

namespace App\Console\Commands;

use App\Models\Category;
use App\Models\User;
use App\Services\WordPressImportService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportWordPressCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:wordpress
                            {file : Path to the SQL file}
                            {--fallback-category= : Fallback category ID (optional)}
                            {--skip-duplicates : Skip duplicate articles}
                            {--no-auto-create-categories : Disable auto-creation of missing categories}
                            {--dry-run : Preview import without saving}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import WordPress articles from SQL file';

    protected WordPressImportService $importService;

    public function __construct(WordPressImportService $importService)
    {
        parent::__construct();
        $this->importService = $importService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        
        // Validate file exists
        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        // Get optional fallback category
        $categoryId = $this->option('fallback-category');
        $category = null;

        if ($categoryId) {
            $category = Category::find($categoryId);
            if (!$category) {
                $this->error("Category not found with ID: {$categoryId}");
                return 1;
            }
        } else {
            $this->info('No fallback category specified. Missing categories will be auto-created or use "WordPress Import" category.');
        }

        $options = [
            'fallback_category_id' => $categoryId,
            'skip_duplicates' => $this->option('skip-duplicates'),
            'create_missing_categories' => !$this->option('no-auto-create-categories'),
            'auto_assign_authors' => true,
            'post_status_mapping' => [
                'publish' => 'Accept',
                'draft' => 'Pending',
                'private' => 'Pending'
            ]
        ];

        $this->info("Starting WordPress import...");
        $this->info("File: {$filePath}");
        $this->info("Fallback Category: " . ($category ? $category->name : 'Auto-create or "WordPress Import"'));
        $this->info("Skip Duplicates: " . ($options['skip_duplicates'] ? 'Yes' : 'No'));
        $this->info("Auto-create Categories: " . ($options['create_missing_categories'] ? 'Yes' : 'No'));
        $this->info("Author Assignment: Placeholder author (requires manual assignment)");

        if ($this->option('dry-run')) {
            $this->warn("DRY RUN MODE - No data will be saved");
            
            try {
                $preview = $this->importService->previewImport($filePath, $options);
                
                $this->info("\n=== PREVIEW RESULTS ===");
                $this->info("Total Posts Found: {$preview['total_posts']}");
                $this->info("Date Range: {$preview['date_range']['min']} - {$preview['date_range']['max']}");
                $this->info("Post Statuses: " . json_encode($preview['post_statuses']));
                
                if (!empty($preview['sample_posts'])) {
                    $this->info("\n=== SAMPLE POSTS ===");
                    $this->table(
                        ['Title', 'Status', 'Date'],
                        array_slice($preview['sample_posts'], 0, 5)
                    );
                }
                
            } catch (\Exception $e) {
                $this->error("Preview failed: " . $e->getMessage());
                return 1;
            }
            
            return 0;
        }

        if (!$this->confirm('Do you want to proceed with the import?')) {
            $this->info('Import cancelled.');
            return 0;
        }

        try {
            DB::beginTransaction();
            
            $progressBar = $this->output->createProgressBar();
            $progressBar->start();
            
            $result = $this->importService->importFromSql($filePath, $options);
            
            $progressBar->finish();
            $this->newLine();
            
            DB::commit();
            
            $this->info("\n=== IMPORT COMPLETED ===");
            $this->info("Total Processed: {$result['total_processed']}");
            $this->info("Successfully Imported: {$result['imported']}");
            $this->info("Skipped: {$result['skipped']}");
            $this->info("Errors: " . count($result['errors']));
            
            if (!empty($result['errors'])) {
                $this->warn("\n=== ERRORS ===");
                foreach ($result['errors'] as $error) {
                    $this->error($error);
                }
            }
            
            return 0;
            
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("Import failed: " . $e->getMessage());
            return 1;
        }
    }
}
