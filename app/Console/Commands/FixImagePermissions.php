<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Helpers\ImageHelper;

class FixImagePermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:fix-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix file permissions for all images in storage';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('Fixing image permissions...');
        
        try {
            // Create storage link if it doesn't exist
            if (!file_exists(public_path('storage'))) {
                $this->call('storage:link');
            }
            
            // Fix all image permissions
            ImageHelper::fixAllImagePermissions();
            
            $this->info('✅ Image permissions fixed successfully!');
            $this->info('📁 Directory permissions set to 755');
            $this->info('📄 File permissions set to 644');
            
        } catch (\Exception $e) {
            $this->error('❌ Error fixing permissions: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
