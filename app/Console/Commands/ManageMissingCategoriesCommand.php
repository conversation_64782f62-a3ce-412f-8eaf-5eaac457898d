<?php

namespace App\Console\Commands;

use App\Services\CategoryMappingService;
use Illuminate\Console\Command;

class ManageMissingCategoriesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:missing-categories 
                            {action : Action to perform (list|create|clear)}
                            {--categories=* : Category names to create (for create action)}
                            {--all : Create all recommended categories (for create action)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage missing WordPress categories from imports';

    protected CategoryMappingService $categoryMappingService;

    public function __construct(CategoryMappingService $categoryMappingService)
    {
        parent::__construct();
        $this->categoryMappingService = $categoryMappingService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'list':
                return $this->listMissingCategories();
            case 'create':
                return $this->createCategories();
            case 'clear':
                return $this->clearCache();
            default:
                $this->error("Invalid action: {$action}");
                $this->info("Available actions: list, create, clear");
                return 1;
        }
    }

    /**
     * List missing categories
     */
    protected function listMissingCategories(): int
    {
        $summary = $this->categoryMappingService->getMissingCategoriesSummary();
        
        if (!$summary['has_missing_categories']) {
            $this->info('No missing categories found.');
            return 0;
        }

        $this->info("=== Missing WordPress Categories Summary ===");
        $this->info("Total Import Sessions: {$summary['total_imports']}");
        $this->info("Unique Missing Categories: {$summary['unique_count']}");
        $this->info("Latest Import: {$summary['latest_import']}");
        $this->newLine();

        if (!empty($summary['unique_missing_categories'])) {
            $this->info("=== Missing Categories ===");
            $this->table(
                ['Name', 'WordPress ID', 'Slug'],
                array_map(function($cat) {
                    return [
                        $cat['name'],
                        $cat['id'] ?? 'N/A',
                        $cat['slug'] ?? 'N/A'
                    ];
                }, $summary['unique_missing_categories'])
            );
        }

        $recommendations = $this->categoryMappingService->generateCategoryRecommendations();
        
        if (!empty($recommendations['high_confidence_mappings'])) {
            $this->newLine();
            $this->info("=== High-Confidence Mapping Suggestions ===");
            $this->table(
                ['WordPress Category', 'Suggested Local Category', 'Similarity %'],
                array_map(function($mapping) {
                    return [
                        $mapping['wp_category'],
                        $mapping['suggested_local_category'],
                        $mapping['similarity_score'] . '%'
                    ];
                }, $recommendations['high_confidence_mappings'])
            );
        }

        if (!empty($recommendations['categories_to_create'])) {
            $this->newLine();
            $this->info("=== Recommended for Creation ===");
            foreach ($recommendations['categories_to_create'] as $category) {
                $this->line("- {$category['name']}");
            }
        }

        return 0;
    }

    /**
     * Create categories
     */
    protected function createCategories(): int
    {
        $categoryNames = $this->option('categories');
        $createAll = $this->option('all');

        if ($createAll) {
            $recommendations = $this->categoryMappingService->generateCategoryRecommendations();
            $categoryNames = array_column($recommendations['categories_to_create'], 'name');
        }

        if (empty($categoryNames)) {
            $this->error('No categories specified. Use --categories option or --all flag.');
            return 1;
        }

        $this->info("Creating categories: " . implode(', ', $categoryNames));
        
        if (!$this->confirm('Do you want to proceed?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        $result = $this->categoryMappingService->createCategoriesFromSuggestions($categoryNames);

        $this->info("=== Creation Results ===");
        $this->info("Successfully Created: {$result['created_count']}");
        $this->info("Errors: {$result['error_count']}");

        if (!empty($result['created'])) {
            $this->newLine();
            $this->info("=== Created Categories ===");
            $this->table(
                ['ID', 'Name', 'Slug'],
                array_map(function($category) {
                    return [
                        $category->id,
                        $category->name,
                        $category->slug
                    ];
                }, $result['created'])
            );
        }

        if (!empty($result['errors'])) {
            $this->newLine();
            $this->warn("=== Errors ===");
            foreach ($result['errors'] as $error) {
                $this->error($error);
            }
        }

        return $result['error_count'] > 0 ? 1 : 0;
    }

    /**
     * Clear cache
     */
    protected function clearCache(): int
    {
        $this->info('Clearing missing categories cache...');
        
        if (!$this->confirm('Are you sure you want to clear the cache?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        $cleared = $this->categoryMappingService->clearCachedMissingCategories();

        if ($cleared) {
            $this->info('Cache cleared successfully.');
            return 0;
        } else {
            $this->error('Failed to clear cache.');
            return 1;
        }
    }
}
