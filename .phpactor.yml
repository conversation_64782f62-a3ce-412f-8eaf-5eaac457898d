language_server_phpstan:
    enabled: true
    level: 5
    config_path: null

language_server_psalm:
    enabled: false

language_server:
    diagnostics_on_update: true
    diagnostics_on_save: true
    enable_watchman: true

code_transform:
    import_globals: true

completion:
    dedupe: true
    snippets: true

file_path_resolvers:
    application: '%project_root%'
    laravel_public: '%project_root%/public'
    
indexer:
    enabled_watchers:
        - php
    exclude_patterns:
        - 'vendor/symfony/*/Tests/*'
        - 'vendor/*/tests/*'
        - 'vendor/*/test/*'
        - 'tests/*'
        - 'storage/*'
        - 'node_modules/*'
    include_patterns:
        - 'app/**/*.php'
        - 'bootstrap/**/*.php'
        - 'config/**/*.php'
        - 'database/**/*.php'
        - 'routes/**/*.php'
        - 'resources/**/*.php'
        - '_ide_helper.php'
        - '_ide_helper_models.php'
        - '_phpactor_helper.php'

navigator:
    autocomplete: true
    destinations:
        source: 'app'
        unit_test: 'tests/Unit'
        feature_test: 'tests/Feature'

symfony:
    enabled: true

laravel:
    enabled: true

worse_reflection:
    enable_cache: true
    cache_lifetime: 5
    enable_context_location: true
    stub_directories:
        - '%project_root%/vendor'
        - '%application_root%/vendor'
    source_locators:
        composer:
            project_root: '%project_root%'
        rpc:
            project_root: '%project_root%'

logging:
    enabled: true
    level: info
    path: '%cache%/phpactor.log'