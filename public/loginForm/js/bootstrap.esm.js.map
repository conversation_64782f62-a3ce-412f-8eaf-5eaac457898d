{"version": 3, "file": "bootstrap.esm.js", "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/polyfill.js", "../../js/src/dom/event-handler.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = parseFloat(transitionDuration)\n  const floatTransitionDelay = parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes)\n    .forEach(property => {\n      const expectedTypes = configTypes[property]\n      const value = config[property]\n      const valueType = value && isElement(value) ?\n        'element' :\n        toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new Error(\n          `${componentName.toUpperCase()}: ` +\n          `Option \"${property}\" provided type \"${valueType}\" ` +\n          `but expected type \"${expectedTypes}\".`)\n      }\n    })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nexport {\n  getjQuery,\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.key === 'undefined') {\n        element.key = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.key.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.key === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.key\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.key === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.key\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.key\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/* istanbul ignore file */\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.0-alpha1): dom/polyfill.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getUID } from '../util/index'\n\nlet find = Element.prototype.querySelectorAll\nlet findOne = Element.prototype.querySelector\n\n// MSEdge resets defaultPrevented flag upon dispatchEvent call if at least one listener is attached\nconst defaultPreventedPreservedOnDispatch = (() => {\n  const e = new CustomEvent('Bootstrap', {\n    cancelable: true\n  })\n\n  const element = document.createElement('div')\n  element.addEventListener('Bootstrap', () => null)\n\n  e.preventDefault()\n  element.dispatchEvent(e)\n  return e.defaultPrevented\n})()\n\nconst scopeSelectorRegex = /:scope\\b/\nconst supportScopeQuery = (() => {\n  const element = document.createElement('div')\n\n  try {\n    element.querySelectorAll(':scope *')\n  } catch (_) {\n    return false\n  }\n\n  return true\n})()\n\nif (!supportScopeQuery) {\n  find = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelectorAll(selector)\n    }\n\n    const hasId = Boolean(this.id)\n\n    if (!hasId) {\n      this.id = getUID('scope')\n    }\n\n    let nodeList = null\n    try {\n      selector = selector.replace(scopeSelectorRegex, `#${this.id}`)\n      nodeList = this.querySelectorAll(selector)\n    } finally {\n      if (!hasId) {\n        this.removeAttribute('id')\n      }\n    }\n\n    return nodeList\n  }\n\n  findOne = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelector(selector)\n    }\n\n    const matches = find.call(this, selector)\n\n    if (typeof matches[0] !== 'undefined') {\n      return matches[0]\n    }\n\n    return null\n  }\n}\n\nexport {\n  find,\n  findOne,\n  defaultPreventedPreservedOnDispatch\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\nimport { defaultPreventedPreservedOnDispatch } from './polyfill'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst $ = getjQuery()\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n]\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent)\n    .forEach(handlerKey => {\n      if (handlerKey.indexOf(namespace) > -1) {\n        const event = storeElementEvent[handlerKey]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.charAt(0) === '.'\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events)\n        .forEach(elementEvent => {\n          removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n        })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent)\n      .forEach(keyHandlers => {\n        const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n        if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n          const event = storeElementEvent[keyHandlers]\n\n          removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n        }\n      })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom informations in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args)\n        .forEach(key => {\n          Object.defineProperty(evt, key, {\n            get() {\n              return args[key]\n            }\n          })\n        })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n\n      if (!defaultPreventedPreservedOnDispatch) {\n        Object.defineProperty(evt, 'defaultPrevented', {\n          get: () => true\n        })\n      }\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this)\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    let rootElement = this._element\n    if (element) {\n      rootElement = this._getRootElement(element)\n    }\n\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler\n      .one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .alert to jQuery only if jQuery is present\n */\n\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Alert.jQueryInterface\n  $.fn[NAME].Constructor = Alert\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Alert.jQueryInterface\n  }\n}\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .button to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Button.jQueryInterface\n  $.fn[NAME].Constructor = Button\n\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Button.jQueryInterface\n  }\n}\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {\n      ...element.dataset\n    }\n\n    Object.keys(attributes).forEach(key => {\n      attributes[key] = normalizeData(attributes[key])\n    })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  },\n\n  toggleClass(element, className) {\n    if (!element) {\n      return\n    }\n\n    if (element.classList.contains(className)) {\n      element.classList.remove(className)\n    } else {\n      element.classList.add(className)\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { find as findFn, findOne } from './polyfill'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...findFn.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return findOne.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler\n        .on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler\n        .on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler\n        .on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement &&\n      this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const nextElementInterval = parseInt(nextElement.getAttribute('data-interval'), 10)\n      if (nextElementInterval) {\n        this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n        this._config.interval = nextElementInterval\n      } else {\n        this._config.interval = this._config.defaultInterval || this._config.interval\n      }\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler\n        .one(activeElement, TRANSITION_END, () => {\n          nextElement.classList.remove(directionalClassName, orderClassName)\n          nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n          activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n          this._isSliding = false\n\n          setTimeout(() => {\n            EventHandler.trigger(this._element, EVENT_SLID, {\n              relatedTarget: nextElement,\n              direction: eventDirectionName,\n              from: activeElementIndex,\n              to: nextElementIndex\n            })\n          }, 0)\n        })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .carousel to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Carousel.jQueryInterface\n  $.fn[NAME].Constructor = Carousel\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Carousel.jQueryInterface\n  }\n}\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.filter(elem => container !== elem)\n      activesData = tempActiveData[0] ? Data.getData(tempActiveData[0], DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    const hasWidth = this._element.classList.contains(WIDTH)\n    return hasWidth ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (element) {\n      const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n      if (triggerArray.length) {\n        triggerArray.forEach(elem => {\n          if (isOpen) {\n            elem.classList.remove(CLASS_NAME_COLLAPSED)\n          } else {\n            elem.classList.add(CLASS_NAME_COLLAPSED)\n          }\n\n          elem.setAttribute('aria-expanded', isOpen)\n        })\n      }\n    }\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .collapse to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Collapse.jQueryInterface\n  $.fn[NAME].Constructor = Collapse\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Collapse.jQueryInterface\n  }\n}\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_NAVBAR = 'navbar'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        parent.classList.add(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    Manipulator.toggleClass(this._menu, CLASS_NAME_SHOW)\n    Manipulator.toggleClass(this._element, CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    Manipulator.toggleClass(this._menu, CLASS_NAME_SHOW)\n    Manipulator.toggleClass(this._element, CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._element, EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      placement = PLACEMENT_TOP\n      if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n        placement = PLACEMENT_TOPEND\n      }\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return Boolean(this._element.closest(`.${CLASS_NAME_NAVBAR}`))\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this._config.offset(data.offsets, this._element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON ||\n      (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent)\n      .filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.key === ARROW_UP_KEY && index > 0) { // Up\n      index--\n    }\n\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) { // Down\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .dropdown to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Dropdown.jQueryInterface\n  $.fn[NAME].Constructor = Dropdown\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Dropdown.jQueryInterface\n  }\n}\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n      if (hideEvent.defaultPrevented) {\n        return\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n      const modalTransitionDuration = getTransitionDurationFromElement(this._element)\n      EventHandler.one(this._element, TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n      })\n      emulateTransitionEnd(this._element, modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...typeof config === 'object' && config ? config : {}\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .modal to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Modal.jQueryInterface\n  $.fn[NAME].Constructor = Modal\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Modal.jQueryInterface\n  }\n}\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultWhitelist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, whiteList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const whitelistKeys = Object.keys(whiteList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (whitelistKeys.indexOf(elName) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const whitelistedAttributes = [].concat(whiteList['*'] || [], whiteList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, whitelistedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultWhitelist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'whiteList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  whiteList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"tooltip-arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  whiteList: DefaultWhitelist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.target, dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.target,\n          this._getDelegateConfig()\n        )\n        Data.setData(event.target, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    Data.removeData(this.element, this.constructor.DATA_KEY)\n\n    EventHandler.off(this.element, this.constructor.EVENT_KEY)\n    EventHandler.off(this.element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if (this.element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this.element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this.element)\n      const isInTheDom = shadowRoot === null ?\n        this.element.ownerDocument.documentElement.contains(this.element) :\n        shadowRoot.contains(this.element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this.element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this.element, this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        EventHandler.trigger(this.element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this.element, this.constructor.Event.HIDDEN)\n      this._popper.destroy()\n    }\n\n    const hideEvent = EventHandler.trigger(this.element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.whiteList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: `.${this.constructor.NAME}-arrow`\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this.config.offset(data.offsets, this.element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this.element,\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this.element,\n          eventIn,\n          this.config.selector,\n          event => this._enter(event)\n        )\n        EventHandler.on(this.element,\n          eventOut,\n          this.config.selector,\n          event => this._leave(event)\n        )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this.element.closest(`.${CLASS_NAME_MODAL}`),\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.target, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.target,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.target, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) ||\n        context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.target, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.target,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.target, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this.element)\n\n    Object.keys(dataAttributes)\n      .forEach(dataAttr => {\n        if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n          delete dataAttributes[dataAttr]\n        }\n      })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.whiteList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const popperInstance = popperData.instance\n    this.tip = popperInstance.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .tooltip to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Tooltip.jQueryInterface\n  $.fn[NAME].Constructor = Tooltip\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tooltip.jQueryInterface\n  }\n}\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  // Private\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Popover.jQueryInterface\n  $.fn[NAME].Constructor = Popover\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Popover.jQueryInterface\n  }\n}\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS},` +\n                          `${this._config.target} ${SELECTOR_LIST_ITEMS},` +\n                          `${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets\n      .map(element => {\n        let target\n        const targetSelector = getSelectorFromElement(element)\n\n        if (targetSelector) {\n          target = SelectorEngine.findOne(targetSelector)\n        }\n\n        if (target) {\n          const targetBCR = target.getBoundingClientRect()\n          if (targetBCR.width || targetBCR.height) {\n            return [\n              Manipulator[offsetMethod](target).top + offsetBase,\n              targetSelector\n            ]\n          }\n        }\n\n        return null\n      })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine\n        .findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine\n        .parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = ScrollSpy.jQueryInterface\n  $.fn[NAME].Constructor = ScrollSpy\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return ScrollSpy.jQueryInterface\n  }\n}\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n\n    Data.setData(this._element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented ||\n      (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback &&\n      (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .tab to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Tab.jQueryInterface\n  $.fn[NAME].Constructor = Tab\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tab.jQueryInterface\n  }\n}\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 500\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(\n      this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      () => this.hide()\n    )\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n *  add .toast to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Toast.jQueryInterface\n  $.fn[NAME].Constructor = Toast\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Toast.jQueryInterface\n  }\n}\n\nexport default Toast\n"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "nodeType", "emulateTransitionEnd", "duration", "called", "durationPadding", "emulatedDuration", "listener", "removeEventListener", "addEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "mapData", "storeData", "id", "set", "key", "data", "get", "keyProperties", "delete", "Data", "setData", "instance", "getData", "removeData", "find", "Element", "prototype", "querySelectorAll", "findOne", "defaultPreventedPreservedOnDispatch", "e", "CustomEvent", "cancelable", "createElement", "preventDefault", "defaultPrevented", "scopeSelectorRegex", "supportScopeQuery", "_", "hasId", "Boolean", "nodeList", "replace", "removeAttribute", "matches", "$", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "fn", "handler", "event", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "target", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "custom", "isNative", "indexOf", "add<PERSON><PERSON><PERSON>", "handlers", "previousFn", "<PERSON><PERSON><PERSON><PERSON>", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "inNamespace", "isNamespace", "char<PERSON>t", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "bubbles", "nativeDispatch", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "defineProperty", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASSNAME_ALERT", "CLASSNAME_FADE", "CLASSNAME_SHOW", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "dispose", "closest", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "getInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "Number", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "getDataAttributes", "attributes", "dataset", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "toggleClass", "className", "add", "NODE_TEXT", "SelectorEngine", "concat", "findFn", "children", "filter", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "DIRECTION_NEXT", "DIRECTION_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_RIGHT", "CLASS_NAME_LEFT", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_keydown", "_addTouchEventListeners", "start", "pointerType", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "tagName", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "slideEvent", "nextElementInterval", "parseInt", "defaultInterval", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "startEvent", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "complete", "capitalizedDimension", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "<PERSON><PERSON><PERSON><PERSON>", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "selectorElements", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPRIGHT", "CLASS_NAME_DROPLEFT", "CLASS_NAME_MENURIGHT", "CLASS_NAME_NAVBAR", "CLASS_NAME_POSITION_STATIC", "SELECTOR_FORM_CHILD", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "flip", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "showEvent", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "focus", "hideEvent", "destroy", "update", "scheduleUpdate", "stopPropagation", "constructor", "_getPlacement", "parentDropdown", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_FADE", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "callback", "animate", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "modalTransitionDuration", "isModalOverflowing", "scrollHeight", "clientHeight", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "whiteList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "whitelist<PERSON><PERSON>s", "elements", "el", "el<PERSON>ame", "attributeList", "whitelistedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacement", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "defaultBsConfig", "behavior", "arrow", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "triggers", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "tabClass", "map", "token", "tClass", "popperData", "popperInstance", "popper", "initConfigAnimation", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;AAOA,IAAMA,OAAO,GAAG,OAAhB;AACA,IAAMC,uBAAuB,GAAG,IAAhC;AACA,IAAMC,cAAc,GAAG,eAAvB;;AAGA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,GAAG,EAAI;AACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;AACrC,gBAAUD,GAAV;AACD;;AAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;AACD,CAND;AAQA;;;;;;;AAMA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,MAAM,EAAI;AACvB,KAAG;AACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBd,OAA3B,CAAV;AACD,GAFD,QAESe,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT;;AAIA,SAAOA,MAAP;AACD,CAND;;AAQA,IAAMM,WAAW,GAAG,SAAdA,WAAc,CAAAC,OAAO,EAAI;AAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,aAArB,CAAf;;AAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;AACjC,QAAME,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAjB;AAEAD,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACC,IAAT,EAA/B,GAAiD,IAA5D;AACD;;AAED,SAAOH,QAAP;AACD,CAVD;;AAYA,IAAMI,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAL,OAAO,EAAI;AACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;;AAEA,MAAIC,QAAJ,EAAc;AACZ,WAAOJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;AACD;;AAED,SAAO,IAAP;AACD,CARD;;AAUA,IAAMM,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAP,OAAO,EAAI;AACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;AAEA,SAAOC,QAAQ,GAAGJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,CAAH,GAAsC,IAArD;AACD,CAJD;;AAMA,IAAMO,gCAAgC,GAAG,SAAnCA,gCAAmC,CAAAR,OAAO,EAAI;AAClD,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,CAAP;AACD,GAHiD;;;AAAA,8BAS9CS,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,CAT8C;AAAA,MAOhDW,kBAPgD,yBAOhDA,kBAPgD;AAAA,MAQhDC,eARgD,yBAQhDA,eARgD;;AAWlD,MAAMC,uBAAuB,GAAGC,UAAU,CAACH,kBAAD,CAA1C;AACA,MAAMI,oBAAoB,GAAGD,UAAU,CAACF,eAAD,CAAvC,CAZkD;;AAelD,MAAI,CAACC,uBAAD,IAA4B,CAACE,oBAAjC,EAAuD;AACrD,WAAO,CAAP;AACD,GAjBiD;;;AAoBlDJ,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACK,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;AACAJ,EAAAA,eAAe,GAAGA,eAAe,CAACI,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;AAEA,SAAO,CAACF,UAAU,CAACH,kBAAD,CAAV,GAAiCG,UAAU,CAACF,eAAD,CAA5C,IAAiE7B,uBAAxE;AACD,CAxBD;;AA0BA,IAAMkC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAAjB,OAAO,EAAI;AACtCA,EAAAA,OAAO,CAACkB,aAAR,CAAsB,IAAIC,KAAJ,CAAUnC,cAAV,CAAtB;AACD,CAFD;;AAIA,IAAMoC,SAAS,GAAG,SAAZA,SAAY,CAAAlC,GAAG;AAAA,SAAI,CAACA,GAAG,CAAC,CAAD,CAAH,IAAUA,GAAX,EAAgBmC,QAApB;AAAA,CAArB;;AAEA,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACtB,OAAD,EAAUuB,QAAV,EAAuB;AAClD,MAAIC,MAAM,GAAG,KAAb;AACA,MAAMC,eAAe,GAAG,CAAxB;AACA,MAAMC,gBAAgB,GAAGH,QAAQ,GAAGE,eAApC;;AACA,WAASE,QAAT,GAAoB;AAClBH,IAAAA,MAAM,GAAG,IAAT;AACAxB,IAAAA,OAAO,CAAC4B,mBAAR,CAA4B5C,cAA5B,EAA4C2C,QAA5C;AACD;;AAED3B,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB7C,cAAzB,EAAyC2C,QAAzC;AACAG,EAAAA,UAAU,CAAC,YAAM;AACf,QAAI,CAACN,MAAL,EAAa;AACXP,MAAAA,oBAAoB,CAACjB,OAAD,CAApB;AACD;AACF,GAJS,EAIP0B,gBAJO,CAAV;AAKD,CAfD;;AAiBA,IAAMK,eAAe,GAAG,SAAlBA,eAAkB,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,EAAwC;AAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EACGG,OADH,CACW,UAAAC,QAAQ,EAAI;AACnB,QAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;AACA,QAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;AACA,QAAMG,SAAS,GAAGD,KAAK,IAAIpB,SAAS,CAACoB,KAAD,CAAlB,GAChB,SADgB,GAEhBvD,MAAM,CAACuD,KAAD,CAFR;;AAIA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;AAC9C,YAAM,IAAIG,KAAJ,CACDZ,aAAa,CAACa,WAAd,EAAH,yBACWP,QADX,2BACuCG,SADvC,sCAEsBF,aAFtB,SADI,CAAN;AAID;AACF,GAdH;AAeD,CAhBD;;AAkBA,IAAMO,SAAS,GAAG,SAAZA,SAAY,CAAA9C,OAAO,EAAI;AAC3B,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,KAAP;AACD;;AAED,MAAIA,OAAO,CAAC+C,KAAR,IAAiB/C,OAAO,CAACgD,UAAzB,IAAuChD,OAAO,CAACgD,UAAR,CAAmBD,KAA9D,EAAqE;AACnE,QAAME,YAAY,GAAGvC,gBAAgB,CAACV,OAAD,CAArC;AACA,QAAMkD,eAAe,GAAGxC,gBAAgB,CAACV,OAAO,CAACgD,UAAT,CAAxC;AAEA,WAAOC,YAAY,CAACE,OAAb,KAAyB,MAAzB,IACLD,eAAe,CAACC,OAAhB,KAA4B,MADvB,IAELF,YAAY,CAACG,UAAb,KAA4B,QAF9B;AAGD;;AAED,SAAO,KAAP;AACD,CAfD;;AAiBA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAAArD,OAAO,EAAI;AAChC,MAAI,CAACH,QAAQ,CAACyD,eAAT,CAAyBC,YAA9B,EAA4C;AAC1C,WAAO,IAAP;AACD,GAH+B;;;AAMhC,MAAI,OAAOvD,OAAO,CAACwD,WAAf,KAA+B,UAAnC,EAA+C;AAC7C,QAAMC,IAAI,GAAGzD,OAAO,CAACwD,WAAR,EAAb;AACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;AACD;;AAED,MAAIzD,OAAO,YAAY0D,UAAvB,EAAmC;AACjC,WAAO1D,OAAP;AACD,GAb+B;;;AAgBhC,MAAI,CAACA,OAAO,CAACgD,UAAb,EAAyB;AACvB,WAAO,IAAP;AACD;;AAED,SAAOK,cAAc,CAACrD,OAAO,CAACgD,UAAT,CAArB;AACD,CArBD;;AAuBA,IAAMW,IAAI,GAAG,SAAPA,IAAO;AAAA,SAAM,YAAY,EAAlB;AAAA,CAAb;;AAEA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAA5D,OAAO;AAAA,SAAIA,OAAO,CAAC6D,YAAZ;AAAA,CAAtB;;AAEA,IAAMC,SAAS,GAAG,SAAZA,SAAY,GAAM;AAAA,gBACHrD,MADG;AAAA,MACdsD,MADc,WACdA,MADc;;AAGtB,MAAIA,MAAM,IAAI,CAAClE,QAAQ,CAACmE,IAAT,CAAcC,YAAd,CAA2B,gBAA3B,CAAf,EAA6D;AAC3D,WAAOF,MAAP;AACD;;AAED,SAAO,IAAP;AACD,CARD;;AC7KA;;;;;;;AAOA;;;;;AAMA,IAAMG,OAAO,GAAI,YAAM;AACrB,MAAMC,SAAS,GAAG,EAAlB;AACA,MAAIC,EAAE,GAAG,CAAT;AACA,SAAO;AACLC,IAAAA,GADK,eACDrE,OADC,EACQsE,GADR,EACaC,IADb,EACmB;AACtB,UAAI,OAAOvE,OAAO,CAACsE,GAAf,KAAuB,WAA3B,EAAwC;AACtCtE,QAAAA,OAAO,CAACsE,GAAR,GAAc;AACZA,UAAAA,GAAG,EAAHA,GADY;AAEZF,UAAAA,EAAE,EAAFA;AAFY,SAAd;AAIAA,QAAAA,EAAE;AACH;;AAEDD,MAAAA,SAAS,CAACnE,OAAO,CAACsE,GAAR,CAAYF,EAAb,CAAT,GAA4BG,IAA5B;AACD,KAXI;AAYLC,IAAAA,GAZK,eAYDxE,OAZC,EAYQsE,GAZR,EAYa;AAChB,UAAI,CAACtE,OAAD,IAAY,OAAOA,OAAO,CAACsE,GAAf,KAAuB,WAAvC,EAAoD;AAClD,eAAO,IAAP;AACD;;AAED,UAAMG,aAAa,GAAGzE,OAAO,CAACsE,GAA9B;;AACA,UAAIG,aAAa,CAACH,GAAd,KAAsBA,GAA1B,EAA+B;AAC7B,eAAOH,SAAS,CAACM,aAAa,CAACL,EAAf,CAAhB;AACD;;AAED,aAAO,IAAP;AACD,KAvBI;AAwBLM,IAAAA,MAxBK,mBAwBE1E,OAxBF,EAwBWsE,GAxBX,EAwBgB;AACnB,UAAI,OAAOtE,OAAO,CAACsE,GAAf,KAAuB,WAA3B,EAAwC;AACtC;AACD;;AAED,UAAMG,aAAa,GAAGzE,OAAO,CAACsE,GAA9B;;AACA,UAAIG,aAAa,CAACH,GAAd,KAAsBA,GAA1B,EAA+B;AAC7B,eAAOH,SAAS,CAACM,aAAa,CAACL,EAAf,CAAhB;AACA,eAAOpE,OAAO,CAACsE,GAAf;AACD;AACF;AAlCI,GAAP;AAoCD,CAvCe,EAAhB;;AAyCA,IAAMK,IAAI,GAAG;AACXC,EAAAA,OADW,mBACHC,QADG,EACOP,GADP,EACYC,IADZ,EACkB;AAC3BL,IAAAA,OAAO,CAACG,GAAR,CAAYQ,QAAZ,EAAsBP,GAAtB,EAA2BC,IAA3B;AACD,GAHU;AAIXO,EAAAA,OAJW,mBAIHD,QAJG,EAIOP,GAJP,EAIY;AACrB,WAAOJ,OAAO,CAACM,GAAR,CAAYK,QAAZ,EAAsBP,GAAtB,CAAP;AACD,GANU;AAOXS,EAAAA,UAPW,sBAOAF,QAPA,EAOUP,GAPV,EAOe;AACxBJ,IAAAA,OAAO,CAACQ,MAAR,CAAeG,QAAf,EAAyBP,GAAzB;AACD;AATU,CAAb;;ACtDA;AAWA,IAAIU,IAAI,GAAGC,OAAO,CAACC,SAAR,CAAkBC,gBAA7B;AACA,IAAIC,OAAO,GAAGH,OAAO,CAACC,SAAR,CAAkB5E,aAAhC;;AAGA,IAAM+E,mCAAmC,GAAI,YAAM;AACjD,MAAMC,CAAC,GAAG,IAAIC,WAAJ,CAAgB,WAAhB,EAA6B;AACrCC,IAAAA,UAAU,EAAE;AADyB,GAA7B,CAAV;AAIA,MAAMxF,OAAO,GAAGH,QAAQ,CAAC4F,aAAT,CAAuB,KAAvB,CAAhB;AACAzF,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB,WAAzB,EAAsC;AAAA,WAAM,IAAN;AAAA,GAAtC;AAEAyD,EAAAA,CAAC,CAACI,cAAF;AACA1F,EAAAA,OAAO,CAACkB,aAAR,CAAsBoE,CAAtB;AACA,SAAOA,CAAC,CAACK,gBAAT;AACD,CAX2C,EAA5C;;AAaA,IAAMC,kBAAkB,GAAG,UAA3B;;AACA,IAAMC,iBAAiB,GAAI,YAAM;AAC/B,MAAM7F,OAAO,GAAGH,QAAQ,CAAC4F,aAAT,CAAuB,KAAvB,CAAhB;;AAEA,MAAI;AACFzF,IAAAA,OAAO,CAACmF,gBAAR,CAAyB,UAAzB;AACD,GAFD,CAEE,OAAOW,CAAP,EAAU;AACV,WAAO,KAAP;AACD;;AAED,SAAO,IAAP;AACD,CAVyB,EAA1B;;AAYA,IAAI,CAACD,iBAAL,EAAwB;AACtBb,EAAAA,IAAI,GAAG,cAAU/E,QAAV,EAAoB;AACzB,QAAI,CAAC2F,kBAAkB,CAACjD,IAAnB,CAAwB1C,QAAxB,CAAL,EAAwC;AACtC,aAAO,KAAKkF,gBAAL,CAAsBlF,QAAtB,CAAP;AACD;;AAED,QAAM8F,KAAK,GAAGC,OAAO,CAAC,KAAK5B,EAAN,CAArB;;AAEA,QAAI,CAAC2B,KAAL,EAAY;AACV,WAAK3B,EAAL,GAAU5E,MAAM,CAAC,OAAD,CAAhB;AACD;;AAED,QAAIyG,QAAQ,GAAG,IAAf;;AACA,QAAI;AACFhG,MAAAA,QAAQ,GAAGA,QAAQ,CAACiG,OAAT,CAAiBN,kBAAjB,QAAyC,KAAKxB,EAA9C,CAAX;AACA6B,MAAAA,QAAQ,GAAG,KAAKd,gBAAL,CAAsBlF,QAAtB,CAAX;AACD,KAHD,SAGU;AACR,UAAI,CAAC8F,KAAL,EAAY;AACV,aAAKI,eAAL,CAAqB,IAArB;AACD;AACF;;AAED,WAAOF,QAAP;AACD,GAtBD;;AAwBAb,EAAAA,OAAO,GAAG,iBAAUnF,QAAV,EAAoB;AAC5B,QAAI,CAAC2F,kBAAkB,CAACjD,IAAnB,CAAwB1C,QAAxB,CAAL,EAAwC;AACtC,aAAO,KAAKK,aAAL,CAAmBL,QAAnB,CAAP;AACD;;AAED,QAAMmG,OAAO,GAAGpB,IAAI,CAAC3F,IAAL,CAAU,IAAV,EAAgBY,QAAhB,CAAhB;;AAEA,QAAI,OAAOmG,OAAO,CAAC,CAAD,CAAd,KAAsB,WAA1B,EAAuC;AACrC,aAAOA,OAAO,CAAC,CAAD,CAAd;AACD;;AAED,WAAO,IAAP;AACD,GAZD;AAaD;;AC/ED;;;;;;AAUA;;;;;;AAMA,IAAMC,CAAC,GAAGvC,SAAS,EAAnB;AACA,IAAMwC,cAAc,GAAG,oBAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,aAAa,GAAG,QAAtB;AACA,IAAMC,aAAa,GAAG,EAAtB;;AACA,IAAIC,QAAQ,GAAG,CAAf;AACA,IAAMC,YAAY,GAAG;AACnBC,EAAAA,UAAU,EAAE,WADO;AAEnBC,EAAAA,UAAU,EAAE;AAFO,CAArB;AAIA,IAAMC,YAAY,GAAG,CACnB,OADmB,EAEnB,UAFmB,EAGnB,SAHmB,EAInB,WAJmB,EAKnB,aALmB,EAMnB,YANmB,EAOnB,gBAPmB,EAQnB,WARmB,EASnB,UATmB,EAUnB,WAVmB,EAWnB,aAXmB,EAYnB,WAZmB,EAanB,SAbmB,EAcnB,UAdmB,EAenB,OAfmB,EAgBnB,mBAhBmB,EAiBnB,YAjBmB,EAkBnB,WAlBmB,EAmBnB,UAnBmB,EAoBnB,aApBmB,EAqBnB,aArBmB,EAsBnB,aAtBmB,EAuBnB,WAvBmB,EAwBnB,cAxBmB,EAyBnB,eAzBmB,EA0BnB,cA1BmB,EA2BnB,eA3BmB,EA4BnB,YA5BmB,EA6BnB,OA7BmB,EA8BnB,MA9BmB,EA+BnB,QA/BmB,EAgCnB,OAhCmB,EAiCnB,QAjCmB,EAkCnB,QAlCmB,EAmCnB,SAnCmB,EAoCnB,UApCmB,EAqCnB,MArCmB,EAsCnB,QAtCmB,EAuCnB,cAvCmB,EAwCnB,QAxCmB,EAyCnB,MAzCmB,EA0CnB,kBA1CmB,EA2CnB,kBA3CmB,EA4CnB,OA5CmB,EA6CnB,OA7CmB,EA8CnB,QA9CmB,CAArB;AAiDA;;;;;;AAMA,SAASC,WAAT,CAAqB/G,OAArB,EAA8BgH,GAA9B,EAAmC;AACjC,SAAQA,GAAG,IAAOA,GAAP,UAAeN,QAAQ,EAA3B,IAAoC1G,OAAO,CAAC0G,QAA5C,IAAwDA,QAAQ,EAAvE;AACD;;AAED,SAASO,QAAT,CAAkBjH,OAAlB,EAA2B;AACzB,MAAMgH,GAAG,GAAGD,WAAW,CAAC/G,OAAD,CAAvB;AAEAA,EAAAA,OAAO,CAAC0G,QAAR,GAAmBM,GAAnB;AACAP,EAAAA,aAAa,CAACO,GAAD,CAAb,GAAqBP,aAAa,CAACO,GAAD,CAAb,IAAsB,EAA3C;AAEA,SAAOP,aAAa,CAACO,GAAD,CAApB;AACD;;AAED,SAASE,gBAAT,CAA0BlH,OAA1B,EAAmCmH,EAAnC,EAAuC;AACrC,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;AAC7B,QAAID,OAAO,CAACE,MAAZ,EAAoB;AAClBC,MAAAA,YAAY,CAACC,GAAb,CAAiBxH,OAAjB,EAA0BqH,KAAK,CAACI,IAAhC,EAAsCN,EAAtC;AACD;;AAED,WAAOA,EAAE,CAACO,KAAH,CAAS1H,OAAT,EAAkB,CAACqH,KAAD,CAAlB,CAAP;AACD,GAND;AAOD;;AAED,SAASM,0BAAT,CAAoC3H,OAApC,EAA6CC,QAA7C,EAAuDkH,EAAvD,EAA2D;AACzD,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;AAC7B,QAAMO,WAAW,GAAG5H,OAAO,CAACmF,gBAAR,CAAyBlF,QAAzB,CAApB;;AAEA,aAAW4H,MAAX,GAAsBR,KAAtB,CAAWQ,MAAX,EAA6BA,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAAC7E,UAAxE,EAAoF;AAClF,WAAK,IAAI8E,CAAC,GAAGF,WAAW,CAACG,MAAzB,EAAiCD,CAAC,EAAlC,GAAuC;AACrC,YAAIF,WAAW,CAACE,CAAD,CAAX,KAAmBD,MAAvB,EAA+B;AAC7B,cAAIT,OAAO,CAACE,MAAZ,EAAoB;AAClBC,YAAAA,YAAY,CAACC,GAAb,CAAiBxH,OAAjB,EAA0BqH,KAAK,CAACI,IAAhC,EAAsCN,EAAtC;AACD;;AAED,iBAAOA,EAAE,CAACO,KAAH,CAASG,MAAT,EAAiB,CAACR,KAAD,CAAjB,CAAP;AACD;AACF;AACF,KAb4B;;;AAgB7B,WAAO,IAAP;AACD,GAjBD;AAkBD;;AAED,SAASW,WAAT,CAAqBC,MAArB,EAA6Bb,OAA7B,EAAsCc,kBAAtC,EAAiE;AAAA,MAA3BA,kBAA2B;AAA3BA,IAAAA,kBAA2B,GAAN,IAAM;AAAA;;AAC/D,MAAMC,YAAY,GAAGhG,MAAM,CAACC,IAAP,CAAY6F,MAAZ,CAArB;;AAEA,OAAK,IAAIH,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGD,YAAY,CAACJ,MAAnC,EAA2CD,CAAC,GAAGM,GAA/C,EAAoDN,CAAC,EAArD,EAAyD;AACvD,QAAMT,KAAK,GAAGY,MAAM,CAACE,YAAY,CAACL,CAAD,CAAb,CAApB;;AAEA,QAAIT,KAAK,CAACgB,eAAN,KAA0BjB,OAA1B,IAAqCC,KAAK,CAACa,kBAAN,KAA6BA,kBAAtE,EAA0F;AACxF,aAAOb,KAAP;AACD;AACF;;AAED,SAAO,IAAP;AACD;;AAED,SAASiB,eAAT,CAAyBC,iBAAzB,EAA4CnB,OAA5C,EAAqDoB,YAArD,EAAmE;AACjE,MAAMC,UAAU,GAAG,OAAOrB,OAAP,KAAmB,QAAtC;AACA,MAAMiB,eAAe,GAAGI,UAAU,GAAGD,YAAH,GAAkBpB,OAApD,CAFiE;;AAKjE,MAAIsB,SAAS,GAAGH,iBAAiB,CAACrC,OAAlB,CAA0BK,cAA1B,EAA0C,EAA1C,CAAhB;AACA,MAAMoC,MAAM,GAAGhC,YAAY,CAAC+B,SAAD,CAA3B;;AAEA,MAAIC,MAAJ,EAAY;AACVD,IAAAA,SAAS,GAAGC,MAAZ;AACD;;AAED,MAAMC,QAAQ,GAAG9B,YAAY,CAAC+B,OAAb,CAAqBH,SAArB,IAAkC,CAAC,CAApD;;AAEA,MAAI,CAACE,QAAL,EAAe;AACbF,IAAAA,SAAS,GAAGH,iBAAZ;AACD;;AAED,SAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP;AACD;;AAED,SAASI,UAAT,CAAoB9I,OAApB,EAA6BuI,iBAA7B,EAAgDnB,OAAhD,EAAyDoB,YAAzD,EAAuElB,MAAvE,EAA+E;AAC7E,MAAI,OAAOiB,iBAAP,KAA6B,QAA7B,IAAyC,CAACvI,OAA9C,EAAuD;AACrD;AACD;;AAED,MAAI,CAACoH,OAAL,EAAc;AACZA,IAAAA,OAAO,GAAGoB,YAAV;AACAA,IAAAA,YAAY,GAAG,IAAf;AACD;;AAR4E,yBAU5BF,eAAe,CAACC,iBAAD,EAAoBnB,OAApB,EAA6BoB,YAA7B,CAVa;AAAA,MAUtEC,UAVsE;AAAA,MAU1DJ,eAV0D;AAAA,MAUzCK,SAVyC;;AAW7E,MAAMT,MAAM,GAAGhB,QAAQ,CAACjH,OAAD,CAAvB;AACA,MAAM+I,QAAQ,GAAGd,MAAM,CAACS,SAAD,CAAN,KAAsBT,MAAM,CAACS,SAAD,CAAN,GAAoB,EAA1C,CAAjB;AACA,MAAMM,UAAU,GAAGhB,WAAW,CAACe,QAAD,EAAWV,eAAX,EAA4BI,UAAU,GAAGrB,OAAH,GAAa,IAAnD,CAA9B;;AAEA,MAAI4B,UAAJ,EAAgB;AACdA,IAAAA,UAAU,CAAC1B,MAAX,GAAoB0B,UAAU,CAAC1B,MAAX,IAAqBA,MAAzC;AAEA;AACD;;AAED,MAAMN,GAAG,GAAGD,WAAW,CAACsB,eAAD,EAAkBE,iBAAiB,CAACrC,OAAlB,CAA0BI,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB;AACA,MAAMa,EAAE,GAAGsB,UAAU,GACnBd,0BAA0B,CAAC3H,OAAD,EAAUoH,OAAV,EAAmBoB,YAAnB,CADP,GAEnBtB,gBAAgB,CAAClH,OAAD,EAAUoH,OAAV,CAFlB;AAIAD,EAAAA,EAAE,CAACe,kBAAH,GAAwBO,UAAU,GAAGrB,OAAH,GAAa,IAA/C;AACAD,EAAAA,EAAE,CAACkB,eAAH,GAAqBA,eAArB;AACAlB,EAAAA,EAAE,CAACG,MAAH,GAAYA,MAAZ;AACAH,EAAAA,EAAE,CAACT,QAAH,GAAcM,GAAd;AACA+B,EAAAA,QAAQ,CAAC/B,GAAD,CAAR,GAAgBG,EAAhB;AAEAnH,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB6G,SAAzB,EAAoCvB,EAApC,EAAwCsB,UAAxC;AACD;;AAED,SAASQ,aAAT,CAAuBjJ,OAAvB,EAAgCiI,MAAhC,EAAwCS,SAAxC,EAAmDtB,OAAnD,EAA4Dc,kBAA5D,EAAgF;AAC9E,MAAMf,EAAE,GAAGa,WAAW,CAACC,MAAM,CAACS,SAAD,CAAP,EAAoBtB,OAApB,EAA6Bc,kBAA7B,CAAtB;;AAEA,MAAI,CAACf,EAAL,EAAS;AACP;AACD;;AAEDnH,EAAAA,OAAO,CAAC4B,mBAAR,CAA4B8G,SAA5B,EAAuCvB,EAAvC,EAA2CnB,OAAO,CAACkC,kBAAD,CAAlD;AACA,SAAOD,MAAM,CAACS,SAAD,CAAN,CAAkBvB,EAAE,CAACT,QAArB,CAAP;AACD;;AAED,SAASwC,wBAAT,CAAkClJ,OAAlC,EAA2CiI,MAA3C,EAAmDS,SAAnD,EAA8DS,SAA9D,EAAyE;AACvE,MAAMC,iBAAiB,GAAGnB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;AAEAvG,EAAAA,MAAM,CAACC,IAAP,CAAYgH,iBAAZ,EACG/G,OADH,CACW,UAAAgH,UAAU,EAAI;AACrB,QAAIA,UAAU,CAACR,OAAX,CAAmBM,SAAnB,IAAgC,CAAC,CAArC,EAAwC;AACtC,UAAM9B,KAAK,GAAG+B,iBAAiB,CAACC,UAAD,CAA/B;AAEAJ,MAAAA,aAAa,CAACjJ,OAAD,EAAUiI,MAAV,EAAkBS,SAAlB,EAA6BrB,KAAK,CAACgB,eAAnC,EAAoDhB,KAAK,CAACa,kBAA1D,CAAb;AACD;AACF,GAPH;AAQD;;AAED,IAAMX,YAAY,GAAG;AACnB+B,EAAAA,EADmB,cAChBtJ,OADgB,EACPqH,KADO,EACAD,OADA,EACSoB,YADT,EACuB;AACxCM,IAAAA,UAAU,CAAC9I,OAAD,EAAUqH,KAAV,EAAiBD,OAAjB,EAA0BoB,YAA1B,EAAwC,KAAxC,CAAV;AACD,GAHkB;AAKnBe,EAAAA,GALmB,eAKfvJ,OALe,EAKNqH,KALM,EAKCD,OALD,EAKUoB,YALV,EAKwB;AACzCM,IAAAA,UAAU,CAAC9I,OAAD,EAAUqH,KAAV,EAAiBD,OAAjB,EAA0BoB,YAA1B,EAAwC,IAAxC,CAAV;AACD,GAPkB;AASnBhB,EAAAA,GATmB,eASfxH,OATe,EASNuI,iBATM,EASanB,OATb,EASsBoB,YATtB,EASoC;AACrD,QAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAACvI,OAA9C,EAAuD;AACrD;AACD;;AAHoD,4BAKJsI,eAAe,CAACC,iBAAD,EAAoBnB,OAApB,EAA6BoB,YAA7B,CALX;AAAA,QAK9CC,UAL8C;AAAA,QAKlCJ,eALkC;AAAA,QAKjBK,SALiB;;AAMrD,QAAMc,WAAW,GAAGd,SAAS,KAAKH,iBAAlC;AACA,QAAMN,MAAM,GAAGhB,QAAQ,CAACjH,OAAD,CAAvB;AACA,QAAMyJ,WAAW,GAAGlB,iBAAiB,CAACmB,MAAlB,CAAyB,CAAzB,MAAgC,GAApD;;AAEA,QAAI,OAAOrB,eAAP,KAA2B,WAA/B,EAA4C;AAC1C;AACA,UAAI,CAACJ,MAAD,IAAW,CAACA,MAAM,CAACS,SAAD,CAAtB,EAAmC;AACjC;AACD;;AAEDO,MAAAA,aAAa,CAACjJ,OAAD,EAAUiI,MAAV,EAAkBS,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAGrB,OAAH,GAAa,IAArE,CAAb;AACA;AACD;;AAED,QAAIqC,WAAJ,EAAiB;AACftH,MAAAA,MAAM,CAACC,IAAP,CAAY6F,MAAZ,EACG5F,OADH,CACW,UAAAsH,YAAY,EAAI;AACvBT,QAAAA,wBAAwB,CAAClJ,OAAD,EAAUiI,MAAV,EAAkB0B,YAAlB,EAAgCpB,iBAAiB,CAACqB,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB;AACD,OAHH;AAID;;AAED,QAAMR,iBAAiB,GAAGnB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;AACAvG,IAAAA,MAAM,CAACC,IAAP,CAAYgH,iBAAZ,EACG/G,OADH,CACW,UAAAwH,WAAW,EAAI;AACtB,UAAMR,UAAU,GAAGQ,WAAW,CAAC3D,OAAZ,CAAoBM,aAApB,EAAmC,EAAnC,CAAnB;;AAEA,UAAI,CAACgD,WAAD,IAAgBjB,iBAAiB,CAACM,OAAlB,CAA0BQ,UAA1B,IAAwC,CAAC,CAA7D,EAAgE;AAC9D,YAAMhC,KAAK,GAAG+B,iBAAiB,CAACS,WAAD,CAA/B;AAEAZ,QAAAA,aAAa,CAACjJ,OAAD,EAAUiI,MAAV,EAAkBS,SAAlB,EAA6BrB,KAAK,CAACgB,eAAnC,EAAoDhB,KAAK,CAACa,kBAA1D,CAAb;AACD;AACF,KATH;AAUD,GA/CkB;AAiDnB4B,EAAAA,OAjDmB,mBAiDX9J,OAjDW,EAiDFqH,KAjDE,EAiDK0C,IAjDL,EAiDW;AAC5B,QAAI,OAAO1C,KAAP,KAAiB,QAAjB,IAA6B,CAACrH,OAAlC,EAA2C;AACzC,aAAO,IAAP;AACD;;AAED,QAAM0I,SAAS,GAAGrB,KAAK,CAACnB,OAAN,CAAcK,cAAd,EAA8B,EAA9B,CAAlB;AACA,QAAMiD,WAAW,GAAGnC,KAAK,KAAKqB,SAA9B;AACA,QAAME,QAAQ,GAAG9B,YAAY,CAAC+B,OAAb,CAAqBH,SAArB,IAAkC,CAAC,CAApD;AAEA,QAAIsB,WAAJ;AACA,QAAIC,OAAO,GAAG,IAAd;AACA,QAAIC,cAAc,GAAG,IAArB;AACA,QAAIvE,gBAAgB,GAAG,KAAvB;AACA,QAAIwE,GAAG,GAAG,IAAV;;AAEA,QAAIX,WAAW,IAAInD,CAAnB,EAAsB;AACpB2D,MAAAA,WAAW,GAAG3D,CAAC,CAAClF,KAAF,CAAQkG,KAAR,EAAe0C,IAAf,CAAd;AAEA1D,MAAAA,CAAC,CAACrG,OAAD,CAAD,CAAW8J,OAAX,CAAmBE,WAAnB;AACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACI,oBAAZ,EAAX;AACAF,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACK,6BAAZ,EAAlB;AACA1E,MAAAA,gBAAgB,GAAGqE,WAAW,CAACM,kBAAZ,EAAnB;AACD;;AAED,QAAI1B,QAAJ,EAAc;AACZuB,MAAAA,GAAG,GAAGtK,QAAQ,CAAC0K,WAAT,CAAqB,YAArB,CAAN;AACAJ,MAAAA,GAAG,CAACK,SAAJ,CAAc9B,SAAd,EAAyBuB,OAAzB,EAAkC,IAAlC;AACD,KAHD,MAGO;AACLE,MAAAA,GAAG,GAAG,IAAI5E,WAAJ,CAAgB8B,KAAhB,EAAuB;AAC3B4C,QAAAA,OAAO,EAAPA,OAD2B;AAE3BzE,QAAAA,UAAU,EAAE;AAFe,OAAvB,CAAN;AAID,KAhC2B;;;AAmC5B,QAAI,OAAOuE,IAAP,KAAgB,WAApB,EAAiC;AAC/B5H,MAAAA,MAAM,CAACC,IAAP,CAAY2H,IAAZ,EACG1H,OADH,CACW,UAAAiC,GAAG,EAAI;AACdnC,QAAAA,MAAM,CAACsI,cAAP,CAAsBN,GAAtB,EAA2B7F,GAA3B,EAAgC;AAC9BE,UAAAA,GAD8B,iBACxB;AACJ,mBAAOuF,IAAI,CAACzF,GAAD,CAAX;AACD;AAH6B,SAAhC;AAKD,OAPH;AAQD;;AAED,QAAIqB,gBAAJ,EAAsB;AACpBwE,MAAAA,GAAG,CAACzE,cAAJ;;AAEA,UAAI,CAACL,mCAAL,EAA0C;AACxClD,QAAAA,MAAM,CAACsI,cAAP,CAAsBN,GAAtB,EAA2B,kBAA3B,EAA+C;AAC7C3F,UAAAA,GAAG,EAAE;AAAA,mBAAM,IAAN;AAAA;AADwC,SAA/C;AAGD;AACF;;AAED,QAAI0F,cAAJ,EAAoB;AAClBlK,MAAAA,OAAO,CAACkB,aAAR,CAAsBiJ,GAAtB;AACD;;AAED,QAAIA,GAAG,CAACxE,gBAAJ,IAAwB,OAAOqE,WAAP,KAAuB,WAAnD,EAAgE;AAC9DA,MAAAA,WAAW,CAACtE,cAAZ;AACD;;AAED,WAAOyE,GAAP;AACD;AAlHkB,CAArB;;AC1MA;;;;;;AAMA,IAAMO,IAAI,GAAG,OAAb;AACA,IAAMC,OAAO,GAAG,cAAhB;AACA,IAAMC,QAAQ,GAAG,UAAjB;AACA,IAAMC,SAAS,SAAOD,QAAtB;AACA,IAAME,YAAY,GAAG,WAArB;AAEA,IAAMC,gBAAgB,GAAG,wBAAzB;AAEA,IAAMC,WAAW,aAAWH,SAA5B;AACA,IAAMI,YAAY,cAAYJ,SAA9B;AACA,IAAMK,oBAAoB,aAAWL,SAAX,GAAuBC,YAAjD;AAEA,IAAMK,eAAe,GAAG,OAAxB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AAEA;;;;;;IAMMC;AACJ,iBAAYtL,OAAZ,EAAqB;AACnB,SAAKuL,QAAL,GAAgBvL,OAAhB;;AAEA,QAAI,KAAKuL,QAAT,EAAmB;AACjB5G,MAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,QAAtB,EAAgC,IAAhC;AACD;AACF;;;;;AAQD;SAEAY,QAAA,eAAMxL,OAAN,EAAe;AACb,QAAIyL,WAAW,GAAG,KAAKF,QAAvB;;AACA,QAAIvL,OAAJ,EAAa;AACXyL,MAAAA,WAAW,GAAG,KAAKC,eAAL,CAAqB1L,OAArB,CAAd;AACD;;AAED,QAAM2L,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;AAEA,QAAIE,WAAW,KAAK,IAAhB,IAAwBA,WAAW,CAAChG,gBAAxC,EAA0D;AACxD;AACD;;AAED,SAAKkG,cAAL,CAAoBJ,WAApB;AACD;;SAEDK,UAAA,mBAAU;AACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,QAA/B;AACA,SAAKW,QAAL,GAAgB,IAAhB;AACD;;;SAIDG,kBAAA,yBAAgB1L,OAAhB,EAAyB;AACvB,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAAC+L,OAAR,OAAoBZ,eAApB,CAA1C;AACD;;SAEDS,qBAAA,4BAAmB5L,OAAnB,EAA4B;AAC1B,WAAOuH,YAAY,CAACuC,OAAb,CAAqB9J,OAArB,EAA8BgL,WAA9B,CAAP;AACD;;SAEDa,iBAAA,wBAAe7L,OAAf,EAAwB;AAAA;;AACtBA,IAAAA,OAAO,CAACgM,SAAR,CAAkBC,MAAlB,CAAyBZ,cAAzB;;AAEA,QAAI,CAACrL,OAAO,CAACgM,SAAR,CAAkBE,QAAlB,CAA2Bd,cAA3B,CAAL,EAAiD;AAC/C,WAAKe,eAAL,CAAqBnM,OAArB;;AACA;AACD;;AAED,QAAMW,kBAAkB,GAAGH,gCAAgC,CAACR,OAAD,CAA3D;AAEAuH,IAAAA,YAAY,CACTgC,GADH,CACOvJ,OADP,EACgBhB,cADhB,EACgC;AAAA,aAAM,KAAI,CAACmN,eAAL,CAAqBnM,OAArB,CAAN;AAAA,KADhC;AAEAsB,IAAAA,oBAAoB,CAACtB,OAAD,EAAUW,kBAAV,CAApB;AACD;;SAEDwL,kBAAA,yBAAgBnM,OAAhB,EAAyB;AACvB,QAAIA,OAAO,CAACgD,UAAZ,EAAwB;AACtBhD,MAAAA,OAAO,CAACgD,UAAR,CAAmBoJ,WAAnB,CAA+BpM,OAA/B;AACD;;AAEDuH,IAAAA,YAAY,CAACuC,OAAb,CAAqB9J,OAArB,EAA8BiL,YAA9B;AACD;;;QAIMoB,kBAAP,yBAAuBpK,MAAvB,EAA+B;AAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;AAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,QAAnB,CAAX;;AAEA,UAAI,CAACrG,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAI+G,KAAJ,CAAU,IAAV,CAAP;AACD;;AAED,UAAIrJ,MAAM,KAAK,OAAf,EAAwB;AACtBsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ,CAAa,IAAb;AACD;AACF,KAVM,CAAP;AAWD;;QAEMsK,gBAAP,uBAAqBC,aAArB,EAAoC;AAClC,WAAO,UAAUnF,KAAV,EAAiB;AACtB,UAAIA,KAAJ,EAAW;AACTA,QAAAA,KAAK,CAAC3B,cAAN;AACD;;AAED8G,MAAAA,aAAa,CAAChB,KAAd,CAAoB,IAApB;AACD,KAND;AAOD;;QAEMiB,cAAP,qBAAmBzM,OAAnB,EAA4B;AAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,QAAtB,CAAP;AACD;;;;wBAvFoB;AACnB,aAAOD,OAAP;AACD;;;;;AAwFH;;;;;;;AAKApD,YAAY,CACT+B,EADH,CACMzJ,QADN,EACgBqL,oBADhB,EACsCH,gBADtC,EACwDO,KAAK,CAACiB,aAAN,CAAoB,IAAIjB,KAAJ,EAApB,CADxD;AAGA,IAAMjF,GAAC,GAAGvC,SAAS,EAAnB;AAEA;;;;;;;AAOA;;AACA,IAAIuC,GAAJ,EAAO;AACL,MAAMqG,kBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,IAAL,CAA3B;AACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,IAAL,IAAaY,KAAK,CAACe,eAAnB;AACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,IAAL,EAAWiC,WAAX,GAAyBrB,KAAzB;;AACAjF,EAAAA,GAAC,CAACc,EAAF,CAAKuD,IAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,IAAL,IAAagC,kBAAb;AACA,WAAOpB,KAAK,CAACe,eAAb;AACD,GAHD;AAID;;ACjKD;;;;;;AAMA,IAAM3B,MAAI,GAAG,QAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,WAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAM+B,iBAAiB,GAAG,QAA1B;AAEA,IAAMC,oBAAoB,GAAG,wBAA7B;AAEA,IAAM5B,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA;;;;;;IAMMiC;AACJ,kBAAY/M,OAAZ,EAAqB;AACnB,SAAKuL,QAAL,GAAgBvL,OAAhB;AACA2E,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;AACD;;;;;AAQD;SAEAoC,SAAA,kBAAS;AACP;AACA,SAAKzB,QAAL,CAAc0B,YAAd,CAA2B,cAA3B,EAA2C,KAAK1B,QAAL,CAAcS,SAAd,CAAwBgB,MAAxB,CAA+BH,iBAA/B,CAA3C;AACD;;SAEDf,UAAA,mBAAU;AACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;AACA,SAAKW,QAAL,GAAgB,IAAhB;AACD;;;SAIMc,kBAAP,yBAAuBpK,MAAvB,EAA+B;AAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;AAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;AAEA,UAAI,CAACrG,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIwI,MAAJ,CAAW,IAAX,CAAP;AACD;;AAED,UAAI9K,MAAM,KAAK,QAAf,EAAyB;AACvBsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ;AACD;AACF,KAVM,CAAP;AAWD;;SAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;AAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;AACD;;;;wBAlCoB;AACnB,aAAOD,SAAP;AACD;;;;;AAmCH;;;;;;;AAMApD,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgD4B,oBAAhD,EAAsE,UAAAzF,KAAK,EAAI;AAC7EA,EAAAA,KAAK,CAAC3B,cAAN;AAEA,MAAMwH,MAAM,GAAG7F,KAAK,CAACQ,MAAN,CAAakE,OAAb,CAAqBe,oBAArB,CAAf;AAEA,MAAIvI,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAaoI,MAAb,EAAqBtC,UAArB,CAAX;;AACA,MAAI,CAACrG,IAAL,EAAW;AACTA,IAAAA,IAAI,GAAG,IAAIwI,MAAJ,CAAWG,MAAX,CAAP;AACD;;AAED3I,EAAAA,IAAI,CAACyI,MAAL;AACD,CAXD;AAaA,IAAM3G,GAAC,GAAGvC,SAAS,EAAnB;AAEA;;;;;;;AAMA;;AACA,IAAIuC,GAAJ,EAAO;AACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;AACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAaqC,MAAM,CAACV,eAApB;AACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyBI,MAAzB;;AAEA1G,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;AACA,WAAOK,MAAM,CAACV,eAAd;AACD,GAHD;AAID;;ACrHD;;;;;;AAOA,SAASc,aAAT,CAAuBC,GAAvB,EAA4B;AAC1B,MAAIA,GAAG,KAAK,MAAZ,EAAoB;AAClB,WAAO,IAAP;AACD;;AAED,MAAIA,GAAG,KAAK,OAAZ,EAAqB;AACnB,WAAO,KAAP;AACD;;AAED,MAAIA,GAAG,KAAKC,MAAM,CAACD,GAAD,CAAN,CAAYhO,QAAZ,EAAZ,EAAoC;AAClC,WAAOiO,MAAM,CAACD,GAAD,CAAb;AACD;;AAED,MAAIA,GAAG,KAAK,EAAR,IAAcA,GAAG,KAAK,MAA1B,EAAkC;AAChC,WAAO,IAAP;AACD;;AAED,SAAOA,GAAP;AACD;;AAED,SAASE,gBAAT,CAA0BhJ,GAA1B,EAA+B;AAC7B,SAAOA,GAAG,CAAC4B,OAAJ,CAAY,QAAZ,EAAsB,UAAAqH,GAAG;AAAA,iBAAQA,GAAG,CAAChO,WAAJ,EAAR;AAAA,GAAzB,CAAP;AACD;;AAED,IAAMiO,WAAW,GAAG;AAClBC,EAAAA,gBADkB,4BACDzN,OADC,EACQsE,GADR,EACa9B,KADb,EACoB;AACpCxC,IAAAA,OAAO,CAACiN,YAAR,WAA6BK,gBAAgB,CAAChJ,GAAD,CAA7C,EAAsD9B,KAAtD;AACD,GAHiB;AAKlBkL,EAAAA,mBALkB,+BAKE1N,OALF,EAKWsE,GALX,EAKgB;AAChCtE,IAAAA,OAAO,CAACmG,eAAR,WAAgCmH,gBAAgB,CAAChJ,GAAD,CAAhD;AACD,GAPiB;AASlBqJ,EAAAA,iBATkB,6BASA3N,OATA,EASS;AACzB,QAAI,CAACA,OAAL,EAAc;AACZ,aAAO,EAAP;AACD;;AAED,QAAM4N,UAAU,sBACX5N,OAAO,CAAC6N,OADG,CAAhB;;AAIA1L,IAAAA,MAAM,CAACC,IAAP,CAAYwL,UAAZ,EAAwBvL,OAAxB,CAAgC,UAAAiC,GAAG,EAAI;AACrCsJ,MAAAA,UAAU,CAACtJ,GAAD,CAAV,GAAkB6I,aAAa,CAACS,UAAU,CAACtJ,GAAD,CAAX,CAA/B;AACD,KAFD;AAIA,WAAOsJ,UAAP;AACD,GAvBiB;AAyBlBE,EAAAA,gBAzBkB,4BAyBD9N,OAzBC,EAyBQsE,GAzBR,EAyBa;AAC7B,WAAO6I,aAAa,CAACnN,OAAO,CAACE,YAAR,WAA6BoN,gBAAgB,CAAChJ,GAAD,CAA7C,CAAD,CAApB;AACD,GA3BiB;AA6BlByJ,EAAAA,MA7BkB,kBA6BX/N,OA7BW,EA6BF;AACd,QAAMgO,IAAI,GAAGhO,OAAO,CAACiO,qBAAR,EAAb;AAEA,WAAO;AACLC,MAAAA,GAAG,EAAEF,IAAI,CAACE,GAAL,GAAWrO,QAAQ,CAACmE,IAAT,CAAcmK,SADzB;AAELC,MAAAA,IAAI,EAAEJ,IAAI,CAACI,IAAL,GAAYvO,QAAQ,CAACmE,IAAT,CAAcqK;AAF3B,KAAP;AAID,GApCiB;AAsClBC,EAAAA,QAtCkB,oBAsCTtO,OAtCS,EAsCA;AAChB,WAAO;AACLkO,MAAAA,GAAG,EAAElO,OAAO,CAACuO,SADR;AAELH,MAAAA,IAAI,EAAEpO,OAAO,CAACwO;AAFT,KAAP;AAID,GA3CiB;AA6ClBC,EAAAA,WA7CkB,uBA6CNzO,OA7CM,EA6CG0O,SA7CH,EA6Cc;AAC9B,QAAI,CAAC1O,OAAL,EAAc;AACZ;AACD;;AAED,QAAIA,OAAO,CAACgM,SAAR,CAAkBE,QAAlB,CAA2BwC,SAA3B,CAAJ,EAA2C;AACzC1O,MAAAA,OAAO,CAACgM,SAAR,CAAkBC,MAAlB,CAAyByC,SAAzB;AACD,KAFD,MAEO;AACL1O,MAAAA,OAAO,CAACgM,SAAR,CAAkB2C,GAAlB,CAAsBD,SAAtB;AACD;AACF;AAvDiB,CAApB;;AC/BA;;;;;;AASA;;;;;;AAMA,IAAME,SAAS,GAAG,CAAlB;AAEA,IAAMC,cAAc,GAAG;AACrBzI,EAAAA,OADqB,mBACbpG,OADa,EACJC,QADI,EACM;AACzB,WAAOD,OAAO,CAACoG,OAAR,CAAgBnG,QAAhB,CAAP;AACD,GAHoB;AAKrB+E,EAAAA,IALqB,kBAKhB/E,QALgB,EAKND,OALM,EAK8B;AAAA;;AAAA,QAApCA,OAAoC;AAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAACyD,eAAiB;AAAA;;AACjD,WAAO,YAAGwL,MAAH,aAAaC,IAAM,CAAC1P,IAAP,CAAYW,OAAZ,EAAqBC,QAArB,CAAb,CAAP;AACD,GAPoB;AASrBmF,EAAAA,OATqB,qBASbnF,QATa,EASHD,OATG,EASiC;AAAA,QAApCA,OAAoC;AAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAACyD,eAAiB;AAAA;;AACpD,WAAO8B,OAAO,CAAC/F,IAAR,CAAaW,OAAb,EAAsBC,QAAtB,CAAP;AACD,GAXoB;AAarB+O,EAAAA,QAbqB,oBAaZhP,OAbY,EAaHC,QAbG,EAaO;AAAA;;AAC1B,QAAM+O,QAAQ,GAAG,aAAGF,MAAH,cAAa9O,OAAO,CAACgP,QAArB,CAAjB;;AAEA,WAAOA,QAAQ,CAACC,MAAT,CAAgB,UAAAC,KAAK;AAAA,aAAIA,KAAK,CAAC9I,OAAN,CAAcnG,QAAd,CAAJ;AAAA,KAArB,CAAP;AACD,GAjBoB;AAmBrBkP,EAAAA,OAnBqB,mBAmBbnP,OAnBa,EAmBJC,QAnBI,EAmBM;AACzB,QAAMkP,OAAO,GAAG,EAAhB;AAEA,QAAIC,QAAQ,GAAGpP,OAAO,CAACgD,UAAvB;;AAEA,WAAOoM,QAAQ,IAAIA,QAAQ,CAAC/N,QAAT,KAAsBgO,IAAI,CAACC,YAAvC,IAAuDF,QAAQ,CAAC/N,QAAT,KAAsBuN,SAApF,EAA+F;AAC7F,UAAI,KAAKxI,OAAL,CAAagJ,QAAb,EAAuBnP,QAAvB,CAAJ,EAAsC;AACpCkP,QAAAA,OAAO,CAACI,IAAR,CAAaH,QAAb;AACD;;AAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACpM,UAApB;AACD;;AAED,WAAOmM,OAAP;AACD,GAjCoB;AAmCrBK,EAAAA,IAnCqB,gBAmChBxP,OAnCgB,EAmCPC,QAnCO,EAmCG;AACtB,QAAIwP,QAAQ,GAAGzP,OAAO,CAAC0P,sBAAvB;;AAEA,WAAOD,QAAP,EAAiB;AACf,UAAIA,QAAQ,CAACrJ,OAAT,CAAiBnG,QAAjB,CAAJ,EAAgC;AAC9B,eAAO,CAACwP,QAAD,CAAP;AACD;;AAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB;AACD;;AAED,WAAO,EAAP;AACD,GA/CoB;AAiDrBC,EAAAA,IAjDqB,gBAiDhB3P,OAjDgB,EAiDPC,QAjDO,EAiDG;AACtB,QAAI0P,IAAI,GAAG3P,OAAO,CAAC4P,kBAAnB;;AAEA,WAAOD,IAAP,EAAa;AACX,UAAI,KAAKvJ,OAAL,CAAauJ,IAAb,EAAmB1P,QAAnB,CAAJ,EAAkC;AAChC,eAAO,CAAC0P,IAAD,CAAP;AACD;;AAEDA,MAAAA,IAAI,GAAGA,IAAI,CAACC,kBAAZ;AACD;;AAED,WAAO,EAAP;AACD;AA7DoB,CAAvB;;ACMA;;;;;;AAMA,IAAMlF,MAAI,GAAG,UAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,aAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAM+E,cAAc,GAAG,WAAvB;AACA,IAAMC,eAAe,GAAG,YAAxB;AACA,IAAMC,sBAAsB,GAAG,GAA/B;;AACA,IAAMC,eAAe,GAAG,EAAxB;AAEA,IAAMC,OAAO,GAAG;AACdC,EAAAA,QAAQ,EAAE,IADI;AAEdC,EAAAA,QAAQ,EAAE,IAFI;AAGdC,EAAAA,KAAK,EAAE,KAHO;AAIdC,EAAAA,KAAK,EAAE,OAJO;AAKdC,EAAAA,IAAI,EAAE,IALQ;AAMdC,EAAAA,KAAK,EAAE;AANO,CAAhB;AASA,IAAMC,WAAW,GAAG;AAClBN,EAAAA,QAAQ,EAAE,kBADQ;AAElBC,EAAAA,QAAQ,EAAE,SAFQ;AAGlBC,EAAAA,KAAK,EAAE,kBAHW;AAIlBC,EAAAA,KAAK,EAAE,kBAJW;AAKlBC,EAAAA,IAAI,EAAE,SALY;AAMlBC,EAAAA,KAAK,EAAE;AANW,CAApB;AASA,IAAME,cAAc,GAAG,MAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,eAAe,GAAG,OAAxB;AAEA,IAAMC,WAAW,aAAWhG,WAA5B;AACA,IAAMiG,UAAU,YAAUjG,WAA1B;AACA,IAAMkG,aAAa,eAAalG,WAAhC;AACA,IAAMmG,gBAAgB,kBAAgBnG,WAAtC;AACA,IAAMoG,gBAAgB,kBAAgBpG,WAAtC;AACA,IAAMqG,gBAAgB,kBAAgBrG,WAAtC;AACA,IAAMsG,eAAe,iBAAetG,WAApC;AACA,IAAMuG,cAAc,gBAAcvG,WAAlC;AACA,IAAMwG,iBAAiB,mBAAiBxG,WAAxC;AACA,IAAMyG,eAAe,iBAAezG,WAApC;AACA,IAAM0G,gBAAgB,iBAAe1G,WAArC;AACA,IAAM2G,mBAAmB,YAAU3G,WAAV,GAAsBC,cAA/C;AACA,IAAMI,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAM2G,mBAAmB,GAAG,UAA5B;AACA,IAAM5E,mBAAiB,GAAG,QAA1B;AACA,IAAM6E,gBAAgB,GAAG,OAAzB;AACA,IAAMC,gBAAgB,GAAG,qBAAzB;AACA,IAAMC,eAAe,GAAG,oBAAxB;AACA,IAAMC,eAAe,GAAG,oBAAxB;AACA,IAAMC,eAAe,GAAG,oBAAxB;AACA,IAAMC,wBAAwB,GAAG,eAAjC;AAEA,IAAMC,eAAe,GAAG,SAAxB;AACA,IAAMC,oBAAoB,GAAG,uBAA7B;AACA,IAAMC,aAAa,GAAG,gBAAtB;AACA,IAAMC,iBAAiB,GAAG,oBAA1B;AACA,IAAMC,kBAAkB,GAAG,0CAA3B;AACA,IAAMC,mBAAmB,GAAG,sBAA5B;AACA,IAAMC,mBAAmB,GAAG,+BAA5B;AACA,IAAMC,kBAAkB,GAAG,wBAA3B;AAEA,IAAMC,WAAW,GAAG;AAClBC,EAAAA,KAAK,EAAE,OADW;AAElBC,EAAAA,GAAG,EAAE;AAFa,CAApB;AAKA;;;;;;IAKMC;AACJ,oBAAY3S,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,SAAK2Q,MAAL,GAAc,IAAd;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,cAAL,GAAsB,IAAtB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,UAAL,GAAkB,KAAlB;AACA,SAAKC,YAAL,GAAoB,IAApB;AACA,SAAKC,WAAL,GAAmB,CAAnB;AACA,SAAKC,WAAL,GAAmB,CAAnB;AAEA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,SAAKsJ,QAAL,GAAgBvL,OAAhB;AACA,SAAKsT,kBAAL,GAA0BzE,cAAc,CAACzJ,OAAf,CAAuBiN,mBAAvB,EAA4C,KAAK9G,QAAjD,CAA1B;AACA,SAAKgI,eAAL,GAAuB,kBAAkB1T,QAAQ,CAACyD,eAA3B,IAA8CkQ,SAAS,CAACC,cAAV,GAA2B,CAAhG;AACA,SAAKC,aAAL,GAAqB1N,OAAO,CAACvF,MAAM,CAACkT,YAAR,CAA5B;;AAEA,SAAKC,kBAAL;;AACAjP,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;AACD;;;;;AAYD;SAEA+E,OAAA,gBAAO;AACL,QAAI,CAAC,KAAKqD,UAAV,EAAsB;AACpB,WAAKa,MAAL,CAAYpD,cAAZ;AACD;AACF;;SAEDqD,kBAAA,2BAAkB;AAChB;AACA;AACA,QAAI,CAACjU,QAAQ,CAACkU,MAAV,IAAoBjR,SAAS,CAAC,KAAKyI,QAAN,CAAjC,EAAkD;AAChD,WAAKoE,IAAL;AACD;AACF;;SAEDH,OAAA,gBAAO;AACL,QAAI,CAAC,KAAKwD,UAAV,EAAsB;AACpB,WAAKa,MAAL,CAAYnD,cAAZ;AACD;AACF;;SAEDL,QAAA,eAAMhJ,KAAN,EAAa;AACX,QAAI,CAACA,KAAL,EAAY;AACV,WAAK0L,SAAL,GAAiB,IAAjB;AACD;;AAED,QAAIlE,cAAc,CAACzJ,OAAf,CAAuBgN,kBAAvB,EAA2C,KAAK7G,QAAhD,CAAJ,EAA+D;AAC7DtK,MAAAA,oBAAoB,CAAC,KAAKsK,QAAN,CAApB;AACA,WAAKyI,KAAL,CAAW,IAAX;AACD;;AAEDC,IAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;AACA,SAAKA,SAAL,GAAiB,IAAjB;AACD;;SAEDmB,QAAA,eAAM3M,KAAN,EAAa;AACX,QAAI,CAACA,KAAL,EAAY;AACV,WAAK0L,SAAL,GAAiB,KAAjB;AACD;;AAED,QAAI,KAAKF,SAAT,EAAoB;AAClBoB,MAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;AACA,WAAKA,SAAL,GAAiB,IAAjB;AACD;;AAED,QAAI,KAAKO,OAAL,IAAgB,KAAKA,OAAL,CAAalD,QAA7B,IAAyC,CAAC,KAAK6C,SAAnD,EAA8D;AAC5D,WAAKF,SAAL,GAAiBqB,WAAW,CAC1B,CAACrU,QAAQ,CAACsU,eAAT,GAA2B,KAAKL,eAAhC,GAAkD,KAAKnE,IAAxD,EAA8DyE,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAKhB,OAAL,CAAalD,QAFa,CAA5B;AAID;AACF;;SAEDmE,KAAA,YAAGC,KAAH,EAAU;AAAA;;AACR,SAAKxB,cAAL,GAAsBjE,cAAc,CAACzJ,OAAf,CAAuB6M,oBAAvB,EAA6C,KAAK1G,QAAlD,CAAtB;;AACA,QAAMgJ,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAK1B,cAAxB,CAApB;;AAEA,QAAIwB,KAAK,GAAG,KAAK1B,MAAL,CAAY7K,MAAZ,GAAqB,CAA7B,IAAkCuM,KAAK,GAAG,CAA9C,EAAiD;AAC/C;AACD;;AAED,QAAI,KAAKtB,UAAT,EAAqB;AACnBzL,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCuF,UAAhC,EAA4C;AAAA,eAAM,KAAI,CAACuD,EAAL,CAAQC,KAAR,CAAN;AAAA,OAA5C;AACA;AACD;;AAED,QAAIC,WAAW,KAAKD,KAApB,EAA2B;AACzB,WAAKjE,KAAL;AACA,WAAK2D,KAAL;AACA;AACD;;AAED,QAAMS,SAAS,GAAGH,KAAK,GAAGC,WAAR,GAChB9D,cADgB,GAEhBC,cAFF;;AAIA,SAAKmD,MAAL,CAAYY,SAAZ,EAAuB,KAAK7B,MAAL,CAAY0B,KAAZ,CAAvB;AACD;;SAEDxI,UAAA,mBAAU;AACRvE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+D,QAAtB,EAAgCV,WAAhC;AACAlG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;AAEA,SAAKgI,MAAL,GAAc,IAAd;AACA,SAAKQ,OAAL,GAAe,IAAf;AACA,SAAK7H,QAAL,GAAgB,IAAhB;AACA,SAAKsH,SAAL,GAAiB,IAAjB;AACA,SAAKE,SAAL,GAAiB,IAAjB;AACA,SAAKC,UAAL,GAAkB,IAAlB;AACA,SAAKF,cAAL,GAAsB,IAAtB;AACA,SAAKQ,kBAAL,GAA0B,IAA1B;AACD;;;SAIDD,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,qCACDgO,OADC,GAEDhO,MAFC,CAAN;AAIAF,IAAAA,eAAe,CAAC2I,MAAD,EAAOzI,MAAP,EAAeuO,WAAf,CAAf;AACA,WAAOvO,MAAP;AACD;;SAEDyS,eAAA,wBAAe;AACb,QAAMC,SAAS,GAAGjV,IAAI,CAACkV,GAAL,CAAS,KAAKzB,WAAd,CAAlB;;AAEA,QAAIwB,SAAS,IAAI3E,eAAjB,EAAkC;AAChC;AACD;;AAED,QAAMyE,SAAS,GAAGE,SAAS,GAAG,KAAKxB,WAAnC;AAEA,SAAKA,WAAL,GAAmB,CAAnB,CATa;;AAYb,QAAIsB,SAAS,GAAG,CAAhB,EAAmB;AACjB,WAAKjF,IAAL;AACD,KAdY;;;AAiBb,QAAIiF,SAAS,GAAG,CAAhB,EAAmB;AACjB,WAAK9E,IAAL;AACD;AACF;;SAEDiE,qBAAA,8BAAqB;AAAA;;AACnB,QAAI,KAAKR,OAAL,CAAajD,QAAjB,EAA2B;AACzB5I,MAAAA,YAAY,CACT+B,EADH,CACM,KAAKiC,QADX,EACqBwF,aADrB,EACoC,UAAA1J,KAAK;AAAA,eAAI,MAAI,CAACwN,QAAL,CAAcxN,KAAd,CAAJ;AAAA,OADzC;AAED;;AAED,QAAI,KAAK+L,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;AAClC9I,MAAAA,YAAY,CACT+B,EADH,CACM,KAAKiC,QADX,EACqByF,gBADrB,EACuC,UAAA3J,KAAK;AAAA,eAAI,MAAI,CAACgJ,KAAL,CAAWhJ,KAAX,CAAJ;AAAA,OAD5C;AAEAE,MAAAA,YAAY,CACT+B,EADH,CACM,KAAKiC,QADX,EACqB0F,gBADrB,EACuC,UAAA5J,KAAK;AAAA,eAAI,MAAI,CAAC2M,KAAL,CAAW3M,KAAX,CAAJ;AAAA,OAD5C;AAED;;AAED,QAAI,KAAK+L,OAAL,CAAa7C,KAAb,IAAsB,KAAKgD,eAA/B,EAAgD;AAC9C,WAAKuB,uBAAL;AACD;AACF;;SAEDA,0BAAA,mCAA0B;AAAA;;AACxB,QAAMC,KAAK,GAAG,SAARA,KAAQ,CAAA1N,KAAK,EAAI;AACrB,UAAI,MAAI,CAACqM,aAAL,IAAsBlB,WAAW,CAACnL,KAAK,CAAC2N,WAAN,CAAkBnS,WAAlB,EAAD,CAArC,EAAwE;AACtE,QAAA,MAAI,CAACqQ,WAAL,GAAmB7L,KAAK,CAAC4N,OAAzB;AACD,OAFD,MAEO,IAAI,CAAC,MAAI,CAACvB,aAAV,EAAyB;AAC9B,QAAA,MAAI,CAACR,WAAL,GAAmB7L,KAAK,CAAC6N,OAAN,CAAc,CAAd,EAAiBD,OAApC;AACD;AACF,KAND;;AAQA,QAAME,IAAI,GAAG,SAAPA,IAAO,CAAA9N,KAAK,EAAI;AACpB;AACA,UAAIA,KAAK,CAAC6N,OAAN,IAAiB7N,KAAK,CAAC6N,OAAN,CAAcnN,MAAd,GAAuB,CAA5C,EAA+C;AAC7C,QAAA,MAAI,CAACoL,WAAL,GAAmB,CAAnB;AACD,OAFD,MAEO;AACL,QAAA,MAAI,CAACA,WAAL,GAAmB9L,KAAK,CAAC6N,OAAN,CAAc,CAAd,EAAiBD,OAAjB,GAA2B,MAAI,CAAC/B,WAAnD;AACD;AACF,KAPD;;AASA,QAAMkC,GAAG,GAAG,SAANA,GAAM,CAAA/N,KAAK,EAAI;AACnB,UAAI,MAAI,CAACqM,aAAL,IAAsBlB,WAAW,CAACnL,KAAK,CAAC2N,WAAN,CAAkBnS,WAAlB,EAAD,CAArC,EAAwE;AACtE,QAAA,MAAI,CAACsQ,WAAL,GAAmB9L,KAAK,CAAC4N,OAAN,GAAgB,MAAI,CAAC/B,WAAxC;AACD;;AAED,MAAA,MAAI,CAACwB,YAAL;;AACA,UAAI,MAAI,CAACtB,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,QAAA,MAAI,CAACA,KAAL;;AACA,YAAI,MAAI,CAAC4C,YAAT,EAAuB;AACrBoC,UAAAA,YAAY,CAAC,MAAI,CAACpC,YAAN,CAAZ;AACD;;AAED,QAAA,MAAI,CAACA,YAAL,GAAoBnR,UAAU,CAAC,UAAAuF,KAAK;AAAA,iBAAI,MAAI,CAAC2M,KAAL,CAAW3M,KAAX,CAAJ;AAAA,SAAN,EAA6B0I,sBAAsB,GAAG,MAAI,CAACqD,OAAL,CAAalD,QAAnE,CAA9B;AACD;AACF,KAtBD;;AAwBArB,IAAAA,cAAc,CAAC7J,IAAf,CAAoBmN,iBAApB,EAAuC,KAAK5G,QAA5C,EAAsDlJ,OAAtD,CAA8D,UAAAiT,OAAO,EAAI;AACvE/N,MAAAA,YAAY,CAAC+B,EAAb,CAAgBgM,OAAhB,EAAyB/D,gBAAzB,EAA2C,UAAAjM,CAAC;AAAA,eAAIA,CAAC,CAACI,cAAF,EAAJ;AAAA,OAA5C;AACD,KAFD;;AAIA,QAAI,KAAKgO,aAAT,EAAwB;AACtBnM,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B8F,iBAA/B,EAAkD,UAAAhK,KAAK;AAAA,eAAI0N,KAAK,CAAC1N,KAAD,CAAT;AAAA,OAAvD;AACAE,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B+F,eAA/B,EAAgD,UAAAjK,KAAK;AAAA,eAAI+N,GAAG,CAAC/N,KAAD,CAAP;AAAA,OAArD;;AAEA,WAAKkE,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BoD,wBAA5B;AACD,KALD,MAKO;AACLxK,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B2F,gBAA/B,EAAiD,UAAA7J,KAAK;AAAA,eAAI0N,KAAK,CAAC1N,KAAD,CAAT;AAAA,OAAtD;AACAE,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B4F,eAA/B,EAAgD,UAAA9J,KAAK;AAAA,eAAI8N,IAAI,CAAC9N,KAAD,CAAR;AAAA,OAArD;AACAE,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B6F,cAA/B,EAA+C,UAAA/J,KAAK;AAAA,eAAI+N,GAAG,CAAC/N,KAAD,CAAP;AAAA,OAApD;AACD;AACF;;SAEDwN,WAAA,kBAASxN,KAAT,EAAgB;AACd,QAAI,kBAAkB1E,IAAlB,CAAuB0E,KAAK,CAACQ,MAAN,CAAa0N,OAApC,CAAJ,EAAkD;AAChD;AACD;;AAED,YAAQlO,KAAK,CAAC/C,GAAd;AACE,WAAKuL,cAAL;AACExI,QAAAA,KAAK,CAAC3B,cAAN;AACA,aAAK8J,IAAL;AACA;;AACF,WAAKM,eAAL;AACEzI,QAAAA,KAAK,CAAC3B,cAAN;AACA,aAAKiK,IAAL;AACA;AARJ;AAWD;;SAED6E,gBAAA,uBAAcxU,OAAd,EAAuB;AACrB,SAAK4S,MAAL,GAAc5S,OAAO,IAAIA,OAAO,CAACgD,UAAnB,GACZ6L,cAAc,CAAC7J,IAAf,CAAoBkN,aAApB,EAAmClS,OAAO,CAACgD,UAA3C,CADY,GAEZ,EAFF;AAIA,WAAO,KAAK4P,MAAL,CAAY/J,OAAZ,CAAoB7I,OAApB,CAAP;AACD;;SAEDwV,sBAAA,6BAAoBf,SAApB,EAA+BgB,aAA/B,EAA8C;AAC5C,QAAMC,eAAe,GAAGjB,SAAS,KAAKhE,cAAtC;AACA,QAAMkF,eAAe,GAAGlB,SAAS,KAAK/D,cAAtC;;AACA,QAAM6D,WAAW,GAAG,KAAKC,aAAL,CAAmBiB,aAAnB,CAApB;;AACA,QAAMG,aAAa,GAAG,KAAKhD,MAAL,CAAY7K,MAAZ,GAAqB,CAA3C;AACA,QAAM8N,aAAa,GAAIF,eAAe,IAAIpB,WAAW,KAAK,CAApC,IACGmB,eAAe,IAAInB,WAAW,KAAKqB,aAD5D;;AAGA,QAAIC,aAAa,IAAI,CAAC,KAAKzC,OAAL,CAAa9C,IAAnC,EAAyC;AACvC,aAAOmF,aAAP;AACD;;AAED,QAAMK,KAAK,GAAGrB,SAAS,KAAK/D,cAAd,GAA+B,CAAC,CAAhC,GAAoC,CAAlD;AACA,QAAMqF,SAAS,GAAG,CAACxB,WAAW,GAAGuB,KAAf,IAAwB,KAAKlD,MAAL,CAAY7K,MAAtD;AAEA,WAAOgO,SAAS,KAAK,CAAC,CAAf,GACL,KAAKnD,MAAL,CAAY,KAAKA,MAAL,CAAY7K,MAAZ,GAAqB,CAAjC,CADK,GAEL,KAAK6K,MAAL,CAAYmD,SAAZ,CAFF;AAGD;;SAEDC,qBAAA,4BAAmBC,aAAnB,EAAkCC,kBAAlC,EAAsD;AACpD,QAAMC,WAAW,GAAG,KAAK3B,aAAL,CAAmByB,aAAnB,CAApB;;AACA,QAAMG,SAAS,GAAG,KAAK5B,aAAL,CAAmB3F,cAAc,CAACzJ,OAAf,CAAuB6M,oBAAvB,EAA6C,KAAK1G,QAAlD,CAAnB,CAAlB;;AAEA,WAAOhE,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCsF,WAApC,EAAiD;AACtDoF,MAAAA,aAAa,EAAbA,aADsD;AAEtDxB,MAAAA,SAAS,EAAEyB,kBAF2C;AAGtDG,MAAAA,IAAI,EAAED,SAHgD;AAItD/B,MAAAA,EAAE,EAAE8B;AAJkD,KAAjD,CAAP;AAMD;;SAEDG,6BAAA,oCAA2BtW,OAA3B,EAAoC;AAClC,QAAI,KAAKsT,kBAAT,EAA6B;AAC3B,UAAMiD,UAAU,GAAG1H,cAAc,CAAC7J,IAAf,CAAoBgN,eAApB,EAAqC,KAAKsB,kBAA1C,CAAnB;;AACA,WAAK,IAAIxL,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGyO,UAAU,CAACxO,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;AAC1CyO,QAAAA,UAAU,CAACzO,CAAD,CAAV,CAAckE,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;AACD;;AAED,UAAM2J,aAAa,GAAG,KAAKlD,kBAAL,CAAwBtE,QAAxB,CACpB,KAAKwF,aAAL,CAAmBxU,OAAnB,CADoB,CAAtB;;AAIA,UAAIwW,aAAJ,EAAmB;AACjBA,QAAAA,aAAa,CAACxK,SAAd,CAAwB2C,GAAxB,CAA4B9B,mBAA5B;AACD;AACF;AACF;;SAEDgH,SAAA,gBAAOY,SAAP,EAAkBzU,OAAlB,EAA2B;AAAA;;AACzB,QAAMyV,aAAa,GAAG5G,cAAc,CAACzJ,OAAf,CAAuB6M,oBAAvB,EAA6C,KAAK1G,QAAlD,CAAtB;;AACA,QAAMkL,kBAAkB,GAAG,KAAKjC,aAAL,CAAmBiB,aAAnB,CAA3B;;AACA,QAAMiB,WAAW,GAAG1W,OAAO,IAAKyV,aAAa,IAC3C,KAAKD,mBAAL,CAAyBf,SAAzB,EAAoCgB,aAApC,CADF;;AAGA,QAAMkB,gBAAgB,GAAG,KAAKnC,aAAL,CAAmBkC,WAAnB,CAAzB;;AACA,QAAME,SAAS,GAAG5Q,OAAO,CAAC,KAAK6M,SAAN,CAAzB;AAEA,QAAIgE,oBAAJ;AACA,QAAIC,cAAJ;AACA,QAAIZ,kBAAJ;;AAEA,QAAIzB,SAAS,KAAKhE,cAAlB,EAAkC;AAChCoG,MAAAA,oBAAoB,GAAGjF,eAAvB;AACAkF,MAAAA,cAAc,GAAGjF,eAAjB;AACAqE,MAAAA,kBAAkB,GAAGvF,cAArB;AACD,KAJD,MAIO;AACLkG,MAAAA,oBAAoB,GAAGlF,gBAAvB;AACAmF,MAAAA,cAAc,GAAGhF,eAAjB;AACAoE,MAAAA,kBAAkB,GAAGtF,eAArB;AACD;;AAED,QAAI8F,WAAW,IAAIA,WAAW,CAAC1K,SAAZ,CAAsBE,QAAtB,CAA+BW,mBAA/B,CAAnB,EAAsE;AACpE,WAAKmG,UAAL,GAAkB,KAAlB;AACA;AACD;;AAED,QAAM+D,UAAU,GAAG,KAAKf,kBAAL,CAAwBU,WAAxB,EAAqCR,kBAArC,CAAnB;;AACA,QAAIa,UAAU,CAACpR,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAI,CAAC8P,aAAD,IAAkB,CAACiB,WAAvB,EAAoC;AAClC;AACA;AACD;;AAED,SAAK1D,UAAL,GAAkB,IAAlB;;AAEA,QAAI4D,SAAJ,EAAe;AACb,WAAKvG,KAAL;AACD;;AAED,SAAKiG,0BAAL,CAAgCI,WAAhC;;AAEA,QAAI,KAAKnL,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCwF,gBAAjC,CAAJ,EAAwD;AACtDgF,MAAAA,WAAW,CAAC1K,SAAZ,CAAsB2C,GAAtB,CAA0BmI,cAA1B;AAEAlT,MAAAA,MAAM,CAAC8S,WAAD,CAAN;AAEAjB,MAAAA,aAAa,CAACzJ,SAAd,CAAwB2C,GAAxB,CAA4BkI,oBAA5B;AACAH,MAAAA,WAAW,CAAC1K,SAAZ,CAAsB2C,GAAtB,CAA0BkI,oBAA1B;AAEA,UAAMG,mBAAmB,GAAGC,QAAQ,CAACP,WAAW,CAACxW,YAAZ,CAAyB,eAAzB,CAAD,EAA4C,EAA5C,CAApC;;AACA,UAAI8W,mBAAJ,EAAyB;AACvB,aAAK5D,OAAL,CAAa8D,eAAb,GAA+B,KAAK9D,OAAL,CAAa8D,eAAb,IAAgC,KAAK9D,OAAL,CAAalD,QAA5E;AACA,aAAKkD,OAAL,CAAalD,QAAb,GAAwB8G,mBAAxB;AACD,OAHD,MAGO;AACL,aAAK5D,OAAL,CAAalD,QAAb,GAAwB,KAAKkD,OAAL,CAAa8D,eAAb,IAAgC,KAAK9D,OAAL,CAAalD,QAArE;AACD;;AAED,UAAMvP,kBAAkB,GAAGH,gCAAgC,CAACiV,aAAD,CAA3D;AAEAlO,MAAAA,YAAY,CACTgC,GADH,CACOkM,aADP,EACsBzW,cADtB,EACsC,YAAM;AACxC0X,QAAAA,WAAW,CAAC1K,SAAZ,CAAsBC,MAAtB,CAA6B4K,oBAA7B,EAAmDC,cAAnD;AACAJ,QAAAA,WAAW,CAAC1K,SAAZ,CAAsB2C,GAAtB,CAA0B9B,mBAA1B;AAEA4I,QAAAA,aAAa,CAACzJ,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B,EAAkDiK,cAAlD,EAAkED,oBAAlE;AAEA,QAAA,MAAI,CAAC7D,UAAL,GAAkB,KAAlB;AAEAlR,QAAAA,UAAU,CAAC,YAAM;AACfyF,UAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAACyB,QAA1B,EAAoCuF,UAApC,EAAgD;AAC9CmF,YAAAA,aAAa,EAAES,WAD+B;AAE9CjC,YAAAA,SAAS,EAAEyB,kBAFmC;AAG9CG,YAAAA,IAAI,EAAEI,kBAHwC;AAI9CpC,YAAAA,EAAE,EAAEsC;AAJ0C,WAAhD;AAMD,SAPS,EAOP,CAPO,CAAV;AAQD,OAjBH;AAmBArV,MAAAA,oBAAoB,CAACmU,aAAD,EAAgB9U,kBAAhB,CAApB;AACD,KAtCD,MAsCO;AACL8U,MAAAA,aAAa,CAACzJ,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;AACA6J,MAAAA,WAAW,CAAC1K,SAAZ,CAAsB2C,GAAtB,CAA0B9B,mBAA1B;AAEA,WAAKmG,UAAL,GAAkB,KAAlB;AACAzL,MAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCuF,UAApC,EAAgD;AAC9CmF,QAAAA,aAAa,EAAES,WAD+B;AAE9CjC,QAAAA,SAAS,EAAEyB,kBAFmC;AAG9CG,QAAAA,IAAI,EAAEI,kBAHwC;AAI9CpC,QAAAA,EAAE,EAAEsC;AAJ0C,OAAhD;AAMD;;AAED,QAAIC,SAAJ,EAAe;AACb,WAAK5C,KAAL;AACD;AACF;;;WAIMmD,oBAAP,2BAAyBnX,OAAzB,EAAkCiC,MAAlC,EAA0C;AACxC,QAAIsC,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAX;;AACA,QAAIwI,OAAO,qCACNnD,OADM,GAENzC,WAAW,CAACG,iBAAZ,CAA8B3N,OAA9B,CAFM,CAAX;;AAKA,QAAI,OAAOiC,MAAP,KAAkB,QAAtB,EAAgC;AAC9BmR,MAAAA,OAAO,qCACFA,OADE,GAEFnR,MAFE,CAAP;AAID;;AAED,QAAMmV,MAAM,GAAG,OAAOnV,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCmR,OAAO,CAAChD,KAA7D;;AAEA,QAAI,CAAC7L,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIoO,QAAJ,CAAa3S,OAAb,EAAsBoT,OAAtB,CAAP;AACD;;AAED,QAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9BsC,MAAAA,IAAI,CAAC8P,EAAL,CAAQpS,MAAR;AACD,KAFD,MAEO,IAAI,OAAOmV,MAAP,KAAkB,QAAtB,EAAgC;AACrC,UAAI,OAAO7S,IAAI,CAAC6S,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIC,SAAJ,wBAAkCD,MAAlC,QAAN;AACD;;AAED7S,MAAAA,IAAI,CAAC6S,MAAD,CAAJ;AACD,KANM,MAMA,IAAIhE,OAAO,CAAClD,QAAR,IAAoBkD,OAAO,CAACkE,IAAhC,EAAsC;AAC3C/S,MAAAA,IAAI,CAAC8L,KAAL;AACA9L,MAAAA,IAAI,CAACyP,KAAL;AACD;AACF;;WAEM3H,kBAAP,yBAAuBpK,MAAvB,EAA+B;AAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;AAC3BqG,MAAAA,QAAQ,CAACwE,iBAAT,CAA2B,IAA3B,EAAiClV,MAAjC;AACD,KAFM,CAAP;AAGD;;WAEMsV,sBAAP,6BAA2BlQ,KAA3B,EAAkC;AAChC,QAAMQ,MAAM,GAAGtH,sBAAsB,CAAC,IAAD,CAArC;;AAEA,QAAI,CAACsH,MAAD,IAAW,CAACA,MAAM,CAACmE,SAAP,CAAiBE,QAAjB,CAA0BuF,mBAA1B,CAAhB,EAAgE;AAC9D;AACD;;AAED,QAAMxP,MAAM,qCACPuL,WAAW,CAACG,iBAAZ,CAA8B9F,MAA9B,CADO,GAEP2F,WAAW,CAACG,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;AAIA,QAAM6J,UAAU,GAAG,KAAKtX,YAAL,CAAkB,eAAlB,CAAnB;;AAEA,QAAIsX,UAAJ,EAAgB;AACdvV,MAAAA,MAAM,CAACiO,QAAP,GAAkB,KAAlB;AACD;;AAEDyC,IAAAA,QAAQ,CAACwE,iBAAT,CAA2BtP,MAA3B,EAAmC5F,MAAnC;;AAEA,QAAIuV,UAAJ,EAAgB;AACd7S,MAAAA,IAAI,CAACG,OAAL,CAAa+C,MAAb,EAAqB+C,UAArB,EAA+ByJ,EAA/B,CAAkCmD,UAAlC;AACD;;AAEDnQ,IAAAA,KAAK,CAAC3B,cAAN;AACD;;WAEM+G,cAAP,qBAAmBzM,OAAnB,EAA4B;AAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;AACD;;;;wBA1coB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOsF,OAAP;AACD;;;;;AAucH;;;;;;;AAMA1I,YAAY,CACT+B,EADH,CACMzJ,QADN,EACgBqL,sBADhB,EACsCoH,mBADtC,EAC2DK,QAAQ,CAAC4E,mBADpE;AAGAhQ,YAAY,CAAC+B,EAAb,CAAgB7I,MAAhB,EAAwB+Q,mBAAxB,EAA6C,YAAM;AACjD,MAAMiG,SAAS,GAAG5I,cAAc,CAAC7J,IAAf,CAAoBuN,kBAApB,CAAlB;;AAEA,OAAK,IAAIzK,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGqP,SAAS,CAAC1P,MAAhC,EAAwCD,CAAC,GAAGM,GAA5C,EAAiDN,CAAC,EAAlD,EAAsD;AACpD6K,IAAAA,QAAQ,CAACwE,iBAAT,CAA2BM,SAAS,CAAC3P,CAAD,CAApC,EAAyCnD,IAAI,CAACG,OAAL,CAAa2S,SAAS,CAAC3P,CAAD,CAAtB,EAA2B8C,UAA3B,CAAzC;AACD;AACF,CAND;AAQA,IAAMvE,GAAC,GAAGvC,SAAS,EAAnB;AAEA;;;;;;;AAMA;;AACA,IAAIuC,GAAJ,EAAO;AACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;AACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAaiI,QAAQ,CAACtG,eAAtB;AACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyBgG,QAAzB;;AACAtM,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;AACA,WAAOiG,QAAQ,CAACtG,eAAhB;AACD,GAHD;AAID;;ACxlBD;;;;;;AAMA,IAAM3B,MAAI,GAAG,UAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,aAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAMmF,SAAO,GAAG;AACdjD,EAAAA,MAAM,EAAE,IADM;AAEd0K,EAAAA,MAAM,EAAE;AAFM,CAAhB;AAKA,IAAMlH,aAAW,GAAG;AAClBxD,EAAAA,MAAM,EAAE,SADU;AAElB0K,EAAAA,MAAM,EAAE;AAFU,CAApB;AAKA,IAAMC,UAAU,YAAU9M,WAA1B;AACA,IAAM+M,WAAW,aAAW/M,WAA5B;AACA,IAAMgN,UAAU,YAAUhN,WAA1B;AACA,IAAMiN,YAAY,cAAYjN,WAA9B;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAMiN,eAAe,GAAG,MAAxB;AACA,IAAMC,mBAAmB,GAAG,UAA5B;AACA,IAAMC,qBAAqB,GAAG,YAA9B;AACA,IAAMC,oBAAoB,GAAG,WAA7B;AAEA,IAAMC,KAAK,GAAG,OAAd;AACA,IAAMC,MAAM,GAAG,QAAf;AAEA,IAAMC,gBAAgB,GAAG,oBAAzB;AACA,IAAMvL,sBAAoB,GAAG,0BAA7B;AAEA;;;;;;IAMMwL;AACJ,oBAAYtY,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,SAAKsW,gBAAL,GAAwB,KAAxB;AACA,SAAKhN,QAAL,GAAgBvL,OAAhB;AACA,SAAKoT,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,SAAKuW,aAAL,GAAqB3J,cAAc,CAAC7J,IAAf,CAChB8H,sBAAH,iBAAkC9M,OAAO,CAACoE,EAA1C,aACG0I,sBADH,wBACyC9M,OAAO,CAACoE,EADjD,SADmB,CAArB;AAKA,QAAMqU,UAAU,GAAG5J,cAAc,CAAC7J,IAAf,CAAoB8H,sBAApB,CAAnB;;AAEA,SAAK,IAAIhF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGqQ,UAAU,CAAC1Q,MAAjC,EAAyCD,CAAC,GAAGM,GAA7C,EAAkDN,CAAC,EAAnD,EAAuD;AACrD,UAAM4Q,IAAI,GAAGD,UAAU,CAAC3Q,CAAD,CAAvB;AACA,UAAM7H,QAAQ,GAAGI,sBAAsB,CAACqY,IAAD,CAAvC;AACA,UAAMC,aAAa,GAAG9J,cAAc,CAAC7J,IAAf,CAAoB/E,QAApB,EACnBgP,MADmB,CACZ,UAAA2J,SAAS;AAAA,eAAIA,SAAS,KAAK5Y,OAAlB;AAAA,OADG,CAAtB;;AAGA,UAAIC,QAAQ,KAAK,IAAb,IAAqB0Y,aAAa,CAAC5Q,MAAvC,EAA+C;AAC7C,aAAK8Q,SAAL,GAAiB5Y,QAAjB;;AACA,aAAKuY,aAAL,CAAmBjJ,IAAnB,CAAwBmJ,IAAxB;AACD;AACF;;AAED,SAAKI,OAAL,GAAe,KAAK1F,OAAL,CAAasE,MAAb,GAAsB,KAAKqB,UAAL,EAAtB,GAA0C,IAAzD;;AAEA,QAAI,CAAC,KAAK3F,OAAL,CAAasE,MAAlB,EAA0B;AACxB,WAAKsB,yBAAL,CAA+B,KAAKzN,QAApC,EAA8C,KAAKiN,aAAnD;AACD;;AAED,QAAI,KAAKpF,OAAL,CAAapG,MAAjB,EAAyB;AACvB,WAAKA,MAAL;AACD;;AAEDrI,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;AACD;;;;;AAYD;SAEAoC,SAAA,kBAAS;AACP,QAAI,KAAKzB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,eAAjC,CAAJ,EAAuD;AACrD,WAAKkB,IAAL;AACD,KAFD,MAEO;AACL,WAAKC,IAAL;AACD;AACF;;SAEDA,OAAA,gBAAO;AAAA;;AACL,QAAI,KAAKX,gBAAL,IACF,KAAKhN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,eAAjC,CADF,EACqD;AACnD;AACD;;AAED,QAAIoB,OAAJ;AACA,QAAIC,WAAJ;;AAEA,QAAI,KAAKN,OAAT,EAAkB;AAChBK,MAAAA,OAAO,GAAGtK,cAAc,CAAC7J,IAAf,CAAoBqT,gBAApB,EAAsC,KAAKS,OAA3C,EACP7J,MADO,CACA,UAAAyJ,IAAI,EAAI;AACd,YAAI,OAAO,KAAI,CAACtF,OAAL,CAAasE,MAApB,KAA+B,QAAnC,EAA6C;AAC3C,iBAAOgB,IAAI,CAACxY,YAAL,CAAkB,aAAlB,MAAqC,KAAI,CAACkT,OAAL,CAAasE,MAAzD;AACD;;AAED,eAAOgB,IAAI,CAAC1M,SAAL,CAAeE,QAAf,CAAwB8L,mBAAxB,CAAP;AACD,OAPO,CAAV;;AASA,UAAImB,OAAO,CAACpR,MAAR,KAAmB,CAAvB,EAA0B;AACxBoR,QAAAA,OAAO,GAAG,IAAV;AACD;AACF;;AAED,QAAME,SAAS,GAAGxK,cAAc,CAACzJ,OAAf,CAAuB,KAAKyT,SAA5B,CAAlB;;AACA,QAAIM,OAAJ,EAAa;AACX,UAAMG,cAAc,GAAGH,OAAO,CAAClK,MAAR,CAAe,UAAAyJ,IAAI;AAAA,eAAIW,SAAS,KAAKX,IAAlB;AAAA,OAAnB,CAAvB;AACAU,MAAAA,WAAW,GAAGE,cAAc,CAAC,CAAD,CAAd,GAAoB3U,IAAI,CAACG,OAAL,CAAawU,cAAc,CAAC,CAAD,CAA3B,EAAgC1O,UAAhC,CAApB,GAAgE,IAA9E;;AAEA,UAAIwO,WAAW,IAAIA,WAAW,CAACb,gBAA/B,EAAiD;AAC/C;AACD;AACF;;AAED,QAAMgB,UAAU,GAAGhS,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCoM,UAApC,CAAnB;;AACA,QAAI4B,UAAU,CAAC5T,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAIwT,OAAJ,EAAa;AACXA,MAAAA,OAAO,CAAC9W,OAAR,CAAgB,UAAAmX,UAAU,EAAI;AAC5B,YAAIH,SAAS,KAAKG,UAAlB,EAA8B;AAC5BlB,UAAAA,QAAQ,CAACmB,iBAAT,CAA2BD,UAA3B,EAAuC,MAAvC;AACD;;AAED,YAAI,CAACJ,WAAL,EAAkB;AAChBzU,UAAAA,IAAI,CAACC,OAAL,CAAa4U,UAAb,EAAyB5O,UAAzB,EAAmC,IAAnC;AACD;AACF,OARD;AASD;;AAED,QAAM8O,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AAEA,SAAKpO,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B+L,mBAA/B;;AACA,SAAKzM,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BsJ,qBAA5B;;AAEA,SAAK1M,QAAL,CAAcxI,KAAd,CAAoB2W,SAApB,IAAiC,CAAjC;;AAEA,QAAI,KAAKlB,aAAL,CAAmBzQ,MAAvB,EAA+B;AAC7B,WAAKyQ,aAAL,CAAmBnW,OAAnB,CAA2B,UAAArC,OAAO,EAAI;AACpCA,QAAAA,OAAO,CAACgM,SAAR,CAAkBC,MAAlB,CAAyBiM,oBAAzB;AACAlY,QAAAA,OAAO,CAACiN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD,OAHD;AAID;;AAED,SAAK2M,gBAAL,CAAsB,IAAtB;;AAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,KAAI,CAACtO,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BgM,qBAA/B;;AACA,MAAA,KAAI,CAAC1M,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BqJ,mBAA5B,EAAiDD,eAAjD;;AAEA,MAAA,KAAI,CAACxM,QAAL,CAAcxI,KAAd,CAAoB2W,SAApB,IAAiC,EAAjC;;AAEA,MAAA,KAAI,CAACE,gBAAL,CAAsB,KAAtB;;AAEArS,MAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAI,CAACyB,QAA1B,EAAoCqM,WAApC;AACD,KATD;;AAWA,QAAMkC,oBAAoB,GAAGJ,SAAS,CAAC,CAAD,CAAT,CAAa7W,WAAb,KAA6B6W,SAAS,CAAC9P,KAAV,CAAgB,CAAhB,CAA1D;AACA,QAAMmQ,UAAU,cAAYD,oBAA5B;AACA,QAAMnZ,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK+K,QAAN,CAA3D;AAEAhE,IAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD6a,QAAhD;AAEAvY,IAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgB5K,kBAAhB,CAApB;AACA,SAAK4K,QAAL,CAAcxI,KAAd,CAAoB2W,SAApB,IAAoC,KAAKnO,QAAL,CAAcwO,UAAd,CAApC;AACD;;SAEDd,OAAA,gBAAO;AAAA;;AACL,QAAI,KAAKV,gBAAL,IACF,CAAC,KAAKhN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,eAAjC,CADH,EACsD;AACpD;AACD;;AAED,QAAMwB,UAAU,GAAGhS,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCsM,UAApC,CAAnB;;AACA,QAAI0B,UAAU,CAAC5T,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAM+T,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AAEA,SAAKpO,QAAL,CAAcxI,KAAd,CAAoB2W,SAApB,IAAoC,KAAKnO,QAAL,CAAc0C,qBAAd,GAAsCyL,SAAtC,CAApC;AAEA9V,IAAAA,MAAM,CAAC,KAAK2H,QAAN,CAAN;;AAEA,SAAKA,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BsJ,qBAA5B;;AACA,SAAK1M,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B+L,mBAA/B,EAAoDD,eAApD;;AAEA,QAAMiC,kBAAkB,GAAG,KAAKxB,aAAL,CAAmBzQ,MAA9C;;AACA,QAAIiS,kBAAkB,GAAG,CAAzB,EAA4B;AAC1B,WAAK,IAAIlS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkS,kBAApB,EAAwClS,CAAC,EAAzC,EAA6C;AAC3C,YAAMgC,OAAO,GAAG,KAAK0O,aAAL,CAAmB1Q,CAAnB,CAAhB;AACA,YAAM4Q,IAAI,GAAGnY,sBAAsB,CAACuJ,OAAD,CAAnC;;AAEA,YAAI4O,IAAI,IAAI,CAACA,IAAI,CAAC1M,SAAL,CAAeE,QAAf,CAAwB6L,eAAxB,CAAb,EAAuD;AACrDjO,UAAAA,OAAO,CAACkC,SAAR,CAAkB2C,GAAlB,CAAsBuJ,oBAAtB;AACApO,UAAAA,OAAO,CAACmD,YAAR,CAAqB,eAArB,EAAsC,KAAtC;AACD;AACF;AACF;;AAED,SAAK2M,gBAAL,CAAsB,IAAtB;;AAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,MAAI,CAACD,gBAAL,CAAsB,KAAtB;;AACA,MAAA,MAAI,CAACrO,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BgM,qBAA/B;;AACA,MAAA,MAAI,CAAC1M,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BqJ,mBAA5B;;AACAzQ,MAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAACyB,QAA1B,EAAoCuM,YAApC;AACD,KALD;;AAOA,SAAKvM,QAAL,CAAcxI,KAAd,CAAoB2W,SAApB,IAAiC,EAAjC;AACA,QAAM/Y,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK+K,QAAN,CAA3D;AAEAhE,IAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD6a,QAAhD;AACAvY,IAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgB5K,kBAAhB,CAApB;AACD;;SAEDiZ,mBAAA,0BAAiBK,eAAjB,EAAkC;AAChC,SAAK1B,gBAAL,GAAwB0B,eAAxB;AACD;;SAEDnO,UAAA,mBAAU;AACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;AAEA,SAAKwI,OAAL,GAAe,IAAf;AACA,SAAK0F,OAAL,GAAe,IAAf;AACA,SAAKvN,QAAL,GAAgB,IAAhB;AACA,SAAKiN,aAAL,GAAqB,IAArB;AACA,SAAKD,gBAAL,GAAwB,IAAxB;AACD;;;SAIDlF,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,qCACDgO,SADC,GAEDhO,MAFC,CAAN;AAIAA,IAAAA,MAAM,CAAC+K,MAAP,GAAgBhH,OAAO,CAAC/D,MAAM,CAAC+K,MAAR,CAAvB,CALiB;;AAMjBjL,IAAAA,eAAe,CAAC2I,MAAD,EAAOzI,MAAP,EAAeuO,aAAf,CAAf;AACA,WAAOvO,MAAP;AACD;;SAED0X,gBAAA,yBAAgB;AACd,QAAMO,QAAQ,GAAG,KAAK3O,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCiM,KAAjC,CAAjB;;AACA,WAAO+B,QAAQ,GAAG/B,KAAH,GAAWC,MAA1B;AACD;;SAEDW,aAAA,sBAAa;AAAA;;AAAA,QACLrB,MADK,GACM,KAAKtE,OADX,CACLsE,MADK;;AAGX,QAAItW,SAAS,CAACsW,MAAD,CAAb,EAAuB;AACrB;AACA,UAAI,OAAOA,MAAM,CAACyC,MAAd,KAAyB,WAAzB,IAAwC,OAAOzC,MAAM,CAAC,CAAD,CAAb,KAAqB,WAAjE,EAA8E;AAC5EA,QAAAA,MAAM,GAAGA,MAAM,CAAC,CAAD,CAAf;AACD;AACF,KALD,MAKO;AACLA,MAAAA,MAAM,GAAG7I,cAAc,CAACzJ,OAAf,CAAuBsS,MAAvB,CAAT;AACD;;AAED,QAAMzX,QAAQ,GAAM6M,sBAAN,uBAA2C4K,MAA3C,QAAd;AAEA7I,IAAAA,cAAc,CAAC7J,IAAf,CAAoB/E,QAApB,EAA8ByX,MAA9B,EACGrV,OADH,CACW,UAAArC,OAAO,EAAI;AAClB,UAAMoa,QAAQ,GAAG7Z,sBAAsB,CAACP,OAAD,CAAvC;;AAEA,MAAA,MAAI,CAACgZ,yBAAL,CACEoB,QADF,EAEE,CAACpa,OAAD,CAFF;AAID,KARH;AAUA,WAAO0X,MAAP;AACD;;SAEDsB,4BAAA,mCAA0BhZ,OAA1B,EAAmCqa,YAAnC,EAAiD;AAC/C,QAAIra,OAAJ,EAAa;AACX,UAAMsa,MAAM,GAAGta,OAAO,CAACgM,SAAR,CAAkBE,QAAlB,CAA2B6L,eAA3B,CAAf;;AAEA,UAAIsC,YAAY,CAACtS,MAAjB,EAAyB;AACvBsS,QAAAA,YAAY,CAAChY,OAAb,CAAqB,UAAAqW,IAAI,EAAI;AAC3B,cAAI4B,MAAJ,EAAY;AACV5B,YAAAA,IAAI,CAAC1M,SAAL,CAAeC,MAAf,CAAsBiM,oBAAtB;AACD,WAFD,MAEO;AACLQ,YAAAA,IAAI,CAAC1M,SAAL,CAAe2C,GAAf,CAAmBuJ,oBAAnB;AACD;;AAEDQ,UAAAA,IAAI,CAACzL,YAAL,CAAkB,eAAlB,EAAmCqN,MAAnC;AACD,SARD;AASD;AACF;AACF;;;WAIMb,oBAAP,2BAAyBzZ,OAAzB,EAAkCiC,MAAlC,EAA0C;AACxC,QAAIsC,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAX;;AACA,QAAMwI,OAAO,oDACRnD,SADQ,GAERzC,WAAW,CAACG,iBAAZ,CAA8B3N,OAA9B,CAFQ,GAGR,OAAOiC,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHxC,CAAb;;AAMA,QAAI,CAACsC,IAAD,IAAS6O,OAAO,CAACpG,MAAjB,IAA2B,OAAO/K,MAAP,KAAkB,QAA7C,IAAyD,YAAYU,IAAZ,CAAiBV,MAAjB,CAA7D,EAAuF;AACrFmR,MAAAA,OAAO,CAACpG,MAAR,GAAiB,KAAjB;AACD;;AAED,QAAI,CAACzI,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAI+T,QAAJ,CAAatY,OAAb,EAAsBoT,OAAtB,CAAP;AACD;;AAED,QAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,UAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;AACD;;AAEDsC,MAAAA,IAAI,CAACtC,MAAD,CAAJ;AACD;AACF;;WAEMoK,kBAAP,yBAAuBpK,MAAvB,EAA+B;AAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;AAC3BgM,MAAAA,QAAQ,CAACmB,iBAAT,CAA2B,IAA3B,EAAiCxX,MAAjC;AACD,KAFM,CAAP;AAGD;;WAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;AAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;AACD;;;;wBA1QoB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOsF,SAAP;AACD;;;;;AAuQH;;;;;;;AAMA1I,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUzF,KAAV,EAAiB;AACrF;AACA,MAAIA,KAAK,CAACQ,MAAN,CAAa0N,OAAb,KAAyB,GAA7B,EAAkC;AAChClO,IAAAA,KAAK,CAAC3B,cAAN;AACD;;AAED,MAAM6U,WAAW,GAAG/M,WAAW,CAACG,iBAAZ,CAA8B,IAA9B,CAApB;AACA,MAAM1N,QAAQ,GAAGI,sBAAsB,CAAC,IAAD,CAAvC;AACA,MAAMma,gBAAgB,GAAG3L,cAAc,CAAC7J,IAAf,CAAoB/E,QAApB,CAAzB;AAEAua,EAAAA,gBAAgB,CAACnY,OAAjB,CAAyB,UAAArC,OAAO,EAAI;AAClC,QAAMuE,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAb;AACA,QAAI3I,MAAJ;;AACA,QAAIsC,IAAJ,EAAU;AACR;AACA,UAAIA,IAAI,CAACuU,OAAL,KAAiB,IAAjB,IAAyB,OAAOyB,WAAW,CAAC7C,MAAnB,KAA8B,QAA3D,EAAqE;AACnEnT,QAAAA,IAAI,CAAC6O,OAAL,CAAasE,MAAb,GAAsB6C,WAAW,CAAC7C,MAAlC;AACAnT,QAAAA,IAAI,CAACuU,OAAL,GAAevU,IAAI,CAACwU,UAAL,EAAf;AACD;;AAED9W,MAAAA,MAAM,GAAG,QAAT;AACD,KARD,MAQO;AACLA,MAAAA,MAAM,GAAGsY,WAAT;AACD;;AAEDjC,IAAAA,QAAQ,CAACmB,iBAAT,CAA2BzZ,OAA3B,EAAoCiC,MAApC;AACD,GAhBD;AAiBD,CA3BD;AA6BA,IAAMoE,GAAC,GAAGvC,SAAS,EAAnB;AAEA;;;;;;;AAMA;;AACA,IAAIuC,GAAJ,EAAO;AACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;AACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAa4N,QAAQ,CAACjM,eAAtB;AACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyB2L,QAAzB;;AACAjS,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;AACA,WAAO4L,QAAQ,CAACjM,eAAhB;AACD,GAHD;AAID;;ACvZD;;;;;;AAMA,IAAM3B,MAAI,GAAG,UAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,aAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAM2P,UAAU,GAAG,QAAnB;AACA,IAAMC,SAAS,GAAG,OAAlB;AACA,IAAMC,OAAO,GAAG,KAAhB;AACA,IAAMC,YAAY,GAAG,SAArB;AACA,IAAMC,cAAc,GAAG,WAAvB;AACA,IAAMC,kBAAkB,GAAG,CAA3B;;AAEA,IAAMC,cAAc,GAAG,IAAIrY,MAAJ,CAAckY,YAAd,SAA8BC,cAA9B,SAAgDJ,UAAhD,CAAvB;AAEA,IAAM5C,YAAU,YAAUhN,WAA1B;AACA,IAAMiN,cAAY,cAAYjN,WAA9B;AACA,IAAM8M,YAAU,YAAU9M,WAA1B;AACA,IAAM+M,aAAW,aAAW/M,WAA5B;AACA,IAAMmQ,WAAW,aAAWnQ,WAA5B;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AACA,IAAMmQ,sBAAsB,eAAapQ,WAAb,GAAyBC,cAArD;AACA,IAAMoQ,oBAAoB,aAAWrQ,WAAX,GAAuBC,cAAjD;AAEA,IAAMqQ,mBAAmB,GAAG,UAA5B;AACA,IAAMpD,iBAAe,GAAG,MAAxB;AACA,IAAMqD,iBAAiB,GAAG,QAA1B;AACA,IAAMC,oBAAoB,GAAG,WAA7B;AACA,IAAMC,mBAAmB,GAAG,UAA5B;AACA,IAAMC,oBAAoB,GAAG,qBAA7B;AACA,IAAMC,iBAAiB,GAAG,QAA1B;AACA,IAAMC,0BAA0B,GAAG,iBAAnC;AAEA,IAAM3O,sBAAoB,GAAG,0BAA7B;AACA,IAAM4O,mBAAmB,GAAG,gBAA5B;AACA,IAAMC,aAAa,GAAG,gBAAtB;AACA,IAAMC,mBAAmB,GAAG,aAA5B;AACA,IAAMC,sBAAsB,GAAG,6DAA/B;AAEA,IAAMC,aAAa,GAAG,WAAtB;AACA,IAAMC,gBAAgB,GAAG,SAAzB;AACA,IAAMC,gBAAgB,GAAG,cAAzB;AACA,IAAMC,mBAAmB,GAAG,YAA5B;AACA,IAAMC,eAAe,GAAG,aAAxB;AACA,IAAMC,cAAc,GAAG,YAAvB;AAEA,IAAMlM,SAAO,GAAG;AACdlC,EAAAA,MAAM,EAAE,CADM;AAEdqO,EAAAA,IAAI,EAAE,IAFQ;AAGdC,EAAAA,QAAQ,EAAE,cAHI;AAIdC,EAAAA,SAAS,EAAE,QAJG;AAKdnZ,EAAAA,OAAO,EAAE,SALK;AAMdoZ,EAAAA,YAAY,EAAE;AANA,CAAhB;AASA,IAAM/L,aAAW,GAAG;AAClBzC,EAAAA,MAAM,EAAE,0BADU;AAElBqO,EAAAA,IAAI,EAAE,SAFY;AAGlBC,EAAAA,QAAQ,EAAE,kBAHQ;AAIlBC,EAAAA,SAAS,EAAE,kBAJO;AAKlBnZ,EAAAA,OAAO,EAAE,QALS;AAMlBoZ,EAAAA,YAAY,EAAE;AANI,CAApB;AASA;;;;;;IAMMC;AACJ,oBAAYxc,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,SAAKsJ,QAAL,GAAgBvL,OAAhB;AACA,SAAKyc,OAAL,GAAe,IAAf;AACA,SAAKrJ,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,SAAKya,KAAL,GAAa,KAAKC,eAAL,EAAb;AACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;AAEA,SAAKjJ,kBAAL;;AACAjP,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;AACD;;;;;AAgBD;SAEAoC,SAAA,kBAAS;AACP,QAAI,KAAKzB,QAAL,CAAcuR,QAAd,IAA0B,KAAKvR,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCiP,mBAAjC,CAA9B,EAAqF;AACnF;AACD;;AAED,QAAM4B,QAAQ,GAAG,KAAKxR,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,iBAAjC,CAAjB;;AAEAyE,IAAAA,QAAQ,CAACQ,UAAT;;AAEA,QAAID,QAAJ,EAAc;AACZ;AACD;;AAED,SAAK7D,IAAL;AACD;;SAEDA,OAAA,gBAAO;AACL,QAAI,KAAK3N,QAAL,CAAcuR,QAAd,IAA0B,KAAKvR,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCiP,mBAAjC,CAA1B,IAAmF,KAAKuB,KAAL,CAAW1Q,SAAX,CAAqBE,QAArB,CAA8B6L,iBAA9B,CAAvF,EAAuI;AACrI;AACD;;AAED,QAAML,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8B,KAAK1R,QAAnC,CAAf;AACA,QAAM0K,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,KAAK1K;AADA,KAAtB;AAIA,QAAM2R,SAAS,GAAG3V,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCoM,YAApC,EAAgD1B,aAAhD,CAAlB;;AAEA,QAAIiH,SAAS,CAACvX,gBAAd,EAAgC;AAC9B;AACD,KAdI;;;AAiBL,QAAI,CAAC,KAAKiX,SAAV,EAAqB;AACnB,UAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;AACjC,cAAM,IAAI9F,SAAJ,CAAc,kEAAd,CAAN;AACD;;AAED,UAAI+F,gBAAgB,GAAG,KAAK7R,QAA5B;;AAEA,UAAI,KAAK6H,OAAL,CAAakJ,SAAb,KAA2B,QAA/B,EAAyC;AACvCc,QAAAA,gBAAgB,GAAG1F,MAAnB;AACD,OAFD,MAEO,IAAItW,SAAS,CAAC,KAAKgS,OAAL,CAAakJ,SAAd,CAAb,EAAuC;AAC5Cc,QAAAA,gBAAgB,GAAG,KAAKhK,OAAL,CAAakJ,SAAhC,CAD4C;;AAI5C,YAAI,OAAO,KAAKlJ,OAAL,CAAakJ,SAAb,CAAuBnC,MAA9B,KAAyC,WAA7C,EAA0D;AACxDiD,UAAAA,gBAAgB,GAAG,KAAKhK,OAAL,CAAakJ,SAAb,CAAuB,CAAvB,CAAnB;AACD;AACF,OAhBkB;AAmBnB;AACA;;;AACA,UAAI,KAAKlJ,OAAL,CAAaiJ,QAAb,KAA0B,cAA9B,EAA8C;AAC5C3E,QAAAA,MAAM,CAAC1L,SAAP,CAAiB2C,GAAjB,CAAqB8M,0BAArB;AACD;;AAED,WAAKgB,OAAL,GAAe,IAAIU,MAAJ,CAAWC,gBAAX,EAA6B,KAAKV,KAAlC,EAAyC,KAAKW,gBAAL,EAAzC,CAAf;AACD,KA3CI;AA8CL;AACA;AACA;;;AACA,QAAI,kBAAkBxd,QAAQ,CAACyD,eAA3B,IACF,CAACoU,MAAM,CAAC3L,OAAP,CAAe6P,mBAAf,CADH,EACwC;AAAA;;AACtC,kBAAG9M,MAAH,aAAajP,QAAQ,CAACmE,IAAT,CAAcgL,QAA3B,EACG3M,OADH,CACW,UAAAqW,IAAI;AAAA,eAAInR,YAAY,CAAC+B,EAAb,CAAgBoP,IAAhB,EAAsB,WAAtB,EAAmC,IAAnC,EAAyC/U,IAAI,EAA7C,CAAJ;AAAA,OADf;AAED;;AAED,SAAK4H,QAAL,CAAc+R,KAAd;;AACA,SAAK/R,QAAL,CAAc0B,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;AAEAO,IAAAA,WAAW,CAACiB,WAAZ,CAAwB,KAAKiO,KAA7B,EAAoC3E,iBAApC;AACAvK,IAAAA,WAAW,CAACiB,WAAZ,CAAwB,KAAKlD,QAA7B,EAAuCwM,iBAAvC;AACAxQ,IAAAA,YAAY,CAACuC,OAAb,CAAqB4N,MAArB,EAA6BE,aAA7B,EAA0C3B,aAA1C;AACD;;SAEDgD,OAAA,gBAAO;AACL,QAAI,KAAK1N,QAAL,CAAcuR,QAAd,IAA0B,KAAKvR,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCiP,mBAAjC,CAA1B,IAAmF,CAAC,KAAKuB,KAAL,CAAW1Q,SAAX,CAAqBE,QAArB,CAA8B6L,iBAA9B,CAAxF,EAAwI;AACtI;AACD;;AAED,QAAML,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8B,KAAK1R,QAAnC,CAAf;AACA,QAAM0K,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,KAAK1K;AADA,KAAtB;AAIA,QAAMgS,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB4N,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;AAEA,QAAIsH,SAAS,CAAC5X,gBAAd,EAAgC;AAC9B;AACD;;AAED,QAAI,KAAK8W,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAae,OAAb;AACD;;AAEDhQ,IAAAA,WAAW,CAACiB,WAAZ,CAAwB,KAAKiO,KAA7B,EAAoC3E,iBAApC;AACAvK,IAAAA,WAAW,CAACiB,WAAZ,CAAwB,KAAKlD,QAA7B,EAAuCwM,iBAAvC;AACAxQ,IAAAA,YAAY,CAACuC,OAAb,CAAqB4N,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;AACD;;SAEDnK,UAAA,mBAAU;AACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;AACArD,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+D,QAAtB,EAAgCV,WAAhC;AACA,SAAKU,QAAL,GAAgB,IAAhB;AACA,SAAKmR,KAAL,GAAa,IAAb;;AACA,QAAI,KAAKD,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAae,OAAb;;AACA,WAAKf,OAAL,GAAe,IAAf;AACD;AACF;;SAEDgB,SAAA,kBAAS;AACP,SAAKb,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;AACA,QAAI,KAAKJ,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAaiB,cAAb;AACD;AACF;;;SAID9J,qBAAA,8BAAqB;AAAA;;AACnBrM,IAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+ByP,WAA/B,EAA4C,UAAA3T,KAAK,EAAI;AACnDA,MAAAA,KAAK,CAAC3B,cAAN;AACA2B,MAAAA,KAAK,CAACsW,eAAN;;AACA,MAAA,KAAI,CAAC3Q,MAAL;AACD,KAJD;AAKD;;SAEDqG,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,oDACD,KAAK2b,WAAL,CAAiB3N,OADhB,GAEDzC,WAAW,CAACG,iBAAZ,CAA8B,KAAKpC,QAAnC,CAFC,GAGDtJ,MAHC,CAAN;AAMAF,IAAAA,eAAe,CACb2I,MADa,EAEbzI,MAFa,EAGb,KAAK2b,WAAL,CAAiBpN,WAHJ,CAAf;AAMA,WAAOvO,MAAP;AACD;;SAED0a,kBAAA,2BAAkB;AAChB,WAAO9N,cAAc,CAACc,IAAf,CAAoB,KAAKpE,QAAzB,EAAmCoQ,aAAnC,EAAkD,CAAlD,CAAP;AACD;;SAEDkC,gBAAA,yBAAgB;AACd,QAAMC,cAAc,GAAG,KAAKvS,QAAL,CAAcvI,UAArC;AACA,QAAI+a,SAAS,GAAG/B,gBAAhB,CAFc;;AAKd,QAAI8B,cAAc,CAAC9R,SAAf,CAAyBE,QAAzB,CAAkCkP,iBAAlC,CAAJ,EAA0D;AACxD2C,MAAAA,SAAS,GAAGjC,aAAZ;;AACA,UAAI,KAAKY,KAAL,CAAW1Q,SAAX,CAAqBE,QAArB,CAA8BqP,oBAA9B,CAAJ,EAAyD;AACvDwC,QAAAA,SAAS,GAAGhC,gBAAZ;AACD;AACF,KALD,MAKO,IAAI+B,cAAc,CAAC9R,SAAf,CAAyBE,QAAzB,CAAkCmP,oBAAlC,CAAJ,EAA6D;AAClE0C,MAAAA,SAAS,GAAG7B,eAAZ;AACD,KAFM,MAEA,IAAI4B,cAAc,CAAC9R,SAAf,CAAyBE,QAAzB,CAAkCoP,mBAAlC,CAAJ,EAA4D;AACjEyC,MAAAA,SAAS,GAAG5B,cAAZ;AACD,KAFM,MAEA,IAAI,KAAKO,KAAL,CAAW1Q,SAAX,CAAqBE,QAArB,CAA8BqP,oBAA9B,CAAJ,EAAyD;AAC9DwC,MAAAA,SAAS,GAAG9B,mBAAZ;AACD;;AAED,WAAO8B,SAAP;AACD;;SAEDlB,gBAAA,yBAAgB;AACd,WAAO7W,OAAO,CAAC,KAAKuF,QAAL,CAAcQ,OAAd,OAA0ByP,iBAA1B,CAAD,CAAd;AACD;;SAEDwC,aAAA,sBAAa;AAAA;;AACX,QAAMjQ,MAAM,GAAG,EAAf;;AAEA,QAAI,OAAO,KAAKqF,OAAL,CAAarF,MAApB,KAA+B,UAAnC,EAA+C;AAC7CA,MAAAA,MAAM,CAAC5G,EAAP,GAAY,UAAA5C,IAAI,EAAI;AAClBA,QAAAA,IAAI,CAAC0Z,OAAL,qCACK1Z,IAAI,CAAC0Z,OADV,GAEK,MAAI,CAAC7K,OAAL,CAAarF,MAAb,CAAoBxJ,IAAI,CAAC0Z,OAAzB,EAAkC,MAAI,CAAC1S,QAAvC,KAAoD,EAFzD;AAKA,eAAOhH,IAAP;AACD,OAPD;AAQD,KATD,MASO;AACLwJ,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKqF,OAAL,CAAarF,MAA7B;AACD;;AAED,WAAOA,MAAP;AACD;;SAEDsP,mBAAA,4BAAmB;AACjB,QAAMd,YAAY,GAAG;AACnBwB,MAAAA,SAAS,EAAE,KAAKF,aAAL,EADQ;AAEnBK,MAAAA,SAAS,EAAE;AACTnQ,QAAAA,MAAM,EAAE,KAAKiQ,UAAL,EADC;AAET5B,QAAAA,IAAI,EAAE;AACJ+B,UAAAA,OAAO,EAAE,KAAK/K,OAAL,CAAagJ;AADlB,SAFG;AAKTgC,QAAAA,eAAe,EAAE;AACfC,UAAAA,iBAAiB,EAAE,KAAKjL,OAAL,CAAaiJ;AADjB;AALR;AAFQ,KAArB,CADiB;;AAejB,QAAI,KAAKjJ,OAAL,CAAajQ,OAAb,KAAyB,QAA7B,EAAuC;AACrCoZ,MAAAA,YAAY,CAAC2B,SAAb,CAAuBI,UAAvB,GAAoC;AAClCH,QAAAA,OAAO,EAAE;AADyB,OAApC;AAGD;;AAED,6CACK5B,YADL,GAEK,KAAKnJ,OAAL,CAAamJ,YAFlB;AAID;;;WAIMgC,oBAAP,2BAAyBve,OAAzB,EAAkCiC,MAAlC,EAA0C;AACxC,QAAIsC,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAX;;AACA,QAAMwI,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;AAEA,QAAI,CAACsC,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIiY,QAAJ,CAAaxc,OAAb,EAAsBoT,OAAtB,CAAP;AACD;;AAED,QAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,UAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;AACD;;AAEDsC,MAAAA,IAAI,CAACtC,MAAD,CAAJ;AACD;AACF;;WAEMoK,kBAAP,yBAAuBpK,MAAvB,EAA+B;AAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;AAC3BkQ,MAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B,EAAiCtc,MAAjC;AACD,KAFM,CAAP;AAGD;;WAEM+a,aAAP,oBAAkB3V,KAAlB,EAAyB;AACvB,QAAIA,KAAK,KAAKA,KAAK,CAAC6F,MAAN,KAAiB4N,kBAAjB,IACXzT,KAAK,CAACI,IAAN,KAAe,OAAf,IAA0BJ,KAAK,CAAC/C,GAAN,KAAcqW,OADlC,CAAT,EACsD;AACpD;AACD;;AAED,QAAM6D,OAAO,GAAG3P,cAAc,CAAC7J,IAAf,CAAoB8H,sBAApB,CAAhB;;AAEA,SAAK,IAAIhF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGoW,OAAO,CAACzW,MAA9B,EAAsCD,CAAC,GAAGM,GAA1C,EAA+CN,CAAC,EAAhD,EAAoD;AAClD,UAAM4P,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8BuB,OAAO,CAAC1W,CAAD,CAArC,CAAf;AACA,UAAM2W,OAAO,GAAG9Z,IAAI,CAACG,OAAL,CAAa0Z,OAAO,CAAC1W,CAAD,CAApB,EAAyB8C,UAAzB,CAAhB;AACA,UAAMqL,aAAa,GAAG;AACpBA,QAAAA,aAAa,EAAEuI,OAAO,CAAC1W,CAAD;AADF,OAAtB;;AAIA,UAAIT,KAAK,IAAIA,KAAK,CAACI,IAAN,KAAe,OAA5B,EAAqC;AACnCwO,QAAAA,aAAa,CAACyI,UAAd,GAA2BrX,KAA3B;AACD;;AAED,UAAI,CAACoX,OAAL,EAAc;AACZ;AACD;;AAED,UAAME,YAAY,GAAGF,OAAO,CAAC/B,KAA7B;;AACA,UAAI,CAAC8B,OAAO,CAAC1W,CAAD,CAAP,CAAWkE,SAAX,CAAqBE,QAArB,CAA8B6L,iBAA9B,CAAL,EAAqD;AACnD;AACD;;AAED,UAAI1Q,KAAK,KAAMA,KAAK,CAACI,IAAN,KAAe,OAAf,IACX,kBAAkB9E,IAAlB,CAAuB0E,KAAK,CAACQ,MAAN,CAAa0N,OAApC,CADU,IAETlO,KAAK,CAACI,IAAN,KAAe,OAAf,IAA0BJ,KAAK,CAAC/C,GAAN,KAAcqW,OAFpC,CAAL,IAGAgE,YAAY,CAACzS,QAAb,CAAsB7E,KAAK,CAACQ,MAA5B,CAHJ,EAGyC;AACvC;AACD;;AAED,UAAM0V,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB4N,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;AACA,UAAIsH,SAAS,CAAC5X,gBAAd,EAAgC;AAC9B;AACD,OA9BiD;AAiClD;;;AACA,UAAI,kBAAkB9F,QAAQ,CAACyD,eAA/B,EAAgD;AAAA;;AAC9C,qBAAGwL,MAAH,cAAajP,QAAQ,CAACmE,IAAT,CAAcgL,QAA3B,EACG3M,OADH,CACW,UAAAqW,IAAI;AAAA,iBAAInR,YAAY,CAACC,GAAb,CAAiBkR,IAAjB,EAAuB,WAAvB,EAAoC,IAApC,EAA0C/U,IAAI,EAA9C,CAAJ;AAAA,SADf;AAED;;AAED6a,MAAAA,OAAO,CAAC1W,CAAD,CAAP,CAAWmF,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;;AAEA,UAAIwR,OAAO,CAAChC,OAAZ,EAAqB;AACnBgC,QAAAA,OAAO,CAAChC,OAAR,CAAgBe,OAAhB;AACD;;AAEDmB,MAAAA,YAAY,CAAC3S,SAAb,CAAuBC,MAAvB,CAA8B8L,iBAA9B;AACAyG,MAAAA,OAAO,CAAC1W,CAAD,CAAP,CAAWkE,SAAX,CAAqBC,MAArB,CAA4B8L,iBAA5B;AACAxQ,MAAAA,YAAY,CAACuC,OAAb,CAAqB4N,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;AACD;AACF;;WAEMgH,uBAAP,8BAA4Bjd,OAA5B,EAAqC;AACnC,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAACgD,UAAlD;AACD;;WAEM4b,wBAAP,+BAA6BvX,KAA7B,EAAoC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAI,kBAAkB1E,IAAlB,CAAuB0E,KAAK,CAACQ,MAAN,CAAa0N,OAApC,IACFlO,KAAK,CAAC/C,GAAN,KAAcoW,SAAd,IAA4BrT,KAAK,CAAC/C,GAAN,KAAcmW,UAAd,KAC1BpT,KAAK,CAAC/C,GAAN,KAAcuW,cAAd,IAAgCxT,KAAK,CAAC/C,GAAN,KAAcsW,YAA/C,IACCvT,KAAK,CAACQ,MAAN,CAAakE,OAAb,CAAqB4P,aAArB,CAF0B,CAD1B,GAIF,CAACZ,cAAc,CAACpY,IAAf,CAAoB0E,KAAK,CAAC/C,GAA1B,CAJH,EAImC;AACjC;AACD;;AAED+C,IAAAA,KAAK,CAAC3B,cAAN;AACA2B,IAAAA,KAAK,CAACsW,eAAN;;AAEA,QAAI,KAAKb,QAAL,IAAiB,KAAK9Q,SAAL,CAAeE,QAAf,CAAwBiP,mBAAxB,CAArB,EAAmE;AACjE;AACD;;AAED,QAAMzD,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8B,IAA9B,CAAf;AACA,QAAMF,QAAQ,GAAG,KAAK/Q,SAAL,CAAeE,QAAf,CAAwB6L,iBAAxB,CAAjB;;AAEA,QAAI1Q,KAAK,CAAC/C,GAAN,KAAcmW,UAAlB,EAA8B;AAC5B,UAAMvN,MAAM,GAAG,KAAK9G,OAAL,CAAa0G,sBAAb,IAAqC,IAArC,GAA4C+B,cAAc,CAACW,IAAf,CAAoB,IAApB,EAA0B1C,sBAA1B,EAAgD,CAAhD,CAA3D;AACAI,MAAAA,MAAM,CAACoQ,KAAP;AACAd,MAAAA,QAAQ,CAACQ,UAAT;AACA;AACD;;AAED,QAAI,CAACD,QAAD,IAAa1V,KAAK,CAAC/C,GAAN,KAAcoW,SAA/B,EAA0C;AACxC8B,MAAAA,QAAQ,CAACQ,UAAT;AACA;AACD;;AAED,QAAM6B,KAAK,GAAGhQ,cAAc,CAAC7J,IAAf,CAAoB6W,sBAApB,EAA4CnE,MAA5C,EACXzI,MADW,CACJnM,SADI,CAAd;;AAGA,QAAI,CAAC+b,KAAK,CAAC9W,MAAX,EAAmB;AACjB;AACD;;AAED,QAAIuM,KAAK,GAAGuK,KAAK,CAAChW,OAAN,CAAcxB,KAAK,CAACQ,MAApB,CAAZ;;AAEA,QAAIR,KAAK,CAAC/C,GAAN,KAAcsW,YAAd,IAA8BtG,KAAK,GAAG,CAA1C,EAA6C;AAAE;AAC7CA,MAAAA,KAAK;AACN;;AAED,QAAIjN,KAAK,CAAC/C,GAAN,KAAcuW,cAAd,IAAgCvG,KAAK,GAAGuK,KAAK,CAAC9W,MAAN,GAAe,CAA3D,EAA8D;AAAE;AAC9DuM,MAAAA,KAAK;AACN,KArDiC;;;AAwDlCA,IAAAA,KAAK,GAAGA,KAAK,KAAK,CAAC,CAAX,GAAe,CAAf,GAAmBA,KAA3B;AAEAuK,IAAAA,KAAK,CAACvK,KAAD,CAAL,CAAagJ,KAAb;AACD;;WAEM7Q,cAAP,qBAAmBzM,OAAnB,EAA4B;AAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;AACD;;;;wBApYoB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOsF,SAAP;AACD;;;wBAEwB;AACvB,aAAOO,aAAP;AACD;;;;;AA6XH;;;;;;;AAMAjJ,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0Bob,sBAA1B,EAAkDnO,sBAAlD,EAAwE0P,QAAQ,CAACoC,qBAAjF;AACArX,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0Bob,sBAA1B,EAAkDU,aAAlD,EAAiEa,QAAQ,CAACoC,qBAA1E;AACArX,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgDsR,QAAQ,CAACQ,UAAzD;AACAzV,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0Bqb,oBAA1B,EAAgDsB,QAAQ,CAACQ,UAAzD;AACAzV,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUzF,KAAV,EAAiB;AACrFA,EAAAA,KAAK,CAAC3B,cAAN;AACA2B,EAAAA,KAAK,CAACsW,eAAN;AACAnB,EAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B,EAAiC,QAAjC;AACD,CAJD;AAKAhX,YAAY,CACT+B,EADH,CACMzJ,QADN,EACgBqL,sBADhB,EACsCwQ,mBADtC,EAC2D,UAAApW,CAAC;AAAA,SAAIA,CAAC,CAACqY,eAAF,EAAJ;AAAA,CAD5D;AAGA,IAAMtX,GAAC,GAAGvC,SAAS,EAAnB;AAEA;;;;;;;AAMA;;AACA,IAAIuC,GAAJ,EAAO;AACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;AACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAa8R,QAAQ,CAACnQ,eAAtB;AACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyB6P,QAAzB;;AACAnW,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;AACA,WAAO8P,QAAQ,CAACnQ,eAAhB;AACD,GAHD;AAID;;ACngBD;;;;;;AAMA,IAAM3B,MAAI,GAAG,OAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,UAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AACA,IAAM2P,YAAU,GAAG,QAAnB;AAEA,IAAMxK,SAAO,GAAG;AACd6O,EAAAA,QAAQ,EAAE,IADI;AAEd3O,EAAAA,QAAQ,EAAE,IAFI;AAGdmN,EAAAA,KAAK,EAAE,IAHO;AAIdpE,EAAAA,IAAI,EAAE;AAJQ,CAAhB;AAOA,IAAM1I,aAAW,GAAG;AAClBsO,EAAAA,QAAQ,EAAE,kBADQ;AAElB3O,EAAAA,QAAQ,EAAE,SAFQ;AAGlBmN,EAAAA,KAAK,EAAE,SAHW;AAIlBpE,EAAAA,IAAI,EAAE;AAJY,CAApB;AAOA,IAAMrB,YAAU,YAAUhN,WAA1B;AACA,IAAMkU,oBAAoB,qBAAmBlU,WAA7C;AACA,IAAMiN,cAAY,cAAYjN,WAA9B;AACA,IAAM8M,YAAU,YAAU9M,WAA1B;AACA,IAAM+M,aAAW,aAAW/M,WAA5B;AACA,IAAMmU,aAAa,eAAanU,WAAhC;AACA,IAAMoU,YAAY,cAAYpU,WAA9B;AACA,IAAMqU,mBAAmB,qBAAmBrU,WAA5C;AACA,IAAMsU,qBAAqB,uBAAqBtU,WAAhD;AACA,IAAMuU,qBAAqB,uBAAqBvU,WAAhD;AACA,IAAMwU,uBAAuB,yBAAuBxU,WAApD;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAMwU,6BAA6B,GAAG,yBAAtC;AACA,IAAMC,mBAAmB,GAAG,gBAA5B;AACA,IAAMC,eAAe,GAAG,YAAxB;AACA,IAAMC,eAAe,GAAG,MAAxB;AACA,IAAM1H,iBAAe,GAAG,MAAxB;AACA,IAAM2H,iBAAiB,GAAG,cAA1B;AAEA,IAAMC,eAAe,GAAG,eAAxB;AACA,IAAMC,mBAAmB,GAAG,aAA5B;AACA,IAAM9S,sBAAoB,GAAG,uBAA7B;AACA,IAAM+S,qBAAqB,GAAG,wBAA9B;AACA,IAAMC,sBAAsB,GAAG,mDAA/B;AACA,IAAMC,uBAAuB,GAAG,aAAhC;AAEA;;;;;;IAMMC;AACJ,iBAAYhgB,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,SAAKmR,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,SAAKsJ,QAAL,GAAgBvL,OAAhB;AACA,SAAKigB,OAAL,GAAepR,cAAc,CAACzJ,OAAf,CAAuBua,eAAvB,EAAwC3f,OAAxC,CAAf;AACA,SAAKkgB,SAAL,GAAiB,IAAjB;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA,SAAKC,kBAAL,GAA0B,KAA1B;AACA,SAAKC,oBAAL,GAA4B,KAA5B;AACA,SAAK9H,gBAAL,GAAwB,KAAxB;AACA,SAAK+H,eAAL,GAAuB,CAAvB;AACA3b,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;AACD;;;;;AAYD;SAEAoC,SAAA,gBAAOiJ,aAAP,EAAsB;AACpB,WAAO,KAAKkK,QAAL,GAAgB,KAAKlH,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUjD,aAAV,CAArC;AACD;;SAEDiD,OAAA,cAAKjD,aAAL,EAAoB;AAAA;;AAClB,QAAI,KAAKkK,QAAL,IAAiB,KAAK5H,gBAA1B,EAA4C;AAC1C;AACD;;AAED,QAAI,KAAKhN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCuT,eAAjC,CAAJ,EAAuD;AACrD,WAAKlH,gBAAL,GAAwB,IAAxB;AACD;;AAED,QAAM2E,SAAS,GAAG3V,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCoM,YAApC,EAAgD;AAChE1B,MAAAA,aAAa,EAAbA;AADgE,KAAhD,CAAlB;;AAIA,QAAI,KAAKkK,QAAL,IAAiBjD,SAAS,CAACvX,gBAA/B,EAAiD;AAC/C;AACD;;AAED,SAAKwa,QAAL,GAAgB,IAAhB;;AAEA,SAAKI,eAAL;;AACA,SAAKC,aAAL;;AAEA,SAAKC,aAAL;;AAEA,SAAKC,eAAL;;AACA,SAAKC,eAAL;;AAEApZ,IAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EACE2T,mBADF,EAEEW,qBAFF,EAGE,UAAAxY,KAAK;AAAA,aAAI,KAAI,CAAC4R,IAAL,CAAU5R,KAAV,CAAJ;AAAA,KAHP;AAMAE,IAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAK2W,OAArB,EAA8BZ,uBAA9B,EAAuD,YAAM;AAC3D9X,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAI,CAACgC,QAAtB,EAAgC6T,qBAAhC,EAAuD,UAAA/X,KAAK,EAAI;AAC9D,YAAIA,KAAK,CAACQ,MAAN,KAAiB,KAAI,CAAC0D,QAA1B,EAAoC;AAClC,UAAA,KAAI,CAAC8U,oBAAL,GAA4B,IAA5B;AACD;AACF,OAJD;AAKD,KAND;;AAQA,SAAKO,aAAL,CAAmB;AAAA,aAAM,KAAI,CAACC,YAAL,CAAkB5K,aAAlB,CAAN;AAAA,KAAnB;AACD;;SAEDgD,OAAA,cAAK5R,KAAL,EAAY;AAAA;;AACV,QAAIA,KAAJ,EAAW;AACTA,MAAAA,KAAK,CAAC3B,cAAN;AACD;;AAED,QAAI,CAAC,KAAKya,QAAN,IAAkB,KAAK5H,gBAA3B,EAA6C;AAC3C;AACD;;AAED,QAAMgF,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCsM,YAApC,CAAlB;;AAEA,QAAI0F,SAAS,CAAC5X,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAKwa,QAAL,GAAgB,KAAhB;;AACA,QAAMW,UAAU,GAAG,KAAKvV,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCuT,eAAjC,CAAnB;;AAEA,QAAIqB,UAAJ,EAAgB;AACd,WAAKvI,gBAAL,GAAwB,IAAxB;AACD;;AAED,SAAKmI,eAAL;;AACA,SAAKC,eAAL;;AAEApZ,IAAAA,YAAY,CAACC,GAAb,CAAiB3H,QAAjB,EAA2Bmf,aAA3B;;AAEA,SAAKzT,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B8L,iBAA/B;;AAEAxQ,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+D,QAAtB,EAAgC2T,mBAAhC;AACA3X,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKyY,OAAtB,EAA+BZ,uBAA/B;;AAEA,QAAIyB,UAAJ,EAAgB;AACd,UAAMngB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK+K,QAAN,CAA3D;AAEAhE,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD,UAAAqI,KAAK;AAAA,eAAI,MAAI,CAAC0Z,UAAL,CAAgB1Z,KAAhB,CAAJ;AAAA,OAArD;AACA/F,MAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgB5K,kBAAhB,CAApB;AACD,KALD,MAKO;AACL,WAAKogB,UAAL;AACD;AACF;;SAEDjV,UAAA,mBAAU;AACR,KAACrL,MAAD,EAAS,KAAK8K,QAAd,EAAwB,KAAK0U,OAA7B,EACG5d,OADH,CACW,UAAA2e,WAAW;AAAA,aAAIzZ,YAAY,CAACC,GAAb,CAAiBwZ,WAAjB,EAA8BnW,WAA9B,CAAJ;AAAA,KADtB;AAGA;;;;;;AAKAtD,IAAAA,YAAY,CAACC,GAAb,CAAiB3H,QAAjB,EAA2Bmf,aAA3B;AAEAra,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;AAEA,SAAKwI,OAAL,GAAe,IAAf;AACA,SAAK7H,QAAL,GAAgB,IAAhB;AACA,SAAK0U,OAAL,GAAe,IAAf;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,kBAAL,GAA0B,IAA1B;AACA,SAAKC,oBAAL,GAA4B,IAA5B;AACA,SAAK9H,gBAAL,GAAwB,IAAxB;AACA,SAAK+H,eAAL,GAAuB,IAAvB;AACD;;SAEDW,eAAA,wBAAe;AACb,SAAKR,aAAL;AACD;;;SAIDpN,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,qCACDgO,SADC,GAEDhO,MAFC,CAAN;AAIAF,IAAAA,eAAe,CAAC2I,MAAD,EAAOzI,MAAP,EAAeuO,aAAf,CAAf;AACA,WAAOvO,MAAP;AACD;;SAED4e,eAAA,sBAAa5K,aAAb,EAA4B;AAAA;;AAC1B,QAAM6K,UAAU,GAAG,KAAKvV,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCuT,eAAjC,CAAnB;;AACA,QAAMyB,SAAS,GAAGrS,cAAc,CAACzJ,OAAf,CAAuBwa,mBAAvB,EAA4C,KAAKK,OAAjD,CAAlB;;AAEA,QAAI,CAAC,KAAK1U,QAAL,CAAcvI,UAAf,IACA,KAAKuI,QAAL,CAAcvI,UAAd,CAAyB3B,QAAzB,KAAsCgO,IAAI,CAACC,YAD/C,EAC6D;AAC3D;AACAzP,MAAAA,QAAQ,CAACmE,IAAT,CAAcmd,WAAd,CAA0B,KAAK5V,QAA/B;AACD;;AAED,SAAKA,QAAL,CAAcxI,KAAd,CAAoBI,OAApB,GAA8B,OAA9B;;AACA,SAAKoI,QAAL,CAAcpF,eAAd,CAA8B,aAA9B;;AACA,SAAKoF,QAAL,CAAc0B,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;AACA,SAAK1B,QAAL,CAAc0B,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;AACA,SAAK1B,QAAL,CAAc4C,SAAd,GAA0B,CAA1B;;AAEA,QAAI+S,SAAJ,EAAe;AACbA,MAAAA,SAAS,CAAC/S,SAAV,GAAsB,CAAtB;AACD;;AAED,QAAI2S,UAAJ,EAAgB;AACdld,MAAAA,MAAM,CAAC,KAAK2H,QAAN,CAAN;AACD;;AAED,SAAKA,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BoJ,iBAA5B;;AAEA,QAAI,KAAK3E,OAAL,CAAakK,KAAjB,EAAwB;AACtB,WAAK8D,aAAL;AACD;;AAED,QAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;AAC/B,UAAI,MAAI,CAACjO,OAAL,CAAakK,KAAjB,EAAwB;AACtB,QAAA,MAAI,CAAC/R,QAAL,CAAc+R,KAAd;AACD;;AAED,MAAA,MAAI,CAAC/E,gBAAL,GAAwB,KAAxB;AACAhR,MAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAACyB,QAA1B,EAAoCqM,aAApC,EAAiD;AAC/C3B,QAAAA,aAAa,EAAbA;AAD+C,OAAjD;AAGD,KATD;;AAWA,QAAI6K,UAAJ,EAAgB;AACd,UAAMngB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKyf,OAAN,CAA3D;AAEA1Y,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAK0W,OAAtB,EAA+BjhB,cAA/B,EAA+CqiB,kBAA/C;AACA/f,MAAAA,oBAAoB,CAAC,KAAK2e,OAAN,EAAetf,kBAAf,CAApB;AACD,KALD,MAKO;AACL0gB,MAAAA,kBAAkB;AACnB;AACF;;SAEDD,gBAAA,yBAAgB;AAAA;;AACd7Z,IAAAA,YAAY,CAACC,GAAb,CAAiB3H,QAAjB,EAA2Bmf,aAA3B,EADc;;AAEdzX,IAAAA,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0Bmf,aAA1B,EAAyC,UAAA3X,KAAK,EAAI;AAChD,UAAIxH,QAAQ,KAAKwH,KAAK,CAACQ,MAAnB,IACA,MAAI,CAAC0D,QAAL,KAAkBlE,KAAK,CAACQ,MADxB,IAEA,CAAC,MAAI,CAAC0D,QAAL,CAAcW,QAAd,CAAuB7E,KAAK,CAACQ,MAA7B,CAFL,EAE2C;AACzC,QAAA,MAAI,CAAC0D,QAAL,CAAc+R,KAAd;AACD;AACF,KAND;AAOD;;SAEDoD,kBAAA,2BAAkB;AAAA;;AAChB,QAAI,KAAKP,QAAT,EAAmB;AACjB5Y,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B4T,qBAA/B,EAAsD,UAAA9X,KAAK,EAAI;AAC7D,YAAI,MAAI,CAAC+L,OAAL,CAAajD,QAAb,IAAyB9I,KAAK,CAAC/C,GAAN,KAAcmW,YAA3C,EAAuD;AACrDpT,UAAAA,KAAK,CAAC3B,cAAN;;AACA,UAAA,MAAI,CAACuT,IAAL;AACD,SAHD,MAGO,IAAI,CAAC,MAAI,CAAC7F,OAAL,CAAajD,QAAd,IAA0B9I,KAAK,CAAC/C,GAAN,KAAcmW,YAA5C,EAAwD;AAC7D,UAAA,MAAI,CAAC6G,0BAAL;AACD;AACF,OAPD;AAQD,KATD,MASO;AACL/Z,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+D,QAAtB,EAAgC4T,qBAAhC;AACD;AACF;;SAEDwB,kBAAA,2BAAkB;AAAA;;AAChB,QAAI,KAAKR,QAAT,EAAmB;AACjB5Y,MAAAA,YAAY,CAAC+B,EAAb,CAAgB7I,MAAhB,EAAwBwe,YAAxB,EAAsC;AAAA,eAAM,MAAI,CAACwB,aAAL,EAAN;AAAA,OAAtC;AACD,KAFD,MAEO;AACLlZ,MAAAA,YAAY,CAACC,GAAb,CAAiB/G,MAAjB,EAAyBwe,YAAzB;AACD;AACF;;SAED8B,aAAA,sBAAa;AAAA;;AACX,SAAKxV,QAAL,CAAcxI,KAAd,CAAoBI,OAApB,GAA8B,MAA9B;;AACA,SAAKoI,QAAL,CAAc0B,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;AACA,SAAK1B,QAAL,CAAcpF,eAAd,CAA8B,YAA9B;;AACA,SAAKoF,QAAL,CAAcpF,eAAd,CAA8B,MAA9B;;AACA,SAAKoS,gBAAL,GAAwB,KAAxB;;AACA,SAAKqI,aAAL,CAAmB,YAAM;AACvB/gB,MAAAA,QAAQ,CAACmE,IAAT,CAAcgI,SAAd,CAAwBC,MAAxB,CAA+BuT,eAA/B;;AACA,MAAA,MAAI,CAAC+B,iBAAL;;AACA,MAAA,MAAI,CAACC,eAAL;;AACAja,MAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAACyB,QAA1B,EAAoCuM,cAApC;AACD,KALD;AAMD;;SAED2J,kBAAA,2BAAkB;AAChB,SAAKvB,SAAL,CAAeld,UAAf,CAA0BoJ,WAA1B,CAAsC,KAAK8T,SAA3C;;AACA,SAAKA,SAAL,GAAiB,IAAjB;AACD;;SAEDU,gBAAA,uBAAcc,QAAd,EAAwB;AAAA;;AACtB,QAAMC,OAAO,GAAG,KAAKpW,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCuT,eAAjC,IACdA,eADc,GAEd,EAFF;;AAIA,QAAI,KAAKU,QAAL,IAAiB,KAAK/M,OAAL,CAAa0L,QAAlC,EAA4C;AAC1C,WAAKoB,SAAL,GAAiBrgB,QAAQ,CAAC4F,aAAT,CAAuB,KAAvB,CAAjB;AACA,WAAKya,SAAL,CAAexR,SAAf,GAA2B6Q,mBAA3B;;AAEA,UAAIoC,OAAJ,EAAa;AACX,aAAKzB,SAAL,CAAelU,SAAf,CAAyB2C,GAAzB,CAA6BgT,OAA7B;AACD;;AAED9hB,MAAAA,QAAQ,CAACmE,IAAT,CAAcmd,WAAd,CAA0B,KAAKjB,SAA/B;AAEA3Y,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B2T,mBAA/B,EAAoD,UAAA7X,KAAK,EAAI;AAC3D,YAAI,MAAI,CAACgZ,oBAAT,EAA+B;AAC7B,UAAA,MAAI,CAACA,oBAAL,GAA4B,KAA5B;AACA;AACD;;AAED,YAAIhZ,KAAK,CAACQ,MAAN,KAAiBR,KAAK,CAACua,aAA3B,EAA0C;AACxC;AACD;;AAED,QAAA,MAAI,CAACN,0BAAL;AACD,OAXD;;AAaA,UAAIK,OAAJ,EAAa;AACX/d,QAAAA,MAAM,CAAC,KAAKsc,SAAN,CAAN;AACD;;AAED,WAAKA,SAAL,CAAelU,SAAf,CAAyB2C,GAAzB,CAA6BoJ,iBAA7B;;AAEA,UAAI,CAAC4J,OAAL,EAAc;AACZD,QAAAA,QAAQ;AACR;AACD;;AAED,UAAMG,0BAA0B,GAAGrhB,gCAAgC,CAAC,KAAK0f,SAAN,CAAnE;AAEA3Y,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAK2W,SAAtB,EAAiClhB,cAAjC,EAAiD0iB,QAAjD;AACApgB,MAAAA,oBAAoB,CAAC,KAAK4e,SAAN,EAAiB2B,0BAAjB,CAApB;AACD,KAtCD,MAsCO,IAAI,CAAC,KAAK1B,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;AAC3C,WAAKA,SAAL,CAAelU,SAAf,CAAyBC,MAAzB,CAAgC8L,iBAAhC;;AAEA,UAAM+J,cAAc,GAAG,SAAjBA,cAAiB,GAAM;AAC3B,QAAA,MAAI,CAACL,eAAL;;AACAC,QAAAA,QAAQ;AACT,OAHD;;AAKA,UAAI,KAAKnW,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCuT,eAAjC,CAAJ,EAAuD;AACrD,YAAMoC,2BAA0B,GAAGrhB,gCAAgC,CAAC,KAAK0f,SAAN,CAAnE;;AACA3Y,QAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAK2W,SAAtB,EAAiClhB,cAAjC,EAAiD8iB,cAAjD;AACAxgB,QAAAA,oBAAoB,CAAC,KAAK4e,SAAN,EAAiB2B,2BAAjB,CAApB;AACD,OAJD,MAIO;AACLC,QAAAA,cAAc;AACf;AACF,KAfM,MAeA;AACLJ,MAAAA,QAAQ;AACT;AACF;;SAEDJ,6BAAA,sCAA6B;AAAA;;AAC3B,QAAI,KAAKlO,OAAL,CAAa0L,QAAb,KAA0B,QAA9B,EAAwC;AACtC,UAAMvB,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCwT,oBAApC,CAAlB;;AACA,UAAIxB,SAAS,CAAC5X,gBAAd,EAAgC;AAC9B;AACD;;AAED,WAAK4F,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4B+Q,iBAA5B;;AACA,UAAMqC,uBAAuB,GAAGvhB,gCAAgC,CAAC,KAAK+K,QAAN,CAAhE;AACAhE,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD,YAAM;AACpD,QAAA,MAAI,CAACuM,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+ByT,iBAA/B;AACD,OAFD;AAGApe,MAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgBwW,uBAAhB,CAApB;;AACA,WAAKxW,QAAL,CAAc+R,KAAd;AACD,KAbD,MAaO;AACL,WAAKrE,IAAL;AACD;AACF;AAGD;AACA;;;SAEAwH,gBAAA,yBAAgB;AACd,QAAMuB,kBAAkB,GACtB,KAAKzW,QAAL,CAAc0W,YAAd,GAA6BpiB,QAAQ,CAACyD,eAAT,CAAyB4e,YADxD;;AAGA,QAAI,CAAC,KAAK9B,kBAAN,IAA4B4B,kBAAhC,EAAoD;AAClD,WAAKzW,QAAL,CAAcxI,KAAd,CAAoBof,WAApB,GAAqC,KAAK7B,eAA1C;AACD;;AAED,QAAI,KAAKF,kBAAL,IAA2B,CAAC4B,kBAAhC,EAAoD;AAClD,WAAKzW,QAAL,CAAcxI,KAAd,CAAoBqf,YAApB,GAAsC,KAAK9B,eAA3C;AACD;AACF;;SAEDiB,oBAAA,6BAAoB;AAClB,SAAKhW,QAAL,CAAcxI,KAAd,CAAoBof,WAApB,GAAkC,EAAlC;AACA,SAAK5W,QAAL,CAAcxI,KAAd,CAAoBqf,YAApB,GAAmC,EAAnC;AACD;;SAED7B,kBAAA,2BAAkB;AAChB,QAAMvS,IAAI,GAAGnO,QAAQ,CAACmE,IAAT,CAAciK,qBAAd,EAAb;AACA,SAAKmS,kBAAL,GAA0B1gB,IAAI,CAAC2iB,KAAL,CAAWrU,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAACsU,KAA5B,IAAqC7hB,MAAM,CAAC8hB,UAAtE;AACA,SAAKjC,eAAL,GAAuB,KAAKkC,kBAAL,EAAvB;AACD;;SAEDhC,gBAAA,yBAAgB;AAAA;;AACd,QAAI,KAAKJ,kBAAT,EAA6B;AAC3B;AACA;AAEA;AACAvR,MAAAA,cAAc,CAAC7J,IAAf,CAAoB8a,sBAApB,EACGzd,OADH,CACW,UAAArC,OAAO,EAAI;AAClB,YAAMyiB,aAAa,GAAGziB,OAAO,CAAC+C,KAAR,CAAcqf,YAApC;AACA,YAAMM,iBAAiB,GAAGjiB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,eAAjC,CAA1B;AACAwN,QAAAA,WAAW,CAACC,gBAAZ,CAA6BzN,OAA7B,EAAsC,eAAtC,EAAuDyiB,aAAvD;AACAziB,QAAAA,OAAO,CAAC+C,KAAR,CAAcqf,YAAd,GAAgCthB,UAAU,CAAC4hB,iBAAD,CAAV,GAAgC,OAAI,CAACpC,eAArE;AACD,OANH,EAL2B;;AAc3BzR,MAAAA,cAAc,CAAC7J,IAAf,CAAoB+a,uBAApB,EACG1d,OADH,CACW,UAAArC,OAAO,EAAI;AAClB,YAAM2iB,YAAY,GAAG3iB,OAAO,CAAC+C,KAAR,CAAc6f,WAAnC;AACA,YAAMC,gBAAgB,GAAGpiB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,cAAjC,CAAzB;AACAwN,QAAAA,WAAW,CAACC,gBAAZ,CAA6BzN,OAA7B,EAAsC,cAAtC,EAAsD2iB,YAAtD;AACA3iB,QAAAA,OAAO,CAAC+C,KAAR,CAAc6f,WAAd,GAA+B9hB,UAAU,CAAC+hB,gBAAD,CAAV,GAA+B,OAAI,CAACvC,eAAnE;AACD,OANH,EAd2B;;AAuB3B,UAAMmC,aAAa,GAAG5iB,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBqf,YAA1C;AACA,UAAMM,iBAAiB,GAAGjiB,MAAM,CAACC,gBAAP,CAAwBb,QAAQ,CAACmE,IAAjC,EAAuC,eAAvC,CAA1B;AAEAwJ,MAAAA,WAAW,CAACC,gBAAZ,CAA6B5N,QAAQ,CAACmE,IAAtC,EAA4C,eAA5C,EAA6Dye,aAA7D;AACA5iB,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBqf,YAApB,GAAsCthB,UAAU,CAAC4hB,iBAAD,CAAV,GAAgC,KAAKpC,eAA3E;AACD;;AAEDzgB,IAAAA,QAAQ,CAACmE,IAAT,CAAcgI,SAAd,CAAwB2C,GAAxB,CAA4B6Q,eAA5B;AACD;;SAEDgC,kBAAA,2BAAkB;AAChB;AACA3S,IAAAA,cAAc,CAAC7J,IAAf,CAAoB8a,sBAApB,EACGzd,OADH,CACW,UAAArC,OAAO,EAAI;AAClB,UAAM8iB,OAAO,GAAGtV,WAAW,CAACM,gBAAZ,CAA6B9N,OAA7B,EAAsC,eAAtC,CAAhB;;AACA,UAAI,OAAO8iB,OAAP,KAAmB,WAAvB,EAAoC;AAClCtV,QAAAA,WAAW,CAACE,mBAAZ,CAAgC1N,OAAhC,EAAyC,eAAzC;AACAA,QAAAA,OAAO,CAAC+C,KAAR,CAAcqf,YAAd,GAA6BU,OAA7B;AACD;AACF,KAPH,EAFgB;;AAYhBjU,IAAAA,cAAc,CAAC7J,IAAf,MAAuB+a,uBAAvB,EACG1d,OADH,CACW,UAAArC,OAAO,EAAI;AAClB,UAAM+iB,MAAM,GAAGvV,WAAW,CAACM,gBAAZ,CAA6B9N,OAA7B,EAAsC,cAAtC,CAAf;;AACA,UAAI,OAAO+iB,MAAP,KAAkB,WAAtB,EAAmC;AACjCvV,QAAAA,WAAW,CAACE,mBAAZ,CAAgC1N,OAAhC,EAAyC,cAAzC;AACAA,QAAAA,OAAO,CAAC+C,KAAR,CAAc6f,WAAd,GAA4BG,MAA5B;AACD;AACF,KAPH,EAZgB;;AAsBhB,QAAMD,OAAO,GAAGtV,WAAW,CAACM,gBAAZ,CAA6BjO,QAAQ,CAACmE,IAAtC,EAA4C,eAA5C,CAAhB;;AACA,QAAI,OAAO8e,OAAP,KAAmB,WAAvB,EAAoC;AAClCjjB,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBqf,YAApB,GAAmC,EAAnC;AACD,KAFD,MAEO;AACL5U,MAAAA,WAAW,CAACE,mBAAZ,CAAgC7N,QAAQ,CAACmE,IAAzC,EAA+C,eAA/C;AACAnE,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBqf,YAApB,GAAmCU,OAAnC;AACD;AACF;;SAEDN,qBAAA,8BAAqB;AAAE;AACrB,QAAMQ,SAAS,GAAGnjB,QAAQ,CAAC4F,aAAT,CAAuB,KAAvB,CAAlB;AACAud,IAAAA,SAAS,CAACtU,SAAV,GAAsB4Q,6BAAtB;AACAzf,IAAAA,QAAQ,CAACmE,IAAT,CAAcmd,WAAd,CAA0B6B,SAA1B;AACA,QAAMC,cAAc,GAAGD,SAAS,CAAC/U,qBAAV,GAAkCiV,KAAlC,GAA0CF,SAAS,CAACG,WAA3E;AACAtjB,IAAAA,QAAQ,CAACmE,IAAT,CAAcoI,WAAd,CAA0B4W,SAA1B;AACA,WAAOC,cAAP;AACD;;;QAIM5W,kBAAP,yBAAuBpK,MAAvB,EAA+BgU,aAA/B,EAA8C;AAC5C,WAAO,KAAK3J,IAAL,CAAU,YAAY;AAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;AACA,UAAMwI,OAAO,oDACRnD,SADQ,GAERzC,WAAW,CAACG,iBAAZ,CAA8B,IAA9B,CAFQ,GAGR,OAAO1L,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHxC,CAAb;;AAMA,UAAI,CAACsC,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIyb,KAAJ,CAAU,IAAV,EAAgB5M,OAAhB,CAAP;AACD;;AAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;AACD;;AAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ,CAAagU,aAAb;AACD,OAND,MAMO,IAAI7C,OAAO,CAAC8F,IAAZ,EAAkB;AACvB3U,QAAAA,IAAI,CAAC2U,IAAL,CAAUjD,aAAV;AACD;AACF,KArBM,CAAP;AAsBD;;QAEMxJ,cAAP,qBAAmBzM,OAAnB,EAA4B;AAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;AACD;;;;wBAxcoB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOsF,SAAP;AACD;;;;;AAqcH;;;;;;;AAMA1I,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUzF,KAAV,EAAiB;AAAA;;AACrF,MAAMQ,MAAM,GAAGtH,sBAAsB,CAAC,IAAD,CAArC;;AAEA,MAAI,KAAKgV,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;AACnDlO,IAAAA,KAAK,CAAC3B,cAAN;AACD;;AAED6B,EAAAA,YAAY,CAACgC,GAAb,CAAiB1B,MAAjB,EAAyB8P,YAAzB,EAAqC,UAAAuF,SAAS,EAAI;AAChD,QAAIA,SAAS,CAACvX,gBAAd,EAAgC;AAC9B;AACA;AACD;;AAED4B,IAAAA,YAAY,CAACgC,GAAb,CAAiB1B,MAAjB,EAAyBiQ,cAAzB,EAAuC,YAAM;AAC3C,UAAIhV,SAAS,CAAC,OAAD,CAAb,EAAqB;AACnB,QAAA,OAAI,CAACwa,KAAL;AACD;AACF,KAJD;AAKD,GAXD;AAaA,MAAI/Y,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa+C,MAAb,EAAqB+C,UAArB,CAAX;;AACA,MAAI,CAACrG,IAAL,EAAW;AACT,QAAMtC,MAAM,qCACPuL,WAAW,CAACG,iBAAZ,CAA8B9F,MAA9B,CADO,GAEP2F,WAAW,CAACG,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;AAKApJ,IAAAA,IAAI,GAAG,IAAIyb,KAAJ,CAAUnY,MAAV,EAAkB5F,MAAlB,CAAP;AACD;;AAEDsC,EAAAA,IAAI,CAAC2U,IAAL,CAAU,IAAV;AACD,CA/BD;AAiCA,IAAM7S,GAAC,GAAGvC,SAAS,EAAnB;AAEA;;;;;;;AAMA;;AACA,IAAIuC,GAAJ,EAAO;AACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;AACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAasV,KAAK,CAAC3T,eAAnB;AACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyBqT,KAAzB;;AACA3Z,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;AACA,WAAOsT,KAAK,CAAC3T,eAAb;AACD,GAHD;AAID;;ACrmBD;;;;;;AAOA,IAAM+W,QAAQ,GAAG,CACf,YADe,EAEf,MAFe,EAGf,MAHe,EAIf,UAJe,EAKf,UALe,EAMf,QANe,EAOf,KAPe,EAQf,YARe,CAAjB;AAWA,IAAMC,sBAAsB,GAAG,gBAA/B;AAEA;;;;;;AAKA,IAAMC,gBAAgB,GAAG,6DAAzB;AAEA;;;;;;AAKA,IAAMC,gBAAgB,GAAG,oIAAzB;;AAEA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,IAAD,EAAOC,oBAAP,EAAgC;AACvD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcrkB,WAAd,EAAjB;;AAEA,MAAImkB,oBAAoB,CAAC7a,OAArB,CAA6B8a,QAA7B,MAA2C,CAAC,CAAhD,EAAmD;AACjD,QAAIP,QAAQ,CAACva,OAAT,CAAiB8a,QAAjB,MAA+B,CAAC,CAApC,EAAuC;AACrC,aAAO3d,OAAO,CAACyd,IAAI,CAACI,SAAL,CAAevkB,KAAf,CAAqBgkB,gBAArB,KAA0CG,IAAI,CAACI,SAAL,CAAevkB,KAAf,CAAqBikB,gBAArB,CAA3C,CAAd;AACD;;AAED,WAAO,IAAP;AACD;;AAED,MAAMO,MAAM,GAAGJ,oBAAoB,CAACzU,MAArB,CAA4B,UAAA8U,SAAS;AAAA,WAAIA,SAAS,YAAYrhB,MAAzB;AAAA,GAArC,CAAf,CAXuD;;AAcvD,OAAK,IAAIoF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG0b,MAAM,CAAC/b,MAA7B,EAAqCD,CAAC,GAAGM,GAAzC,EAA8CN,CAAC,EAA/C,EAAmD;AACjD,QAAI6b,QAAQ,CAACrkB,KAAT,CAAewkB,MAAM,CAAChc,CAAD,CAArB,CAAJ,EAA+B;AAC7B,aAAO,IAAP;AACD;AACF;;AAED,SAAO,KAAP;AACD,CArBD;;AAuBO,IAAMkc,gBAAgB,GAAG;AAC9B;AACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCX,sBAAvC,CAFyB;AAG9BY,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;AAI9BC,EAAAA,IAAI,EAAE,EAJwB;AAK9BC,EAAAA,CAAC,EAAE,EAL2B;AAM9BC,EAAAA,EAAE,EAAE,EAN0B;AAO9BC,EAAAA,GAAG,EAAE,EAPyB;AAQ9BC,EAAAA,IAAI,EAAE,EARwB;AAS9BC,EAAAA,GAAG,EAAE,EATyB;AAU9BC,EAAAA,EAAE,EAAE,EAV0B;AAW9BC,EAAAA,EAAE,EAAE,EAX0B;AAY9BC,EAAAA,EAAE,EAAE,EAZ0B;AAa9BC,EAAAA,EAAE,EAAE,EAb0B;AAc9BC,EAAAA,EAAE,EAAE,EAd0B;AAe9BC,EAAAA,EAAE,EAAE,EAf0B;AAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;AAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;AAkB9Bjd,EAAAA,CAAC,EAAE,EAlB2B;AAmB9Bkd,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;AAoB9BC,EAAAA,EAAE,EAAE,EApB0B;AAqB9BC,EAAAA,EAAE,EAAE,EArB0B;AAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;AAuB9BC,EAAAA,GAAG,EAAE,EAvByB;AAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;AAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;AA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;AA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;AA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;AA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;AA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;AA+B9BC,EAAAA,EAAE,EAAE;AA/B0B,CAAzB;AAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;AAAA;;AAC9D,MAAI,CAACF,UAAU,CAAC/d,MAAhB,EAAwB;AACtB,WAAO+d,UAAP;AACD;;AAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;AAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;AACD;;AAED,MAAMG,SAAS,GAAG,IAAIxlB,MAAM,CAACylB,SAAX,EAAlB;AACA,MAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;AACA,MAAMO,aAAa,GAAGlkB,MAAM,CAACC,IAAP,CAAY2jB,SAAZ,CAAtB;;AACA,MAAMO,QAAQ,GAAG,YAAGxX,MAAH,aAAaqX,eAAe,CAACniB,IAAhB,CAAqBmB,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;AAZ8D,6BAcrD2C,CAdqD,EAc9CM,GAd8C;AAAA;;AAe5D,QAAMme,EAAE,GAAGD,QAAQ,CAACxe,CAAD,CAAnB;AACA,QAAM0e,MAAM,GAAGD,EAAE,CAAC3C,QAAH,CAAYrkB,WAAZ,EAAf;;AAEA,QAAI8mB,aAAa,CAACxd,OAAd,CAAsB2d,MAAtB,MAAkC,CAAC,CAAvC,EAA0C;AACxCD,MAAAA,EAAE,CAACvjB,UAAH,CAAcoJ,WAAd,CAA0Bma,EAA1B;AAEA;AACD;;AAED,QAAME,aAAa,GAAG,aAAG3X,MAAH,cAAayX,EAAE,CAAC3Y,UAAhB,CAAtB;;AACA,QAAM8Y,qBAAqB,GAAG,GAAG5X,MAAH,CAAUiX,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACS,MAAD,CAAT,IAAqB,EAArD,CAA9B;AAEAC,IAAAA,aAAa,CAACpkB,OAAd,CAAsB,UAAAohB,IAAI,EAAI;AAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOiD,qBAAP,CAArB,EAAoD;AAClDH,QAAAA,EAAE,CAACpgB,eAAH,CAAmBsd,IAAI,CAACG,QAAxB;AACD;AACF,KAJD;AA3B4D;;AAc9D,OAAK,IAAI9b,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGke,QAAQ,CAACve,MAA/B,EAAuCD,CAAC,GAAGM,GAA3C,EAAgDN,CAAC,EAAjD,EAAqD;AAAA,qBAA5CA,CAA4C;;AAAA,6BAOjD;AAWH;;AAED,SAAOqe,eAAe,CAACniB,IAAhB,CAAqB2iB,SAA5B;AACD;;AClGD;;;;;;AAMA,IAAMjc,MAAI,GAAG,SAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,YAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAMgc,YAAY,GAAG,YAArB;AACA,IAAMC,kBAAkB,GAAG,IAAInkB,MAAJ,aAAqBkkB,YAArB,WAAyC,GAAzC,CAA3B;AACA,IAAME,qBAAqB,GAAG,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAA9B;AAEA,IAAMtW,aAAW,GAAG;AAClBuW,EAAAA,SAAS,EAAE,SADO;AAElBC,EAAAA,QAAQ,EAAE,QAFQ;AAGlBC,EAAAA,KAAK,EAAE,2BAHW;AAIlBnd,EAAAA,OAAO,EAAE,QAJS;AAKlBod,EAAAA,KAAK,EAAE,iBALW;AAMlBC,EAAAA,IAAI,EAAE,SANY;AAOlBlnB,EAAAA,QAAQ,EAAE,kBAPQ;AAQlB8d,EAAAA,SAAS,EAAE,mBARO;AASlBhQ,EAAAA,MAAM,EAAE,0BATU;AAUlBsL,EAAAA,SAAS,EAAE,0BAVO;AAWlB+N,EAAAA,iBAAiB,EAAE,gBAXD;AAYlB/K,EAAAA,QAAQ,EAAE,kBAZQ;AAalBgL,EAAAA,QAAQ,EAAE,SAbQ;AAclBrB,EAAAA,UAAU,EAAE,iBAdM;AAelBD,EAAAA,SAAS,EAAE,QAfO;AAgBlBxJ,EAAAA,YAAY,EAAE;AAhBI,CAApB;AAmBA,IAAM+K,aAAa,GAAG;AACpBC,EAAAA,IAAI,EAAE,MADc;AAEpBC,EAAAA,GAAG,EAAE,KAFe;AAGpBC,EAAAA,KAAK,EAAE,OAHa;AAIpBC,EAAAA,MAAM,EAAE,QAJY;AAKpBC,EAAAA,IAAI,EAAE;AALc,CAAtB;AAQA,IAAM1X,SAAO,GAAG;AACd8W,EAAAA,SAAS,EAAE,IADG;AAEdC,EAAAA,QAAQ,EAAE,yCACQ,mCADR,GAEQ,yCAJJ;AAKdld,EAAAA,OAAO,EAAE,aALK;AAMdmd,EAAAA,KAAK,EAAE,EANO;AAOdC,EAAAA,KAAK,EAAE,CAPO;AAQdC,EAAAA,IAAI,EAAE,KARQ;AASdlnB,EAAAA,QAAQ,EAAE,KATI;AAUd8d,EAAAA,SAAS,EAAE,KAVG;AAWdhQ,EAAAA,MAAM,EAAE,CAXM;AAYdsL,EAAAA,SAAS,EAAE,KAZG;AAad+N,EAAAA,iBAAiB,EAAE,MAbL;AAcd/K,EAAAA,QAAQ,EAAE,cAdI;AAedgL,EAAAA,QAAQ,EAAE,IAfI;AAgBdrB,EAAAA,UAAU,EAAE,IAhBE;AAiBdD,EAAAA,SAAS,EAAE/B,gBAjBG;AAkBdzH,EAAAA,YAAY,EAAE;AAlBA,CAAhB;AAqBA,IAAMpb,OAAK,GAAG;AACZymB,EAAAA,IAAI,WAAS/c,WADD;AAEZgd,EAAAA,MAAM,aAAWhd,WAFL;AAGZid,EAAAA,IAAI,WAASjd,WAHD;AAIZkd,EAAAA,KAAK,YAAUld,WAJH;AAKZmd,EAAAA,QAAQ,eAAand,WALT;AAMZod,EAAAA,KAAK,YAAUpd,WANH;AAOZqd,EAAAA,OAAO,cAAYrd,WAPP;AAQZsd,EAAAA,QAAQ,eAAatd,WART;AASZud,EAAAA,UAAU,iBAAevd,WATb;AAUZwd,EAAAA,UAAU,iBAAexd;AAVb,CAAd;AAaA,IAAM4U,iBAAe,GAAG,MAAxB;AACA,IAAM6I,gBAAgB,GAAG,OAAzB;AACA,IAAMvQ,iBAAe,GAAG,MAAxB;AAEA,IAAMwQ,gBAAgB,GAAG,MAAzB;AACA,IAAMC,eAAe,GAAG,KAAxB;AAEA,IAAMC,sBAAsB,GAAG,gBAA/B;AAEA,IAAMC,aAAa,GAAG,OAAtB;AACA,IAAMC,aAAa,GAAG,OAAtB;AACA,IAAMC,aAAa,GAAG,OAAtB;AACA,IAAMC,cAAc,GAAG,QAAvB;AAEA;;;;;;IAMMC;AACJ,mBAAY9oB,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,QAAI,OAAOkb,MAAP,KAAkB,WAAtB,EAAmC;AACjC,YAAM,IAAI9F,SAAJ,CAAc,iEAAd,CAAN;AACD,KAH0B;;;AAM3B,SAAK0R,UAAL,GAAkB,IAAlB;AACA,SAAKC,QAAL,GAAgB,CAAhB;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA,SAAKC,cAAL,GAAsB,EAAtB;AACA,SAAKzM,OAAL,GAAe,IAAf,CAV2B;;AAa3B,SAAKzc,OAAL,GAAeA,OAAf;AACA,SAAKiC,MAAL,GAAc,KAAKoR,UAAL,CAAgBpR,MAAhB,CAAd;AACA,SAAKknB,GAAL,GAAW,IAAX;;AAEA,SAAKC,aAAL;;AACAzkB,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB,KAAK4d,WAAL,CAAiBhT,QAAvC,EAAiD,IAAjD;AACD;;;;;AAgCD;SAEAye,SAAA,kBAAS;AACP,SAAKN,UAAL,GAAkB,IAAlB;AACD;;SAEDO,UAAA,mBAAU;AACR,SAAKP,UAAL,GAAkB,KAAlB;AACD;;SAEDQ,gBAAA,yBAAgB;AACd,SAAKR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;AACD;;SAED/b,SAAA,gBAAO3F,KAAP,EAAc;AACZ,QAAI,CAAC,KAAK0hB,UAAV,EAAsB;AACpB;AACD;;AAED,QAAI1hB,KAAJ,EAAW;AACT,UAAMmiB,OAAO,GAAG,KAAK5L,WAAL,CAAiBhT,QAAjC;AACA,UAAI6T,OAAO,GAAG9Z,IAAI,CAACG,OAAL,CAAauC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,CAAd;;AAEA,UAAI,CAAC/K,OAAL,EAAc;AACZA,QAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRvW,KAAK,CAACQ,MADE,EAER,KAAK4hB,kBAAL,EAFQ,CAAV;AAIA9kB,QAAAA,IAAI,CAACC,OAAL,CAAayC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,EAAoC/K,OAApC;AACD;;AAEDA,MAAAA,OAAO,CAACyK,cAAR,CAAuBQ,KAAvB,GAA+B,CAACjL,OAAO,CAACyK,cAAR,CAAuBQ,KAAvD;;AAEA,UAAIjL,OAAO,CAACkL,oBAAR,EAAJ,EAAoC;AAClClL,QAAAA,OAAO,CAACmL,MAAR,CAAe,IAAf,EAAqBnL,OAArB;AACD,OAFD,MAEO;AACLA,QAAAA,OAAO,CAACoL,MAAR,CAAe,IAAf,EAAqBpL,OAArB;AACD;AACF,KAnBD,MAmBO;AACL,UAAI,KAAKqL,aAAL,GAAqB9d,SAArB,CAA+BE,QAA/B,CAAwC6L,iBAAxC,CAAJ,EAA8D;AAC5D,aAAK8R,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;AACA;AACD;;AAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACD;AACF;;SAED9d,UAAA,mBAAU;AACRuJ,IAAAA,YAAY,CAAC,KAAK2T,QAAN,CAAZ;AAEArkB,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK/E,OAArB,EAA8B,KAAK4d,WAAL,CAAiBhT,QAA/C;AAEArD,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKxH,OAAtB,EAA+B,KAAK4d,WAAL,CAAiB/S,SAAhD;AACAtD,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKxH,OAAL,CAAa+L,OAAb,OAAyBuc,gBAAzB,CAAjB,EAA+D,eAA/D,EAAgF,KAAKyB,iBAArF;;AAEA,QAAI,KAAKZ,GAAT,EAAc;AACZ,WAAKA,GAAL,CAASnmB,UAAT,CAAoBoJ,WAApB,CAAgC,KAAK+c,GAArC;AACD;;AAED,SAAKJ,UAAL,GAAkB,IAAlB;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKC,cAAL,GAAsB,IAAtB;;AACA,QAAI,KAAKzM,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAae,OAAb;AACD;;AAED,SAAKf,OAAL,GAAe,IAAf;AACA,SAAKzc,OAAL,GAAe,IAAf;AACA,SAAKiC,MAAL,GAAc,IAAd;AACA,SAAKknB,GAAL,GAAW,IAAX;AACD;;SAEDjQ,OAAA,gBAAO;AAAA;;AACL,QAAI,KAAKlZ,OAAL,CAAa+C,KAAb,CAAmBI,OAAnB,KAA+B,MAAnC,EAA2C;AACzC,YAAM,IAAIP,KAAJ,CAAU,qCAAV,CAAN;AACD;;AAED,QAAI,KAAKonB,aAAL,MAAwB,KAAKjB,UAAjC,EAA6C;AAC3C,UAAM7L,SAAS,GAAG3V,YAAY,CAACuC,OAAb,CAAqB,KAAK9J,OAA1B,EAAmC,KAAK4d,WAAL,CAAiBzc,KAAjB,CAAuB2mB,IAA1D,CAAlB;AACA,UAAMmC,UAAU,GAAG5mB,cAAc,CAAC,KAAKrD,OAAN,CAAjC;AACA,UAAMkqB,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAKjqB,OAAL,CAAamqB,aAAb,CAA2B7mB,eAA3B,CAA2C4I,QAA3C,CAAoD,KAAKlM,OAAzD,CADiB,GAEjBiqB,UAAU,CAAC/d,QAAX,CAAoB,KAAKlM,OAAzB,CAFF;;AAIA,UAAIkd,SAAS,CAACvX,gBAAV,IAA8B,CAACukB,UAAnC,EAA+C;AAC7C;AACD;;AAED,UAAMf,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,UAAMM,KAAK,GAAG5qB,MAAM,CAAC,KAAKoe,WAAL,CAAiBlT,IAAlB,CAApB;AAEAye,MAAAA,GAAG,CAAClc,YAAJ,CAAiB,IAAjB,EAAuBmd,KAAvB;AACA,WAAKpqB,OAAL,CAAaiN,YAAb,CAA0B,kBAA1B,EAA8Cmd,KAA9C;AAEA,WAAKC,UAAL;;AAEA,UAAI,KAAKpoB,MAAL,CAAY8kB,SAAhB,EAA2B;AACzBoC,QAAAA,GAAG,CAACnd,SAAJ,CAAc2C,GAAd,CAAkB8Q,iBAAlB;AACD;;AAED,UAAM1B,SAAS,GAAG,OAAO,KAAK9b,MAAL,CAAY8b,SAAnB,KAAiC,UAAjC,GAChB,KAAK9b,MAAL,CAAY8b,SAAZ,CAAsB1e,IAAtB,CAA2B,IAA3B,EAAiC8pB,GAAjC,EAAsC,KAAKnpB,OAA3C,CADgB,GAEhB,KAAKiC,MAAL,CAAY8b,SAFd;;AAIA,UAAMuM,UAAU,GAAG,KAAKC,cAAL,CAAoBxM,SAApB,CAAnB;;AACA,WAAKyM,mBAAL,CAAyBF,UAAzB;;AAEA,UAAMjR,SAAS,GAAG,KAAKoR,aAAL,EAAlB;;AACA9lB,MAAAA,IAAI,CAACC,OAAL,CAAaukB,GAAb,EAAkB,KAAKvL,WAAL,CAAiBhT,QAAnC,EAA6C,IAA7C;;AAEA,UAAI,CAAC,KAAK5K,OAAL,CAAamqB,aAAb,CAA2B7mB,eAA3B,CAA2C4I,QAA3C,CAAoD,KAAKid,GAAzD,CAAL,EAAoE;AAClE9P,QAAAA,SAAS,CAAC8H,WAAV,CAAsBgI,GAAtB;AACD;;AAED5hB,MAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAK9J,OAA1B,EAAmC,KAAK4d,WAAL,CAAiBzc,KAAjB,CAAuB6mB,QAA1D;AAEA,WAAKvL,OAAL,GAAe,IAAIU,MAAJ,CAAW,KAAKnd,OAAhB,EAAyBmpB,GAAzB,EAA8B,KAAK9L,gBAAL,CAAsBiN,UAAtB,CAA9B,CAAf;AAEAnB,MAAAA,GAAG,CAACnd,SAAJ,CAAc2C,GAAd,CAAkBoJ,iBAAlB,EAzC2C;AA4C3C;AACA;AACA;;AACA,UAAI,kBAAkBlY,QAAQ,CAACyD,eAA/B,EAAgD;AAAA;;AAC9C,oBAAGwL,MAAH,aAAajP,QAAQ,CAACmE,IAAT,CAAcgL,QAA3B,EAAqC3M,OAArC,CAA6C,UAAArC,OAAO,EAAI;AACtDuH,UAAAA,YAAY,CAAC+B,EAAb,CAAgBtJ,OAAhB,EAAyB,WAAzB,EAAsC2D,IAAI,EAA1C;AACD,SAFD;AAGD;;AAED,UAAMkW,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,YAAI,KAAI,CAAC5X,MAAL,CAAY8kB,SAAhB,EAA2B;AACzB,UAAA,KAAI,CAAC2D,cAAL;AACD;;AAED,YAAMC,cAAc,GAAG,KAAI,CAAC1B,WAA5B;AACA,QAAA,KAAI,CAACA,WAAL,GAAmB,IAAnB;AAEA1hB,QAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAI,CAAC9J,OAA1B,EAAmC,KAAI,CAAC4d,WAAL,CAAiBzc,KAAjB,CAAuB4mB,KAA1D;;AAEA,YAAI4C,cAAc,KAAKnC,eAAvB,EAAwC;AACtC,UAAA,KAAI,CAACqB,MAAL,CAAY,IAAZ,EAAkB,KAAlB;AACD;AACF,OAbD;;AAeA,UAAI,KAAKV,GAAL,CAASnd,SAAT,CAAmBE,QAAnB,CAA4BuT,iBAA5B,CAAJ,EAAkD;AAChD,YAAM9e,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK2oB,GAAN,CAA3D;AACA5hB,QAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAK4f,GAAtB,EAA2BnqB,cAA3B,EAA2C6a,QAA3C;AACAvY,QAAAA,oBAAoB,CAAC,KAAK6nB,GAAN,EAAWxoB,kBAAX,CAApB;AACD,OAJD,MAIO;AACLkZ,QAAAA,QAAQ;AACT;AACF;AACF;;SAEDZ,OAAA,gBAAO;AAAA;;AACL,QAAMkQ,GAAG,GAAG,KAAKW,aAAL,EAAZ;;AACA,QAAMjQ,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,UAAI,MAAI,CAACoP,WAAL,KAAqBV,gBAArB,IAAyCY,GAAG,CAACnmB,UAAjD,EAA6D;AAC3DmmB,QAAAA,GAAG,CAACnmB,UAAJ,CAAeoJ,WAAf,CAA2B+c,GAA3B;AACD;;AAED,MAAA,MAAI,CAACyB,cAAL;;AACA,MAAA,MAAI,CAAC5qB,OAAL,CAAamG,eAAb,CAA6B,kBAA7B;;AACAoB,MAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAAC9J,OAA1B,EAAmC,MAAI,CAAC4d,WAAL,CAAiBzc,KAAjB,CAAuB0mB,MAA1D;;AACA,MAAA,MAAI,CAACpL,OAAL,CAAae,OAAb;AACD,KATD;;AAWA,QAAMD,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB,KAAK9J,OAA1B,EAAmC,KAAK4d,WAAL,CAAiBzc,KAAjB,CAAuBymB,IAA1D,CAAlB;;AACA,QAAIrK,SAAS,CAAC5X,gBAAd,EAAgC;AAC9B;AACD;;AAEDwjB,IAAAA,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqB8L,iBAArB,EAlBK;AAqBL;;AACA,QAAI,kBAAkBlY,QAAQ,CAACyD,eAA/B,EAAgD;AAAA;;AAC9C,mBAAGwL,MAAH,cAAajP,QAAQ,CAACmE,IAAT,CAAcgL,QAA3B,EACG3M,OADH,CACW,UAAArC,OAAO;AAAA,eAAIuH,YAAY,CAACC,GAAb,CAAiBxH,OAAjB,EAA0B,WAA1B,EAAuC2D,IAAvC,CAAJ;AAAA,OADlB;AAED;;AAED,SAAKulB,cAAL,CAAoBN,aAApB,IAAqC,KAArC;AACA,SAAKM,cAAL,CAAoBP,aAApB,IAAqC,KAArC;AACA,SAAKO,cAAL,CAAoBR,aAApB,IAAqC,KAArC;;AAEA,QAAI,KAAKS,GAAL,CAASnd,SAAT,CAAmBE,QAAnB,CAA4BuT,iBAA5B,CAAJ,EAAkD;AAChD,UAAM9e,kBAAkB,GAAGH,gCAAgC,CAAC2oB,GAAD,CAA3D;AAEA5hB,MAAAA,YAAY,CAACgC,GAAb,CAAiB4f,GAAjB,EAAsBnqB,cAAtB,EAAsC6a,QAAtC;AACAvY,MAAAA,oBAAoB,CAAC6nB,GAAD,EAAMxoB,kBAAN,CAApB;AACD,KALD,MAKO;AACLkZ,MAAAA,QAAQ;AACT;;AAED,SAAKoP,WAAL,GAAmB,EAAnB;AACD;;SAEDxL,SAAA,kBAAS;AACP,QAAI,KAAKhB,OAAL,KAAiB,IAArB,EAA2B;AACzB,WAAKA,OAAL,CAAaiB,cAAb;AACD;AACF;;;SAIDsM,gBAAA,yBAAgB;AACd,WAAOhkB,OAAO,CAAC,KAAK6kB,QAAL,EAAD,CAAd;AACD;;SAEDf,gBAAA,yBAAgB;AACd,QAAI,KAAKX,GAAT,EAAc;AACZ,aAAO,KAAKA,GAAZ;AACD;;AAED,QAAMnpB,OAAO,GAAGH,QAAQ,CAAC4F,aAAT,CAAuB,KAAvB,CAAhB;AACAzF,IAAAA,OAAO,CAAC2mB,SAAR,GAAoB,KAAK1kB,MAAL,CAAY+kB,QAAhC;AAEA,SAAKmC,GAAL,GAAWnpB,OAAO,CAACgP,QAAR,CAAiB,CAAjB,CAAX;AACA,WAAO,KAAKma,GAAZ;AACD;;SAEDkB,aAAA,sBAAa;AACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,SAAKgB,iBAAL,CAAuBjc,cAAc,CAACzJ,OAAf,CAAuBqjB,sBAAvB,EAA+CU,GAA/C,CAAvB,EAA4E,KAAK0B,QAAL,EAA5E;AACA1B,IAAAA,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqBwT,iBAArB,EAAsC1H,iBAAtC;AACD;;SAED+S,oBAAA,2BAAkB9qB,OAAlB,EAA2B+qB,OAA3B,EAAoC;AAClC,QAAI/qB,OAAO,KAAK,IAAhB,EAAsB;AACpB;AACD;;AAED,QAAI,OAAO+qB,OAAP,KAAmB,QAAnB,IAA+B3pB,SAAS,CAAC2pB,OAAD,CAA5C,EAAuD;AACrD,UAAIA,OAAO,CAAC5Q,MAAZ,EAAoB;AAClB4Q,QAAAA,OAAO,GAAGA,OAAO,CAAC,CAAD,CAAjB;AACD,OAHoD;;;AAMrD,UAAI,KAAK9oB,MAAL,CAAYklB,IAAhB,EAAsB;AACpB,YAAI4D,OAAO,CAAC/nB,UAAR,KAAuBhD,OAA3B,EAAoC;AAClCA,UAAAA,OAAO,CAAC2mB,SAAR,GAAoB,EAApB;AACA3mB,UAAAA,OAAO,CAACmhB,WAAR,CAAoB4J,OAApB;AACD;AACF,OALD,MAKO;AACL/qB,QAAAA,OAAO,CAACgrB,WAAR,GAAsBD,OAAO,CAACC,WAA9B;AACD;;AAED;AACD;;AAED,QAAI,KAAK/oB,MAAL,CAAYklB,IAAhB,EAAsB;AACpB,UAAI,KAAKllB,MAAL,CAAYolB,QAAhB,EAA0B;AACxB0D,QAAAA,OAAO,GAAGlF,YAAY,CAACkF,OAAD,EAAU,KAAK9oB,MAAL,CAAY8jB,SAAtB,EAAiC,KAAK9jB,MAAL,CAAY+jB,UAA7C,CAAtB;AACD;;AAEDhmB,MAAAA,OAAO,CAAC2mB,SAAR,GAAoBoE,OAApB;AACD,KAND,MAMO;AACL/qB,MAAAA,OAAO,CAACgrB,WAAR,GAAsBD,OAAtB;AACD;AACF;;SAEDF,WAAA,oBAAW;AACT,QAAI5D,KAAK,GAAG,KAAKjnB,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAZ;;AAEA,QAAI,CAAC+mB,KAAL,EAAY;AACVA,MAAAA,KAAK,GAAG,OAAO,KAAKhlB,MAAL,CAAYglB,KAAnB,KAA6B,UAA7B,GACN,KAAKhlB,MAAL,CAAYglB,KAAZ,CAAkB5nB,IAAlB,CAAuB,KAAKW,OAA5B,CADM,GAEN,KAAKiC,MAAL,CAAYglB,KAFd;AAGD;;AAED,WAAOA,KAAP;AACD;;;SAID5J,mBAAA,0BAAiBiN,UAAjB,EAA6B;AAAA;;AAC3B,QAAMW,eAAe,GAAG;AACtBlN,MAAAA,SAAS,EAAEuM,UADW;AAEtBpM,MAAAA,SAAS,EAAE;AACTnQ,QAAAA,MAAM,EAAE,KAAKiQ,UAAL,EADC;AAET5B,QAAAA,IAAI,EAAE;AACJ8O,UAAAA,QAAQ,EAAE,KAAKjpB,MAAL,CAAYmlB;AADlB,SAFG;AAKT+D,QAAAA,KAAK,EAAE;AACLnrB,UAAAA,OAAO,QAAM,KAAK4d,WAAL,CAAiBlT,IAAvB;AADF,SALE;AAQT0T,QAAAA,eAAe,EAAE;AACfC,UAAAA,iBAAiB,EAAE,KAAKpc,MAAL,CAAYoa;AADhB;AARR,OAFW;AActB+O,MAAAA,QAAQ,EAAE,kBAAA7mB,IAAI,EAAI;AAChB,YAAIA,IAAI,CAAC8mB,iBAAL,KAA2B9mB,IAAI,CAACwZ,SAApC,EAA+C;AAC7C,UAAA,MAAI,CAACuN,4BAAL,CAAkC/mB,IAAlC;AACD;AACF,OAlBqB;AAmBtBgnB,MAAAA,QAAQ,EAAE,kBAAAhnB,IAAI;AAAA,eAAI,MAAI,CAAC+mB,4BAAL,CAAkC/mB,IAAlC,CAAJ;AAAA;AAnBQ,KAAxB;AAsBA,6CACK0mB,eADL,GAEK,KAAKhpB,MAAL,CAAYsa,YAFjB;AAID;;SAEDiO,sBAAA,6BAAoBF,UAApB,EAAgC;AAC9B,SAAKR,aAAL,GAAqB9d,SAArB,CAA+B2C,GAA/B,CAAsCiY,YAAtC,SAAsD0D,UAAtD;AACD;;SAEDtM,aAAA,sBAAa;AAAA;;AACX,QAAMjQ,MAAM,GAAG,EAAf;;AAEA,QAAI,OAAO,KAAK9L,MAAL,CAAY8L,MAAnB,KAA8B,UAAlC,EAA8C;AAC5CA,MAAAA,MAAM,CAAC5G,EAAP,GAAY,UAAA5C,IAAI,EAAI;AAClBA,QAAAA,IAAI,CAAC0Z,OAAL,qCACK1Z,IAAI,CAAC0Z,OADV,GAEK,MAAI,CAAChc,MAAL,CAAY8L,MAAZ,CAAmBxJ,IAAI,CAAC0Z,OAAxB,EAAiC,MAAI,CAACje,OAAtC,KAAkD,EAFvD;AAKA,eAAOuE,IAAP;AACD,OAPD;AAQD,KATD,MASO;AACLwJ,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAK9L,MAAL,CAAY8L,MAA5B;AACD;;AAED,WAAOA,MAAP;AACD;;SAED0c,gBAAA,yBAAgB;AACd,QAAI,KAAKxoB,MAAL,CAAYoX,SAAZ,KAA0B,KAA9B,EAAqC;AACnC,aAAOxZ,QAAQ,CAACmE,IAAhB;AACD;;AAED,QAAI5C,SAAS,CAAC,KAAKa,MAAL,CAAYoX,SAAb,CAAb,EAAsC;AACpC,aAAO,KAAKpX,MAAL,CAAYoX,SAAnB;AACD;;AAED,WAAOxK,cAAc,CAACzJ,OAAf,CAAuB,KAAKnD,MAAL,CAAYoX,SAAnC,CAAP;AACD;;SAEDkR,iBAAA,wBAAexM,SAAf,EAA0B;AACxB,WAAOuJ,aAAa,CAACvJ,SAAS,CAAClb,WAAV,EAAD,CAApB;AACD;;SAEDumB,gBAAA,yBAAgB;AAAA;;AACd,QAAMoC,QAAQ,GAAG,KAAKvpB,MAAL,CAAY6H,OAAZ,CAAoB9I,KAApB,CAA0B,GAA1B,CAAjB;AAEAwqB,IAAAA,QAAQ,CAACnpB,OAAT,CAAiB,UAAAyH,OAAO,EAAI;AAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;AACvBvC,QAAAA,YAAY,CAAC+B,EAAb,CAAgB,MAAI,CAACtJ,OAArB,EACE,MAAI,CAAC4d,WAAL,CAAiBzc,KAAjB,CAAuB8mB,KADzB,EAEE,MAAI,CAAChmB,MAAL,CAAYhC,QAFd,EAGE,UAAAoH,KAAK;AAAA,iBAAI,MAAI,CAAC2F,MAAL,CAAY3F,KAAZ,CAAJ;AAAA,SAHP;AAKD,OAND,MAMO,IAAIyC,OAAO,KAAK+e,cAAhB,EAAgC;AACrC,YAAM4C,OAAO,GAAG3hB,OAAO,KAAK4e,aAAZ,GACd,MAAI,CAAC9K,WAAL,CAAiBzc,KAAjB,CAAuBinB,UADT,GAEd,MAAI,CAACxK,WAAL,CAAiBzc,KAAjB,CAAuB+mB,OAFzB;AAGA,YAAMwD,QAAQ,GAAG5hB,OAAO,KAAK4e,aAAZ,GACf,MAAI,CAAC9K,WAAL,CAAiBzc,KAAjB,CAAuBknB,UADR,GAEf,MAAI,CAACzK,WAAL,CAAiBzc,KAAjB,CAAuBgnB,QAFzB;AAIA5gB,QAAAA,YAAY,CAAC+B,EAAb,CAAgB,MAAI,CAACtJ,OAArB,EACEyrB,OADF,EAEE,MAAI,CAACxpB,MAAL,CAAYhC,QAFd,EAGE,UAAAoH,KAAK;AAAA,iBAAI,MAAI,CAACuiB,MAAL,CAAYviB,KAAZ,CAAJ;AAAA,SAHP;AAKAE,QAAAA,YAAY,CAAC+B,EAAb,CAAgB,MAAI,CAACtJ,OAArB,EACE0rB,QADF,EAEE,MAAI,CAACzpB,MAAL,CAAYhC,QAFd,EAGE,UAAAoH,KAAK;AAAA,iBAAI,MAAI,CAACwiB,MAAL,CAAYxiB,KAAZ,CAAJ;AAAA,SAHP;AAKD;AACF,KA1BD;;AA4BA,SAAK0iB,iBAAL,GAAyB,YAAM;AAC7B,UAAI,MAAI,CAAC/pB,OAAT,EAAkB;AAChB,QAAA,MAAI,CAACiZ,IAAL;AACD;AACF,KAJD;;AAMA1R,IAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKtJ,OAAL,CAAa+L,OAAb,OAAyBuc,gBAAzB,CAAhB,EACE,eADF,EAEE,KAAKyB,iBAFP;;AAKA,QAAI,KAAK9nB,MAAL,CAAYhC,QAAhB,EAA0B;AACxB,WAAKgC,MAAL,qCACK,KAAKA,MADV;AAEE6H,QAAAA,OAAO,EAAE,QAFX;AAGE7J,QAAAA,QAAQ,EAAE;AAHZ;AAKD,KAND,MAMO;AACL,WAAK0rB,SAAL;AACD;AACF;;SAEDA,YAAA,qBAAY;AACV,QAAMC,SAAS,GAAG,OAAO,KAAK5rB,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAzB;;AAEA,QAAI,KAAKF,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KAAsC0rB,SAAS,KAAK,QAAxD,EAAkE;AAChE,WAAK5rB,OAAL,CAAaiN,YAAb,CACE,qBADF,EAEE,KAAKjN,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KAAsC,EAFxC;AAKA,WAAKF,OAAL,CAAaiN,YAAb,CAA0B,OAA1B,EAAmC,EAAnC;AACD;AACF;;SAED2c,SAAA,gBAAOviB,KAAP,EAAcoX,OAAd,EAAuB;AACrB,QAAM+K,OAAO,GAAG,KAAK5L,WAAL,CAAiBhT,QAAjC;AACA6T,IAAAA,OAAO,GAAGA,OAAO,IAAI9Z,IAAI,CAACG,OAAL,CAAauC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,CAArB;;AAEA,QAAI,CAAC/K,OAAL,EAAc;AACZA,MAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRvW,KAAK,CAACQ,MADE,EAER,KAAK4hB,kBAAL,EAFQ,CAAV;AAIA9kB,MAAAA,IAAI,CAACC,OAAL,CAAayC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,EAAoC/K,OAApC;AACD;;AAED,QAAIpX,KAAJ,EAAW;AACToX,MAAAA,OAAO,CAACyK,cAAR,CACE7hB,KAAK,CAACI,IAAN,KAAe,SAAf,GAA2BkhB,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;AAGD;;AAED,QAAIjK,OAAO,CAACqL,aAAR,GAAwB9d,SAAxB,CAAkCE,QAAlC,CAA2C6L,iBAA3C,KACA0G,OAAO,CAACwK,WAAR,KAAwBV,gBAD5B,EAC8C;AAC5C9J,MAAAA,OAAO,CAACwK,WAAR,GAAsBV,gBAAtB;AACA;AACD;;AAEDlT,IAAAA,YAAY,CAACoJ,OAAO,CAACuK,QAAT,CAAZ;AAEAvK,IAAAA,OAAO,CAACwK,WAAR,GAAsBV,gBAAtB;;AAEA,QAAI,CAAC9J,OAAO,CAACxc,MAAR,CAAeilB,KAAhB,IAAyB,CAACzI,OAAO,CAACxc,MAAR,CAAeilB,KAAf,CAAqBhO,IAAnD,EAAyD;AACvDuF,MAAAA,OAAO,CAACvF,IAAR;AACA;AACD;;AAEDuF,IAAAA,OAAO,CAACuK,QAAR,GAAmBlnB,UAAU,CAAC,YAAM;AAClC,UAAI2c,OAAO,CAACwK,WAAR,KAAwBV,gBAA5B,EAA8C;AAC5C9J,QAAAA,OAAO,CAACvF,IAAR;AACD;AACF,KAJ4B,EAI1BuF,OAAO,CAACxc,MAAR,CAAeilB,KAAf,CAAqBhO,IAJK,CAA7B;AAKD;;SAED2Q,SAAA,gBAAOxiB,KAAP,EAAcoX,OAAd,EAAuB;AACrB,QAAM+K,OAAO,GAAG,KAAK5L,WAAL,CAAiBhT,QAAjC;AACA6T,IAAAA,OAAO,GAAGA,OAAO,IAAI9Z,IAAI,CAACG,OAAL,CAAauC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,CAArB;;AAEA,QAAI,CAAC/K,OAAL,EAAc;AACZA,MAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRvW,KAAK,CAACQ,MADE,EAER,KAAK4hB,kBAAL,EAFQ,CAAV;AAIA9kB,MAAAA,IAAI,CAACC,OAAL,CAAayC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,EAAoC/K,OAApC;AACD;;AAED,QAAIpX,KAAJ,EAAW;AACToX,MAAAA,OAAO,CAACyK,cAAR,CACE7hB,KAAK,CAACI,IAAN,KAAe,UAAf,GAA4BkhB,aAA5B,GAA4CD,aAD9C,IAEI,KAFJ;AAGD;;AAED,QAAIjK,OAAO,CAACkL,oBAAR,EAAJ,EAAoC;AAClC;AACD;;AAEDtU,IAAAA,YAAY,CAACoJ,OAAO,CAACuK,QAAT,CAAZ;AAEAvK,IAAAA,OAAO,CAACwK,WAAR,GAAsBT,eAAtB;;AAEA,QAAI,CAAC/J,OAAO,CAACxc,MAAR,CAAeilB,KAAhB,IAAyB,CAACzI,OAAO,CAACxc,MAAR,CAAeilB,KAAf,CAAqBjO,IAAnD,EAAyD;AACvDwF,MAAAA,OAAO,CAACxF,IAAR;AACA;AACD;;AAEDwF,IAAAA,OAAO,CAACuK,QAAR,GAAmBlnB,UAAU,CAAC,YAAM;AAClC,UAAI2c,OAAO,CAACwK,WAAR,KAAwBT,eAA5B,EAA6C;AAC3C/J,QAAAA,OAAO,CAACxF,IAAR;AACD;AACF,KAJ4B,EAI1BwF,OAAO,CAACxc,MAAR,CAAeilB,KAAf,CAAqBjO,IAJK,CAA7B;AAKD;;SAED0Q,uBAAA,gCAAuB;AACrB,SAAK,IAAM7f,OAAX,IAAsB,KAAKof,cAA3B,EAA2C;AACzC,UAAI,KAAKA,cAAL,CAAoBpf,OAApB,CAAJ,EAAkC;AAChC,eAAO,IAAP;AACD;AACF;;AAED,WAAO,KAAP;AACD;;SAEDuJ,aAAA,oBAAWpR,MAAX,EAAmB;AACjB,QAAM4pB,cAAc,GAAGre,WAAW,CAACG,iBAAZ,CAA8B,KAAK3N,OAAnC,CAAvB;AAEAmC,IAAAA,MAAM,CAACC,IAAP,CAAYypB,cAAZ,EACGxpB,OADH,CACW,UAAAypB,QAAQ,EAAI;AACnB,UAAIhF,qBAAqB,CAACje,OAAtB,CAA8BijB,QAA9B,MAA4C,CAAC,CAAjD,EAAoD;AAClD,eAAOD,cAAc,CAACC,QAAD,CAArB;AACD;AACF,KALH;;AAOA,QAAI7pB,MAAM,IAAI,OAAOA,MAAM,CAACoX,SAAd,KAA4B,QAAtC,IAAkDpX,MAAM,CAACoX,SAAP,CAAiBc,MAAvE,EAA+E;AAC7ElY,MAAAA,MAAM,CAACoX,SAAP,GAAmBpX,MAAM,CAACoX,SAAP,CAAiB,CAAjB,CAAnB;AACD;;AAEDpX,IAAAA,MAAM,oDACD,KAAK2b,WAAL,CAAiB3N,OADhB,GAED4b,cAFC,GAGD,OAAO5pB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAH/C,CAAN;;AAMA,QAAI,OAAOA,MAAM,CAACilB,KAAd,KAAwB,QAA5B,EAAsC;AACpCjlB,MAAAA,MAAM,CAACilB,KAAP,GAAe;AACbhO,QAAAA,IAAI,EAAEjX,MAAM,CAACilB,KADA;AAEbjO,QAAAA,IAAI,EAAEhX,MAAM,CAACilB;AAFA,OAAf;AAID;;AAED,QAAI,OAAOjlB,MAAM,CAACglB,KAAd,KAAwB,QAA5B,EAAsC;AACpChlB,MAAAA,MAAM,CAACglB,KAAP,GAAehlB,MAAM,CAACglB,KAAP,CAAa7nB,QAAb,EAAf;AACD;;AAED,QAAI,OAAO6C,MAAM,CAAC8oB,OAAd,KAA0B,QAA9B,EAAwC;AACtC9oB,MAAAA,MAAM,CAAC8oB,OAAP,GAAiB9oB,MAAM,CAAC8oB,OAAP,CAAe3rB,QAAf,EAAjB;AACD;;AAED2C,IAAAA,eAAe,CACb2I,MADa,EAEbzI,MAFa,EAGb,KAAK2b,WAAL,CAAiBpN,WAHJ,CAAf;;AAMA,QAAIvO,MAAM,CAAColB,QAAX,EAAqB;AACnBplB,MAAAA,MAAM,CAAC+kB,QAAP,GAAkBnB,YAAY,CAAC5jB,MAAM,CAAC+kB,QAAR,EAAkB/kB,MAAM,CAAC8jB,SAAzB,EAAoC9jB,MAAM,CAAC+jB,UAA3C,CAA9B;AACD;;AAED,WAAO/jB,MAAP;AACD;;SAEDwnB,qBAAA,8BAAqB;AACnB,QAAMxnB,MAAM,GAAG,EAAf;;AAEA,QAAI,KAAKA,MAAT,EAAiB;AACf,WAAK,IAAMqC,GAAX,IAAkB,KAAKrC,MAAvB,EAA+B;AAC7B,YAAI,KAAK2b,WAAL,CAAiB3N,OAAjB,CAAyB3L,GAAzB,MAAkC,KAAKrC,MAAL,CAAYqC,GAAZ,CAAtC,EAAwD;AACtDrC,UAAAA,MAAM,CAACqC,GAAD,CAAN,GAAc,KAAKrC,MAAL,CAAYqC,GAAZ,CAAd;AACD;AACF;AACF;;AAED,WAAOrC,MAAP;AACD;;SAED2oB,iBAAA,0BAAiB;AACf,QAAMzB,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,QAAMiC,QAAQ,GAAG5C,GAAG,CAACjpB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgCunB,kBAAhC,CAAjB;;AACA,QAAIkF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAChkB,MAAT,GAAkB,CAA3C,EAA8C;AAC5CgkB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;AAAA,eAAIA,KAAK,CAAC7rB,IAAN,EAAJ;AAAA,OAAlB,EACGiC,OADH,CACW,UAAA6pB,MAAM;AAAA,eAAI/C,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqBigB,MAArB,CAAJ;AAAA,OADjB;AAED;AACF;;SAEDZ,+BAAA,sCAA6Ba,UAA7B,EAAyC;AACvC,QAAMC,cAAc,GAAGD,UAAU,CAACtnB,QAAlC;AACA,SAAKskB,GAAL,GAAWiD,cAAc,CAACC,MAA1B;;AACA,SAAKzB,cAAL;;AACA,SAAKJ,mBAAL,CAAyB,KAAKD,cAAL,CAAoB4B,UAAU,CAACpO,SAA/B,CAAzB;AACD;;SAED2M,iBAAA,0BAAiB;AACf,QAAMvB,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,QAAMwC,mBAAmB,GAAG,KAAKrqB,MAAL,CAAY8kB,SAAxC;;AACA,QAAIoC,GAAG,CAACjpB,YAAJ,CAAiB,aAAjB,MAAoC,IAAxC,EAA8C;AAC5C;AACD;;AAEDipB,IAAAA,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqBwT,iBAArB;AACA,SAAKxd,MAAL,CAAY8kB,SAAZ,GAAwB,KAAxB;AACA,SAAK9N,IAAL;AACA,SAAKC,IAAL;AACA,SAAKjX,MAAL,CAAY8kB,SAAZ,GAAwBuF,mBAAxB;AACD;;;UAIMjgB,kBAAP,yBAAuBpK,MAAvB,EAA+B;AAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;AAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;AACA,UAAMwI,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,UAAI,CAACsC,IAAD,IAAS,eAAe5B,IAAf,CAAoBV,MAApB,CAAb,EAA0C;AACxC;AACD;;AAED,UAAI,CAACsC,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIukB,OAAJ,CAAY,IAAZ,EAAkB1V,OAAlB,CAAP;AACD;;AAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;AACD;;AAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ;AACD;AACF,KAnBM,CAAP;AAoBD;;UAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;AAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;AACD;;;;wBAvoBoB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOsF,SAAP;AACD;;;wBAEiB;AAChB,aAAOvF,MAAP;AACD;;;wBAEqB;AACpB,aAAOE,UAAP;AACD;;;wBAEkB;AACjB,aAAOzJ,OAAP;AACD;;;wBAEsB;AACrB,aAAO0J,WAAP;AACD;;;wBAEwB;AACvB,aAAO2F,aAAP;AACD;;;;;;AAgnBH,IAAMnK,GAAC,GAAGvC,SAAS,EAAnB;AAEA;;;;;;;AAMA;;AACA,IAAIuC,GAAJ,EAAO;AACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;AACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAaoe,OAAO,CAACzc,eAArB;AACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyBmc,OAAzB;;AACAziB,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;AACA,WAAOoc,OAAO,CAACzc,eAAf;AACD,GAHD;AAID;;AClyBD;;;;;;AAMA,IAAM3B,MAAI,GAAG,SAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,YAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAMgc,cAAY,GAAG,YAArB;AACA,IAAMC,oBAAkB,GAAG,IAAInkB,MAAJ,aAAqBkkB,cAArB,WAAyC,GAAzC,CAA3B;;AAEA,IAAM3W,SAAO,qCACR6Y,OAAO,CAAC7Y,OADA;AAEX8N,EAAAA,SAAS,EAAE,OAFA;AAGXjU,EAAAA,OAAO,EAAE,OAHE;AAIXihB,EAAAA,OAAO,EAAE,EAJE;AAKX/D,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,kCAFF,GAGE;AARD,EAAb;;AAWA,IAAMxW,aAAW,qCACZsY,OAAO,CAACtY,WADI;AAEfua,EAAAA,OAAO,EAAE;AAFM,EAAjB;;AAKA,IAAM5pB,OAAK,GAAG;AACZymB,EAAAA,IAAI,WAAS/c,WADD;AAEZgd,EAAAA,MAAM,aAAWhd,WAFL;AAGZid,EAAAA,IAAI,WAASjd,WAHD;AAIZkd,EAAAA,KAAK,YAAUld,WAJH;AAKZmd,EAAAA,QAAQ,eAAand,WALT;AAMZod,EAAAA,KAAK,YAAUpd,WANH;AAOZqd,EAAAA,OAAO,cAAYrd,WAPP;AAQZsd,EAAAA,QAAQ,eAAatd,WART;AASZud,EAAAA,UAAU,iBAAevd,WATb;AAUZwd,EAAAA,UAAU,iBAAexd;AAVb,CAAd;AAaA,IAAM4U,iBAAe,GAAG,MAAxB;AACA,IAAM1H,iBAAe,GAAG,MAAxB;AAEA,IAAMwU,cAAc,GAAG,iBAAvB;AACA,IAAMC,gBAAgB,GAAG,eAAzB;AAEA;;;;;;IAMMC;;;;;;;;;AA+BJ;SAEAzC,gBAAA,yBAAgB;AACd,WAAO,KAAKa,QAAL,MAAmB,KAAK6B,WAAL,EAA1B;AACD;;SAEDrC,aAAA,sBAAa;AACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ,CADW;;AAIX,SAAKgB,iBAAL,CAAuBjc,cAAc,CAACzJ,OAAf,CAAuBmnB,cAAvB,EAAuCpD,GAAvC,CAAvB,EAAoE,KAAK0B,QAAL,EAApE;;AACA,QAAIE,OAAO,GAAG,KAAK2B,WAAL,EAAd;;AACA,QAAI,OAAO3B,OAAP,KAAmB,UAAvB,EAAmC;AACjCA,MAAAA,OAAO,GAAGA,OAAO,CAAC1rB,IAAR,CAAa,KAAKW,OAAlB,CAAV;AACD;;AAED,SAAK8qB,iBAAL,CAAuBjc,cAAc,CAACzJ,OAAf,CAAuBonB,gBAAvB,EAAyCrD,GAAzC,CAAvB,EAAsE4B,OAAtE;AAEA5B,IAAAA,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqBwT,iBAArB,EAAsC1H,iBAAtC;AACD;;SAEDyS,sBAAA,6BAAoBF,UAApB,EAAgC;AAC9B,SAAKR,aAAL,GAAqB9d,SAArB,CAA+B2C,GAA/B,CAAsCiY,cAAtC,SAAsD0D,UAAtD;AACD;;;SAIDoC,cAAA,uBAAc;AACZ,WAAO,KAAK1sB,OAAL,CAAaE,YAAb,CAA0B,cAA1B,KACL,KAAK+B,MAAL,CAAY8oB,OADd;AAED;;SAEDH,iBAAA,0BAAiB;AACf,QAAMzB,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,QAAMiC,QAAQ,GAAG5C,GAAG,CAACjpB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgCunB,oBAAhC,CAAjB;;AACA,QAAIkF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAChkB,MAAT,GAAkB,CAA3C,EAA8C;AAC5CgkB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;AAAA,eAAIA,KAAK,CAAC7rB,IAAN,EAAJ;AAAA,OAAlB,EACGiC,OADH,CACW,UAAA6pB,MAAM;AAAA,eAAI/C,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqBigB,MAArB,CAAJ;AAAA,OADjB;AAED;AACF;;;UAIM7f,kBAAP,yBAAuBpK,MAAvB,EAA+B;AAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;AAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;AACA,UAAMwI,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;AAEA,UAAI,CAACsC,IAAD,IAAS,eAAe5B,IAAf,CAAoBV,MAApB,CAAb,EAA0C;AACxC;AACD;;AAED,UAAI,CAACsC,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIkoB,OAAJ,CAAY,IAAZ,EAAkBrZ,OAAlB,CAAP;AACAzO,QAAAA,IAAI,CAACC,OAAL,CAAa,IAAb,EAAmBgG,UAAnB,EAA6BrG,IAA7B;AACD;;AAED,UAAI,OAAOtC,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;AACD;;AAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ;AACD;AACF,KApBM,CAAP;AAqBD;;UAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;AAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;AACD;;;;AAnGD;wBAEqB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOsF,SAAP;AACD;;;wBAEiB;AAChB,aAAOvF,MAAP;AACD;;;wBAEqB;AACpB,aAAOE,UAAP;AACD;;;wBAEkB;AACjB,aAAOzJ,OAAP;AACD;;;wBAEsB;AACrB,aAAO0J,WAAP;AACD;;;wBAEwB;AACvB,aAAO2F,aAAP;AACD;;;;EA7BmBsY;;AAuGtB,IAAMziB,GAAC,GAAGvC,SAAS,EAAnB;AAEA;;;;;;AAKA;;AACA,IAAIuC,GAAJ,EAAO;AACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;AACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAa+hB,OAAO,CAACpgB,eAArB;AACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyB8f,OAAzB;;AACApmB,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;AACA,WAAO+f,OAAO,CAACpgB,eAAf;AACD,GAHD;AAID;;ACtKD;;;;;;AAMA,IAAM3B,MAAI,GAAG,WAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,cAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAMmF,SAAO,GAAG;AACdlC,EAAAA,MAAM,EAAE,EADM;AAEd4e,EAAAA,MAAM,EAAE,MAFM;AAGd9kB,EAAAA,MAAM,EAAE;AAHM,CAAhB;AAMA,IAAM2I,aAAW,GAAG;AAClBzC,EAAAA,MAAM,EAAE,QADU;AAElB4e,EAAAA,MAAM,EAAE,QAFU;AAGlB9kB,EAAAA,MAAM,EAAE;AAHU,CAApB;AAMA,IAAM+kB,cAAc,gBAAc/hB,WAAlC;AACA,IAAMgiB,YAAY,cAAYhiB,WAA9B;AACA,IAAM2G,qBAAmB,YAAU3G,WAAV,GAAsBC,cAA/C;AAEA,IAAMgiB,wBAAwB,GAAG,eAAjC;AACA,IAAMjgB,mBAAiB,GAAG,QAA1B;AAEA,IAAMkgB,iBAAiB,GAAG,qBAA1B;AACA,IAAMC,uBAAuB,GAAG,mBAAhC;AACA,IAAMC,kBAAkB,GAAG,WAA3B;AACA,IAAMC,kBAAkB,GAAG,WAA3B;AACA,IAAMC,mBAAmB,GAAG,kBAA5B;AACA,IAAMC,iBAAiB,GAAG,WAA1B;AACA,IAAMC,wBAAwB,GAAG,kBAAjC;AAEA,IAAMC,aAAa,GAAG,QAAtB;AACA,IAAMC,eAAe,GAAG,UAAxB;AAEA;;;;;;IAMMC;AACJ,qBAAYxtB,OAAZ,EAAqBiC,MAArB,EAA6B;AAAA;;AAC3B,SAAKsJ,QAAL,GAAgBvL,OAAhB;AACA,SAAKytB,cAAL,GAAsBztB,OAAO,CAACuV,OAAR,KAAoB,MAApB,GAA6B9U,MAA7B,GAAsCT,OAA5D;AACA,SAAKoT,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,SAAK4W,SAAL,GAAoB,KAAKzF,OAAL,CAAavL,MAAhB,SAA0BolB,kBAA1B,UACQ,KAAK7Z,OAAL,CAAavL,MADrB,SAC+BslB,mBAD/B,WAEQ,KAAK/Z,OAAL,CAAavL,MAFrB,UAEgCilB,wBAFhC,CAAjB;AAGA,SAAKY,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,aAAL,GAAqB,IAArB;AACA,SAAKC,aAAL,GAAqB,CAArB;AAEAtmB,IAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKmkB,cAArB,EAAqCZ,YAArC,EAAmD,UAAAxlB,KAAK;AAAA,aAAI,KAAI,CAACymB,QAAL,CAAczmB,KAAd,CAAJ;AAAA,KAAxD;AAEA,SAAK0mB,OAAL;;AACA,SAAKD,QAAL;;AAEAnpB,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;AACD;;;;;AAYD;SAEAmjB,UAAA,mBAAU;AAAA;;AACR,QAAMC,UAAU,GAAG,KAAKP,cAAL,KAAwB,KAAKA,cAAL,CAAoBhtB,MAA5C,GACjB6sB,aADiB,GAEjBC,eAFF;AAIA,QAAMU,YAAY,GAAG,KAAK7a,OAAL,CAAauZ,MAAb,KAAwB,MAAxB,GACnBqB,UADmB,GAEnB,KAAK5a,OAAL,CAAauZ,MAFf;AAIA,QAAMuB,UAAU,GAAGD,YAAY,KAAKV,eAAjB,GACjB,KAAKY,aAAL,EADiB,GAEjB,CAFF;AAIA,SAAKT,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AAEA,SAAKE,aAAL,GAAqB,KAAKO,gBAAL,EAArB;AAEA,QAAMC,OAAO,GAAGxf,cAAc,CAAC7J,IAAf,CAAoB,KAAK6T,SAAzB,CAAhB;AAEAwV,IAAAA,OAAO,CACJrC,GADH,CACO,UAAAhsB,OAAO,EAAI;AACd,UAAI6H,MAAJ;AACA,UAAMymB,cAAc,GAAGjuB,sBAAsB,CAACL,OAAD,CAA7C;;AAEA,UAAIsuB,cAAJ,EAAoB;AAClBzmB,QAAAA,MAAM,GAAGgH,cAAc,CAACzJ,OAAf,CAAuBkpB,cAAvB,CAAT;AACD;;AAED,UAAIzmB,MAAJ,EAAY;AACV,YAAM0mB,SAAS,GAAG1mB,MAAM,CAACoG,qBAAP,EAAlB;;AACA,YAAIsgB,SAAS,CAACrL,KAAV,IAAmBqL,SAAS,CAACC,MAAjC,EAAyC;AACvC,iBAAO,CACLhhB,WAAW,CAACygB,YAAD,CAAX,CAA0BpmB,MAA1B,EAAkCqG,GAAlC,GAAwCggB,UADnC,EAELI,cAFK,CAAP;AAID;AACF;;AAED,aAAO,IAAP;AACD,KApBH,EAqBGrf,MArBH,CAqBU,UAAAwf,IAAI;AAAA,aAAIA,IAAJ;AAAA,KArBd,EAsBGC,IAtBH,CAsBQ,UAACzK,CAAD,EAAIE,CAAJ;AAAA,aAAUF,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAC,CAAC,CAAD,CAAlB;AAAA,KAtBR,EAuBG9hB,OAvBH,CAuBW,UAAAosB,IAAI,EAAI;AACf,MAAA,MAAI,CAACf,QAAL,CAAcne,IAAd,CAAmBkf,IAAI,CAAC,CAAD,CAAvB;;AACA,MAAA,MAAI,CAACd,QAAL,CAAcpe,IAAd,CAAmBkf,IAAI,CAAC,CAAD,CAAvB;AACD,KA1BH;AA2BD;;SAED3iB,UAAA,mBAAU;AACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;AACArD,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKimB,cAAtB,EAAsC5iB,WAAtC;AAEA,SAAKU,QAAL,GAAgB,IAAhB;AACA,SAAKkiB,cAAL,GAAsB,IAAtB;AACA,SAAKra,OAAL,GAAe,IAAf;AACA,SAAKyF,SAAL,GAAiB,IAAjB;AACA,SAAK6U,QAAL,GAAgB,IAAhB;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,aAAL,GAAqB,IAArB;AACA,SAAKC,aAAL,GAAqB,IAArB;AACD;;;SAIDxa,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,qCACDgO,SADC,GAED,OAAOhO,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAF/C,CAAN;;AAKA,QAAI,OAAOA,MAAM,CAAC4F,MAAd,KAAyB,QAAzB,IAAqCzG,SAAS,CAACa,MAAM,CAAC4F,MAAR,CAAlD,EAAmE;AAAA,UAC3DzD,EAD2D,GACpDnC,MAAM,CAAC4F,MAD6C,CAC3DzD,EAD2D;;AAEjE,UAAI,CAACA,EAAL,EAAS;AACPA,QAAAA,EAAE,GAAG5E,MAAM,CAACkL,MAAD,CAAX;AACAzI,QAAAA,MAAM,CAAC4F,MAAP,CAAczD,EAAd,GAAmBA,EAAnB;AACD;;AAEDnC,MAAAA,MAAM,CAAC4F,MAAP,SAAoBzD,EAApB;AACD;;AAEDrC,IAAAA,eAAe,CAAC2I,MAAD,EAAOzI,MAAP,EAAeuO,aAAf,CAAf;AAEA,WAAOvO,MAAP;AACD;;SAEDksB,gBAAA,yBAAgB;AACd,WAAO,KAAKV,cAAL,KAAwBhtB,MAAxB,GACL,KAAKgtB,cAAL,CAAoBkB,WADf,GAEL,KAAKlB,cAAL,CAAoBtf,SAFtB;AAGD;;SAEDigB,mBAAA,4BAAmB;AACjB,WAAO,KAAKX,cAAL,CAAoBxL,YAApB,IAAoCviB,IAAI,CAACkvB,GAAL,CACzC/uB,QAAQ,CAACmE,IAAT,CAAcie,YAD2B,EAEzCpiB,QAAQ,CAACyD,eAAT,CAAyB2e,YAFgB,CAA3C;AAID;;SAED4M,mBAAA,4BAAmB;AACjB,WAAO,KAAKpB,cAAL,KAAwBhtB,MAAxB,GACLA,MAAM,CAACquB,WADF,GAEL,KAAKrB,cAAL,CAAoBxf,qBAApB,GAA4CugB,MAF9C;AAGD;;SAEDV,WAAA,oBAAW;AACT,QAAM3f,SAAS,GAAG,KAAKggB,aAAL,KAAuB,KAAK/a,OAAL,CAAarF,MAAtD;;AACA,QAAMkU,YAAY,GAAG,KAAKmM,gBAAL,EAArB;;AACA,QAAMW,SAAS,GAAG,KAAK3b,OAAL,CAAarF,MAAb,GAChBkU,YADgB,GAEhB,KAAK4M,gBAAL,EAFF;;AAIA,QAAI,KAAKhB,aAAL,KAAuB5L,YAA3B,EAAyC;AACvC,WAAK8L,OAAL;AACD;;AAED,QAAI5f,SAAS,IAAI4gB,SAAjB,EAA4B;AAC1B,UAAMlnB,MAAM,GAAG,KAAK8lB,QAAL,CAAc,KAAKA,QAAL,CAAc5lB,MAAd,GAAuB,CAArC,CAAf;;AAEA,UAAI,KAAK6lB,aAAL,KAAuB/lB,MAA3B,EAAmC;AACjC,aAAKmnB,SAAL,CAAennB,MAAf;AACD;;AAED;AACD;;AAED,QAAI,KAAK+lB,aAAL,IAAsBzf,SAAS,GAAG,KAAKuf,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;AAC9E,WAAKE,aAAL,GAAqB,IAArB;;AACA,WAAKqB,MAAL;;AACA;AACD;;AAED,SAAK,IAAInnB,CAAC,GAAG,KAAK4lB,QAAL,CAAc3lB,MAA3B,EAAmCD,CAAC,EAApC,GAAyC;AACvC,UAAMonB,cAAc,GAAG,KAAKtB,aAAL,KAAuB,KAAKD,QAAL,CAAc7lB,CAAd,CAAvB,IACnBqG,SAAS,IAAI,KAAKuf,QAAL,CAAc5lB,CAAd,CADM,KAElB,OAAO,KAAK4lB,QAAL,CAAc5lB,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IACGqG,SAAS,GAAG,KAAKuf,QAAL,CAAc5lB,CAAC,GAAG,CAAlB,CAHG,CAAvB;;AAKA,UAAIonB,cAAJ,EAAoB;AAClB,aAAKF,SAAL,CAAe,KAAKrB,QAAL,CAAc7lB,CAAd,CAAf;AACD;AACF;AACF;;SAEDknB,YAAA,mBAAUnnB,MAAV,EAAkB;AAChB,SAAK+lB,aAAL,GAAqB/lB,MAArB;;AAEA,SAAKonB,MAAL;;AAEA,QAAME,OAAO,GAAG,KAAKtW,SAAL,CAAe7X,KAAf,CAAqB,GAArB,EACbgrB,GADa,CACT,UAAA/rB,QAAQ;AAAA,aAAOA,QAAP,uBAAgC4H,MAAhC,YAA4C5H,QAA5C,gBAA8D4H,MAA9D;AAAA,KADC,CAAhB;;AAGA,QAAMunB,IAAI,GAAGvgB,cAAc,CAACzJ,OAAf,CAAuB+pB,OAAO,CAACE,IAAR,CAAa,GAAb,CAAvB,CAAb;;AAEA,QAAID,IAAI,CAACpjB,SAAL,CAAeE,QAAf,CAAwB4gB,wBAAxB,CAAJ,EAAuD;AACrDje,MAAAA,cAAc,CACXzJ,OADH,CACWioB,wBADX,EACqC+B,IAAI,CAACrjB,OAAL,CAAaqhB,iBAAb,CADrC,EAEGphB,SAFH,CAEa2C,GAFb,CAEiB9B,mBAFjB;AAIAuiB,MAAAA,IAAI,CAACpjB,SAAL,CAAe2C,GAAf,CAAmB9B,mBAAnB;AACD,KAND,MAMO;AACL;AACAuiB,MAAAA,IAAI,CAACpjB,SAAL,CAAe2C,GAAf,CAAmB9B,mBAAnB;AAEAgC,MAAAA,cAAc,CACXM,OADH,CACWigB,IADX,EACiBpC,uBADjB,EAEG3qB,OAFH,CAEW,UAAAitB,SAAS,EAAI;AACpB;AACA;AACAzgB,QAAAA,cAAc,CAACW,IAAf,CAAoB8f,SAApB,EAAkCrC,kBAAlC,UAAyDE,mBAAzD,EACG9qB,OADH,CACW,UAAAosB,IAAI;AAAA,iBAAIA,IAAI,CAACziB,SAAL,CAAe2C,GAAf,CAAmB9B,mBAAnB,CAAJ;AAAA,SADf,EAHoB;;AAOpBgC,QAAAA,cAAc,CAACW,IAAf,CAAoB8f,SAApB,EAA+BpC,kBAA/B,EACG7qB,OADH,CACW,UAAAktB,OAAO,EAAI;AAClB1gB,UAAAA,cAAc,CAACG,QAAf,CAAwBugB,OAAxB,EAAiCtC,kBAAjC,EACG5qB,OADH,CACW,UAAAosB,IAAI;AAAA,mBAAIA,IAAI,CAACziB,SAAL,CAAe2C,GAAf,CAAmB9B,mBAAnB,CAAJ;AAAA,WADf;AAED,SAJH;AAKD,OAdH;AAeD;;AAEDtF,IAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAK2jB,cAA1B,EAA0Cb,cAA1C,EAA0D;AACxD3W,MAAAA,aAAa,EAAEpO;AADyC,KAA1D;AAGD;;SAEDonB,SAAA,kBAAS;AACPpgB,IAAAA,cAAc,CAAC7J,IAAf,CAAoB,KAAK6T,SAAzB,EACG5J,MADH,CACU,UAAAugB,IAAI;AAAA,aAAIA,IAAI,CAACxjB,SAAL,CAAeE,QAAf,CAAwBW,mBAAxB,CAAJ;AAAA,KADd,EAEGxK,OAFH,CAEW,UAAAmtB,IAAI;AAAA,aAAIA,IAAI,CAACxjB,SAAL,CAAeC,MAAf,CAAsBY,mBAAtB,CAAJ;AAAA,KAFf;AAGD;;;YAIMR,kBAAP,yBAAuBpK,MAAvB,EAA+B;AAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;AAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;AACA,UAAMwI,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,UAAI,CAACsC,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIipB,SAAJ,CAAc,IAAd,EAAoBpa,OAApB,CAAP;AACD;;AAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;AACD;;AAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ;AACD;AACF,KAfM,CAAP;AAgBD;;YAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;AAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;AACD;;;;wBAjOoB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOsF,SAAP;AACD;;;;;AA8NH;;;;;;;AAMA1I,YAAY,CAAC+B,EAAb,CAAgB7I,MAAhB,EAAwB+Q,qBAAxB,EAA6C,YAAM;AACjD3C,EAAAA,cAAc,CAAC7J,IAAf,CAAoB+nB,iBAApB,EACG1qB,OADH,CACW,UAAAotB,GAAG;AAAA,WAAI,IAAIjC,SAAJ,CAAciC,GAAd,EAAmBjiB,WAAW,CAACG,iBAAZ,CAA8B8hB,GAA9B,CAAnB,CAAJ;AAAA,GADd;AAED,CAHD;AAKA,IAAMppB,GAAC,GAAGvC,SAAS,EAAnB;AAEA;;;;;;AAKA;;AACA,IAAIuC,GAAJ,EAAO;AACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;AACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAa8iB,SAAS,CAACnhB,eAAvB;AACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyB6gB,SAAzB;;AACAnnB,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;AACA,WAAO8gB,SAAS,CAACnhB,eAAjB;AACD,GAHD;AAID;;ACtUD;;;;;;AAMA,IAAM3B,MAAI,GAAG,KAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,QAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAM+M,YAAU,YAAUhN,WAA1B;AACA,IAAMiN,cAAY,cAAYjN,WAA9B;AACA,IAAM8M,YAAU,YAAU9M,WAA1B;AACA,IAAM+M,aAAW,aAAW/M,WAA5B;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAM4kB,wBAAwB,GAAG,eAAjC;AACA,IAAM7iB,mBAAiB,GAAG,QAA1B;AACA,IAAMsO,qBAAmB,GAAG,UAA5B;AACA,IAAMsE,iBAAe,GAAG,MAAxB;AACA,IAAM1H,iBAAe,GAAG,MAAxB;AAEA,IAAMqV,mBAAiB,GAAG,WAA1B;AACA,IAAMJ,yBAAuB,GAAG,mBAAhC;AACA,IAAMhb,iBAAe,GAAG,SAAxB;AACA,IAAM2d,kBAAkB,GAAG,uBAA3B;AACA,IAAM7iB,sBAAoB,GAAG,iEAA7B;AACA,IAAMugB,0BAAwB,GAAG,kBAAjC;AACA,IAAMuC,8BAA8B,GAAG,iCAAvC;AAEA;;;;;;IAMMC;AACJ,eAAY7vB,OAAZ,EAAqB;AACnB,SAAKuL,QAAL,GAAgBvL,OAAhB;AAEA2E,IAAAA,IAAI,CAACC,OAAL,CAAa,KAAK2G,QAAlB,EAA4BX,UAA5B,EAAsC,IAAtC;AACD;;;;;AAQD;SAEAsO,OAAA,gBAAO;AAAA;;AACL,QAAK,KAAK3N,QAAL,CAAcvI,UAAd,IACH,KAAKuI,QAAL,CAAcvI,UAAd,CAAyB3B,QAAzB,KAAsCgO,IAAI,CAACC,YADxC,IAEH,KAAK/D,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCW,mBAAjC,CAFE,IAGF,KAAKtB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCiP,qBAAjC,CAHF,EAGyD;AACvD;AACD;;AAED,QAAI1L,QAAJ;AACA,QAAM5H,MAAM,GAAGtH,sBAAsB,CAAC,KAAKgL,QAAN,CAArC;;AACA,QAAMukB,WAAW,GAAG,KAAKvkB,QAAL,CAAcQ,OAAd,CAAsBihB,yBAAtB,CAApB;;AAEA,QAAI8C,WAAJ,EAAiB;AACf,UAAMC,YAAY,GAAGD,WAAW,CAAClM,QAAZ,KAAyB,IAAzB,IAAiCkM,WAAW,CAAClM,QAAZ,KAAyB,IAA1D,GAAiE+L,kBAAjE,GAAsF3d,iBAA3G;AACAvC,MAAAA,QAAQ,GAAGZ,cAAc,CAAC7J,IAAf,CAAoB+qB,YAApB,EAAkCD,WAAlC,CAAX;AACArgB,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAAC1H,MAAT,GAAkB,CAAnB,CAAnB;AACD;;AAED,QAAIwV,SAAS,GAAG,IAAhB;;AAEA,QAAI9N,QAAJ,EAAc;AACZ8N,MAAAA,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB2F,QAArB,EAA+BoI,YAA/B,EAA2C;AACrD5B,QAAAA,aAAa,EAAE,KAAK1K;AADiC,OAA3C,CAAZ;AAGD;;AAED,QAAM2R,SAAS,GAAG3V,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCoM,YAApC,EAAgD;AAChE1B,MAAAA,aAAa,EAAExG;AADiD,KAAhD,CAAlB;;AAIA,QAAIyN,SAAS,CAACvX,gBAAV,IACD4X,SAAS,KAAK,IAAd,IAAsBA,SAAS,CAAC5X,gBADnC,EACsD;AACpD;AACD;;AAED,SAAKqpB,SAAL,CACE,KAAKzjB,QADP,EAEEukB,WAFF;;AAKA,QAAMjW,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrBtS,MAAAA,YAAY,CAACuC,OAAb,CAAqB2F,QAArB,EAA+BqI,cAA/B,EAA6C;AAC3C7B,QAAAA,aAAa,EAAE,KAAI,CAAC1K;AADuB,OAA7C;AAGAhE,MAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAI,CAACyB,QAA1B,EAAoCqM,aAApC,EAAiD;AAC/C3B,QAAAA,aAAa,EAAExG;AADgC,OAAjD;AAGD,KAPD;;AASA,QAAI5H,MAAJ,EAAY;AACV,WAAKmnB,SAAL,CAAennB,MAAf,EAAuBA,MAAM,CAAC7E,UAA9B,EAA0C6W,QAA1C;AACD,KAFD,MAEO;AACLA,MAAAA,QAAQ;AACT;AACF;;SAED/N,UAAA,mBAAU;AACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;AACA,SAAKW,QAAL,GAAgB,IAAhB;AACD;;;SAIDyjB,YAAA,mBAAUhvB,OAAV,EAAmBqZ,SAAnB,EAA8BqI,QAA9B,EAAwC;AAAA;;AACtC,QAAMsO,cAAc,GAAG3W,SAAS,KAAKA,SAAS,CAACuK,QAAV,KAAuB,IAAvB,IAA+BvK,SAAS,CAACuK,QAAV,KAAuB,IAA3D,CAAT,GACrB/U,cAAc,CAAC7J,IAAf,CAAoB2qB,kBAApB,EAAwCtW,SAAxC,CADqB,GAErBxK,cAAc,CAACG,QAAf,CAAwBqK,SAAxB,EAAmCrH,iBAAnC,CAFF;AAIA,QAAMie,MAAM,GAAGD,cAAc,CAAC,CAAD,CAA7B;AACA,QAAM/V,eAAe,GAAGyH,QAAQ,IAC7BuO,MAAM,IAAIA,MAAM,CAACjkB,SAAP,CAAiBE,QAAjB,CAA0BuT,iBAA1B,CADb;;AAGA,QAAM5F,QAAQ,GAAG,SAAXA,QAAW;AAAA,aAAM,MAAI,CAACqW,mBAAL,CACrBlwB,OADqB,EAErBiwB,MAFqB,EAGrBvO,QAHqB,CAAN;AAAA,KAAjB;;AAMA,QAAIuO,MAAM,IAAIhW,eAAd,EAA+B;AAC7B,UAAMtZ,kBAAkB,GAAGH,gCAAgC,CAACyvB,MAAD,CAA3D;AACAA,MAAAA,MAAM,CAACjkB,SAAP,CAAiBC,MAAjB,CAAwB8L,iBAAxB;AAEAxQ,MAAAA,YAAY,CAACgC,GAAb,CAAiB0mB,MAAjB,EAAyBjxB,cAAzB,EAAyC6a,QAAzC;AACAvY,MAAAA,oBAAoB,CAAC2uB,MAAD,EAAStvB,kBAAT,CAApB;AACD,KAND,MAMO;AACLkZ,MAAAA,QAAQ;AACT;AACF;;SAEDqW,sBAAA,6BAAoBlwB,OAApB,EAA6BiwB,MAA7B,EAAqCvO,QAArC,EAA+C;AAC7C,QAAIuO,MAAJ,EAAY;AACVA,MAAAA,MAAM,CAACjkB,SAAP,CAAiBC,MAAjB,CAAwBY,mBAAxB;AAEA,UAAMsjB,aAAa,GAAGthB,cAAc,CAACzJ,OAAf,CAAuBwqB,8BAAvB,EAAuDK,MAAM,CAACjtB,UAA9D,CAAtB;;AAEA,UAAImtB,aAAJ,EAAmB;AACjBA,QAAAA,aAAa,CAACnkB,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;AACD;;AAED,UAAIojB,MAAM,CAAC/vB,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;AACzC+vB,QAAAA,MAAM,CAAChjB,YAAP,CAAoB,eAApB,EAAqC,KAArC;AACD;AACF;;AAEDjN,IAAAA,OAAO,CAACgM,SAAR,CAAkB2C,GAAlB,CAAsB9B,mBAAtB;;AACA,QAAI7M,OAAO,CAACE,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;AAC1CF,MAAAA,OAAO,CAACiN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD;;AAEDrJ,IAAAA,MAAM,CAAC5D,OAAD,CAAN;;AAEA,QAAIA,OAAO,CAACgM,SAAR,CAAkBE,QAAlB,CAA2BuT,iBAA3B,CAAJ,EAAiD;AAC/Czf,MAAAA,OAAO,CAACgM,SAAR,CAAkB2C,GAAlB,CAAsBoJ,iBAAtB;AACD;;AAED,QAAI/X,OAAO,CAACgD,UAAR,IAAsBhD,OAAO,CAACgD,UAAR,CAAmBgJ,SAAnB,CAA6BE,QAA7B,CAAsCwjB,wBAAtC,CAA1B,EAA2F;AACzF,UAAMU,eAAe,GAAGpwB,OAAO,CAAC+L,OAAR,CAAgBqhB,mBAAhB,CAAxB;;AAEA,UAAIgD,eAAJ,EAAqB;AACnBvhB,QAAAA,cAAc,CAAC7J,IAAf,CAAoBqoB,0BAApB,EACGhrB,OADH,CACW,UAAAguB,QAAQ;AAAA,iBAAIA,QAAQ,CAACrkB,SAAT,CAAmB2C,GAAnB,CAAuB9B,mBAAvB,CAAJ;AAAA,SADnB;AAED;;AAED7M,MAAAA,OAAO,CAACiN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD;;AAED,QAAIyU,QAAJ,EAAc;AACZA,MAAAA,QAAQ;AACT;AACF;;;MAIMrV,kBAAP,yBAAuBpK,MAAvB,EAA+B;AAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;AAC3B,UAAM/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,KAAgC,IAAIilB,GAAJ,CAAQ,IAAR,CAA7C;;AAEA,UAAI,OAAO5tB,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;AACD;;AAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ;AACD;AACF,KAVM,CAAP;AAWD;;MAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;AAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;AACD;;;;wBA3JoB;AACnB,aAAOD,SAAP;AACD;;;;;AA4JH;;;;;;;AAMApD,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUzF,KAAV,EAAiB;AACrFA,EAAAA,KAAK,CAAC3B,cAAN;AAEA,MAAMnB,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,KAAgC,IAAIilB,GAAJ,CAAQ,IAAR,CAA7C;AACAtrB,EAAAA,IAAI,CAAC2U,IAAL;AACD,CALD;AAOA,IAAM7S,GAAC,GAAGvC,SAAS,EAAnB;AAEA;;;;;;;AAMA;;AACA,IAAIuC,GAAJ,EAAO;AACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;AACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAamlB,GAAG,CAACxjB,eAAjB;AACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyBkjB,GAAzB;;AACAxpB,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;AACA,WAAOmjB,GAAG,CAACxjB,eAAX;AACD,GAHD;AAID;;AC3OD;;;;;;AAMA,IAAM3B,MAAI,GAAG,OAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,UAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AAEA,IAAMsU,qBAAmB,qBAAmBrU,WAA5C;AACA,IAAMgN,YAAU,YAAUhN,WAA1B;AACA,IAAMiN,cAAY,cAAYjN,WAA9B;AACA,IAAM8M,YAAU,YAAU9M,WAA1B;AACA,IAAM+M,aAAW,aAAW/M,WAA5B;AAEA,IAAM4U,iBAAe,GAAG,MAAxB;AACA,IAAM6Q,eAAe,GAAG,MAAxB;AACA,IAAMvY,iBAAe,GAAG,MAAxB;AACA,IAAMwY,kBAAkB,GAAG,SAA3B;AAEA,IAAM/f,aAAW,GAAG;AAClBuW,EAAAA,SAAS,EAAE,SADO;AAElByJ,EAAAA,QAAQ,EAAE,SAFQ;AAGlBtJ,EAAAA,KAAK,EAAE;AAHW,CAApB;AAMA,IAAMjX,SAAO,GAAG;AACd8W,EAAAA,SAAS,EAAE,IADG;AAEdyJ,EAAAA,QAAQ,EAAE,IAFI;AAGdtJ,EAAAA,KAAK,EAAE;AAHO,CAAhB;AAMA,IAAMrH,uBAAqB,GAAG,wBAA9B;AAEA;;;;;;IAMM4Q;AACJ,iBAAYzwB,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,SAAKsJ,QAAL,GAAgBvL,OAAhB;AACA,SAAKoT,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,SAAK+mB,QAAL,GAAgB,IAAhB;;AACA,SAAKI,aAAL;;AACAzkB,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;AACD;;;;;AAgBD;SAEAsO,OAAA,gBAAO;AAAA;;AACL,QAAMgE,SAAS,GAAG3V,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCoM,YAApC,CAAlB;;AAEA,QAAIuF,SAAS,CAACvX,gBAAd,EAAgC;AAC9B;AACD;;AAED,QAAI,KAAKyN,OAAL,CAAa2T,SAAjB,EAA4B;AAC1B,WAAKxb,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4B8Q,iBAA5B;AACD;;AAED,QAAM5F,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,KAAI,CAACtO,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BskB,kBAA/B;;AACA,MAAA,KAAI,CAAChlB,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BoJ,iBAA5B;;AAEAxQ,MAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAI,CAACyB,QAA1B,EAAoCqM,aAApC;;AAEA,UAAI,KAAI,CAACxE,OAAL,CAAaod,QAAjB,EAA2B;AACzB,QAAA,KAAI,CAACxH,QAAL,GAAgBlnB,UAAU,CAAC,YAAM;AAC/B,UAAA,KAAI,CAACmX,IAAL;AACD,SAFyB,EAEvB,KAAI,CAAC7F,OAAL,CAAa8T,KAFU,CAA1B;AAGD;AACF,KAXD;;AAaA,SAAK3b,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BqkB,eAA/B;;AACA1sB,IAAAA,MAAM,CAAC,KAAK2H,QAAN,CAAN;;AACA,SAAKA,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4B4hB,kBAA5B;;AACA,QAAI,KAAKnd,OAAL,CAAa2T,SAAjB,EAA4B;AAC1B,UAAMpmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK+K,QAAN,CAA3D;AAEAhE,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD6a,QAAhD;AACAvY,MAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgB5K,kBAAhB,CAApB;AACD,KALD,MAKO;AACLkZ,MAAAA,QAAQ;AACT;AACF;;SAEDZ,OAAA,gBAAO;AAAA;;AACL,QAAI,CAAC,KAAK1N,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,iBAAjC,CAAL,EAAwD;AACtD;AACD;;AAED,QAAMwF,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCsM,YAApC,CAAlB;;AAEA,QAAI0F,SAAS,CAAC5X,gBAAd,EAAgC;AAC9B;AACD;;AAED,QAAMkU,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,MAAI,CAACtO,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4B2hB,eAA5B;;AACA/oB,MAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAACyB,QAA1B,EAAoCuM,cAApC;AACD,KAHD;;AAKA,SAAKvM,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B8L,iBAA/B;;AACA,QAAI,KAAK3E,OAAL,CAAa2T,SAAjB,EAA4B;AAC1B,UAAMpmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK+K,QAAN,CAA3D;AAEAhE,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD6a,QAAhD;AACAvY,MAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgB5K,kBAAhB,CAApB;AACD,KALD,MAKO;AACLkZ,MAAAA,QAAQ;AACT;AACF;;SAED/N,UAAA,mBAAU;AACRuJ,IAAAA,YAAY,CAAC,KAAK2T,QAAN,CAAZ;AACA,SAAKA,QAAL,GAAgB,IAAhB;;AAEA,QAAI,KAAKzd,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,iBAAjC,CAAJ,EAAuD;AACrD,WAAKxM,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B8L,iBAA/B;AACD;;AAEDxQ,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+D,QAAtB,EAAgC2T,qBAAhC;AACAva,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;AAEA,SAAKW,QAAL,GAAgB,IAAhB;AACA,SAAK6H,OAAL,GAAe,IAAf;AACD;;;SAIDC,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,oDACDgO,SADC,GAEDzC,WAAW,CAACG,iBAAZ,CAA8B,KAAKpC,QAAnC,CAFC,GAGD,OAAOtJ,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAH/C,CAAN;AAMAF,IAAAA,eAAe,CACb2I,MADa,EAEbzI,MAFa,EAGb,KAAK2b,WAAL,CAAiBpN,WAHJ,CAAf;AAMA,WAAOvO,MAAP;AACD;;SAEDmnB,gBAAA,yBAAgB;AAAA;;AACd7hB,IAAAA,YAAY,CAAC+B,EAAb,CACE,KAAKiC,QADP,EAEE2T,qBAFF,EAGEW,uBAHF,EAIE;AAAA,aAAM,MAAI,CAAC5G,IAAL,EAAN;AAAA,KAJF;AAMD;;;QAIM5M,kBAAP,yBAAuBpK,MAAvB,EAA+B;AAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;AAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;AACA,UAAMwI,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,UAAI,CAACsC,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIksB,KAAJ,CAAU,IAAV,EAAgBrd,OAAhB,CAAP;AACD;;AAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;AACD;;AAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ,CAAa,IAAb;AACD;AACF,KAfM,CAAP;AAgBD;;QAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;AAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;AACD;;;;wBA/IoB;AACnB,aAAOD,SAAP;AACD;;;wBAEwB;AACvB,aAAO6F,aAAP;AACD;;;wBAEoB;AACnB,aAAOP,SAAP;AACD;;;;;;AAwIH,IAAM5J,GAAC,GAAGvC,SAAS,EAAnB;AAEA;;;;;;;AAMA;;AACA,IAAIuC,GAAJ,EAAO;AACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;AACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAa+lB,KAAK,CAACpkB,eAAnB;AACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyB8jB,KAAzB;;AACApqB,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;AACA,WAAO+jB,KAAK,CAACpkB,eAAb;AACD,GAHD;AAID;;;;"}