{"version": 3, "file": "bootstrap.js", "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/polyfill.js", "../../js/src/dom/event-handler.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = parseFloat(transitionDuration)\n  const floatTransitionDelay = parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes)\n    .forEach(property => {\n      const expectedTypes = configTypes[property]\n      const value = config[property]\n      const valueType = value && isElement(value) ?\n        'element' :\n        toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new Error(\n          `${componentName.toUpperCase()}: ` +\n          `Option \"${property}\" provided type \"${valueType}\" ` +\n          `but expected type \"${expectedTypes}\".`)\n      }\n    })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nexport {\n  getjQuery,\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.key === 'undefined') {\n        element.key = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.key.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.key === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.key\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.key === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.key\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.key\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/* istanbul ignore file */\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.0-alpha1): dom/polyfill.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getUID } from '../util/index'\n\nlet find = Element.prototype.querySelectorAll\nlet findOne = Element.prototype.querySelector\n\n// MSEdge resets defaultPrevented flag upon dispatchEvent call if at least one listener is attached\nconst defaultPreventedPreservedOnDispatch = (() => {\n  const e = new CustomEvent('Bootstrap', {\n    cancelable: true\n  })\n\n  const element = document.createElement('div')\n  element.addEventListener('Bootstrap', () => null)\n\n  e.preventDefault()\n  element.dispatchEvent(e)\n  return e.defaultPrevented\n})()\n\nconst scopeSelectorRegex = /:scope\\b/\nconst supportScopeQuery = (() => {\n  const element = document.createElement('div')\n\n  try {\n    element.querySelectorAll(':scope *')\n  } catch (_) {\n    return false\n  }\n\n  return true\n})()\n\nif (!supportScopeQuery) {\n  find = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelectorAll(selector)\n    }\n\n    const hasId = Boolean(this.id)\n\n    if (!hasId) {\n      this.id = getUID('scope')\n    }\n\n    let nodeList = null\n    try {\n      selector = selector.replace(scopeSelectorRegex, `#${this.id}`)\n      nodeList = this.querySelectorAll(selector)\n    } finally {\n      if (!hasId) {\n        this.removeAttribute('id')\n      }\n    }\n\n    return nodeList\n  }\n\n  findOne = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelector(selector)\n    }\n\n    const matches = find.call(this, selector)\n\n    if (typeof matches[0] !== 'undefined') {\n      return matches[0]\n    }\n\n    return null\n  }\n}\n\nexport {\n  find,\n  findOne,\n  defaultPreventedPreservedOnDispatch\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\nimport { defaultPreventedPreservedOnDispatch } from './polyfill'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst $ = getjQuery()\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n]\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent)\n    .forEach(handlerKey => {\n      if (handlerKey.indexOf(namespace) > -1) {\n        const event = storeElementEvent[handlerKey]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.charAt(0) === '.'\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events)\n        .forEach(elementEvent => {\n          removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n        })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent)\n      .forEach(keyHandlers => {\n        const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n        if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n          const event = storeElementEvent[keyHandlers]\n\n          removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n        }\n      })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom informations in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args)\n        .forEach(key => {\n          Object.defineProperty(evt, key, {\n            get() {\n              return args[key]\n            }\n          })\n        })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n\n      if (!defaultPreventedPreservedOnDispatch) {\n        Object.defineProperty(evt, 'defaultPrevented', {\n          get: () => true\n        })\n      }\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this)\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    let rootElement = this._element\n    if (element) {\n      rootElement = this._getRootElement(element)\n    }\n\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler\n      .one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .alert to jQuery only if jQuery is present\n */\n\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Alert.jQueryInterface\n  $.fn[NAME].Constructor = Alert\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Alert.jQueryInterface\n  }\n}\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .button to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Button.jQueryInterface\n  $.fn[NAME].Constructor = Button\n\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Button.jQueryInterface\n  }\n}\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {\n      ...element.dataset\n    }\n\n    Object.keys(attributes).forEach(key => {\n      attributes[key] = normalizeData(attributes[key])\n    })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  },\n\n  toggleClass(element, className) {\n    if (!element) {\n      return\n    }\n\n    if (element.classList.contains(className)) {\n      element.classList.remove(className)\n    } else {\n      element.classList.add(className)\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { find as findFn, findOne } from './polyfill'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...findFn.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return findOne.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler\n        .on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler\n        .on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler\n        .on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement &&\n      this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const nextElementInterval = parseInt(nextElement.getAttribute('data-interval'), 10)\n      if (nextElementInterval) {\n        this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n        this._config.interval = nextElementInterval\n      } else {\n        this._config.interval = this._config.defaultInterval || this._config.interval\n      }\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler\n        .one(activeElement, TRANSITION_END, () => {\n          nextElement.classList.remove(directionalClassName, orderClassName)\n          nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n          activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n          this._isSliding = false\n\n          setTimeout(() => {\n            EventHandler.trigger(this._element, EVENT_SLID, {\n              relatedTarget: nextElement,\n              direction: eventDirectionName,\n              from: activeElementIndex,\n              to: nextElementIndex\n            })\n          }, 0)\n        })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .carousel to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Carousel.jQueryInterface\n  $.fn[NAME].Constructor = Carousel\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Carousel.jQueryInterface\n  }\n}\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.filter(elem => container !== elem)\n      activesData = tempActiveData[0] ? Data.getData(tempActiveData[0], DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    const hasWidth = this._element.classList.contains(WIDTH)\n    return hasWidth ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (element) {\n      const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n      if (triggerArray.length) {\n        triggerArray.forEach(elem => {\n          if (isOpen) {\n            elem.classList.remove(CLASS_NAME_COLLAPSED)\n          } else {\n            elem.classList.add(CLASS_NAME_COLLAPSED)\n          }\n\n          elem.setAttribute('aria-expanded', isOpen)\n        })\n      }\n    }\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .collapse to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Collapse.jQueryInterface\n  $.fn[NAME].Constructor = Collapse\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Collapse.jQueryInterface\n  }\n}\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_NAVBAR = 'navbar'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        parent.classList.add(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    Manipulator.toggleClass(this._menu, CLASS_NAME_SHOW)\n    Manipulator.toggleClass(this._element, CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    Manipulator.toggleClass(this._menu, CLASS_NAME_SHOW)\n    Manipulator.toggleClass(this._element, CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._element, EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      placement = PLACEMENT_TOP\n      if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n        placement = PLACEMENT_TOPEND\n      }\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return Boolean(this._element.closest(`.${CLASS_NAME_NAVBAR}`))\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this._config.offset(data.offsets, this._element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON ||\n      (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent)\n      .filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.key === ARROW_UP_KEY && index > 0) { // Up\n      index--\n    }\n\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) { // Down\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .dropdown to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Dropdown.jQueryInterface\n  $.fn[NAME].Constructor = Dropdown\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Dropdown.jQueryInterface\n  }\n}\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n      if (hideEvent.defaultPrevented) {\n        return\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n      const modalTransitionDuration = getTransitionDurationFromElement(this._element)\n      EventHandler.one(this._element, TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n      })\n      emulateTransitionEnd(this._element, modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...typeof config === 'object' && config ? config : {}\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .modal to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Modal.jQueryInterface\n  $.fn[NAME].Constructor = Modal\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Modal.jQueryInterface\n  }\n}\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultWhitelist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, whiteList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const whitelistKeys = Object.keys(whiteList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (whitelistKeys.indexOf(elName) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const whitelistedAttributes = [].concat(whiteList['*'] || [], whiteList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, whitelistedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultWhitelist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'whiteList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  whiteList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"tooltip-arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  whiteList: DefaultWhitelist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.target, dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.target,\n          this._getDelegateConfig()\n        )\n        Data.setData(event.target, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    Data.removeData(this.element, this.constructor.DATA_KEY)\n\n    EventHandler.off(this.element, this.constructor.EVENT_KEY)\n    EventHandler.off(this.element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if (this.element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this.element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this.element)\n      const isInTheDom = shadowRoot === null ?\n        this.element.ownerDocument.documentElement.contains(this.element) :\n        shadowRoot.contains(this.element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this.element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this.element, this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        EventHandler.trigger(this.element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this.element, this.constructor.Event.HIDDEN)\n      this._popper.destroy()\n    }\n\n    const hideEvent = EventHandler.trigger(this.element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.whiteList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: `.${this.constructor.NAME}-arrow`\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this.config.offset(data.offsets, this.element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this.element,\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this.element,\n          eventIn,\n          this.config.selector,\n          event => this._enter(event)\n        )\n        EventHandler.on(this.element,\n          eventOut,\n          this.config.selector,\n          event => this._leave(event)\n        )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this.element.closest(`.${CLASS_NAME_MODAL}`),\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.target, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.target,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.target, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) ||\n        context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.target, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.target,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.target, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this.element)\n\n    Object.keys(dataAttributes)\n      .forEach(dataAttr => {\n        if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n          delete dataAttributes[dataAttr]\n        }\n      })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.whiteList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const popperInstance = popperData.instance\n    this.tip = popperInstance.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .tooltip to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Tooltip.jQueryInterface\n  $.fn[NAME].Constructor = Tooltip\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tooltip.jQueryInterface\n  }\n}\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  // Private\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Popover.jQueryInterface\n  $.fn[NAME].Constructor = Popover\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Popover.jQueryInterface\n  }\n}\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS},` +\n                          `${this._config.target} ${SELECTOR_LIST_ITEMS},` +\n                          `${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets\n      .map(element => {\n        let target\n        const targetSelector = getSelectorFromElement(element)\n\n        if (targetSelector) {\n          target = SelectorEngine.findOne(targetSelector)\n        }\n\n        if (target) {\n          const targetBCR = target.getBoundingClientRect()\n          if (targetBCR.width || targetBCR.height) {\n            return [\n              Manipulator[offsetMethod](target).top + offsetBase,\n              targetSelector\n            ]\n          }\n        }\n\n        return null\n      })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine\n        .findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine\n        .parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = ScrollSpy.jQueryInterface\n  $.fn[NAME].Constructor = ScrollSpy\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return ScrollSpy.jQueryInterface\n  }\n}\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n\n    Data.setData(this._element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented ||\n      (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback &&\n      (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .tab to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Tab.jQueryInterface\n  $.fn[NAME].Constructor = Tab\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tab.jQueryInterface\n  }\n}\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 500\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(\n      this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      () => this.hide()\n    )\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n *  add .toast to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Toast.jQueryInterface\n  $.fn[NAME].Constructor = Toast\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Toast.jQueryInterface\n  }\n}\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "nodeType", "emulateTransitionEnd", "duration", "called", "durationPadding", "emulatedDuration", "listener", "removeEventListener", "addEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "mapData", "storeData", "id", "set", "key", "data", "get", "keyProperties", "delete", "Data", "setData", "instance", "getData", "removeData", "find", "Element", "prototype", "querySelectorAll", "findOne", "defaultPreventedPreservedOnDispatch", "e", "CustomEvent", "cancelable", "createElement", "preventDefault", "defaultPrevented", "scopeSelectorRegex", "supportScopeQuery", "_", "hasId", "Boolean", "nodeList", "replace", "removeAttribute", "matches", "$", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "fn", "handler", "event", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "target", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "custom", "isNative", "indexOf", "add<PERSON><PERSON><PERSON>", "handlers", "previousFn", "<PERSON><PERSON><PERSON><PERSON>", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "inNamespace", "isNamespace", "char<PERSON>t", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "bubbles", "nativeDispatch", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "defineProperty", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASSNAME_ALERT", "CLASSNAME_FADE", "CLASSNAME_SHOW", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "dispose", "closest", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "getInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "Number", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "getDataAttributes", "attributes", "dataset", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "toggleClass", "className", "add", "NODE_TEXT", "SelectorEngine", "concat", "findFn", "children", "filter", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "DIRECTION_NEXT", "DIRECTION_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_RIGHT", "CLASS_NAME_LEFT", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_keydown", "_addTouchEventListeners", "start", "pointerType", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "tagName", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "slideEvent", "nextElementInterval", "parseInt", "defaultInterval", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "startEvent", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "complete", "capitalizedDimension", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "<PERSON><PERSON><PERSON><PERSON>", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "selectorElements", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPRIGHT", "CLASS_NAME_DROPLEFT", "CLASS_NAME_MENURIGHT", "CLASS_NAME_NAVBAR", "CLASS_NAME_POSITION_STATIC", "SELECTOR_FORM_CHILD", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "flip", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "showEvent", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "focus", "hideEvent", "destroy", "update", "scheduleUpdate", "stopPropagation", "constructor", "_getPlacement", "parentDropdown", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_FADE", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "callback", "animate", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "modalTransitionDuration", "isModalOverflowing", "scrollHeight", "clientHeight", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "whiteList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "whitelist<PERSON><PERSON>s", "elements", "el", "el<PERSON>ame", "attributeList", "whitelistedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacement", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "defaultBsConfig", "behavior", "arrow", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "triggers", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "tabClass", "map", "token", "tClass", "popperData", "popperInstance", "popper", "initConfigAnimation", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;EAOA,IAAMA,OAAO,GAAG,OAAhB;EACA,IAAMC,uBAAuB,GAAG,IAAhC;EACA,IAAMC,cAAc,GAAG,eAAvB;;EAGA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,GAAG,EAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,gBAAUD,GAAV;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;EAQA;;;;;;;EAMA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,MAAM,EAAI;EACvB,KAAG;EACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBd,OAA3B,CAAV;EACD,GAFD,QAESe,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT;;EAIA,SAAOA,MAAP;EACD,CAND;;EAQA,IAAMM,WAAW,GAAG,SAAdA,WAAc,CAAAC,OAAO,EAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,aAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAME,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAjB;EAEAD,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACC,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAOH,QAAP;EACD,CAVD;;EAYA,IAAMI,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAL,OAAO,EAAI;EACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;;EAEA,MAAIC,QAAJ,EAAc;EACZ,WAAOJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,IAAMM,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAP,OAAO,EAAI;EACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAMA,IAAMO,gCAAgC,GAAG,SAAnCA,gCAAmC,CAAAR,OAAO,EAAI;EAClD,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,CAAP;EACD,GAHiD;;;EAAA,8BAS9CS,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,CAT8C;EAAA,MAOhDW,kBAPgD,yBAOhDA,kBAPgD;EAAA,MAQhDC,eARgD,yBAQhDA,eARgD;;EAWlD,MAAMC,uBAAuB,GAAGC,UAAU,CAACH,kBAAD,CAA1C;EACA,MAAMI,oBAAoB,GAAGD,UAAU,CAACF,eAAD,CAAvC,CAZkD;;EAelD,MAAI,CAACC,uBAAD,IAA4B,CAACE,oBAAjC,EAAuD;EACrD,WAAO,CAAP;EACD,GAjBiD;;;EAoBlDJ,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACK,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;EACAJ,EAAAA,eAAe,GAAGA,eAAe,CAACI,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;EAEA,SAAO,CAACF,UAAU,CAACH,kBAAD,CAAV,GAAiCG,UAAU,CAACF,eAAD,CAA5C,IAAiE7B,uBAAxE;EACD,CAxBD;;EA0BA,IAAMkC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAAjB,OAAO,EAAI;EACtCA,EAAAA,OAAO,CAACkB,aAAR,CAAsB,IAAIC,KAAJ,CAAUnC,cAAV,CAAtB;EACD,CAFD;;EAIA,IAAMoC,SAAS,GAAG,SAAZA,SAAY,CAAAlC,GAAG;EAAA,SAAI,CAACA,GAAG,CAAC,CAAD,CAAH,IAAUA,GAAX,EAAgBmC,QAApB;EAAA,CAArB;;EAEA,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACtB,OAAD,EAAUuB,QAAV,EAAuB;EAClD,MAAIC,MAAM,GAAG,KAAb;EACA,MAAMC,eAAe,GAAG,CAAxB;EACA,MAAMC,gBAAgB,GAAGH,QAAQ,GAAGE,eAApC;;EACA,WAASE,QAAT,GAAoB;EAClBH,IAAAA,MAAM,GAAG,IAAT;EACAxB,IAAAA,OAAO,CAAC4B,mBAAR,CAA4B5C,cAA5B,EAA4C2C,QAA5C;EACD;;EAED3B,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB7C,cAAzB,EAAyC2C,QAAzC;EACAG,EAAAA,UAAU,CAAC,YAAM;EACf,QAAI,CAACN,MAAL,EAAa;EACXP,MAAAA,oBAAoB,CAACjB,OAAD,CAApB;EACD;EACF,GAJS,EAIP0B,gBAJO,CAAV;EAKD,CAfD;;EAiBA,IAAMK,eAAe,GAAG,SAAlBA,eAAkB,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,EAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EACGG,OADH,CACW,UAAAC,QAAQ,EAAI;EACnB,QAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,QAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,QAAMG,SAAS,GAAGD,KAAK,IAAIpB,SAAS,CAACoB,KAAD,CAAlB,GAChB,SADgB,GAEhBvD,MAAM,CAACuD,KAAD,CAFR;;EAIA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,KAAJ,CACDZ,aAAa,CAACa,WAAd,EAAH,yBACWP,QADX,2BACuCG,SADvC,sCAEsBF,aAFtB,SADI,CAAN;EAID;EACF,GAdH;EAeD,CAhBD;;EAkBA,IAAMO,SAAS,GAAG,SAAZA,SAAY,CAAA9C,OAAO,EAAI;EAC3B,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,KAAP;EACD;;EAED,MAAIA,OAAO,CAAC+C,KAAR,IAAiB/C,OAAO,CAACgD,UAAzB,IAAuChD,OAAO,CAACgD,UAAR,CAAmBD,KAA9D,EAAqE;EACnE,QAAME,YAAY,GAAGvC,gBAAgB,CAACV,OAAD,CAArC;EACA,QAAMkD,eAAe,GAAGxC,gBAAgB,CAACV,OAAO,CAACgD,UAAT,CAAxC;EAEA,WAAOC,YAAY,CAACE,OAAb,KAAyB,MAAzB,IACLD,eAAe,CAACC,OAAhB,KAA4B,MADvB,IAELF,YAAY,CAACG,UAAb,KAA4B,QAF9B;EAGD;;EAED,SAAO,KAAP;EACD,CAfD;;EAiBA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAAArD,OAAO,EAAI;EAChC,MAAI,CAACH,QAAQ,CAACyD,eAAT,CAAyBC,YAA9B,EAA4C;EAC1C,WAAO,IAAP;EACD,GAH+B;;;EAMhC,MAAI,OAAOvD,OAAO,CAACwD,WAAf,KAA+B,UAAnC,EAA+C;EAC7C,QAAMC,IAAI,GAAGzD,OAAO,CAACwD,WAAR,EAAb;EACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;EACD;;EAED,MAAIzD,OAAO,YAAY0D,UAAvB,EAAmC;EACjC,WAAO1D,OAAP;EACD,GAb+B;;;EAgBhC,MAAI,CAACA,OAAO,CAACgD,UAAb,EAAyB;EACvB,WAAO,IAAP;EACD;;EAED,SAAOK,cAAc,CAACrD,OAAO,CAACgD,UAAT,CAArB;EACD,CArBD;;EAuBA,IAAMW,IAAI,GAAG,SAAPA,IAAO;EAAA,SAAM,YAAY,EAAlB;EAAA,CAAb;;EAEA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAA5D,OAAO;EAAA,SAAIA,OAAO,CAAC6D,YAAZ;EAAA,CAAtB;;EAEA,IAAMC,SAAS,GAAG,SAAZA,SAAY,GAAM;EAAA,gBACHrD,MADG;EAAA,MACdsD,MADc,WACdA,MADc;;EAGtB,MAAIA,MAAM,IAAI,CAAClE,QAAQ,CAACmE,IAAT,CAAcC,YAAd,CAA2B,gBAA3B,CAAf,EAA6D;EAC3D,WAAOF,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EC7KA;;;;;;;EAOA;;;;;EAMA,IAAMG,OAAO,GAAI,YAAM;EACrB,MAAMC,SAAS,GAAG,EAAlB;EACA,MAAIC,EAAE,GAAG,CAAT;EACA,SAAO;EACLC,IAAAA,GADK,eACDrE,OADC,EACQsE,GADR,EACaC,IADb,EACmB;EACtB,UAAI,OAAOvE,OAAO,CAACsE,GAAf,KAAuB,WAA3B,EAAwC;EACtCtE,QAAAA,OAAO,CAACsE,GAAR,GAAc;EACZA,UAAAA,GAAG,EAAHA,GADY;EAEZF,UAAAA,EAAE,EAAFA;EAFY,SAAd;EAIAA,QAAAA,EAAE;EACH;;EAEDD,MAAAA,SAAS,CAACnE,OAAO,CAACsE,GAAR,CAAYF,EAAb,CAAT,GAA4BG,IAA5B;EACD,KAXI;EAYLC,IAAAA,GAZK,eAYDxE,OAZC,EAYQsE,GAZR,EAYa;EAChB,UAAI,CAACtE,OAAD,IAAY,OAAOA,OAAO,CAACsE,GAAf,KAAuB,WAAvC,EAAoD;EAClD,eAAO,IAAP;EACD;;EAED,UAAMG,aAAa,GAAGzE,OAAO,CAACsE,GAA9B;;EACA,UAAIG,aAAa,CAACH,GAAd,KAAsBA,GAA1B,EAA+B;EAC7B,eAAOH,SAAS,CAACM,aAAa,CAACL,EAAf,CAAhB;EACD;;EAED,aAAO,IAAP;EACD,KAvBI;EAwBLM,IAAAA,MAxBK,mBAwBE1E,OAxBF,EAwBWsE,GAxBX,EAwBgB;EACnB,UAAI,OAAOtE,OAAO,CAACsE,GAAf,KAAuB,WAA3B,EAAwC;EACtC;EACD;;EAED,UAAMG,aAAa,GAAGzE,OAAO,CAACsE,GAA9B;;EACA,UAAIG,aAAa,CAACH,GAAd,KAAsBA,GAA1B,EAA+B;EAC7B,eAAOH,SAAS,CAACM,aAAa,CAACL,EAAf,CAAhB;EACA,eAAOpE,OAAO,CAACsE,GAAf;EACD;EACF;EAlCI,GAAP;EAoCD,CAvCe,EAAhB;;EAyCA,IAAMK,IAAI,GAAG;EACXC,EAAAA,OADW,mBACHC,QADG,EACOP,GADP,EACYC,IADZ,EACkB;EAC3BL,IAAAA,OAAO,CAACG,GAAR,CAAYQ,QAAZ,EAAsBP,GAAtB,EAA2BC,IAA3B;EACD,GAHU;EAIXO,EAAAA,OAJW,mBAIHD,QAJG,EAIOP,GAJP,EAIY;EACrB,WAAOJ,OAAO,CAACM,GAAR,CAAYK,QAAZ,EAAsBP,GAAtB,CAAP;EACD,GANU;EAOXS,EAAAA,UAPW,sBAOAF,QAPA,EAOUP,GAPV,EAOe;EACxBJ,IAAAA,OAAO,CAACQ,MAAR,CAAeG,QAAf,EAAyBP,GAAzB;EACD;EATU,CAAb;;ECtDA;EAWA,IAAIU,IAAI,GAAGC,OAAO,CAACC,SAAR,CAAkBC,gBAA7B;EACA,IAAIC,OAAO,GAAGH,OAAO,CAACC,SAAR,CAAkB5E,aAAhC;;EAGA,IAAM+E,mCAAmC,GAAI,YAAM;EACjD,MAAMC,CAAC,GAAG,IAAIC,WAAJ,CAAgB,WAAhB,EAA6B;EACrCC,IAAAA,UAAU,EAAE;EADyB,GAA7B,CAAV;EAIA,MAAMxF,OAAO,GAAGH,QAAQ,CAAC4F,aAAT,CAAuB,KAAvB,CAAhB;EACAzF,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB,WAAzB,EAAsC;EAAA,WAAM,IAAN;EAAA,GAAtC;EAEAyD,EAAAA,CAAC,CAACI,cAAF;EACA1F,EAAAA,OAAO,CAACkB,aAAR,CAAsBoE,CAAtB;EACA,SAAOA,CAAC,CAACK,gBAAT;EACD,CAX2C,EAA5C;;EAaA,IAAMC,kBAAkB,GAAG,UAA3B;;EACA,IAAMC,iBAAiB,GAAI,YAAM;EAC/B,MAAM7F,OAAO,GAAGH,QAAQ,CAAC4F,aAAT,CAAuB,KAAvB,CAAhB;;EAEA,MAAI;EACFzF,IAAAA,OAAO,CAACmF,gBAAR,CAAyB,UAAzB;EACD,GAFD,CAEE,OAAOW,CAAP,EAAU;EACV,WAAO,KAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVyB,EAA1B;;EAYA,IAAI,CAACD,iBAAL,EAAwB;EACtBb,EAAAA,IAAI,GAAG,cAAU/E,QAAV,EAAoB;EACzB,QAAI,CAAC2F,kBAAkB,CAACjD,IAAnB,CAAwB1C,QAAxB,CAAL,EAAwC;EACtC,aAAO,KAAKkF,gBAAL,CAAsBlF,QAAtB,CAAP;EACD;;EAED,QAAM8F,KAAK,GAAGC,OAAO,CAAC,KAAK5B,EAAN,CAArB;;EAEA,QAAI,CAAC2B,KAAL,EAAY;EACV,WAAK3B,EAAL,GAAU5E,MAAM,CAAC,OAAD,CAAhB;EACD;;EAED,QAAIyG,QAAQ,GAAG,IAAf;;EACA,QAAI;EACFhG,MAAAA,QAAQ,GAAGA,QAAQ,CAACiG,OAAT,CAAiBN,kBAAjB,QAAyC,KAAKxB,EAA9C,CAAX;EACA6B,MAAAA,QAAQ,GAAG,KAAKd,gBAAL,CAAsBlF,QAAtB,CAAX;EACD,KAHD,SAGU;EACR,UAAI,CAAC8F,KAAL,EAAY;EACV,aAAKI,eAAL,CAAqB,IAArB;EACD;EACF;;EAED,WAAOF,QAAP;EACD,GAtBD;;EAwBAb,EAAAA,OAAO,GAAG,iBAAUnF,QAAV,EAAoB;EAC5B,QAAI,CAAC2F,kBAAkB,CAACjD,IAAnB,CAAwB1C,QAAxB,CAAL,EAAwC;EACtC,aAAO,KAAKK,aAAL,CAAmBL,QAAnB,CAAP;EACD;;EAED,QAAMmG,OAAO,GAAGpB,IAAI,CAAC3F,IAAL,CAAU,IAAV,EAAgBY,QAAhB,CAAhB;;EAEA,QAAI,OAAOmG,OAAO,CAAC,CAAD,CAAd,KAAsB,WAA1B,EAAuC;EACrC,aAAOA,OAAO,CAAC,CAAD,CAAd;EACD;;EAED,WAAO,IAAP;EACD,GAZD;EAaD;;EC/ED;;;;;;EAUA;;;;;;EAMA,IAAMC,CAAC,GAAGvC,SAAS,EAAnB;EACA,IAAMwC,cAAc,GAAG,oBAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,aAAa,GAAG,QAAtB;EACA,IAAMC,aAAa,GAAG,EAAtB;;EACA,IAAIC,QAAQ,GAAG,CAAf;EACA,IAAMC,YAAY,GAAG;EACnBC,EAAAA,UAAU,EAAE,WADO;EAEnBC,EAAAA,UAAU,EAAE;EAFO,CAArB;EAIA,IAAMC,YAAY,GAAG,CACnB,OADmB,EAEnB,UAFmB,EAGnB,SAHmB,EAInB,WAJmB,EAKnB,aALmB,EAMnB,YANmB,EAOnB,gBAPmB,EAQnB,WARmB,EASnB,UATmB,EAUnB,WAVmB,EAWnB,aAXmB,EAYnB,WAZmB,EAanB,SAbmB,EAcnB,UAdmB,EAenB,OAfmB,EAgBnB,mBAhBmB,EAiBnB,YAjBmB,EAkBnB,WAlBmB,EAmBnB,UAnBmB,EAoBnB,aApBmB,EAqBnB,aArBmB,EAsBnB,aAtBmB,EAuBnB,WAvBmB,EAwBnB,cAxBmB,EAyBnB,eAzBmB,EA0BnB,cA1BmB,EA2BnB,eA3BmB,EA4BnB,YA5BmB,EA6BnB,OA7BmB,EA8BnB,MA9BmB,EA+BnB,QA/BmB,EAgCnB,OAhCmB,EAiCnB,QAjCmB,EAkCnB,QAlCmB,EAmCnB,SAnCmB,EAoCnB,UApCmB,EAqCnB,MArCmB,EAsCnB,QAtCmB,EAuCnB,cAvCmB,EAwCnB,QAxCmB,EAyCnB,MAzCmB,EA0CnB,kBA1CmB,EA2CnB,kBA3CmB,EA4CnB,OA5CmB,EA6CnB,OA7CmB,EA8CnB,QA9CmB,CAArB;EAiDA;;;;;;EAMA,SAASC,WAAT,CAAqB/G,OAArB,EAA8BgH,GAA9B,EAAmC;EACjC,SAAQA,GAAG,IAAOA,GAAP,UAAeN,QAAQ,EAA3B,IAAoC1G,OAAO,CAAC0G,QAA5C,IAAwDA,QAAQ,EAAvE;EACD;;EAED,SAASO,QAAT,CAAkBjH,OAAlB,EAA2B;EACzB,MAAMgH,GAAG,GAAGD,WAAW,CAAC/G,OAAD,CAAvB;EAEAA,EAAAA,OAAO,CAAC0G,QAAR,GAAmBM,GAAnB;EACAP,EAAAA,aAAa,CAACO,GAAD,CAAb,GAAqBP,aAAa,CAACO,GAAD,CAAb,IAAsB,EAA3C;EAEA,SAAOP,aAAa,CAACO,GAAD,CAApB;EACD;;EAED,SAASE,gBAAT,CAA0BlH,OAA1B,EAAmCmH,EAAnC,EAAuC;EACrC,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7B,QAAID,OAAO,CAACE,MAAZ,EAAoB;EAClBC,MAAAA,YAAY,CAACC,GAAb,CAAiBxH,OAAjB,EAA0BqH,KAAK,CAACI,IAAhC,EAAsCN,EAAtC;EACD;;EAED,WAAOA,EAAE,CAACO,KAAH,CAAS1H,OAAT,EAAkB,CAACqH,KAAD,CAAlB,CAAP;EACD,GAND;EAOD;;EAED,SAASM,0BAAT,CAAoC3H,OAApC,EAA6CC,QAA7C,EAAuDkH,EAAvD,EAA2D;EACzD,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7B,QAAMO,WAAW,GAAG5H,OAAO,CAACmF,gBAAR,CAAyBlF,QAAzB,CAApB;;EAEA,aAAW4H,MAAX,GAAsBR,KAAtB,CAAWQ,MAAX,EAA6BA,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAAC7E,UAAxE,EAAoF;EAClF,WAAK,IAAI8E,CAAC,GAAGF,WAAW,CAACG,MAAzB,EAAiCD,CAAC,EAAlC,GAAuC;EACrC,YAAIF,WAAW,CAACE,CAAD,CAAX,KAAmBD,MAAvB,EAA+B;EAC7B,cAAIT,OAAO,CAACE,MAAZ,EAAoB;EAClBC,YAAAA,YAAY,CAACC,GAAb,CAAiBxH,OAAjB,EAA0BqH,KAAK,CAACI,IAAhC,EAAsCN,EAAtC;EACD;;EAED,iBAAOA,EAAE,CAACO,KAAH,CAASG,MAAT,EAAiB,CAACR,KAAD,CAAjB,CAAP;EACD;EACF;EACF,KAb4B;;;EAgB7B,WAAO,IAAP;EACD,GAjBD;EAkBD;;EAED,SAASW,WAAT,CAAqBC,MAArB,EAA6Bb,OAA7B,EAAsCc,kBAAtC,EAAiE;EAAA,MAA3BA,kBAA2B;EAA3BA,IAAAA,kBAA2B,GAAN,IAAM;EAAA;;EAC/D,MAAMC,YAAY,GAAGhG,MAAM,CAACC,IAAP,CAAY6F,MAAZ,CAArB;;EAEA,OAAK,IAAIH,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGD,YAAY,CAACJ,MAAnC,EAA2CD,CAAC,GAAGM,GAA/C,EAAoDN,CAAC,EAArD,EAAyD;EACvD,QAAMT,KAAK,GAAGY,MAAM,CAACE,YAAY,CAACL,CAAD,CAAb,CAApB;;EAEA,QAAIT,KAAK,CAACgB,eAAN,KAA0BjB,OAA1B,IAAqCC,KAAK,CAACa,kBAAN,KAA6BA,kBAAtE,EAA0F;EACxF,aAAOb,KAAP;EACD;EACF;;EAED,SAAO,IAAP;EACD;;EAED,SAASiB,eAAT,CAAyBC,iBAAzB,EAA4CnB,OAA5C,EAAqDoB,YAArD,EAAmE;EACjE,MAAMC,UAAU,GAAG,OAAOrB,OAAP,KAAmB,QAAtC;EACA,MAAMiB,eAAe,GAAGI,UAAU,GAAGD,YAAH,GAAkBpB,OAApD,CAFiE;;EAKjE,MAAIsB,SAAS,GAAGH,iBAAiB,CAACrC,OAAlB,CAA0BK,cAA1B,EAA0C,EAA1C,CAAhB;EACA,MAAMoC,MAAM,GAAGhC,YAAY,CAAC+B,SAAD,CAA3B;;EAEA,MAAIC,MAAJ,EAAY;EACVD,IAAAA,SAAS,GAAGC,MAAZ;EACD;;EAED,MAAMC,QAAQ,GAAG9B,YAAY,CAAC+B,OAAb,CAAqBH,SAArB,IAAkC,CAAC,CAApD;;EAEA,MAAI,CAACE,QAAL,EAAe;EACbF,IAAAA,SAAS,GAAGH,iBAAZ;EACD;;EAED,SAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP;EACD;;EAED,SAASI,UAAT,CAAoB9I,OAApB,EAA6BuI,iBAA7B,EAAgDnB,OAAhD,EAAyDoB,YAAzD,EAAuElB,MAAvE,EAA+E;EAC7E,MAAI,OAAOiB,iBAAP,KAA6B,QAA7B,IAAyC,CAACvI,OAA9C,EAAuD;EACrD;EACD;;EAED,MAAI,CAACoH,OAAL,EAAc;EACZA,IAAAA,OAAO,GAAGoB,YAAV;EACAA,IAAAA,YAAY,GAAG,IAAf;EACD;;EAR4E,yBAU5BF,eAAe,CAACC,iBAAD,EAAoBnB,OAApB,EAA6BoB,YAA7B,CAVa;EAAA,MAUtEC,UAVsE;EAAA,MAU1DJ,eAV0D;EAAA,MAUzCK,SAVyC;;EAW7E,MAAMT,MAAM,GAAGhB,QAAQ,CAACjH,OAAD,CAAvB;EACA,MAAM+I,QAAQ,GAAGd,MAAM,CAACS,SAAD,CAAN,KAAsBT,MAAM,CAACS,SAAD,CAAN,GAAoB,EAA1C,CAAjB;EACA,MAAMM,UAAU,GAAGhB,WAAW,CAACe,QAAD,EAAWV,eAAX,EAA4BI,UAAU,GAAGrB,OAAH,GAAa,IAAnD,CAA9B;;EAEA,MAAI4B,UAAJ,EAAgB;EACdA,IAAAA,UAAU,CAAC1B,MAAX,GAAoB0B,UAAU,CAAC1B,MAAX,IAAqBA,MAAzC;EAEA;EACD;;EAED,MAAMN,GAAG,GAAGD,WAAW,CAACsB,eAAD,EAAkBE,iBAAiB,CAACrC,OAAlB,CAA0BI,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB;EACA,MAAMa,EAAE,GAAGsB,UAAU,GACnBd,0BAA0B,CAAC3H,OAAD,EAAUoH,OAAV,EAAmBoB,YAAnB,CADP,GAEnBtB,gBAAgB,CAAClH,OAAD,EAAUoH,OAAV,CAFlB;EAIAD,EAAAA,EAAE,CAACe,kBAAH,GAAwBO,UAAU,GAAGrB,OAAH,GAAa,IAA/C;EACAD,EAAAA,EAAE,CAACkB,eAAH,GAAqBA,eAArB;EACAlB,EAAAA,EAAE,CAACG,MAAH,GAAYA,MAAZ;EACAH,EAAAA,EAAE,CAACT,QAAH,GAAcM,GAAd;EACA+B,EAAAA,QAAQ,CAAC/B,GAAD,CAAR,GAAgBG,EAAhB;EAEAnH,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB6G,SAAzB,EAAoCvB,EAApC,EAAwCsB,UAAxC;EACD;;EAED,SAASQ,aAAT,CAAuBjJ,OAAvB,EAAgCiI,MAAhC,EAAwCS,SAAxC,EAAmDtB,OAAnD,EAA4Dc,kBAA5D,EAAgF;EAC9E,MAAMf,EAAE,GAAGa,WAAW,CAACC,MAAM,CAACS,SAAD,CAAP,EAAoBtB,OAApB,EAA6Bc,kBAA7B,CAAtB;;EAEA,MAAI,CAACf,EAAL,EAAS;EACP;EACD;;EAEDnH,EAAAA,OAAO,CAAC4B,mBAAR,CAA4B8G,SAA5B,EAAuCvB,EAAvC,EAA2CnB,OAAO,CAACkC,kBAAD,CAAlD;EACA,SAAOD,MAAM,CAACS,SAAD,CAAN,CAAkBvB,EAAE,CAACT,QAArB,CAAP;EACD;;EAED,SAASwC,wBAAT,CAAkClJ,OAAlC,EAA2CiI,MAA3C,EAAmDS,SAAnD,EAA8DS,SAA9D,EAAyE;EACvE,MAAMC,iBAAiB,GAAGnB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;EAEAvG,EAAAA,MAAM,CAACC,IAAP,CAAYgH,iBAAZ,EACG/G,OADH,CACW,UAAAgH,UAAU,EAAI;EACrB,QAAIA,UAAU,CAACR,OAAX,CAAmBM,SAAnB,IAAgC,CAAC,CAArC,EAAwC;EACtC,UAAM9B,KAAK,GAAG+B,iBAAiB,CAACC,UAAD,CAA/B;EAEAJ,MAAAA,aAAa,CAACjJ,OAAD,EAAUiI,MAAV,EAAkBS,SAAlB,EAA6BrB,KAAK,CAACgB,eAAnC,EAAoDhB,KAAK,CAACa,kBAA1D,CAAb;EACD;EACF,GAPH;EAQD;;EAED,IAAMX,YAAY,GAAG;EACnB+B,EAAAA,EADmB,cAChBtJ,OADgB,EACPqH,KADO,EACAD,OADA,EACSoB,YADT,EACuB;EACxCM,IAAAA,UAAU,CAAC9I,OAAD,EAAUqH,KAAV,EAAiBD,OAAjB,EAA0BoB,YAA1B,EAAwC,KAAxC,CAAV;EACD,GAHkB;EAKnBe,EAAAA,GALmB,eAKfvJ,OALe,EAKNqH,KALM,EAKCD,OALD,EAKUoB,YALV,EAKwB;EACzCM,IAAAA,UAAU,CAAC9I,OAAD,EAAUqH,KAAV,EAAiBD,OAAjB,EAA0BoB,YAA1B,EAAwC,IAAxC,CAAV;EACD,GAPkB;EASnBhB,EAAAA,GATmB,eASfxH,OATe,EASNuI,iBATM,EASanB,OATb,EASsBoB,YATtB,EASoC;EACrD,QAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAACvI,OAA9C,EAAuD;EACrD;EACD;;EAHoD,4BAKJsI,eAAe,CAACC,iBAAD,EAAoBnB,OAApB,EAA6BoB,YAA7B,CALX;EAAA,QAK9CC,UAL8C;EAAA,QAKlCJ,eALkC;EAAA,QAKjBK,SALiB;;EAMrD,QAAMc,WAAW,GAAGd,SAAS,KAAKH,iBAAlC;EACA,QAAMN,MAAM,GAAGhB,QAAQ,CAACjH,OAAD,CAAvB;EACA,QAAMyJ,WAAW,GAAGlB,iBAAiB,CAACmB,MAAlB,CAAyB,CAAzB,MAAgC,GAApD;;EAEA,QAAI,OAAOrB,eAAP,KAA2B,WAA/B,EAA4C;EAC1C;EACA,UAAI,CAACJ,MAAD,IAAW,CAACA,MAAM,CAACS,SAAD,CAAtB,EAAmC;EACjC;EACD;;EAEDO,MAAAA,aAAa,CAACjJ,OAAD,EAAUiI,MAAV,EAAkBS,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAGrB,OAAH,GAAa,IAArE,CAAb;EACA;EACD;;EAED,QAAIqC,WAAJ,EAAiB;EACftH,MAAAA,MAAM,CAACC,IAAP,CAAY6F,MAAZ,EACG5F,OADH,CACW,UAAAsH,YAAY,EAAI;EACvBT,QAAAA,wBAAwB,CAAClJ,OAAD,EAAUiI,MAAV,EAAkB0B,YAAlB,EAAgCpB,iBAAiB,CAACqB,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB;EACD,OAHH;EAID;;EAED,QAAMR,iBAAiB,GAAGnB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;EACAvG,IAAAA,MAAM,CAACC,IAAP,CAAYgH,iBAAZ,EACG/G,OADH,CACW,UAAAwH,WAAW,EAAI;EACtB,UAAMR,UAAU,GAAGQ,WAAW,CAAC3D,OAAZ,CAAoBM,aAApB,EAAmC,EAAnC,CAAnB;;EAEA,UAAI,CAACgD,WAAD,IAAgBjB,iBAAiB,CAACM,OAAlB,CAA0BQ,UAA1B,IAAwC,CAAC,CAA7D,EAAgE;EAC9D,YAAMhC,KAAK,GAAG+B,iBAAiB,CAACS,WAAD,CAA/B;EAEAZ,QAAAA,aAAa,CAACjJ,OAAD,EAAUiI,MAAV,EAAkBS,SAAlB,EAA6BrB,KAAK,CAACgB,eAAnC,EAAoDhB,KAAK,CAACa,kBAA1D,CAAb;EACD;EACF,KATH;EAUD,GA/CkB;EAiDnB4B,EAAAA,OAjDmB,mBAiDX9J,OAjDW,EAiDFqH,KAjDE,EAiDK0C,IAjDL,EAiDW;EAC5B,QAAI,OAAO1C,KAAP,KAAiB,QAAjB,IAA6B,CAACrH,OAAlC,EAA2C;EACzC,aAAO,IAAP;EACD;;EAED,QAAM0I,SAAS,GAAGrB,KAAK,CAACnB,OAAN,CAAcK,cAAd,EAA8B,EAA9B,CAAlB;EACA,QAAMiD,WAAW,GAAGnC,KAAK,KAAKqB,SAA9B;EACA,QAAME,QAAQ,GAAG9B,YAAY,CAAC+B,OAAb,CAAqBH,SAArB,IAAkC,CAAC,CAApD;EAEA,QAAIsB,WAAJ;EACA,QAAIC,OAAO,GAAG,IAAd;EACA,QAAIC,cAAc,GAAG,IAArB;EACA,QAAIvE,gBAAgB,GAAG,KAAvB;EACA,QAAIwE,GAAG,GAAG,IAAV;;EAEA,QAAIX,WAAW,IAAInD,CAAnB,EAAsB;EACpB2D,MAAAA,WAAW,GAAG3D,CAAC,CAAClF,KAAF,CAAQkG,KAAR,EAAe0C,IAAf,CAAd;EAEA1D,MAAAA,CAAC,CAACrG,OAAD,CAAD,CAAW8J,OAAX,CAAmBE,WAAnB;EACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACI,oBAAZ,EAAX;EACAF,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACK,6BAAZ,EAAlB;EACA1E,MAAAA,gBAAgB,GAAGqE,WAAW,CAACM,kBAAZ,EAAnB;EACD;;EAED,QAAI1B,QAAJ,EAAc;EACZuB,MAAAA,GAAG,GAAGtK,QAAQ,CAAC0K,WAAT,CAAqB,YAArB,CAAN;EACAJ,MAAAA,GAAG,CAACK,SAAJ,CAAc9B,SAAd,EAAyBuB,OAAzB,EAAkC,IAAlC;EACD,KAHD,MAGO;EACLE,MAAAA,GAAG,GAAG,IAAI5E,WAAJ,CAAgB8B,KAAhB,EAAuB;EAC3B4C,QAAAA,OAAO,EAAPA,OAD2B;EAE3BzE,QAAAA,UAAU,EAAE;EAFe,OAAvB,CAAN;EAID,KAhC2B;;;EAmC5B,QAAI,OAAOuE,IAAP,KAAgB,WAApB,EAAiC;EAC/B5H,MAAAA,MAAM,CAACC,IAAP,CAAY2H,IAAZ,EACG1H,OADH,CACW,UAAAiC,GAAG,EAAI;EACdnC,QAAAA,MAAM,CAACsI,cAAP,CAAsBN,GAAtB,EAA2B7F,GAA3B,EAAgC;EAC9BE,UAAAA,GAD8B,iBACxB;EACJ,mBAAOuF,IAAI,CAACzF,GAAD,CAAX;EACD;EAH6B,SAAhC;EAKD,OAPH;EAQD;;EAED,QAAIqB,gBAAJ,EAAsB;EACpBwE,MAAAA,GAAG,CAACzE,cAAJ;;EAEA,UAAI,CAACL,mCAAL,EAA0C;EACxClD,QAAAA,MAAM,CAACsI,cAAP,CAAsBN,GAAtB,EAA2B,kBAA3B,EAA+C;EAC7C3F,UAAAA,GAAG,EAAE;EAAA,mBAAM,IAAN;EAAA;EADwC,SAA/C;EAGD;EACF;;EAED,QAAI0F,cAAJ,EAAoB;EAClBlK,MAAAA,OAAO,CAACkB,aAAR,CAAsBiJ,GAAtB;EACD;;EAED,QAAIA,GAAG,CAACxE,gBAAJ,IAAwB,OAAOqE,WAAP,KAAuB,WAAnD,EAAgE;EAC9DA,MAAAA,WAAW,CAACtE,cAAZ;EACD;;EAED,WAAOyE,GAAP;EACD;EAlHkB,CAArB;;EC1MA;;;;;;EAMA,IAAMO,IAAI,GAAG,OAAb;EACA,IAAMC,OAAO,GAAG,cAAhB;EACA,IAAMC,QAAQ,GAAG,UAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,YAAY,GAAG,WAArB;EAEA,IAAMC,gBAAgB,GAAG,wBAAzB;EAEA,IAAMC,WAAW,aAAWH,SAA5B;EACA,IAAMI,YAAY,cAAYJ,SAA9B;EACA,IAAMK,oBAAoB,aAAWL,SAAX,GAAuBC,YAAjD;EAEA,IAAMK,eAAe,GAAG,OAAxB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EAEA;;;;;;MAMMC;EACJ,iBAAYtL,OAAZ,EAAqB;EACnB,SAAKuL,QAAL,GAAgBvL,OAAhB;;EAEA,QAAI,KAAKuL,QAAT,EAAmB;EACjB5G,MAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,QAAtB,EAAgC,IAAhC;EACD;EACF;;;;;EAQD;WAEAY,QAAA,eAAMxL,OAAN,EAAe;EACb,QAAIyL,WAAW,GAAG,KAAKF,QAAvB;;EACA,QAAIvL,OAAJ,EAAa;EACXyL,MAAAA,WAAW,GAAG,KAAKC,eAAL,CAAqB1L,OAArB,CAAd;EACD;;EAED,QAAM2L,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;EAEA,QAAIE,WAAW,KAAK,IAAhB,IAAwBA,WAAW,CAAChG,gBAAxC,EAA0D;EACxD;EACD;;EAED,SAAKkG,cAAL,CAAoBJ,WAApB;EACD;;WAEDK,UAAA,mBAAU;EACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,QAA/B;EACA,SAAKW,QAAL,GAAgB,IAAhB;EACD;;;WAIDG,kBAAA,yBAAgB1L,OAAhB,EAAyB;EACvB,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAAC+L,OAAR,OAAoBZ,eAApB,CAA1C;EACD;;WAEDS,qBAAA,4BAAmB5L,OAAnB,EAA4B;EAC1B,WAAOuH,YAAY,CAACuC,OAAb,CAAqB9J,OAArB,EAA8BgL,WAA9B,CAAP;EACD;;WAEDa,iBAAA,wBAAe7L,OAAf,EAAwB;EAAA;;EACtBA,IAAAA,OAAO,CAACgM,SAAR,CAAkBC,MAAlB,CAAyBZ,cAAzB;;EAEA,QAAI,CAACrL,OAAO,CAACgM,SAAR,CAAkBE,QAAlB,CAA2Bd,cAA3B,CAAL,EAAiD;EAC/C,WAAKe,eAAL,CAAqBnM,OAArB;;EACA;EACD;;EAED,QAAMW,kBAAkB,GAAGH,gCAAgC,CAACR,OAAD,CAA3D;EAEAuH,IAAAA,YAAY,CACTgC,GADH,CACOvJ,OADP,EACgBhB,cADhB,EACgC;EAAA,aAAM,KAAI,CAACmN,eAAL,CAAqBnM,OAArB,CAAN;EAAA,KADhC;EAEAsB,IAAAA,oBAAoB,CAACtB,OAAD,EAAUW,kBAAV,CAApB;EACD;;WAEDwL,kBAAA,yBAAgBnM,OAAhB,EAAyB;EACvB,QAAIA,OAAO,CAACgD,UAAZ,EAAwB;EACtBhD,MAAAA,OAAO,CAACgD,UAAR,CAAmBoJ,WAAnB,CAA+BpM,OAA/B;EACD;;EAEDuH,IAAAA,YAAY,CAACuC,OAAb,CAAqB9J,OAArB,EAA8BiL,YAA9B;EACD;;;UAIMoB,kBAAP,yBAAuBpK,MAAvB,EAA+B;EAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;EAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,QAAnB,CAAX;;EAEA,UAAI,CAACrG,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI+G,KAAJ,CAAU,IAAV,CAAP;EACD;;EAED,UAAIrJ,MAAM,KAAK,OAAf,EAAwB;EACtBsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAVM,CAAP;EAWD;;UAEMsK,gBAAP,uBAAqBC,aAArB,EAAoC;EAClC,WAAO,UAAUnF,KAAV,EAAiB;EACtB,UAAIA,KAAJ,EAAW;EACTA,QAAAA,KAAK,CAAC3B,cAAN;EACD;;EAED8G,MAAAA,aAAa,CAAChB,KAAd,CAAoB,IAApB;EACD,KAND;EAOD;;UAEMiB,cAAP,qBAAmBzM,OAAnB,EAA4B;EAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,QAAtB,CAAP;EACD;;;;0BAvFoB;EACnB,aAAOD,OAAP;EACD;;;;;EAwFH;;;;;;;EAKApD,YAAY,CACT+B,EADH,CACMzJ,QADN,EACgBqL,oBADhB,EACsCH,gBADtC,EACwDO,KAAK,CAACiB,aAAN,CAAoB,IAAIjB,KAAJ,EAApB,CADxD;EAGA,IAAMjF,GAAC,GAAGvC,SAAS,EAAnB;EAEA;;;;;;;EAOA;;EACA,IAAIuC,GAAJ,EAAO;EACL,MAAMqG,kBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,IAAL,CAA3B;EACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,IAAL,IAAaY,KAAK,CAACe,eAAnB;EACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,IAAL,EAAWiC,WAAX,GAAyBrB,KAAzB;;EACAjF,EAAAA,GAAC,CAACc,EAAF,CAAKuD,IAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,IAAL,IAAagC,kBAAb;EACA,WAAOpB,KAAK,CAACe,eAAb;EACD,GAHD;EAID;;ECjKD;;;;;;EAMA,IAAM3B,MAAI,GAAG,QAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,WAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAM+B,iBAAiB,GAAG,QAA1B;EAEA,IAAMC,oBAAoB,GAAG,wBAA7B;EAEA,IAAM5B,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA;;;;;;MAMMiC;EACJ,kBAAY/M,OAAZ,EAAqB;EACnB,SAAKuL,QAAL,GAAgBvL,OAAhB;EACA2E,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;EACD;;;;;EAQD;WAEAoC,SAAA,kBAAS;EACP;EACA,SAAKzB,QAAL,CAAc0B,YAAd,CAA2B,cAA3B,EAA2C,KAAK1B,QAAL,CAAcS,SAAd,CAAwBgB,MAAxB,CAA+BH,iBAA/B,CAA3C;EACD;;WAEDf,UAAA,mBAAU;EACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;EACA,SAAKW,QAAL,GAAgB,IAAhB;EACD;;;WAIMc,kBAAP,yBAAuBpK,MAAvB,EAA+B;EAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;EAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;EAEA,UAAI,CAACrG,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIwI,MAAJ,CAAW,IAAX,CAAP;EACD;;EAED,UAAI9K,MAAM,KAAK,QAAf,EAAyB;EACvBsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;WAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;EAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;EACD;;;;0BAlCoB;EACnB,aAAOD,SAAP;EACD;;;;;EAmCH;;;;;;;EAMApD,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgD4B,oBAAhD,EAAsE,UAAAzF,KAAK,EAAI;EAC7EA,EAAAA,KAAK,CAAC3B,cAAN;EAEA,MAAMwH,MAAM,GAAG7F,KAAK,CAACQ,MAAN,CAAakE,OAAb,CAAqBe,oBAArB,CAAf;EAEA,MAAIvI,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAaoI,MAAb,EAAqBtC,UAArB,CAAX;;EACA,MAAI,CAACrG,IAAL,EAAW;EACTA,IAAAA,IAAI,GAAG,IAAIwI,MAAJ,CAAWG,MAAX,CAAP;EACD;;EAED3I,EAAAA,IAAI,CAACyI,MAAL;EACD,CAXD;EAaA,IAAM3G,GAAC,GAAGvC,SAAS,EAAnB;EAEA;;;;;;;EAMA;;EACA,IAAIuC,GAAJ,EAAO;EACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;EACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAaqC,MAAM,CAACV,eAApB;EACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyBI,MAAzB;;EAEA1G,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;EACA,WAAOK,MAAM,CAACV,eAAd;EACD,GAHD;EAID;;ECrHD;;;;;;EAOA,SAASc,aAAT,CAAuBC,GAAvB,EAA4B;EAC1B,MAAIA,GAAG,KAAK,MAAZ,EAAoB;EAClB,WAAO,IAAP;EACD;;EAED,MAAIA,GAAG,KAAK,OAAZ,EAAqB;EACnB,WAAO,KAAP;EACD;;EAED,MAAIA,GAAG,KAAKC,MAAM,CAACD,GAAD,CAAN,CAAYhO,QAAZ,EAAZ,EAAoC;EAClC,WAAOiO,MAAM,CAACD,GAAD,CAAb;EACD;;EAED,MAAIA,GAAG,KAAK,EAAR,IAAcA,GAAG,KAAK,MAA1B,EAAkC;EAChC,WAAO,IAAP;EACD;;EAED,SAAOA,GAAP;EACD;;EAED,SAASE,gBAAT,CAA0BhJ,GAA1B,EAA+B;EAC7B,SAAOA,GAAG,CAAC4B,OAAJ,CAAY,QAAZ,EAAsB,UAAAqH,GAAG;EAAA,iBAAQA,GAAG,CAAChO,WAAJ,EAAR;EAAA,GAAzB,CAAP;EACD;;EAED,IAAMiO,WAAW,GAAG;EAClBC,EAAAA,gBADkB,4BACDzN,OADC,EACQsE,GADR,EACa9B,KADb,EACoB;EACpCxC,IAAAA,OAAO,CAACiN,YAAR,WAA6BK,gBAAgB,CAAChJ,GAAD,CAA7C,EAAsD9B,KAAtD;EACD,GAHiB;EAKlBkL,EAAAA,mBALkB,+BAKE1N,OALF,EAKWsE,GALX,EAKgB;EAChCtE,IAAAA,OAAO,CAACmG,eAAR,WAAgCmH,gBAAgB,CAAChJ,GAAD,CAAhD;EACD,GAPiB;EASlBqJ,EAAAA,iBATkB,6BASA3N,OATA,EASS;EACzB,QAAI,CAACA,OAAL,EAAc;EACZ,aAAO,EAAP;EACD;;EAED,QAAM4N,UAAU,sBACX5N,OAAO,CAAC6N,OADG,CAAhB;;EAIA1L,IAAAA,MAAM,CAACC,IAAP,CAAYwL,UAAZ,EAAwBvL,OAAxB,CAAgC,UAAAiC,GAAG,EAAI;EACrCsJ,MAAAA,UAAU,CAACtJ,GAAD,CAAV,GAAkB6I,aAAa,CAACS,UAAU,CAACtJ,GAAD,CAAX,CAA/B;EACD,KAFD;EAIA,WAAOsJ,UAAP;EACD,GAvBiB;EAyBlBE,EAAAA,gBAzBkB,4BAyBD9N,OAzBC,EAyBQsE,GAzBR,EAyBa;EAC7B,WAAO6I,aAAa,CAACnN,OAAO,CAACE,YAAR,WAA6BoN,gBAAgB,CAAChJ,GAAD,CAA7C,CAAD,CAApB;EACD,GA3BiB;EA6BlByJ,EAAAA,MA7BkB,kBA6BX/N,OA7BW,EA6BF;EACd,QAAMgO,IAAI,GAAGhO,OAAO,CAACiO,qBAAR,EAAb;EAEA,WAAO;EACLC,MAAAA,GAAG,EAAEF,IAAI,CAACE,GAAL,GAAWrO,QAAQ,CAACmE,IAAT,CAAcmK,SADzB;EAELC,MAAAA,IAAI,EAAEJ,IAAI,CAACI,IAAL,GAAYvO,QAAQ,CAACmE,IAAT,CAAcqK;EAF3B,KAAP;EAID,GApCiB;EAsClBC,EAAAA,QAtCkB,oBAsCTtO,OAtCS,EAsCA;EAChB,WAAO;EACLkO,MAAAA,GAAG,EAAElO,OAAO,CAACuO,SADR;EAELH,MAAAA,IAAI,EAAEpO,OAAO,CAACwO;EAFT,KAAP;EAID,GA3CiB;EA6ClBC,EAAAA,WA7CkB,uBA6CNzO,OA7CM,EA6CG0O,SA7CH,EA6Cc;EAC9B,QAAI,CAAC1O,OAAL,EAAc;EACZ;EACD;;EAED,QAAIA,OAAO,CAACgM,SAAR,CAAkBE,QAAlB,CAA2BwC,SAA3B,CAAJ,EAA2C;EACzC1O,MAAAA,OAAO,CAACgM,SAAR,CAAkBC,MAAlB,CAAyByC,SAAzB;EACD,KAFD,MAEO;EACL1O,MAAAA,OAAO,CAACgM,SAAR,CAAkB2C,GAAlB,CAAsBD,SAAtB;EACD;EACF;EAvDiB,CAApB;;EC/BA;;;;;;EASA;;;;;;EAMA,IAAME,SAAS,GAAG,CAAlB;EAEA,IAAMC,cAAc,GAAG;EACrBzI,EAAAA,OADqB,mBACbpG,OADa,EACJC,QADI,EACM;EACzB,WAAOD,OAAO,CAACoG,OAAR,CAAgBnG,QAAhB,CAAP;EACD,GAHoB;EAKrB+E,EAAAA,IALqB,kBAKhB/E,QALgB,EAKND,OALM,EAK8B;EAAA;;EAAA,QAApCA,OAAoC;EAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAACyD,eAAiB;EAAA;;EACjD,WAAO,YAAGwL,MAAH,aAAaC,IAAM,CAAC1P,IAAP,CAAYW,OAAZ,EAAqBC,QAArB,CAAb,CAAP;EACD,GAPoB;EASrBmF,EAAAA,OATqB,qBASbnF,QATa,EASHD,OATG,EASiC;EAAA,QAApCA,OAAoC;EAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAACyD,eAAiB;EAAA;;EACpD,WAAO8B,OAAO,CAAC/F,IAAR,CAAaW,OAAb,EAAsBC,QAAtB,CAAP;EACD,GAXoB;EAarB+O,EAAAA,QAbqB,oBAaZhP,OAbY,EAaHC,QAbG,EAaO;EAAA;;EAC1B,QAAM+O,QAAQ,GAAG,aAAGF,MAAH,cAAa9O,OAAO,CAACgP,QAArB,CAAjB;;EAEA,WAAOA,QAAQ,CAACC,MAAT,CAAgB,UAAAC,KAAK;EAAA,aAAIA,KAAK,CAAC9I,OAAN,CAAcnG,QAAd,CAAJ;EAAA,KAArB,CAAP;EACD,GAjBoB;EAmBrBkP,EAAAA,OAnBqB,mBAmBbnP,OAnBa,EAmBJC,QAnBI,EAmBM;EACzB,QAAMkP,OAAO,GAAG,EAAhB;EAEA,QAAIC,QAAQ,GAAGpP,OAAO,CAACgD,UAAvB;;EAEA,WAAOoM,QAAQ,IAAIA,QAAQ,CAAC/N,QAAT,KAAsBgO,IAAI,CAACC,YAAvC,IAAuDF,QAAQ,CAAC/N,QAAT,KAAsBuN,SAApF,EAA+F;EAC7F,UAAI,KAAKxI,OAAL,CAAagJ,QAAb,EAAuBnP,QAAvB,CAAJ,EAAsC;EACpCkP,QAAAA,OAAO,CAACI,IAAR,CAAaH,QAAb;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACpM,UAApB;EACD;;EAED,WAAOmM,OAAP;EACD,GAjCoB;EAmCrBK,EAAAA,IAnCqB,gBAmChBxP,OAnCgB,EAmCPC,QAnCO,EAmCG;EACtB,QAAIwP,QAAQ,GAAGzP,OAAO,CAAC0P,sBAAvB;;EAEA,WAAOD,QAAP,EAAiB;EACf,UAAIA,QAAQ,CAACrJ,OAAT,CAAiBnG,QAAjB,CAAJ,EAAgC;EAC9B,eAAO,CAACwP,QAAD,CAAP;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB;EACD;;EAED,WAAO,EAAP;EACD,GA/CoB;EAiDrBC,EAAAA,IAjDqB,gBAiDhB3P,OAjDgB,EAiDPC,QAjDO,EAiDG;EACtB,QAAI0P,IAAI,GAAG3P,OAAO,CAAC4P,kBAAnB;;EAEA,WAAOD,IAAP,EAAa;EACX,UAAI,KAAKvJ,OAAL,CAAauJ,IAAb,EAAmB1P,QAAnB,CAAJ,EAAkC;EAChC,eAAO,CAAC0P,IAAD,CAAP;EACD;;EAEDA,MAAAA,IAAI,GAAGA,IAAI,CAACC,kBAAZ;EACD;;EAED,WAAO,EAAP;EACD;EA7DoB,CAAvB;;ECMA;;;;;;EAMA,IAAMlF,MAAI,GAAG,UAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,aAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAM+E,cAAc,GAAG,WAAvB;EACA,IAAMC,eAAe,GAAG,YAAxB;EACA,IAAMC,sBAAsB,GAAG,GAA/B;;EACA,IAAMC,eAAe,GAAG,EAAxB;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,KAHO;EAIdC,EAAAA,KAAK,EAAE,OAJO;EAKdC,EAAAA,IAAI,EAAE,IALQ;EAMdC,EAAAA,KAAK,EAAE;EANO,CAAhB;EASA,IAAMC,WAAW,GAAG;EAClBN,EAAAA,QAAQ,EAAE,kBADQ;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,kBAHW;EAIlBC,EAAAA,KAAK,EAAE,kBAJW;EAKlBC,EAAAA,IAAI,EAAE,SALY;EAMlBC,EAAAA,KAAK,EAAE;EANW,CAApB;EASA,IAAME,cAAc,GAAG,MAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,eAAe,GAAG,OAAxB;EAEA,IAAMC,WAAW,aAAWhG,WAA5B;EACA,IAAMiG,UAAU,YAAUjG,WAA1B;EACA,IAAMkG,aAAa,eAAalG,WAAhC;EACA,IAAMmG,gBAAgB,kBAAgBnG,WAAtC;EACA,IAAMoG,gBAAgB,kBAAgBpG,WAAtC;EACA,IAAMqG,gBAAgB,kBAAgBrG,WAAtC;EACA,IAAMsG,eAAe,iBAAetG,WAApC;EACA,IAAMuG,cAAc,gBAAcvG,WAAlC;EACA,IAAMwG,iBAAiB,mBAAiBxG,WAAxC;EACA,IAAMyG,eAAe,iBAAezG,WAApC;EACA,IAAM0G,gBAAgB,iBAAe1G,WAArC;EACA,IAAM2G,mBAAmB,YAAU3G,WAAV,GAAsBC,cAA/C;EACA,IAAMI,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAM2G,mBAAmB,GAAG,UAA5B;EACA,IAAM5E,mBAAiB,GAAG,QAA1B;EACA,IAAM6E,gBAAgB,GAAG,OAAzB;EACA,IAAMC,gBAAgB,GAAG,qBAAzB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,wBAAwB,GAAG,eAAjC;EAEA,IAAMC,eAAe,GAAG,SAAxB;EACA,IAAMC,oBAAoB,GAAG,uBAA7B;EACA,IAAMC,aAAa,GAAG,gBAAtB;EACA,IAAMC,iBAAiB,GAAG,oBAA1B;EACA,IAAMC,kBAAkB,GAAG,0CAA3B;EACA,IAAMC,mBAAmB,GAAG,sBAA5B;EACA,IAAMC,mBAAmB,GAAG,+BAA5B;EACA,IAAMC,kBAAkB,GAAG,wBAA3B;EAEA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,KAAK,EAAE,OADW;EAElBC,EAAAA,GAAG,EAAE;EAFa,CAApB;EAKA;;;;;;MAKMC;EACJ,oBAAY3S,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAK2Q,MAAL,GAAc,IAAd;EACA,SAAKC,SAAL,GAAiB,IAAjB;EACA,SAAKC,cAAL,GAAsB,IAAtB;EACA,SAAKC,SAAL,GAAiB,KAAjB;EACA,SAAKC,UAAL,GAAkB,KAAlB;EACA,SAAKC,YAAL,GAAoB,IAApB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EAEA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;EACA,SAAKsJ,QAAL,GAAgBvL,OAAhB;EACA,SAAKsT,kBAAL,GAA0BzE,cAAc,CAACzJ,OAAf,CAAuBiN,mBAAvB,EAA4C,KAAK9G,QAAjD,CAA1B;EACA,SAAKgI,eAAL,GAAuB,kBAAkB1T,QAAQ,CAACyD,eAA3B,IAA8CkQ,SAAS,CAACC,cAAV,GAA2B,CAAhG;EACA,SAAKC,aAAL,GAAqB1N,OAAO,CAACvF,MAAM,CAACkT,YAAR,CAA5B;;EAEA,SAAKC,kBAAL;;EACAjP,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEA+E,OAAA,gBAAO;EACL,QAAI,CAAC,KAAKqD,UAAV,EAAsB;EACpB,WAAKa,MAAL,CAAYpD,cAAZ;EACD;EACF;;WAEDqD,kBAAA,2BAAkB;EAChB;EACA;EACA,QAAI,CAACjU,QAAQ,CAACkU,MAAV,IAAoBjR,SAAS,CAAC,KAAKyI,QAAN,CAAjC,EAAkD;EAChD,WAAKoE,IAAL;EACD;EACF;;WAEDH,OAAA,gBAAO;EACL,QAAI,CAAC,KAAKwD,UAAV,EAAsB;EACpB,WAAKa,MAAL,CAAYnD,cAAZ;EACD;EACF;;WAEDL,QAAA,eAAMhJ,KAAN,EAAa;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAK0L,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAIlE,cAAc,CAACzJ,OAAf,CAAuBgN,kBAAvB,EAA2C,KAAK7G,QAAhD,CAAJ,EAA+D;EAC7DtK,MAAAA,oBAAoB,CAAC,KAAKsK,QAAN,CAApB;EACA,WAAKyI,KAAL,CAAW,IAAX;EACD;;EAEDC,IAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;WAEDmB,QAAA,eAAM3M,KAAN,EAAa;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAK0L,SAAL,GAAiB,KAAjB;EACD;;EAED,QAAI,KAAKF,SAAT,EAAoB;EAClBoB,MAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;EACA,WAAKA,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAI,KAAKO,OAAL,IAAgB,KAAKA,OAAL,CAAalD,QAA7B,IAAyC,CAAC,KAAK6C,SAAnD,EAA8D;EAC5D,WAAKF,SAAL,GAAiBqB,WAAW,CAC1B,CAACrU,QAAQ,CAACsU,eAAT,GAA2B,KAAKL,eAAhC,GAAkD,KAAKnE,IAAxD,EAA8DyE,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAKhB,OAAL,CAAalD,QAFa,CAA5B;EAID;EACF;;WAEDmE,KAAA,YAAGC,KAAH,EAAU;EAAA;;EACR,SAAKxB,cAAL,GAAsBjE,cAAc,CAACzJ,OAAf,CAAuB6M,oBAAvB,EAA6C,KAAK1G,QAAlD,CAAtB;;EACA,QAAMgJ,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAK1B,cAAxB,CAApB;;EAEA,QAAIwB,KAAK,GAAG,KAAK1B,MAAL,CAAY7K,MAAZ,GAAqB,CAA7B,IAAkCuM,KAAK,GAAG,CAA9C,EAAiD;EAC/C;EACD;;EAED,QAAI,KAAKtB,UAAT,EAAqB;EACnBzL,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCuF,UAAhC,EAA4C;EAAA,eAAM,KAAI,CAACuD,EAAL,CAAQC,KAAR,CAAN;EAAA,OAA5C;EACA;EACD;;EAED,QAAIC,WAAW,KAAKD,KAApB,EAA2B;EACzB,WAAKjE,KAAL;EACA,WAAK2D,KAAL;EACA;EACD;;EAED,QAAMS,SAAS,GAAGH,KAAK,GAAGC,WAAR,GAChB9D,cADgB,GAEhBC,cAFF;;EAIA,SAAKmD,MAAL,CAAYY,SAAZ,EAAuB,KAAK7B,MAAL,CAAY0B,KAAZ,CAAvB;EACD;;WAEDxI,UAAA,mBAAU;EACRvE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+D,QAAtB,EAAgCV,WAAhC;EACAlG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;EAEA,SAAKgI,MAAL,GAAc,IAAd;EACA,SAAKQ,OAAL,GAAe,IAAf;EACA,SAAK7H,QAAL,GAAgB,IAAhB;EACA,SAAKsH,SAAL,GAAiB,IAAjB;EACA,SAAKE,SAAL,GAAiB,IAAjB;EACA,SAAKC,UAAL,GAAkB,IAAlB;EACA,SAAKF,cAAL,GAAsB,IAAtB;EACA,SAAKQ,kBAAL,GAA0B,IAA1B;EACD;;;WAIDD,aAAA,oBAAWpR,MAAX,EAAmB;EACjBA,IAAAA,MAAM,qCACDgO,OADC,GAEDhO,MAFC,CAAN;EAIAF,IAAAA,eAAe,CAAC2I,MAAD,EAAOzI,MAAP,EAAeuO,WAAf,CAAf;EACA,WAAOvO,MAAP;EACD;;WAEDyS,eAAA,wBAAe;EACb,QAAMC,SAAS,GAAGjV,IAAI,CAACkV,GAAL,CAAS,KAAKzB,WAAd,CAAlB;;EAEA,QAAIwB,SAAS,IAAI3E,eAAjB,EAAkC;EAChC;EACD;;EAED,QAAMyE,SAAS,GAAGE,SAAS,GAAG,KAAKxB,WAAnC;EAEA,SAAKA,WAAL,GAAmB,CAAnB,CATa;;EAYb,QAAIsB,SAAS,GAAG,CAAhB,EAAmB;EACjB,WAAKjF,IAAL;EACD,KAdY;;;EAiBb,QAAIiF,SAAS,GAAG,CAAhB,EAAmB;EACjB,WAAK9E,IAAL;EACD;EACF;;WAEDiE,qBAAA,8BAAqB;EAAA;;EACnB,QAAI,KAAKR,OAAL,CAAajD,QAAjB,EAA2B;EACzB5I,MAAAA,YAAY,CACT+B,EADH,CACM,KAAKiC,QADX,EACqBwF,aADrB,EACoC,UAAA1J,KAAK;EAAA,eAAI,MAAI,CAACwN,QAAL,CAAcxN,KAAd,CAAJ;EAAA,OADzC;EAED;;EAED,QAAI,KAAK+L,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;EAClC9I,MAAAA,YAAY,CACT+B,EADH,CACM,KAAKiC,QADX,EACqByF,gBADrB,EACuC,UAAA3J,KAAK;EAAA,eAAI,MAAI,CAACgJ,KAAL,CAAWhJ,KAAX,CAAJ;EAAA,OAD5C;EAEAE,MAAAA,YAAY,CACT+B,EADH,CACM,KAAKiC,QADX,EACqB0F,gBADrB,EACuC,UAAA5J,KAAK;EAAA,eAAI,MAAI,CAAC2M,KAAL,CAAW3M,KAAX,CAAJ;EAAA,OAD5C;EAED;;EAED,QAAI,KAAK+L,OAAL,CAAa7C,KAAb,IAAsB,KAAKgD,eAA/B,EAAgD;EAC9C,WAAKuB,uBAAL;EACD;EACF;;WAEDA,0BAAA,mCAA0B;EAAA;;EACxB,QAAMC,KAAK,GAAG,SAARA,KAAQ,CAAA1N,KAAK,EAAI;EACrB,UAAI,MAAI,CAACqM,aAAL,IAAsBlB,WAAW,CAACnL,KAAK,CAAC2N,WAAN,CAAkBnS,WAAlB,EAAD,CAArC,EAAwE;EACtE,QAAA,MAAI,CAACqQ,WAAL,GAAmB7L,KAAK,CAAC4N,OAAzB;EACD,OAFD,MAEO,IAAI,CAAC,MAAI,CAACvB,aAAV,EAAyB;EAC9B,QAAA,MAAI,CAACR,WAAL,GAAmB7L,KAAK,CAAC6N,OAAN,CAAc,CAAd,EAAiBD,OAApC;EACD;EACF,KAND;;EAQA,QAAME,IAAI,GAAG,SAAPA,IAAO,CAAA9N,KAAK,EAAI;EACpB;EACA,UAAIA,KAAK,CAAC6N,OAAN,IAAiB7N,KAAK,CAAC6N,OAAN,CAAcnN,MAAd,GAAuB,CAA5C,EAA+C;EAC7C,QAAA,MAAI,CAACoL,WAAL,GAAmB,CAAnB;EACD,OAFD,MAEO;EACL,QAAA,MAAI,CAACA,WAAL,GAAmB9L,KAAK,CAAC6N,OAAN,CAAc,CAAd,EAAiBD,OAAjB,GAA2B,MAAI,CAAC/B,WAAnD;EACD;EACF,KAPD;;EASA,QAAMkC,GAAG,GAAG,SAANA,GAAM,CAAA/N,KAAK,EAAI;EACnB,UAAI,MAAI,CAACqM,aAAL,IAAsBlB,WAAW,CAACnL,KAAK,CAAC2N,WAAN,CAAkBnS,WAAlB,EAAD,CAArC,EAAwE;EACtE,QAAA,MAAI,CAACsQ,WAAL,GAAmB9L,KAAK,CAAC4N,OAAN,GAAgB,MAAI,CAAC/B,WAAxC;EACD;;EAED,MAAA,MAAI,CAACwB,YAAL;;EACA,UAAI,MAAI,CAACtB,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,QAAA,MAAI,CAACA,KAAL;;EACA,YAAI,MAAI,CAAC4C,YAAT,EAAuB;EACrBoC,UAAAA,YAAY,CAAC,MAAI,CAACpC,YAAN,CAAZ;EACD;;EAED,QAAA,MAAI,CAACA,YAAL,GAAoBnR,UAAU,CAAC,UAAAuF,KAAK;EAAA,iBAAI,MAAI,CAAC2M,KAAL,CAAW3M,KAAX,CAAJ;EAAA,SAAN,EAA6B0I,sBAAsB,GAAG,MAAI,CAACqD,OAAL,CAAalD,QAAnE,CAA9B;EACD;EACF,KAtBD;;EAwBArB,IAAAA,cAAc,CAAC7J,IAAf,CAAoBmN,iBAApB,EAAuC,KAAK5G,QAA5C,EAAsDlJ,OAAtD,CAA8D,UAAAiT,OAAO,EAAI;EACvE/N,MAAAA,YAAY,CAAC+B,EAAb,CAAgBgM,OAAhB,EAAyB/D,gBAAzB,EAA2C,UAAAjM,CAAC;EAAA,eAAIA,CAAC,CAACI,cAAF,EAAJ;EAAA,OAA5C;EACD,KAFD;;EAIA,QAAI,KAAKgO,aAAT,EAAwB;EACtBnM,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B8F,iBAA/B,EAAkD,UAAAhK,KAAK;EAAA,eAAI0N,KAAK,CAAC1N,KAAD,CAAT;EAAA,OAAvD;EACAE,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B+F,eAA/B,EAAgD,UAAAjK,KAAK;EAAA,eAAI+N,GAAG,CAAC/N,KAAD,CAAP;EAAA,OAArD;;EAEA,WAAKkE,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BoD,wBAA5B;EACD,KALD,MAKO;EACLxK,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B2F,gBAA/B,EAAiD,UAAA7J,KAAK;EAAA,eAAI0N,KAAK,CAAC1N,KAAD,CAAT;EAAA,OAAtD;EACAE,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B4F,eAA/B,EAAgD,UAAA9J,KAAK;EAAA,eAAI8N,IAAI,CAAC9N,KAAD,CAAR;EAAA,OAArD;EACAE,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B6F,cAA/B,EAA+C,UAAA/J,KAAK;EAAA,eAAI+N,GAAG,CAAC/N,KAAD,CAAP;EAAA,OAApD;EACD;EACF;;WAEDwN,WAAA,kBAASxN,KAAT,EAAgB;EACd,QAAI,kBAAkB1E,IAAlB,CAAuB0E,KAAK,CAACQ,MAAN,CAAa0N,OAApC,CAAJ,EAAkD;EAChD;EACD;;EAED,YAAQlO,KAAK,CAAC/C,GAAd;EACE,WAAKuL,cAAL;EACExI,QAAAA,KAAK,CAAC3B,cAAN;EACA,aAAK8J,IAAL;EACA;;EACF,WAAKM,eAAL;EACEzI,QAAAA,KAAK,CAAC3B,cAAN;EACA,aAAKiK,IAAL;EACA;EARJ;EAWD;;WAED6E,gBAAA,uBAAcxU,OAAd,EAAuB;EACrB,SAAK4S,MAAL,GAAc5S,OAAO,IAAIA,OAAO,CAACgD,UAAnB,GACZ6L,cAAc,CAAC7J,IAAf,CAAoBkN,aAApB,EAAmClS,OAAO,CAACgD,UAA3C,CADY,GAEZ,EAFF;EAIA,WAAO,KAAK4P,MAAL,CAAY/J,OAAZ,CAAoB7I,OAApB,CAAP;EACD;;WAEDwV,sBAAA,6BAAoBf,SAApB,EAA+BgB,aAA/B,EAA8C;EAC5C,QAAMC,eAAe,GAAGjB,SAAS,KAAKhE,cAAtC;EACA,QAAMkF,eAAe,GAAGlB,SAAS,KAAK/D,cAAtC;;EACA,QAAM6D,WAAW,GAAG,KAAKC,aAAL,CAAmBiB,aAAnB,CAApB;;EACA,QAAMG,aAAa,GAAG,KAAKhD,MAAL,CAAY7K,MAAZ,GAAqB,CAA3C;EACA,QAAM8N,aAAa,GAAIF,eAAe,IAAIpB,WAAW,KAAK,CAApC,IACGmB,eAAe,IAAInB,WAAW,KAAKqB,aAD5D;;EAGA,QAAIC,aAAa,IAAI,CAAC,KAAKzC,OAAL,CAAa9C,IAAnC,EAAyC;EACvC,aAAOmF,aAAP;EACD;;EAED,QAAMK,KAAK,GAAGrB,SAAS,KAAK/D,cAAd,GAA+B,CAAC,CAAhC,GAAoC,CAAlD;EACA,QAAMqF,SAAS,GAAG,CAACxB,WAAW,GAAGuB,KAAf,IAAwB,KAAKlD,MAAL,CAAY7K,MAAtD;EAEA,WAAOgO,SAAS,KAAK,CAAC,CAAf,GACL,KAAKnD,MAAL,CAAY,KAAKA,MAAL,CAAY7K,MAAZ,GAAqB,CAAjC,CADK,GAEL,KAAK6K,MAAL,CAAYmD,SAAZ,CAFF;EAGD;;WAEDC,qBAAA,4BAAmBC,aAAnB,EAAkCC,kBAAlC,EAAsD;EACpD,QAAMC,WAAW,GAAG,KAAK3B,aAAL,CAAmByB,aAAnB,CAApB;;EACA,QAAMG,SAAS,GAAG,KAAK5B,aAAL,CAAmB3F,cAAc,CAACzJ,OAAf,CAAuB6M,oBAAvB,EAA6C,KAAK1G,QAAlD,CAAnB,CAAlB;;EAEA,WAAOhE,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCsF,WAApC,EAAiD;EACtDoF,MAAAA,aAAa,EAAbA,aADsD;EAEtDxB,MAAAA,SAAS,EAAEyB,kBAF2C;EAGtDG,MAAAA,IAAI,EAAED,SAHgD;EAItD/B,MAAAA,EAAE,EAAE8B;EAJkD,KAAjD,CAAP;EAMD;;WAEDG,6BAAA,oCAA2BtW,OAA3B,EAAoC;EAClC,QAAI,KAAKsT,kBAAT,EAA6B;EAC3B,UAAMiD,UAAU,GAAG1H,cAAc,CAAC7J,IAAf,CAAoBgN,eAApB,EAAqC,KAAKsB,kBAA1C,CAAnB;;EACA,WAAK,IAAIxL,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGyO,UAAU,CAACxO,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;EAC1CyO,QAAAA,UAAU,CAACzO,CAAD,CAAV,CAAckE,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;EACD;;EAED,UAAM2J,aAAa,GAAG,KAAKlD,kBAAL,CAAwBtE,QAAxB,CACpB,KAAKwF,aAAL,CAAmBxU,OAAnB,CADoB,CAAtB;;EAIA,UAAIwW,aAAJ,EAAmB;EACjBA,QAAAA,aAAa,CAACxK,SAAd,CAAwB2C,GAAxB,CAA4B9B,mBAA5B;EACD;EACF;EACF;;WAEDgH,SAAA,gBAAOY,SAAP,EAAkBzU,OAAlB,EAA2B;EAAA;;EACzB,QAAMyV,aAAa,GAAG5G,cAAc,CAACzJ,OAAf,CAAuB6M,oBAAvB,EAA6C,KAAK1G,QAAlD,CAAtB;;EACA,QAAMkL,kBAAkB,GAAG,KAAKjC,aAAL,CAAmBiB,aAAnB,CAA3B;;EACA,QAAMiB,WAAW,GAAG1W,OAAO,IAAKyV,aAAa,IAC3C,KAAKD,mBAAL,CAAyBf,SAAzB,EAAoCgB,aAApC,CADF;;EAGA,QAAMkB,gBAAgB,GAAG,KAAKnC,aAAL,CAAmBkC,WAAnB,CAAzB;;EACA,QAAME,SAAS,GAAG5Q,OAAO,CAAC,KAAK6M,SAAN,CAAzB;EAEA,QAAIgE,oBAAJ;EACA,QAAIC,cAAJ;EACA,QAAIZ,kBAAJ;;EAEA,QAAIzB,SAAS,KAAKhE,cAAlB,EAAkC;EAChCoG,MAAAA,oBAAoB,GAAGjF,eAAvB;EACAkF,MAAAA,cAAc,GAAGjF,eAAjB;EACAqE,MAAAA,kBAAkB,GAAGvF,cAArB;EACD,KAJD,MAIO;EACLkG,MAAAA,oBAAoB,GAAGlF,gBAAvB;EACAmF,MAAAA,cAAc,GAAGhF,eAAjB;EACAoE,MAAAA,kBAAkB,GAAGtF,eAArB;EACD;;EAED,QAAI8F,WAAW,IAAIA,WAAW,CAAC1K,SAAZ,CAAsBE,QAAtB,CAA+BW,mBAA/B,CAAnB,EAAsE;EACpE,WAAKmG,UAAL,GAAkB,KAAlB;EACA;EACD;;EAED,QAAM+D,UAAU,GAAG,KAAKf,kBAAL,CAAwBU,WAAxB,EAAqCR,kBAArC,CAAnB;;EACA,QAAIa,UAAU,CAACpR,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAI,CAAC8P,aAAD,IAAkB,CAACiB,WAAvB,EAAoC;EAClC;EACA;EACD;;EAED,SAAK1D,UAAL,GAAkB,IAAlB;;EAEA,QAAI4D,SAAJ,EAAe;EACb,WAAKvG,KAAL;EACD;;EAED,SAAKiG,0BAAL,CAAgCI,WAAhC;;EAEA,QAAI,KAAKnL,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCwF,gBAAjC,CAAJ,EAAwD;EACtDgF,MAAAA,WAAW,CAAC1K,SAAZ,CAAsB2C,GAAtB,CAA0BmI,cAA1B;EAEAlT,MAAAA,MAAM,CAAC8S,WAAD,CAAN;EAEAjB,MAAAA,aAAa,CAACzJ,SAAd,CAAwB2C,GAAxB,CAA4BkI,oBAA5B;EACAH,MAAAA,WAAW,CAAC1K,SAAZ,CAAsB2C,GAAtB,CAA0BkI,oBAA1B;EAEA,UAAMG,mBAAmB,GAAGC,QAAQ,CAACP,WAAW,CAACxW,YAAZ,CAAyB,eAAzB,CAAD,EAA4C,EAA5C,CAApC;;EACA,UAAI8W,mBAAJ,EAAyB;EACvB,aAAK5D,OAAL,CAAa8D,eAAb,GAA+B,KAAK9D,OAAL,CAAa8D,eAAb,IAAgC,KAAK9D,OAAL,CAAalD,QAA5E;EACA,aAAKkD,OAAL,CAAalD,QAAb,GAAwB8G,mBAAxB;EACD,OAHD,MAGO;EACL,aAAK5D,OAAL,CAAalD,QAAb,GAAwB,KAAKkD,OAAL,CAAa8D,eAAb,IAAgC,KAAK9D,OAAL,CAAalD,QAArE;EACD;;EAED,UAAMvP,kBAAkB,GAAGH,gCAAgC,CAACiV,aAAD,CAA3D;EAEAlO,MAAAA,YAAY,CACTgC,GADH,CACOkM,aADP,EACsBzW,cADtB,EACsC,YAAM;EACxC0X,QAAAA,WAAW,CAAC1K,SAAZ,CAAsBC,MAAtB,CAA6B4K,oBAA7B,EAAmDC,cAAnD;EACAJ,QAAAA,WAAW,CAAC1K,SAAZ,CAAsB2C,GAAtB,CAA0B9B,mBAA1B;EAEA4I,QAAAA,aAAa,CAACzJ,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B,EAAkDiK,cAAlD,EAAkED,oBAAlE;EAEA,QAAA,MAAI,CAAC7D,UAAL,GAAkB,KAAlB;EAEAlR,QAAAA,UAAU,CAAC,YAAM;EACfyF,UAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAACyB,QAA1B,EAAoCuF,UAApC,EAAgD;EAC9CmF,YAAAA,aAAa,EAAES,WAD+B;EAE9CjC,YAAAA,SAAS,EAAEyB,kBAFmC;EAG9CG,YAAAA,IAAI,EAAEI,kBAHwC;EAI9CpC,YAAAA,EAAE,EAAEsC;EAJ0C,WAAhD;EAMD,SAPS,EAOP,CAPO,CAAV;EAQD,OAjBH;EAmBArV,MAAAA,oBAAoB,CAACmU,aAAD,EAAgB9U,kBAAhB,CAApB;EACD,KAtCD,MAsCO;EACL8U,MAAAA,aAAa,CAACzJ,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;EACA6J,MAAAA,WAAW,CAAC1K,SAAZ,CAAsB2C,GAAtB,CAA0B9B,mBAA1B;EAEA,WAAKmG,UAAL,GAAkB,KAAlB;EACAzL,MAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCuF,UAApC,EAAgD;EAC9CmF,QAAAA,aAAa,EAAES,WAD+B;EAE9CjC,QAAAA,SAAS,EAAEyB,kBAFmC;EAG9CG,QAAAA,IAAI,EAAEI,kBAHwC;EAI9CpC,QAAAA,EAAE,EAAEsC;EAJ0C,OAAhD;EAMD;;EAED,QAAIC,SAAJ,EAAe;EACb,WAAK5C,KAAL;EACD;EACF;;;aAIMmD,oBAAP,2BAAyBnX,OAAzB,EAAkCiC,MAAlC,EAA0C;EACxC,QAAIsC,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAX;;EACA,QAAIwI,OAAO,qCACNnD,OADM,GAENzC,WAAW,CAACG,iBAAZ,CAA8B3N,OAA9B,CAFM,CAAX;;EAKA,QAAI,OAAOiC,MAAP,KAAkB,QAAtB,EAAgC;EAC9BmR,MAAAA,OAAO,qCACFA,OADE,GAEFnR,MAFE,CAAP;EAID;;EAED,QAAMmV,MAAM,GAAG,OAAOnV,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCmR,OAAO,CAAChD,KAA7D;;EAEA,QAAI,CAAC7L,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIoO,QAAJ,CAAa3S,OAAb,EAAsBoT,OAAtB,CAAP;EACD;;EAED,QAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;EAC9BsC,MAAAA,IAAI,CAAC8P,EAAL,CAAQpS,MAAR;EACD,KAFD,MAEO,IAAI,OAAOmV,MAAP,KAAkB,QAAtB,EAAgC;EACrC,UAAI,OAAO7S,IAAI,CAAC6S,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIC,SAAJ,wBAAkCD,MAAlC,QAAN;EACD;;EAED7S,MAAAA,IAAI,CAAC6S,MAAD,CAAJ;EACD,KANM,MAMA,IAAIhE,OAAO,CAAClD,QAAR,IAAoBkD,OAAO,CAACkE,IAAhC,EAAsC;EAC3C/S,MAAAA,IAAI,CAAC8L,KAAL;EACA9L,MAAAA,IAAI,CAACyP,KAAL;EACD;EACF;;aAEM3H,kBAAP,yBAAuBpK,MAAvB,EAA+B;EAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;EAC3BqG,MAAAA,QAAQ,CAACwE,iBAAT,CAA2B,IAA3B,EAAiClV,MAAjC;EACD,KAFM,CAAP;EAGD;;aAEMsV,sBAAP,6BAA2BlQ,KAA3B,EAAkC;EAChC,QAAMQ,MAAM,GAAGtH,sBAAsB,CAAC,IAAD,CAArC;;EAEA,QAAI,CAACsH,MAAD,IAAW,CAACA,MAAM,CAACmE,SAAP,CAAiBE,QAAjB,CAA0BuF,mBAA1B,CAAhB,EAAgE;EAC9D;EACD;;EAED,QAAMxP,MAAM,qCACPuL,WAAW,CAACG,iBAAZ,CAA8B9F,MAA9B,CADO,GAEP2F,WAAW,CAACG,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;EAIA,QAAM6J,UAAU,GAAG,KAAKtX,YAAL,CAAkB,eAAlB,CAAnB;;EAEA,QAAIsX,UAAJ,EAAgB;EACdvV,MAAAA,MAAM,CAACiO,QAAP,GAAkB,KAAlB;EACD;;EAEDyC,IAAAA,QAAQ,CAACwE,iBAAT,CAA2BtP,MAA3B,EAAmC5F,MAAnC;;EAEA,QAAIuV,UAAJ,EAAgB;EACd7S,MAAAA,IAAI,CAACG,OAAL,CAAa+C,MAAb,EAAqB+C,UAArB,EAA+ByJ,EAA/B,CAAkCmD,UAAlC;EACD;;EAEDnQ,IAAAA,KAAK,CAAC3B,cAAN;EACD;;aAEM+G,cAAP,qBAAmBzM,OAAnB,EAA4B;EAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;EACD;;;;0BA1coB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOsF,OAAP;EACD;;;;;EAucH;;;;;;;EAMA1I,YAAY,CACT+B,EADH,CACMzJ,QADN,EACgBqL,sBADhB,EACsCoH,mBADtC,EAC2DK,QAAQ,CAAC4E,mBADpE;EAGAhQ,YAAY,CAAC+B,EAAb,CAAgB7I,MAAhB,EAAwB+Q,mBAAxB,EAA6C,YAAM;EACjD,MAAMiG,SAAS,GAAG5I,cAAc,CAAC7J,IAAf,CAAoBuN,kBAApB,CAAlB;;EAEA,OAAK,IAAIzK,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGqP,SAAS,CAAC1P,MAAhC,EAAwCD,CAAC,GAAGM,GAA5C,EAAiDN,CAAC,EAAlD,EAAsD;EACpD6K,IAAAA,QAAQ,CAACwE,iBAAT,CAA2BM,SAAS,CAAC3P,CAAD,CAApC,EAAyCnD,IAAI,CAACG,OAAL,CAAa2S,SAAS,CAAC3P,CAAD,CAAtB,EAA2B8C,UAA3B,CAAzC;EACD;EACF,CAND;EAQA,IAAMvE,GAAC,GAAGvC,SAAS,EAAnB;EAEA;;;;;;;EAMA;;EACA,IAAIuC,GAAJ,EAAO;EACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;EACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAaiI,QAAQ,CAACtG,eAAtB;EACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyBgG,QAAzB;;EACAtM,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;EACA,WAAOiG,QAAQ,CAACtG,eAAhB;EACD,GAHD;EAID;;ECxlBD;;;;;;EAMA,IAAM3B,MAAI,GAAG,UAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,aAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAMmF,SAAO,GAAG;EACdjD,EAAAA,MAAM,EAAE,IADM;EAEd0K,EAAAA,MAAM,EAAE;EAFM,CAAhB;EAKA,IAAMlH,aAAW,GAAG;EAClBxD,EAAAA,MAAM,EAAE,SADU;EAElB0K,EAAAA,MAAM,EAAE;EAFU,CAApB;EAKA,IAAMC,UAAU,YAAU9M,WAA1B;EACA,IAAM+M,WAAW,aAAW/M,WAA5B;EACA,IAAMgN,UAAU,YAAUhN,WAA1B;EACA,IAAMiN,YAAY,cAAYjN,WAA9B;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAMiN,eAAe,GAAG,MAAxB;EACA,IAAMC,mBAAmB,GAAG,UAA5B;EACA,IAAMC,qBAAqB,GAAG,YAA9B;EACA,IAAMC,oBAAoB,GAAG,WAA7B;EAEA,IAAMC,KAAK,GAAG,OAAd;EACA,IAAMC,MAAM,GAAG,QAAf;EAEA,IAAMC,gBAAgB,GAAG,oBAAzB;EACA,IAAMvL,sBAAoB,GAAG,0BAA7B;EAEA;;;;;;MAMMwL;EACJ,oBAAYtY,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAKsW,gBAAL,GAAwB,KAAxB;EACA,SAAKhN,QAAL,GAAgBvL,OAAhB;EACA,SAAKoT,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;EACA,SAAKuW,aAAL,GAAqB3J,cAAc,CAAC7J,IAAf,CAChB8H,sBAAH,iBAAkC9M,OAAO,CAACoE,EAA1C,aACG0I,sBADH,wBACyC9M,OAAO,CAACoE,EADjD,SADmB,CAArB;EAKA,QAAMqU,UAAU,GAAG5J,cAAc,CAAC7J,IAAf,CAAoB8H,sBAApB,CAAnB;;EAEA,SAAK,IAAIhF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGqQ,UAAU,CAAC1Q,MAAjC,EAAyCD,CAAC,GAAGM,GAA7C,EAAkDN,CAAC,EAAnD,EAAuD;EACrD,UAAM4Q,IAAI,GAAGD,UAAU,CAAC3Q,CAAD,CAAvB;EACA,UAAM7H,QAAQ,GAAGI,sBAAsB,CAACqY,IAAD,CAAvC;EACA,UAAMC,aAAa,GAAG9J,cAAc,CAAC7J,IAAf,CAAoB/E,QAApB,EACnBgP,MADmB,CACZ,UAAA2J,SAAS;EAAA,eAAIA,SAAS,KAAK5Y,OAAlB;EAAA,OADG,CAAtB;;EAGA,UAAIC,QAAQ,KAAK,IAAb,IAAqB0Y,aAAa,CAAC5Q,MAAvC,EAA+C;EAC7C,aAAK8Q,SAAL,GAAiB5Y,QAAjB;;EACA,aAAKuY,aAAL,CAAmBjJ,IAAnB,CAAwBmJ,IAAxB;EACD;EACF;;EAED,SAAKI,OAAL,GAAe,KAAK1F,OAAL,CAAasE,MAAb,GAAsB,KAAKqB,UAAL,EAAtB,GAA0C,IAAzD;;EAEA,QAAI,CAAC,KAAK3F,OAAL,CAAasE,MAAlB,EAA0B;EACxB,WAAKsB,yBAAL,CAA+B,KAAKzN,QAApC,EAA8C,KAAKiN,aAAnD;EACD;;EAED,QAAI,KAAKpF,OAAL,CAAapG,MAAjB,EAAyB;EACvB,WAAKA,MAAL;EACD;;EAEDrI,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEAoC,SAAA,kBAAS;EACP,QAAI,KAAKzB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,eAAjC,CAAJ,EAAuD;EACrD,WAAKkB,IAAL;EACD,KAFD,MAEO;EACL,WAAKC,IAAL;EACD;EACF;;WAEDA,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAKX,gBAAL,IACF,KAAKhN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,eAAjC,CADF,EACqD;EACnD;EACD;;EAED,QAAIoB,OAAJ;EACA,QAAIC,WAAJ;;EAEA,QAAI,KAAKN,OAAT,EAAkB;EAChBK,MAAAA,OAAO,GAAGtK,cAAc,CAAC7J,IAAf,CAAoBqT,gBAApB,EAAsC,KAAKS,OAA3C,EACP7J,MADO,CACA,UAAAyJ,IAAI,EAAI;EACd,YAAI,OAAO,KAAI,CAACtF,OAAL,CAAasE,MAApB,KAA+B,QAAnC,EAA6C;EAC3C,iBAAOgB,IAAI,CAACxY,YAAL,CAAkB,aAAlB,MAAqC,KAAI,CAACkT,OAAL,CAAasE,MAAzD;EACD;;EAED,eAAOgB,IAAI,CAAC1M,SAAL,CAAeE,QAAf,CAAwB8L,mBAAxB,CAAP;EACD,OAPO,CAAV;;EASA,UAAImB,OAAO,CAACpR,MAAR,KAAmB,CAAvB,EAA0B;EACxBoR,QAAAA,OAAO,GAAG,IAAV;EACD;EACF;;EAED,QAAME,SAAS,GAAGxK,cAAc,CAACzJ,OAAf,CAAuB,KAAKyT,SAA5B,CAAlB;;EACA,QAAIM,OAAJ,EAAa;EACX,UAAMG,cAAc,GAAGH,OAAO,CAAClK,MAAR,CAAe,UAAAyJ,IAAI;EAAA,eAAIW,SAAS,KAAKX,IAAlB;EAAA,OAAnB,CAAvB;EACAU,MAAAA,WAAW,GAAGE,cAAc,CAAC,CAAD,CAAd,GAAoB3U,IAAI,CAACG,OAAL,CAAawU,cAAc,CAAC,CAAD,CAA3B,EAAgC1O,UAAhC,CAApB,GAAgE,IAA9E;;EAEA,UAAIwO,WAAW,IAAIA,WAAW,CAACb,gBAA/B,EAAiD;EAC/C;EACD;EACF;;EAED,QAAMgB,UAAU,GAAGhS,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCoM,UAApC,CAAnB;;EACA,QAAI4B,UAAU,CAAC5T,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAIwT,OAAJ,EAAa;EACXA,MAAAA,OAAO,CAAC9W,OAAR,CAAgB,UAAAmX,UAAU,EAAI;EAC5B,YAAIH,SAAS,KAAKG,UAAlB,EAA8B;EAC5BlB,UAAAA,QAAQ,CAACmB,iBAAT,CAA2BD,UAA3B,EAAuC,MAAvC;EACD;;EAED,YAAI,CAACJ,WAAL,EAAkB;EAChBzU,UAAAA,IAAI,CAACC,OAAL,CAAa4U,UAAb,EAAyB5O,UAAzB,EAAmC,IAAnC;EACD;EACF,OARD;EASD;;EAED,QAAM8O,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKpO,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B+L,mBAA/B;;EACA,SAAKzM,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BsJ,qBAA5B;;EAEA,SAAK1M,QAAL,CAAcxI,KAAd,CAAoB2W,SAApB,IAAiC,CAAjC;;EAEA,QAAI,KAAKlB,aAAL,CAAmBzQ,MAAvB,EAA+B;EAC7B,WAAKyQ,aAAL,CAAmBnW,OAAnB,CAA2B,UAAArC,OAAO,EAAI;EACpCA,QAAAA,OAAO,CAACgM,SAAR,CAAkBC,MAAlB,CAAyBiM,oBAAzB;EACAlY,QAAAA,OAAO,CAACiN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD,OAHD;EAID;;EAED,SAAK2M,gBAAL,CAAsB,IAAtB;;EAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,KAAI,CAACtO,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BgM,qBAA/B;;EACA,MAAA,KAAI,CAAC1M,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BqJ,mBAA5B,EAAiDD,eAAjD;;EAEA,MAAA,KAAI,CAACxM,QAAL,CAAcxI,KAAd,CAAoB2W,SAApB,IAAiC,EAAjC;;EAEA,MAAA,KAAI,CAACE,gBAAL,CAAsB,KAAtB;;EAEArS,MAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAI,CAACyB,QAA1B,EAAoCqM,WAApC;EACD,KATD;;EAWA,QAAMkC,oBAAoB,GAAGJ,SAAS,CAAC,CAAD,CAAT,CAAa7W,WAAb,KAA6B6W,SAAS,CAAC9P,KAAV,CAAgB,CAAhB,CAA1D;EACA,QAAMmQ,UAAU,cAAYD,oBAA5B;EACA,QAAMnZ,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK+K,QAAN,CAA3D;EAEAhE,IAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD6a,QAAhD;EAEAvY,IAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgB5K,kBAAhB,CAApB;EACA,SAAK4K,QAAL,CAAcxI,KAAd,CAAoB2W,SAApB,IAAoC,KAAKnO,QAAL,CAAcwO,UAAd,CAApC;EACD;;WAEDd,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAKV,gBAAL,IACF,CAAC,KAAKhN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,eAAjC,CADH,EACsD;EACpD;EACD;;EAED,QAAMwB,UAAU,GAAGhS,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCsM,UAApC,CAAnB;;EACA,QAAI0B,UAAU,CAAC5T,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAM+T,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKpO,QAAL,CAAcxI,KAAd,CAAoB2W,SAApB,IAAoC,KAAKnO,QAAL,CAAc0C,qBAAd,GAAsCyL,SAAtC,CAApC;EAEA9V,IAAAA,MAAM,CAAC,KAAK2H,QAAN,CAAN;;EAEA,SAAKA,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BsJ,qBAA5B;;EACA,SAAK1M,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B+L,mBAA/B,EAAoDD,eAApD;;EAEA,QAAMiC,kBAAkB,GAAG,KAAKxB,aAAL,CAAmBzQ,MAA9C;;EACA,QAAIiS,kBAAkB,GAAG,CAAzB,EAA4B;EAC1B,WAAK,IAAIlS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkS,kBAApB,EAAwClS,CAAC,EAAzC,EAA6C;EAC3C,YAAMgC,OAAO,GAAG,KAAK0O,aAAL,CAAmB1Q,CAAnB,CAAhB;EACA,YAAM4Q,IAAI,GAAGnY,sBAAsB,CAACuJ,OAAD,CAAnC;;EAEA,YAAI4O,IAAI,IAAI,CAACA,IAAI,CAAC1M,SAAL,CAAeE,QAAf,CAAwB6L,eAAxB,CAAb,EAAuD;EACrDjO,UAAAA,OAAO,CAACkC,SAAR,CAAkB2C,GAAlB,CAAsBuJ,oBAAtB;EACApO,UAAAA,OAAO,CAACmD,YAAR,CAAqB,eAArB,EAAsC,KAAtC;EACD;EACF;EACF;;EAED,SAAK2M,gBAAL,CAAsB,IAAtB;;EAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAACD,gBAAL,CAAsB,KAAtB;;EACA,MAAA,MAAI,CAACrO,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BgM,qBAA/B;;EACA,MAAA,MAAI,CAAC1M,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BqJ,mBAA5B;;EACAzQ,MAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAACyB,QAA1B,EAAoCuM,YAApC;EACD,KALD;;EAOA,SAAKvM,QAAL,CAAcxI,KAAd,CAAoB2W,SAApB,IAAiC,EAAjC;EACA,QAAM/Y,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK+K,QAAN,CAA3D;EAEAhE,IAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD6a,QAAhD;EACAvY,IAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgB5K,kBAAhB,CAApB;EACD;;WAEDiZ,mBAAA,0BAAiBK,eAAjB,EAAkC;EAChC,SAAK1B,gBAAL,GAAwB0B,eAAxB;EACD;;WAEDnO,UAAA,mBAAU;EACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;EAEA,SAAKwI,OAAL,GAAe,IAAf;EACA,SAAK0F,OAAL,GAAe,IAAf;EACA,SAAKvN,QAAL,GAAgB,IAAhB;EACA,SAAKiN,aAAL,GAAqB,IAArB;EACA,SAAKD,gBAAL,GAAwB,IAAxB;EACD;;;WAIDlF,aAAA,oBAAWpR,MAAX,EAAmB;EACjBA,IAAAA,MAAM,qCACDgO,SADC,GAEDhO,MAFC,CAAN;EAIAA,IAAAA,MAAM,CAAC+K,MAAP,GAAgBhH,OAAO,CAAC/D,MAAM,CAAC+K,MAAR,CAAvB,CALiB;;EAMjBjL,IAAAA,eAAe,CAAC2I,MAAD,EAAOzI,MAAP,EAAeuO,aAAf,CAAf;EACA,WAAOvO,MAAP;EACD;;WAED0X,gBAAA,yBAAgB;EACd,QAAMO,QAAQ,GAAG,KAAK3O,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCiM,KAAjC,CAAjB;;EACA,WAAO+B,QAAQ,GAAG/B,KAAH,GAAWC,MAA1B;EACD;;WAEDW,aAAA,sBAAa;EAAA;;EAAA,QACLrB,MADK,GACM,KAAKtE,OADX,CACLsE,MADK;;EAGX,QAAItW,SAAS,CAACsW,MAAD,CAAb,EAAuB;EACrB;EACA,UAAI,OAAOA,MAAM,CAACyC,MAAd,KAAyB,WAAzB,IAAwC,OAAOzC,MAAM,CAAC,CAAD,CAAb,KAAqB,WAAjE,EAA8E;EAC5EA,QAAAA,MAAM,GAAGA,MAAM,CAAC,CAAD,CAAf;EACD;EACF,KALD,MAKO;EACLA,MAAAA,MAAM,GAAG7I,cAAc,CAACzJ,OAAf,CAAuBsS,MAAvB,CAAT;EACD;;EAED,QAAMzX,QAAQ,GAAM6M,sBAAN,uBAA2C4K,MAA3C,QAAd;EAEA7I,IAAAA,cAAc,CAAC7J,IAAf,CAAoB/E,QAApB,EAA8ByX,MAA9B,EACGrV,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,UAAMoa,QAAQ,GAAG7Z,sBAAsB,CAACP,OAAD,CAAvC;;EAEA,MAAA,MAAI,CAACgZ,yBAAL,CACEoB,QADF,EAEE,CAACpa,OAAD,CAFF;EAID,KARH;EAUA,WAAO0X,MAAP;EACD;;WAEDsB,4BAAA,mCAA0BhZ,OAA1B,EAAmCqa,YAAnC,EAAiD;EAC/C,QAAIra,OAAJ,EAAa;EACX,UAAMsa,MAAM,GAAGta,OAAO,CAACgM,SAAR,CAAkBE,QAAlB,CAA2B6L,eAA3B,CAAf;;EAEA,UAAIsC,YAAY,CAACtS,MAAjB,EAAyB;EACvBsS,QAAAA,YAAY,CAAChY,OAAb,CAAqB,UAAAqW,IAAI,EAAI;EAC3B,cAAI4B,MAAJ,EAAY;EACV5B,YAAAA,IAAI,CAAC1M,SAAL,CAAeC,MAAf,CAAsBiM,oBAAtB;EACD,WAFD,MAEO;EACLQ,YAAAA,IAAI,CAAC1M,SAAL,CAAe2C,GAAf,CAAmBuJ,oBAAnB;EACD;;EAEDQ,UAAAA,IAAI,CAACzL,YAAL,CAAkB,eAAlB,EAAmCqN,MAAnC;EACD,SARD;EASD;EACF;EACF;;;aAIMb,oBAAP,2BAAyBzZ,OAAzB,EAAkCiC,MAAlC,EAA0C;EACxC,QAAIsC,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAX;;EACA,QAAMwI,OAAO,oDACRnD,SADQ,GAERzC,WAAW,CAACG,iBAAZ,CAA8B3N,OAA9B,CAFQ,GAGR,OAAOiC,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHxC,CAAb;;EAMA,QAAI,CAACsC,IAAD,IAAS6O,OAAO,CAACpG,MAAjB,IAA2B,OAAO/K,MAAP,KAAkB,QAA7C,IAAyD,YAAYU,IAAZ,CAAiBV,MAAjB,CAA7D,EAAuF;EACrFmR,MAAAA,OAAO,CAACpG,MAAR,GAAiB,KAAjB;EACD;;EAED,QAAI,CAACzI,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAI+T,QAAJ,CAAatY,OAAb,EAAsBoT,OAAtB,CAAP;EACD;;EAED,QAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;EACD;;EAEDsC,MAAAA,IAAI,CAACtC,MAAD,CAAJ;EACD;EACF;;aAEMoK,kBAAP,yBAAuBpK,MAAvB,EAA+B;EAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;EAC3BgM,MAAAA,QAAQ,CAACmB,iBAAT,CAA2B,IAA3B,EAAiCxX,MAAjC;EACD,KAFM,CAAP;EAGD;;aAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;EAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;EACD;;;;0BA1QoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOsF,SAAP;EACD;;;;;EAuQH;;;;;;;EAMA1I,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUzF,KAAV,EAAiB;EACrF;EACA,MAAIA,KAAK,CAACQ,MAAN,CAAa0N,OAAb,KAAyB,GAA7B,EAAkC;EAChClO,IAAAA,KAAK,CAAC3B,cAAN;EACD;;EAED,MAAM6U,WAAW,GAAG/M,WAAW,CAACG,iBAAZ,CAA8B,IAA9B,CAApB;EACA,MAAM1N,QAAQ,GAAGI,sBAAsB,CAAC,IAAD,CAAvC;EACA,MAAMma,gBAAgB,GAAG3L,cAAc,CAAC7J,IAAf,CAAoB/E,QAApB,CAAzB;EAEAua,EAAAA,gBAAgB,CAACnY,OAAjB,CAAyB,UAAArC,OAAO,EAAI;EAClC,QAAMuE,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAb;EACA,QAAI3I,MAAJ;;EACA,QAAIsC,IAAJ,EAAU;EACR;EACA,UAAIA,IAAI,CAACuU,OAAL,KAAiB,IAAjB,IAAyB,OAAOyB,WAAW,CAAC7C,MAAnB,KAA8B,QAA3D,EAAqE;EACnEnT,QAAAA,IAAI,CAAC6O,OAAL,CAAasE,MAAb,GAAsB6C,WAAW,CAAC7C,MAAlC;EACAnT,QAAAA,IAAI,CAACuU,OAAL,GAAevU,IAAI,CAACwU,UAAL,EAAf;EACD;;EAED9W,MAAAA,MAAM,GAAG,QAAT;EACD,KARD,MAQO;EACLA,MAAAA,MAAM,GAAGsY,WAAT;EACD;;EAEDjC,IAAAA,QAAQ,CAACmB,iBAAT,CAA2BzZ,OAA3B,EAAoCiC,MAApC;EACD,GAhBD;EAiBD,CA3BD;EA6BA,IAAMoE,GAAC,GAAGvC,SAAS,EAAnB;EAEA;;;;;;;EAMA;;EACA,IAAIuC,GAAJ,EAAO;EACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;EACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAa4N,QAAQ,CAACjM,eAAtB;EACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyB2L,QAAzB;;EACAjS,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;EACA,WAAO4L,QAAQ,CAACjM,eAAhB;EACD,GAHD;EAID;;ECvZD;;;;;;EAMA,IAAM3B,MAAI,GAAG,UAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,aAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAM2P,UAAU,GAAG,QAAnB;EACA,IAAMC,SAAS,GAAG,OAAlB;EACA,IAAMC,OAAO,GAAG,KAAhB;EACA,IAAMC,YAAY,GAAG,SAArB;EACA,IAAMC,cAAc,GAAG,WAAvB;EACA,IAAMC,kBAAkB,GAAG,CAA3B;;EAEA,IAAMC,cAAc,GAAG,IAAIrY,MAAJ,CAAckY,YAAd,SAA8BC,cAA9B,SAAgDJ,UAAhD,CAAvB;EAEA,IAAM5C,YAAU,YAAUhN,WAA1B;EACA,IAAMiN,cAAY,cAAYjN,WAA9B;EACA,IAAM8M,YAAU,YAAU9M,WAA1B;EACA,IAAM+M,aAAW,aAAW/M,WAA5B;EACA,IAAMmQ,WAAW,aAAWnQ,WAA5B;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EACA,IAAMmQ,sBAAsB,eAAapQ,WAAb,GAAyBC,cAArD;EACA,IAAMoQ,oBAAoB,aAAWrQ,WAAX,GAAuBC,cAAjD;EAEA,IAAMqQ,mBAAmB,GAAG,UAA5B;EACA,IAAMpD,iBAAe,GAAG,MAAxB;EACA,IAAMqD,iBAAiB,GAAG,QAA1B;EACA,IAAMC,oBAAoB,GAAG,WAA7B;EACA,IAAMC,mBAAmB,GAAG,UAA5B;EACA,IAAMC,oBAAoB,GAAG,qBAA7B;EACA,IAAMC,iBAAiB,GAAG,QAA1B;EACA,IAAMC,0BAA0B,GAAG,iBAAnC;EAEA,IAAM3O,sBAAoB,GAAG,0BAA7B;EACA,IAAM4O,mBAAmB,GAAG,gBAA5B;EACA,IAAMC,aAAa,GAAG,gBAAtB;EACA,IAAMC,mBAAmB,GAAG,aAA5B;EACA,IAAMC,sBAAsB,GAAG,6DAA/B;EAEA,IAAMC,aAAa,GAAG,WAAtB;EACA,IAAMC,gBAAgB,GAAG,SAAzB;EACA,IAAMC,gBAAgB,GAAG,cAAzB;EACA,IAAMC,mBAAmB,GAAG,YAA5B;EACA,IAAMC,eAAe,GAAG,aAAxB;EACA,IAAMC,cAAc,GAAG,YAAvB;EAEA,IAAMlM,SAAO,GAAG;EACdlC,EAAAA,MAAM,EAAE,CADM;EAEdqO,EAAAA,IAAI,EAAE,IAFQ;EAGdC,EAAAA,QAAQ,EAAE,cAHI;EAIdC,EAAAA,SAAS,EAAE,QAJG;EAKdnZ,EAAAA,OAAO,EAAE,SALK;EAMdoZ,EAAAA,YAAY,EAAE;EANA,CAAhB;EASA,IAAM/L,aAAW,GAAG;EAClBzC,EAAAA,MAAM,EAAE,0BADU;EAElBqO,EAAAA,IAAI,EAAE,SAFY;EAGlBC,EAAAA,QAAQ,EAAE,kBAHQ;EAIlBC,EAAAA,SAAS,EAAE,kBAJO;EAKlBnZ,EAAAA,OAAO,EAAE,QALS;EAMlBoZ,EAAAA,YAAY,EAAE;EANI,CAApB;EASA;;;;;;MAMMC;EACJ,oBAAYxc,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAKsJ,QAAL,GAAgBvL,OAAhB;EACA,SAAKyc,OAAL,GAAe,IAAf;EACA,SAAKrJ,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;EACA,SAAKya,KAAL,GAAa,KAAKC,eAAL,EAAb;EACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EAEA,SAAKjJ,kBAAL;;EACAjP,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;EACD;;;;;EAgBD;WAEAoC,SAAA,kBAAS;EACP,QAAI,KAAKzB,QAAL,CAAcuR,QAAd,IAA0B,KAAKvR,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCiP,mBAAjC,CAA9B,EAAqF;EACnF;EACD;;EAED,QAAM4B,QAAQ,GAAG,KAAKxR,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,iBAAjC,CAAjB;;EAEAyE,IAAAA,QAAQ,CAACQ,UAAT;;EAEA,QAAID,QAAJ,EAAc;EACZ;EACD;;EAED,SAAK7D,IAAL;EACD;;WAEDA,OAAA,gBAAO;EACL,QAAI,KAAK3N,QAAL,CAAcuR,QAAd,IAA0B,KAAKvR,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCiP,mBAAjC,CAA1B,IAAmF,KAAKuB,KAAL,CAAW1Q,SAAX,CAAqBE,QAArB,CAA8B6L,iBAA9B,CAAvF,EAAuI;EACrI;EACD;;EAED,QAAML,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8B,KAAK1R,QAAnC,CAAf;EACA,QAAM0K,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAK1K;EADA,KAAtB;EAIA,QAAM2R,SAAS,GAAG3V,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCoM,YAApC,EAAgD1B,aAAhD,CAAlB;;EAEA,QAAIiH,SAAS,CAACvX,gBAAd,EAAgC;EAC9B;EACD,KAdI;;;EAiBL,QAAI,CAAC,KAAKiX,SAAV,EAAqB;EACnB,UAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;EACjC,cAAM,IAAI9F,SAAJ,CAAc,kEAAd,CAAN;EACD;;EAED,UAAI+F,gBAAgB,GAAG,KAAK7R,QAA5B;;EAEA,UAAI,KAAK6H,OAAL,CAAakJ,SAAb,KAA2B,QAA/B,EAAyC;EACvCc,QAAAA,gBAAgB,GAAG1F,MAAnB;EACD,OAFD,MAEO,IAAItW,SAAS,CAAC,KAAKgS,OAAL,CAAakJ,SAAd,CAAb,EAAuC;EAC5Cc,QAAAA,gBAAgB,GAAG,KAAKhK,OAAL,CAAakJ,SAAhC,CAD4C;;EAI5C,YAAI,OAAO,KAAKlJ,OAAL,CAAakJ,SAAb,CAAuBnC,MAA9B,KAAyC,WAA7C,EAA0D;EACxDiD,UAAAA,gBAAgB,GAAG,KAAKhK,OAAL,CAAakJ,SAAb,CAAuB,CAAvB,CAAnB;EACD;EACF,OAhBkB;EAmBnB;EACA;;;EACA,UAAI,KAAKlJ,OAAL,CAAaiJ,QAAb,KAA0B,cAA9B,EAA8C;EAC5C3E,QAAAA,MAAM,CAAC1L,SAAP,CAAiB2C,GAAjB,CAAqB8M,0BAArB;EACD;;EAED,WAAKgB,OAAL,GAAe,IAAIU,MAAJ,CAAWC,gBAAX,EAA6B,KAAKV,KAAlC,EAAyC,KAAKW,gBAAL,EAAzC,CAAf;EACD,KA3CI;EA8CL;EACA;EACA;;;EACA,QAAI,kBAAkBxd,QAAQ,CAACyD,eAA3B,IACF,CAACoU,MAAM,CAAC3L,OAAP,CAAe6P,mBAAf,CADH,EACwC;EAAA;;EACtC,kBAAG9M,MAAH,aAAajP,QAAQ,CAACmE,IAAT,CAAcgL,QAA3B,EACG3M,OADH,CACW,UAAAqW,IAAI;EAAA,eAAInR,YAAY,CAAC+B,EAAb,CAAgBoP,IAAhB,EAAsB,WAAtB,EAAmC,IAAnC,EAAyC/U,IAAI,EAA7C,CAAJ;EAAA,OADf;EAED;;EAED,SAAK4H,QAAL,CAAc+R,KAAd;;EACA,SAAK/R,QAAL,CAAc0B,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;EAEAO,IAAAA,WAAW,CAACiB,WAAZ,CAAwB,KAAKiO,KAA7B,EAAoC3E,iBAApC;EACAvK,IAAAA,WAAW,CAACiB,WAAZ,CAAwB,KAAKlD,QAA7B,EAAuCwM,iBAAvC;EACAxQ,IAAAA,YAAY,CAACuC,OAAb,CAAqB4N,MAArB,EAA6BE,aAA7B,EAA0C3B,aAA1C;EACD;;WAEDgD,OAAA,gBAAO;EACL,QAAI,KAAK1N,QAAL,CAAcuR,QAAd,IAA0B,KAAKvR,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCiP,mBAAjC,CAA1B,IAAmF,CAAC,KAAKuB,KAAL,CAAW1Q,SAAX,CAAqBE,QAArB,CAA8B6L,iBAA9B,CAAxF,EAAwI;EACtI;EACD;;EAED,QAAML,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8B,KAAK1R,QAAnC,CAAf;EACA,QAAM0K,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAK1K;EADA,KAAtB;EAIA,QAAMgS,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB4N,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;EAEA,QAAIsH,SAAS,CAAC5X,gBAAd,EAAgC;EAC9B;EACD;;EAED,QAAI,KAAK8W,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAae,OAAb;EACD;;EAEDhQ,IAAAA,WAAW,CAACiB,WAAZ,CAAwB,KAAKiO,KAA7B,EAAoC3E,iBAApC;EACAvK,IAAAA,WAAW,CAACiB,WAAZ,CAAwB,KAAKlD,QAA7B,EAAuCwM,iBAAvC;EACAxQ,IAAAA,YAAY,CAACuC,OAAb,CAAqB4N,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;EACD;;WAEDnK,UAAA,mBAAU;EACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;EACArD,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+D,QAAtB,EAAgCV,WAAhC;EACA,SAAKU,QAAL,GAAgB,IAAhB;EACA,SAAKmR,KAAL,GAAa,IAAb;;EACA,QAAI,KAAKD,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAae,OAAb;;EACA,WAAKf,OAAL,GAAe,IAAf;EACD;EACF;;WAEDgB,SAAA,kBAAS;EACP,SAAKb,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EACA,QAAI,KAAKJ,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAaiB,cAAb;EACD;EACF;;;WAID9J,qBAAA,8BAAqB;EAAA;;EACnBrM,IAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+ByP,WAA/B,EAA4C,UAAA3T,KAAK,EAAI;EACnDA,MAAAA,KAAK,CAAC3B,cAAN;EACA2B,MAAAA,KAAK,CAACsW,eAAN;;EACA,MAAA,KAAI,CAAC3Q,MAAL;EACD,KAJD;EAKD;;WAEDqG,aAAA,oBAAWpR,MAAX,EAAmB;EACjBA,IAAAA,MAAM,oDACD,KAAK2b,WAAL,CAAiB3N,OADhB,GAEDzC,WAAW,CAACG,iBAAZ,CAA8B,KAAKpC,QAAnC,CAFC,GAGDtJ,MAHC,CAAN;EAMAF,IAAAA,eAAe,CACb2I,MADa,EAEbzI,MAFa,EAGb,KAAK2b,WAAL,CAAiBpN,WAHJ,CAAf;EAMA,WAAOvO,MAAP;EACD;;WAED0a,kBAAA,2BAAkB;EAChB,WAAO9N,cAAc,CAACc,IAAf,CAAoB,KAAKpE,QAAzB,EAAmCoQ,aAAnC,EAAkD,CAAlD,CAAP;EACD;;WAEDkC,gBAAA,yBAAgB;EACd,QAAMC,cAAc,GAAG,KAAKvS,QAAL,CAAcvI,UAArC;EACA,QAAI+a,SAAS,GAAG/B,gBAAhB,CAFc;;EAKd,QAAI8B,cAAc,CAAC9R,SAAf,CAAyBE,QAAzB,CAAkCkP,iBAAlC,CAAJ,EAA0D;EACxD2C,MAAAA,SAAS,GAAGjC,aAAZ;;EACA,UAAI,KAAKY,KAAL,CAAW1Q,SAAX,CAAqBE,QAArB,CAA8BqP,oBAA9B,CAAJ,EAAyD;EACvDwC,QAAAA,SAAS,GAAGhC,gBAAZ;EACD;EACF,KALD,MAKO,IAAI+B,cAAc,CAAC9R,SAAf,CAAyBE,QAAzB,CAAkCmP,oBAAlC,CAAJ,EAA6D;EAClE0C,MAAAA,SAAS,GAAG7B,eAAZ;EACD,KAFM,MAEA,IAAI4B,cAAc,CAAC9R,SAAf,CAAyBE,QAAzB,CAAkCoP,mBAAlC,CAAJ,EAA4D;EACjEyC,MAAAA,SAAS,GAAG5B,cAAZ;EACD,KAFM,MAEA,IAAI,KAAKO,KAAL,CAAW1Q,SAAX,CAAqBE,QAArB,CAA8BqP,oBAA9B,CAAJ,EAAyD;EAC9DwC,MAAAA,SAAS,GAAG9B,mBAAZ;EACD;;EAED,WAAO8B,SAAP;EACD;;WAEDlB,gBAAA,yBAAgB;EACd,WAAO7W,OAAO,CAAC,KAAKuF,QAAL,CAAcQ,OAAd,OAA0ByP,iBAA1B,CAAD,CAAd;EACD;;WAEDwC,aAAA,sBAAa;EAAA;;EACX,QAAMjQ,MAAM,GAAG,EAAf;;EAEA,QAAI,OAAO,KAAKqF,OAAL,CAAarF,MAApB,KAA+B,UAAnC,EAA+C;EAC7CA,MAAAA,MAAM,CAAC5G,EAAP,GAAY,UAAA5C,IAAI,EAAI;EAClBA,QAAAA,IAAI,CAAC0Z,OAAL,qCACK1Z,IAAI,CAAC0Z,OADV,GAEK,MAAI,CAAC7K,OAAL,CAAarF,MAAb,CAAoBxJ,IAAI,CAAC0Z,OAAzB,EAAkC,MAAI,CAAC1S,QAAvC,KAAoD,EAFzD;EAKA,eAAOhH,IAAP;EACD,OAPD;EAQD,KATD,MASO;EACLwJ,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKqF,OAAL,CAAarF,MAA7B;EACD;;EAED,WAAOA,MAAP;EACD;;WAEDsP,mBAAA,4BAAmB;EACjB,QAAMd,YAAY,GAAG;EACnBwB,MAAAA,SAAS,EAAE,KAAKF,aAAL,EADQ;EAEnBK,MAAAA,SAAS,EAAE;EACTnQ,QAAAA,MAAM,EAAE,KAAKiQ,UAAL,EADC;EAET5B,QAAAA,IAAI,EAAE;EACJ+B,UAAAA,OAAO,EAAE,KAAK/K,OAAL,CAAagJ;EADlB,SAFG;EAKTgC,QAAAA,eAAe,EAAE;EACfC,UAAAA,iBAAiB,EAAE,KAAKjL,OAAL,CAAaiJ;EADjB;EALR;EAFQ,KAArB,CADiB;;EAejB,QAAI,KAAKjJ,OAAL,CAAajQ,OAAb,KAAyB,QAA7B,EAAuC;EACrCoZ,MAAAA,YAAY,CAAC2B,SAAb,CAAuBI,UAAvB,GAAoC;EAClCH,QAAAA,OAAO,EAAE;EADyB,OAApC;EAGD;;EAED,6CACK5B,YADL,GAEK,KAAKnJ,OAAL,CAAamJ,YAFlB;EAID;;;aAIMgC,oBAAP,2BAAyBve,OAAzB,EAAkCiC,MAAlC,EAA0C;EACxC,QAAIsC,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAX;;EACA,QAAMwI,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,QAAI,CAACsC,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIiY,QAAJ,CAAaxc,OAAb,EAAsBoT,OAAtB,CAAP;EACD;;EAED,QAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;EACD;;EAEDsC,MAAAA,IAAI,CAACtC,MAAD,CAAJ;EACD;EACF;;aAEMoK,kBAAP,yBAAuBpK,MAAvB,EAA+B;EAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;EAC3BkQ,MAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B,EAAiCtc,MAAjC;EACD,KAFM,CAAP;EAGD;;aAEM+a,aAAP,oBAAkB3V,KAAlB,EAAyB;EACvB,QAAIA,KAAK,KAAKA,KAAK,CAAC6F,MAAN,KAAiB4N,kBAAjB,IACXzT,KAAK,CAACI,IAAN,KAAe,OAAf,IAA0BJ,KAAK,CAAC/C,GAAN,KAAcqW,OADlC,CAAT,EACsD;EACpD;EACD;;EAED,QAAM6D,OAAO,GAAG3P,cAAc,CAAC7J,IAAf,CAAoB8H,sBAApB,CAAhB;;EAEA,SAAK,IAAIhF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGoW,OAAO,CAACzW,MAA9B,EAAsCD,CAAC,GAAGM,GAA1C,EAA+CN,CAAC,EAAhD,EAAoD;EAClD,UAAM4P,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8BuB,OAAO,CAAC1W,CAAD,CAArC,CAAf;EACA,UAAM2W,OAAO,GAAG9Z,IAAI,CAACG,OAAL,CAAa0Z,OAAO,CAAC1W,CAAD,CAApB,EAAyB8C,UAAzB,CAAhB;EACA,UAAMqL,aAAa,GAAG;EACpBA,QAAAA,aAAa,EAAEuI,OAAO,CAAC1W,CAAD;EADF,OAAtB;;EAIA,UAAIT,KAAK,IAAIA,KAAK,CAACI,IAAN,KAAe,OAA5B,EAAqC;EACnCwO,QAAAA,aAAa,CAACyI,UAAd,GAA2BrX,KAA3B;EACD;;EAED,UAAI,CAACoX,OAAL,EAAc;EACZ;EACD;;EAED,UAAME,YAAY,GAAGF,OAAO,CAAC/B,KAA7B;;EACA,UAAI,CAAC8B,OAAO,CAAC1W,CAAD,CAAP,CAAWkE,SAAX,CAAqBE,QAArB,CAA8B6L,iBAA9B,CAAL,EAAqD;EACnD;EACD;;EAED,UAAI1Q,KAAK,KAAMA,KAAK,CAACI,IAAN,KAAe,OAAf,IACX,kBAAkB9E,IAAlB,CAAuB0E,KAAK,CAACQ,MAAN,CAAa0N,OAApC,CADU,IAETlO,KAAK,CAACI,IAAN,KAAe,OAAf,IAA0BJ,KAAK,CAAC/C,GAAN,KAAcqW,OAFpC,CAAL,IAGAgE,YAAY,CAACzS,QAAb,CAAsB7E,KAAK,CAACQ,MAA5B,CAHJ,EAGyC;EACvC;EACD;;EAED,UAAM0V,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB4N,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;EACA,UAAIsH,SAAS,CAAC5X,gBAAd,EAAgC;EAC9B;EACD,OA9BiD;EAiClD;;;EACA,UAAI,kBAAkB9F,QAAQ,CAACyD,eAA/B,EAAgD;EAAA;;EAC9C,qBAAGwL,MAAH,cAAajP,QAAQ,CAACmE,IAAT,CAAcgL,QAA3B,EACG3M,OADH,CACW,UAAAqW,IAAI;EAAA,iBAAInR,YAAY,CAACC,GAAb,CAAiBkR,IAAjB,EAAuB,WAAvB,EAAoC,IAApC,EAA0C/U,IAAI,EAA9C,CAAJ;EAAA,SADf;EAED;;EAED6a,MAAAA,OAAO,CAAC1W,CAAD,CAAP,CAAWmF,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;;EAEA,UAAIwR,OAAO,CAAChC,OAAZ,EAAqB;EACnBgC,QAAAA,OAAO,CAAChC,OAAR,CAAgBe,OAAhB;EACD;;EAEDmB,MAAAA,YAAY,CAAC3S,SAAb,CAAuBC,MAAvB,CAA8B8L,iBAA9B;EACAyG,MAAAA,OAAO,CAAC1W,CAAD,CAAP,CAAWkE,SAAX,CAAqBC,MAArB,CAA4B8L,iBAA5B;EACAxQ,MAAAA,YAAY,CAACuC,OAAb,CAAqB4N,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;EACD;EACF;;aAEMgH,uBAAP,8BAA4Bjd,OAA5B,EAAqC;EACnC,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAACgD,UAAlD;EACD;;aAEM4b,wBAAP,+BAA6BvX,KAA7B,EAAoC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,QAAI,kBAAkB1E,IAAlB,CAAuB0E,KAAK,CAACQ,MAAN,CAAa0N,OAApC,IACFlO,KAAK,CAAC/C,GAAN,KAAcoW,SAAd,IAA4BrT,KAAK,CAAC/C,GAAN,KAAcmW,UAAd,KAC1BpT,KAAK,CAAC/C,GAAN,KAAcuW,cAAd,IAAgCxT,KAAK,CAAC/C,GAAN,KAAcsW,YAA/C,IACCvT,KAAK,CAACQ,MAAN,CAAakE,OAAb,CAAqB4P,aAArB,CAF0B,CAD1B,GAIF,CAACZ,cAAc,CAACpY,IAAf,CAAoB0E,KAAK,CAAC/C,GAA1B,CAJH,EAImC;EACjC;EACD;;EAED+C,IAAAA,KAAK,CAAC3B,cAAN;EACA2B,IAAAA,KAAK,CAACsW,eAAN;;EAEA,QAAI,KAAKb,QAAL,IAAiB,KAAK9Q,SAAL,CAAeE,QAAf,CAAwBiP,mBAAxB,CAArB,EAAmE;EACjE;EACD;;EAED,QAAMzD,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8B,IAA9B,CAAf;EACA,QAAMF,QAAQ,GAAG,KAAK/Q,SAAL,CAAeE,QAAf,CAAwB6L,iBAAxB,CAAjB;;EAEA,QAAI1Q,KAAK,CAAC/C,GAAN,KAAcmW,UAAlB,EAA8B;EAC5B,UAAMvN,MAAM,GAAG,KAAK9G,OAAL,CAAa0G,sBAAb,IAAqC,IAArC,GAA4C+B,cAAc,CAACW,IAAf,CAAoB,IAApB,EAA0B1C,sBAA1B,EAAgD,CAAhD,CAA3D;EACAI,MAAAA,MAAM,CAACoQ,KAAP;EACAd,MAAAA,QAAQ,CAACQ,UAAT;EACA;EACD;;EAED,QAAI,CAACD,QAAD,IAAa1V,KAAK,CAAC/C,GAAN,KAAcoW,SAA/B,EAA0C;EACxC8B,MAAAA,QAAQ,CAACQ,UAAT;EACA;EACD;;EAED,QAAM6B,KAAK,GAAGhQ,cAAc,CAAC7J,IAAf,CAAoB6W,sBAApB,EAA4CnE,MAA5C,EACXzI,MADW,CACJnM,SADI,CAAd;;EAGA,QAAI,CAAC+b,KAAK,CAAC9W,MAAX,EAAmB;EACjB;EACD;;EAED,QAAIuM,KAAK,GAAGuK,KAAK,CAAChW,OAAN,CAAcxB,KAAK,CAACQ,MAApB,CAAZ;;EAEA,QAAIR,KAAK,CAAC/C,GAAN,KAAcsW,YAAd,IAA8BtG,KAAK,GAAG,CAA1C,EAA6C;EAAE;EAC7CA,MAAAA,KAAK;EACN;;EAED,QAAIjN,KAAK,CAAC/C,GAAN,KAAcuW,cAAd,IAAgCvG,KAAK,GAAGuK,KAAK,CAAC9W,MAAN,GAAe,CAA3D,EAA8D;EAAE;EAC9DuM,MAAAA,KAAK;EACN,KArDiC;;;EAwDlCA,IAAAA,KAAK,GAAGA,KAAK,KAAK,CAAC,CAAX,GAAe,CAAf,GAAmBA,KAA3B;EAEAuK,IAAAA,KAAK,CAACvK,KAAD,CAAL,CAAagJ,KAAb;EACD;;aAEM7Q,cAAP,qBAAmBzM,OAAnB,EAA4B;EAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;EACD;;;;0BApYoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOsF,SAAP;EACD;;;0BAEwB;EACvB,aAAOO,aAAP;EACD;;;;;EA6XH;;;;;;;EAMAjJ,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0Bob,sBAA1B,EAAkDnO,sBAAlD,EAAwE0P,QAAQ,CAACoC,qBAAjF;EACArX,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0Bob,sBAA1B,EAAkDU,aAAlD,EAAiEa,QAAQ,CAACoC,qBAA1E;EACArX,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgDsR,QAAQ,CAACQ,UAAzD;EACAzV,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0Bqb,oBAA1B,EAAgDsB,QAAQ,CAACQ,UAAzD;EACAzV,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUzF,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAAC3B,cAAN;EACA2B,EAAAA,KAAK,CAACsW,eAAN;EACAnB,EAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B,EAAiC,QAAjC;EACD,CAJD;EAKAhX,YAAY,CACT+B,EADH,CACMzJ,QADN,EACgBqL,sBADhB,EACsCwQ,mBADtC,EAC2D,UAAApW,CAAC;EAAA,SAAIA,CAAC,CAACqY,eAAF,EAAJ;EAAA,CAD5D;EAGA,IAAMtX,GAAC,GAAGvC,SAAS,EAAnB;EAEA;;;;;;;EAMA;;EACA,IAAIuC,GAAJ,EAAO;EACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;EACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAa8R,QAAQ,CAACnQ,eAAtB;EACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyB6P,QAAzB;;EACAnW,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;EACA,WAAO8P,QAAQ,CAACnQ,eAAhB;EACD,GAHD;EAID;;ECngBD;;;;;;EAMA,IAAM3B,MAAI,GAAG,OAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,UAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EACA,IAAM2P,YAAU,GAAG,QAAnB;EAEA,IAAMxK,SAAO,GAAG;EACd6O,EAAAA,QAAQ,EAAE,IADI;EAEd3O,EAAAA,QAAQ,EAAE,IAFI;EAGdmN,EAAAA,KAAK,EAAE,IAHO;EAIdpE,EAAAA,IAAI,EAAE;EAJQ,CAAhB;EAOA,IAAM1I,aAAW,GAAG;EAClBsO,EAAAA,QAAQ,EAAE,kBADQ;EAElB3O,EAAAA,QAAQ,EAAE,SAFQ;EAGlBmN,EAAAA,KAAK,EAAE,SAHW;EAIlBpE,EAAAA,IAAI,EAAE;EAJY,CAApB;EAOA,IAAMrB,YAAU,YAAUhN,WAA1B;EACA,IAAMkU,oBAAoB,qBAAmBlU,WAA7C;EACA,IAAMiN,cAAY,cAAYjN,WAA9B;EACA,IAAM8M,YAAU,YAAU9M,WAA1B;EACA,IAAM+M,aAAW,aAAW/M,WAA5B;EACA,IAAMmU,aAAa,eAAanU,WAAhC;EACA,IAAMoU,YAAY,cAAYpU,WAA9B;EACA,IAAMqU,mBAAmB,qBAAmBrU,WAA5C;EACA,IAAMsU,qBAAqB,uBAAqBtU,WAAhD;EACA,IAAMuU,qBAAqB,uBAAqBvU,WAAhD;EACA,IAAMwU,uBAAuB,yBAAuBxU,WAApD;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAMwU,6BAA6B,GAAG,yBAAtC;EACA,IAAMC,mBAAmB,GAAG,gBAA5B;EACA,IAAMC,eAAe,GAAG,YAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EACA,IAAM1H,iBAAe,GAAG,MAAxB;EACA,IAAM2H,iBAAiB,GAAG,cAA1B;EAEA,IAAMC,eAAe,GAAG,eAAxB;EACA,IAAMC,mBAAmB,GAAG,aAA5B;EACA,IAAM9S,sBAAoB,GAAG,uBAA7B;EACA,IAAM+S,qBAAqB,GAAG,wBAA9B;EACA,IAAMC,sBAAsB,GAAG,mDAA/B;EACA,IAAMC,uBAAuB,GAAG,aAAhC;EAEA;;;;;;MAMMC;EACJ,iBAAYhgB,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAKmR,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;EACA,SAAKsJ,QAAL,GAAgBvL,OAAhB;EACA,SAAKigB,OAAL,GAAepR,cAAc,CAACzJ,OAAf,CAAuBua,eAAvB,EAAwC3f,OAAxC,CAAf;EACA,SAAKkgB,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,KAAhB;EACA,SAAKC,kBAAL,GAA0B,KAA1B;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAK9H,gBAAL,GAAwB,KAAxB;EACA,SAAK+H,eAAL,GAAuB,CAAvB;EACA3b,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEAoC,SAAA,gBAAOiJ,aAAP,EAAsB;EACpB,WAAO,KAAKkK,QAAL,GAAgB,KAAKlH,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUjD,aAAV,CAArC;EACD;;WAEDiD,OAAA,cAAKjD,aAAL,EAAoB;EAAA;;EAClB,QAAI,KAAKkK,QAAL,IAAiB,KAAK5H,gBAA1B,EAA4C;EAC1C;EACD;;EAED,QAAI,KAAKhN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCuT,eAAjC,CAAJ,EAAuD;EACrD,WAAKlH,gBAAL,GAAwB,IAAxB;EACD;;EAED,QAAM2E,SAAS,GAAG3V,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCoM,YAApC,EAAgD;EAChE1B,MAAAA,aAAa,EAAbA;EADgE,KAAhD,CAAlB;;EAIA,QAAI,KAAKkK,QAAL,IAAiBjD,SAAS,CAACvX,gBAA/B,EAAiD;EAC/C;EACD;;EAED,SAAKwa,QAAL,GAAgB,IAAhB;;EAEA,SAAKI,eAAL;;EACA,SAAKC,aAAL;;EAEA,SAAKC,aAAL;;EAEA,SAAKC,eAAL;;EACA,SAAKC,eAAL;;EAEApZ,IAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EACE2T,mBADF,EAEEW,qBAFF,EAGE,UAAAxY,KAAK;EAAA,aAAI,KAAI,CAAC4R,IAAL,CAAU5R,KAAV,CAAJ;EAAA,KAHP;EAMAE,IAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAK2W,OAArB,EAA8BZ,uBAA9B,EAAuD,YAAM;EAC3D9X,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAI,CAACgC,QAAtB,EAAgC6T,qBAAhC,EAAuD,UAAA/X,KAAK,EAAI;EAC9D,YAAIA,KAAK,CAACQ,MAAN,KAAiB,KAAI,CAAC0D,QAA1B,EAAoC;EAClC,UAAA,KAAI,CAAC8U,oBAAL,GAA4B,IAA5B;EACD;EACF,OAJD;EAKD,KAND;;EAQA,SAAKO,aAAL,CAAmB;EAAA,aAAM,KAAI,CAACC,YAAL,CAAkB5K,aAAlB,CAAN;EAAA,KAAnB;EACD;;WAEDgD,OAAA,cAAK5R,KAAL,EAAY;EAAA;;EACV,QAAIA,KAAJ,EAAW;EACTA,MAAAA,KAAK,CAAC3B,cAAN;EACD;;EAED,QAAI,CAAC,KAAKya,QAAN,IAAkB,KAAK5H,gBAA3B,EAA6C;EAC3C;EACD;;EAED,QAAMgF,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCsM,YAApC,CAAlB;;EAEA,QAAI0F,SAAS,CAAC5X,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKwa,QAAL,GAAgB,KAAhB;;EACA,QAAMW,UAAU,GAAG,KAAKvV,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCuT,eAAjC,CAAnB;;EAEA,QAAIqB,UAAJ,EAAgB;EACd,WAAKvI,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKmI,eAAL;;EACA,SAAKC,eAAL;;EAEApZ,IAAAA,YAAY,CAACC,GAAb,CAAiB3H,QAAjB,EAA2Bmf,aAA3B;;EAEA,SAAKzT,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B8L,iBAA/B;;EAEAxQ,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+D,QAAtB,EAAgC2T,mBAAhC;EACA3X,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKyY,OAAtB,EAA+BZ,uBAA/B;;EAEA,QAAIyB,UAAJ,EAAgB;EACd,UAAMngB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK+K,QAAN,CAA3D;EAEAhE,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD,UAAAqI,KAAK;EAAA,eAAI,MAAI,CAAC0Z,UAAL,CAAgB1Z,KAAhB,CAAJ;EAAA,OAArD;EACA/F,MAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgB5K,kBAAhB,CAApB;EACD,KALD,MAKO;EACL,WAAKogB,UAAL;EACD;EACF;;WAEDjV,UAAA,mBAAU;EACR,KAACrL,MAAD,EAAS,KAAK8K,QAAd,EAAwB,KAAK0U,OAA7B,EACG5d,OADH,CACW,UAAA2e,WAAW;EAAA,aAAIzZ,YAAY,CAACC,GAAb,CAAiBwZ,WAAjB,EAA8BnW,WAA9B,CAAJ;EAAA,KADtB;EAGA;;;;;;EAKAtD,IAAAA,YAAY,CAACC,GAAb,CAAiB3H,QAAjB,EAA2Bmf,aAA3B;EAEAra,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;EAEA,SAAKwI,OAAL,GAAe,IAAf;EACA,SAAK7H,QAAL,GAAgB,IAAhB;EACA,SAAK0U,OAAL,GAAe,IAAf;EACA,SAAKC,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,kBAAL,GAA0B,IAA1B;EACA,SAAKC,oBAAL,GAA4B,IAA5B;EACA,SAAK9H,gBAAL,GAAwB,IAAxB;EACA,SAAK+H,eAAL,GAAuB,IAAvB;EACD;;WAEDW,eAAA,wBAAe;EACb,SAAKR,aAAL;EACD;;;WAIDpN,aAAA,oBAAWpR,MAAX,EAAmB;EACjBA,IAAAA,MAAM,qCACDgO,SADC,GAEDhO,MAFC,CAAN;EAIAF,IAAAA,eAAe,CAAC2I,MAAD,EAAOzI,MAAP,EAAeuO,aAAf,CAAf;EACA,WAAOvO,MAAP;EACD;;WAED4e,eAAA,sBAAa5K,aAAb,EAA4B;EAAA;;EAC1B,QAAM6K,UAAU,GAAG,KAAKvV,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCuT,eAAjC,CAAnB;;EACA,QAAMyB,SAAS,GAAGrS,cAAc,CAACzJ,OAAf,CAAuBwa,mBAAvB,EAA4C,KAAKK,OAAjD,CAAlB;;EAEA,QAAI,CAAC,KAAK1U,QAAL,CAAcvI,UAAf,IACA,KAAKuI,QAAL,CAAcvI,UAAd,CAAyB3B,QAAzB,KAAsCgO,IAAI,CAACC,YAD/C,EAC6D;EAC3D;EACAzP,MAAAA,QAAQ,CAACmE,IAAT,CAAcmd,WAAd,CAA0B,KAAK5V,QAA/B;EACD;;EAED,SAAKA,QAAL,CAAcxI,KAAd,CAAoBI,OAApB,GAA8B,OAA9B;;EACA,SAAKoI,QAAL,CAAcpF,eAAd,CAA8B,aAA9B;;EACA,SAAKoF,QAAL,CAAc0B,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAK1B,QAAL,CAAc0B,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EACA,SAAK1B,QAAL,CAAc4C,SAAd,GAA0B,CAA1B;;EAEA,QAAI+S,SAAJ,EAAe;EACbA,MAAAA,SAAS,CAAC/S,SAAV,GAAsB,CAAtB;EACD;;EAED,QAAI2S,UAAJ,EAAgB;EACdld,MAAAA,MAAM,CAAC,KAAK2H,QAAN,CAAN;EACD;;EAED,SAAKA,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BoJ,iBAA5B;;EAEA,QAAI,KAAK3E,OAAL,CAAakK,KAAjB,EAAwB;EACtB,WAAK8D,aAAL;EACD;;EAED,QAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;EAC/B,UAAI,MAAI,CAACjO,OAAL,CAAakK,KAAjB,EAAwB;EACtB,QAAA,MAAI,CAAC/R,QAAL,CAAc+R,KAAd;EACD;;EAED,MAAA,MAAI,CAAC/E,gBAAL,GAAwB,KAAxB;EACAhR,MAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAACyB,QAA1B,EAAoCqM,aAApC,EAAiD;EAC/C3B,QAAAA,aAAa,EAAbA;EAD+C,OAAjD;EAGD,KATD;;EAWA,QAAI6K,UAAJ,EAAgB;EACd,UAAMngB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKyf,OAAN,CAA3D;EAEA1Y,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAK0W,OAAtB,EAA+BjhB,cAA/B,EAA+CqiB,kBAA/C;EACA/f,MAAAA,oBAAoB,CAAC,KAAK2e,OAAN,EAAetf,kBAAf,CAApB;EACD,KALD,MAKO;EACL0gB,MAAAA,kBAAkB;EACnB;EACF;;WAEDD,gBAAA,yBAAgB;EAAA;;EACd7Z,IAAAA,YAAY,CAACC,GAAb,CAAiB3H,QAAjB,EAA2Bmf,aAA3B,EADc;;EAEdzX,IAAAA,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0Bmf,aAA1B,EAAyC,UAAA3X,KAAK,EAAI;EAChD,UAAIxH,QAAQ,KAAKwH,KAAK,CAACQ,MAAnB,IACA,MAAI,CAAC0D,QAAL,KAAkBlE,KAAK,CAACQ,MADxB,IAEA,CAAC,MAAI,CAAC0D,QAAL,CAAcW,QAAd,CAAuB7E,KAAK,CAACQ,MAA7B,CAFL,EAE2C;EACzC,QAAA,MAAI,CAAC0D,QAAL,CAAc+R,KAAd;EACD;EACF,KAND;EAOD;;WAEDoD,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKP,QAAT,EAAmB;EACjB5Y,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B4T,qBAA/B,EAAsD,UAAA9X,KAAK,EAAI;EAC7D,YAAI,MAAI,CAAC+L,OAAL,CAAajD,QAAb,IAAyB9I,KAAK,CAAC/C,GAAN,KAAcmW,YAA3C,EAAuD;EACrDpT,UAAAA,KAAK,CAAC3B,cAAN;;EACA,UAAA,MAAI,CAACuT,IAAL;EACD,SAHD,MAGO,IAAI,CAAC,MAAI,CAAC7F,OAAL,CAAajD,QAAd,IAA0B9I,KAAK,CAAC/C,GAAN,KAAcmW,YAA5C,EAAwD;EAC7D,UAAA,MAAI,CAAC6G,0BAAL;EACD;EACF,OAPD;EAQD,KATD,MASO;EACL/Z,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+D,QAAtB,EAAgC4T,qBAAhC;EACD;EACF;;WAEDwB,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKR,QAAT,EAAmB;EACjB5Y,MAAAA,YAAY,CAAC+B,EAAb,CAAgB7I,MAAhB,EAAwBwe,YAAxB,EAAsC;EAAA,eAAM,MAAI,CAACwB,aAAL,EAAN;EAAA,OAAtC;EACD,KAFD,MAEO;EACLlZ,MAAAA,YAAY,CAACC,GAAb,CAAiB/G,MAAjB,EAAyBwe,YAAzB;EACD;EACF;;WAED8B,aAAA,sBAAa;EAAA;;EACX,SAAKxV,QAAL,CAAcxI,KAAd,CAAoBI,OAApB,GAA8B,MAA9B;;EACA,SAAKoI,QAAL,CAAc0B,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,SAAK1B,QAAL,CAAcpF,eAAd,CAA8B,YAA9B;;EACA,SAAKoF,QAAL,CAAcpF,eAAd,CAA8B,MAA9B;;EACA,SAAKoS,gBAAL,GAAwB,KAAxB;;EACA,SAAKqI,aAAL,CAAmB,YAAM;EACvB/gB,MAAAA,QAAQ,CAACmE,IAAT,CAAcgI,SAAd,CAAwBC,MAAxB,CAA+BuT,eAA/B;;EACA,MAAA,MAAI,CAAC+B,iBAAL;;EACA,MAAA,MAAI,CAACC,eAAL;;EACAja,MAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAACyB,QAA1B,EAAoCuM,cAApC;EACD,KALD;EAMD;;WAED2J,kBAAA,2BAAkB;EAChB,SAAKvB,SAAL,CAAeld,UAAf,CAA0BoJ,WAA1B,CAAsC,KAAK8T,SAA3C;;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;WAEDU,gBAAA,uBAAcc,QAAd,EAAwB;EAAA;;EACtB,QAAMC,OAAO,GAAG,KAAKpW,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCuT,eAAjC,IACdA,eADc,GAEd,EAFF;;EAIA,QAAI,KAAKU,QAAL,IAAiB,KAAK/M,OAAL,CAAa0L,QAAlC,EAA4C;EAC1C,WAAKoB,SAAL,GAAiBrgB,QAAQ,CAAC4F,aAAT,CAAuB,KAAvB,CAAjB;EACA,WAAKya,SAAL,CAAexR,SAAf,GAA2B6Q,mBAA3B;;EAEA,UAAIoC,OAAJ,EAAa;EACX,aAAKzB,SAAL,CAAelU,SAAf,CAAyB2C,GAAzB,CAA6BgT,OAA7B;EACD;;EAED9hB,MAAAA,QAAQ,CAACmE,IAAT,CAAcmd,WAAd,CAA0B,KAAKjB,SAA/B;EAEA3Y,MAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKiC,QAArB,EAA+B2T,mBAA/B,EAAoD,UAAA7X,KAAK,EAAI;EAC3D,YAAI,MAAI,CAACgZ,oBAAT,EAA+B;EAC7B,UAAA,MAAI,CAACA,oBAAL,GAA4B,KAA5B;EACA;EACD;;EAED,YAAIhZ,KAAK,CAACQ,MAAN,KAAiBR,KAAK,CAACua,aAA3B,EAA0C;EACxC;EACD;;EAED,QAAA,MAAI,CAACN,0BAAL;EACD,OAXD;;EAaA,UAAIK,OAAJ,EAAa;EACX/d,QAAAA,MAAM,CAAC,KAAKsc,SAAN,CAAN;EACD;;EAED,WAAKA,SAAL,CAAelU,SAAf,CAAyB2C,GAAzB,CAA6BoJ,iBAA7B;;EAEA,UAAI,CAAC4J,OAAL,EAAc;EACZD,QAAAA,QAAQ;EACR;EACD;;EAED,UAAMG,0BAA0B,GAAGrhB,gCAAgC,CAAC,KAAK0f,SAAN,CAAnE;EAEA3Y,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAK2W,SAAtB,EAAiClhB,cAAjC,EAAiD0iB,QAAjD;EACApgB,MAAAA,oBAAoB,CAAC,KAAK4e,SAAN,EAAiB2B,0BAAjB,CAApB;EACD,KAtCD,MAsCO,IAAI,CAAC,KAAK1B,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;EAC3C,WAAKA,SAAL,CAAelU,SAAf,CAAyBC,MAAzB,CAAgC8L,iBAAhC;;EAEA,UAAM+J,cAAc,GAAG,SAAjBA,cAAiB,GAAM;EAC3B,QAAA,MAAI,CAACL,eAAL;;EACAC,QAAAA,QAAQ;EACT,OAHD;;EAKA,UAAI,KAAKnW,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCuT,eAAjC,CAAJ,EAAuD;EACrD,YAAMoC,2BAA0B,GAAGrhB,gCAAgC,CAAC,KAAK0f,SAAN,CAAnE;;EACA3Y,QAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAK2W,SAAtB,EAAiClhB,cAAjC,EAAiD8iB,cAAjD;EACAxgB,QAAAA,oBAAoB,CAAC,KAAK4e,SAAN,EAAiB2B,2BAAjB,CAApB;EACD,OAJD,MAIO;EACLC,QAAAA,cAAc;EACf;EACF,KAfM,MAeA;EACLJ,MAAAA,QAAQ;EACT;EACF;;WAEDJ,6BAAA,sCAA6B;EAAA;;EAC3B,QAAI,KAAKlO,OAAL,CAAa0L,QAAb,KAA0B,QAA9B,EAAwC;EACtC,UAAMvB,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCwT,oBAApC,CAAlB;;EACA,UAAIxB,SAAS,CAAC5X,gBAAd,EAAgC;EAC9B;EACD;;EAED,WAAK4F,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4B+Q,iBAA5B;;EACA,UAAMqC,uBAAuB,GAAGvhB,gCAAgC,CAAC,KAAK+K,QAAN,CAAhE;EACAhE,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD,YAAM;EACpD,QAAA,MAAI,CAACuM,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+ByT,iBAA/B;EACD,OAFD;EAGApe,MAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgBwW,uBAAhB,CAApB;;EACA,WAAKxW,QAAL,CAAc+R,KAAd;EACD,KAbD,MAaO;EACL,WAAKrE,IAAL;EACD;EACF;EAGD;EACA;;;WAEAwH,gBAAA,yBAAgB;EACd,QAAMuB,kBAAkB,GACtB,KAAKzW,QAAL,CAAc0W,YAAd,GAA6BpiB,QAAQ,CAACyD,eAAT,CAAyB4e,YADxD;;EAGA,QAAI,CAAC,KAAK9B,kBAAN,IAA4B4B,kBAAhC,EAAoD;EAClD,WAAKzW,QAAL,CAAcxI,KAAd,CAAoBof,WAApB,GAAqC,KAAK7B,eAA1C;EACD;;EAED,QAAI,KAAKF,kBAAL,IAA2B,CAAC4B,kBAAhC,EAAoD;EAClD,WAAKzW,QAAL,CAAcxI,KAAd,CAAoBqf,YAApB,GAAsC,KAAK9B,eAA3C;EACD;EACF;;WAEDiB,oBAAA,6BAAoB;EAClB,SAAKhW,QAAL,CAAcxI,KAAd,CAAoBof,WAApB,GAAkC,EAAlC;EACA,SAAK5W,QAAL,CAAcxI,KAAd,CAAoBqf,YAApB,GAAmC,EAAnC;EACD;;WAED7B,kBAAA,2BAAkB;EAChB,QAAMvS,IAAI,GAAGnO,QAAQ,CAACmE,IAAT,CAAciK,qBAAd,EAAb;EACA,SAAKmS,kBAAL,GAA0B1gB,IAAI,CAAC2iB,KAAL,CAAWrU,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAACsU,KAA5B,IAAqC7hB,MAAM,CAAC8hB,UAAtE;EACA,SAAKjC,eAAL,GAAuB,KAAKkC,kBAAL,EAAvB;EACD;;WAEDhC,gBAAA,yBAAgB;EAAA;;EACd,QAAI,KAAKJ,kBAAT,EAA6B;EAC3B;EACA;EAEA;EACAvR,MAAAA,cAAc,CAAC7J,IAAf,CAAoB8a,sBAApB,EACGzd,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,YAAMyiB,aAAa,GAAGziB,OAAO,CAAC+C,KAAR,CAAcqf,YAApC;EACA,YAAMM,iBAAiB,GAAGjiB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,eAAjC,CAA1B;EACAwN,QAAAA,WAAW,CAACC,gBAAZ,CAA6BzN,OAA7B,EAAsC,eAAtC,EAAuDyiB,aAAvD;EACAziB,QAAAA,OAAO,CAAC+C,KAAR,CAAcqf,YAAd,GAAgCthB,UAAU,CAAC4hB,iBAAD,CAAV,GAAgC,OAAI,CAACpC,eAArE;EACD,OANH,EAL2B;;EAc3BzR,MAAAA,cAAc,CAAC7J,IAAf,CAAoB+a,uBAApB,EACG1d,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,YAAM2iB,YAAY,GAAG3iB,OAAO,CAAC+C,KAAR,CAAc6f,WAAnC;EACA,YAAMC,gBAAgB,GAAGpiB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,cAAjC,CAAzB;EACAwN,QAAAA,WAAW,CAACC,gBAAZ,CAA6BzN,OAA7B,EAAsC,cAAtC,EAAsD2iB,YAAtD;EACA3iB,QAAAA,OAAO,CAAC+C,KAAR,CAAc6f,WAAd,GAA+B9hB,UAAU,CAAC+hB,gBAAD,CAAV,GAA+B,OAAI,CAACvC,eAAnE;EACD,OANH,EAd2B;;EAuB3B,UAAMmC,aAAa,GAAG5iB,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBqf,YAA1C;EACA,UAAMM,iBAAiB,GAAGjiB,MAAM,CAACC,gBAAP,CAAwBb,QAAQ,CAACmE,IAAjC,EAAuC,eAAvC,CAA1B;EAEAwJ,MAAAA,WAAW,CAACC,gBAAZ,CAA6B5N,QAAQ,CAACmE,IAAtC,EAA4C,eAA5C,EAA6Dye,aAA7D;EACA5iB,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBqf,YAApB,GAAsCthB,UAAU,CAAC4hB,iBAAD,CAAV,GAAgC,KAAKpC,eAA3E;EACD;;EAEDzgB,IAAAA,QAAQ,CAACmE,IAAT,CAAcgI,SAAd,CAAwB2C,GAAxB,CAA4B6Q,eAA5B;EACD;;WAEDgC,kBAAA,2BAAkB;EAChB;EACA3S,IAAAA,cAAc,CAAC7J,IAAf,CAAoB8a,sBAApB,EACGzd,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,UAAM8iB,OAAO,GAAGtV,WAAW,CAACM,gBAAZ,CAA6B9N,OAA7B,EAAsC,eAAtC,CAAhB;;EACA,UAAI,OAAO8iB,OAAP,KAAmB,WAAvB,EAAoC;EAClCtV,QAAAA,WAAW,CAACE,mBAAZ,CAAgC1N,OAAhC,EAAyC,eAAzC;EACAA,QAAAA,OAAO,CAAC+C,KAAR,CAAcqf,YAAd,GAA6BU,OAA7B;EACD;EACF,KAPH,EAFgB;;EAYhBjU,IAAAA,cAAc,CAAC7J,IAAf,MAAuB+a,uBAAvB,EACG1d,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,UAAM+iB,MAAM,GAAGvV,WAAW,CAACM,gBAAZ,CAA6B9N,OAA7B,EAAsC,cAAtC,CAAf;;EACA,UAAI,OAAO+iB,MAAP,KAAkB,WAAtB,EAAmC;EACjCvV,QAAAA,WAAW,CAACE,mBAAZ,CAAgC1N,OAAhC,EAAyC,cAAzC;EACAA,QAAAA,OAAO,CAAC+C,KAAR,CAAc6f,WAAd,GAA4BG,MAA5B;EACD;EACF,KAPH,EAZgB;;EAsBhB,QAAMD,OAAO,GAAGtV,WAAW,CAACM,gBAAZ,CAA6BjO,QAAQ,CAACmE,IAAtC,EAA4C,eAA5C,CAAhB;;EACA,QAAI,OAAO8e,OAAP,KAAmB,WAAvB,EAAoC;EAClCjjB,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBqf,YAApB,GAAmC,EAAnC;EACD,KAFD,MAEO;EACL5U,MAAAA,WAAW,CAACE,mBAAZ,CAAgC7N,QAAQ,CAACmE,IAAzC,EAA+C,eAA/C;EACAnE,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBqf,YAApB,GAAmCU,OAAnC;EACD;EACF;;WAEDN,qBAAA,8BAAqB;EAAE;EACrB,QAAMQ,SAAS,GAAGnjB,QAAQ,CAAC4F,aAAT,CAAuB,KAAvB,CAAlB;EACAud,IAAAA,SAAS,CAACtU,SAAV,GAAsB4Q,6BAAtB;EACAzf,IAAAA,QAAQ,CAACmE,IAAT,CAAcmd,WAAd,CAA0B6B,SAA1B;EACA,QAAMC,cAAc,GAAGD,SAAS,CAAC/U,qBAAV,GAAkCiV,KAAlC,GAA0CF,SAAS,CAACG,WAA3E;EACAtjB,IAAAA,QAAQ,CAACmE,IAAT,CAAcoI,WAAd,CAA0B4W,SAA1B;EACA,WAAOC,cAAP;EACD;;;UAIM5W,kBAAP,yBAAuBpK,MAAvB,EAA+BgU,aAA/B,EAA8C;EAC5C,WAAO,KAAK3J,IAAL,CAAU,YAAY;EAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;EACA,UAAMwI,OAAO,oDACRnD,SADQ,GAERzC,WAAW,CAACG,iBAAZ,CAA8B,IAA9B,CAFQ,GAGR,OAAO1L,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHxC,CAAb;;EAMA,UAAI,CAACsC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIyb,KAAJ,CAAU,IAAV,EAAgB5M,OAAhB,CAAP;EACD;;EAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;EACD;;EAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ,CAAagU,aAAb;EACD,OAND,MAMO,IAAI7C,OAAO,CAAC8F,IAAZ,EAAkB;EACvB3U,QAAAA,IAAI,CAAC2U,IAAL,CAAUjD,aAAV;EACD;EACF,KArBM,CAAP;EAsBD;;UAEMxJ,cAAP,qBAAmBzM,OAAnB,EAA4B;EAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;EACD;;;;0BAxcoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOsF,SAAP;EACD;;;;;EAqcH;;;;;;;EAMA1I,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUzF,KAAV,EAAiB;EAAA;;EACrF,MAAMQ,MAAM,GAAGtH,sBAAsB,CAAC,IAAD,CAArC;;EAEA,MAAI,KAAKgV,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;EACnDlO,IAAAA,KAAK,CAAC3B,cAAN;EACD;;EAED6B,EAAAA,YAAY,CAACgC,GAAb,CAAiB1B,MAAjB,EAAyB8P,YAAzB,EAAqC,UAAAuF,SAAS,EAAI;EAChD,QAAIA,SAAS,CAACvX,gBAAd,EAAgC;EAC9B;EACA;EACD;;EAED4B,IAAAA,YAAY,CAACgC,GAAb,CAAiB1B,MAAjB,EAAyBiQ,cAAzB,EAAuC,YAAM;EAC3C,UAAIhV,SAAS,CAAC,OAAD,CAAb,EAAqB;EACnB,QAAA,OAAI,CAACwa,KAAL;EACD;EACF,KAJD;EAKD,GAXD;EAaA,MAAI/Y,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa+C,MAAb,EAAqB+C,UAArB,CAAX;;EACA,MAAI,CAACrG,IAAL,EAAW;EACT,QAAMtC,MAAM,qCACPuL,WAAW,CAACG,iBAAZ,CAA8B9F,MAA9B,CADO,GAEP2F,WAAW,CAACG,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;EAKApJ,IAAAA,IAAI,GAAG,IAAIyb,KAAJ,CAAUnY,MAAV,EAAkB5F,MAAlB,CAAP;EACD;;EAEDsC,EAAAA,IAAI,CAAC2U,IAAL,CAAU,IAAV;EACD,CA/BD;EAiCA,IAAM7S,GAAC,GAAGvC,SAAS,EAAnB;EAEA;;;;;;;EAMA;;EACA,IAAIuC,GAAJ,EAAO;EACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;EACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAasV,KAAK,CAAC3T,eAAnB;EACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyBqT,KAAzB;;EACA3Z,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;EACA,WAAOsT,KAAK,CAAC3T,eAAb;EACD,GAHD;EAID;;ECrmBD;;;;;;EAOA,IAAM+W,QAAQ,GAAG,CACf,YADe,EAEf,MAFe,EAGf,MAHe,EAIf,UAJe,EAKf,UALe,EAMf,QANe,EAOf,KAPe,EAQf,YARe,CAAjB;EAWA,IAAMC,sBAAsB,GAAG,gBAA/B;EAEA;;;;;;EAKA,IAAMC,gBAAgB,GAAG,6DAAzB;EAEA;;;;;;EAKA,IAAMC,gBAAgB,GAAG,oIAAzB;;EAEA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,IAAD,EAAOC,oBAAP,EAAgC;EACvD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcrkB,WAAd,EAAjB;;EAEA,MAAImkB,oBAAoB,CAAC7a,OAArB,CAA6B8a,QAA7B,MAA2C,CAAC,CAAhD,EAAmD;EACjD,QAAIP,QAAQ,CAACva,OAAT,CAAiB8a,QAAjB,MAA+B,CAAC,CAApC,EAAuC;EACrC,aAAO3d,OAAO,CAACyd,IAAI,CAACI,SAAL,CAAevkB,KAAf,CAAqBgkB,gBAArB,KAA0CG,IAAI,CAACI,SAAL,CAAevkB,KAAf,CAAqBikB,gBAArB,CAA3C,CAAd;EACD;;EAED,WAAO,IAAP;EACD;;EAED,MAAMO,MAAM,GAAGJ,oBAAoB,CAACzU,MAArB,CAA4B,UAAA8U,SAAS;EAAA,WAAIA,SAAS,YAAYrhB,MAAzB;EAAA,GAArC,CAAf,CAXuD;;EAcvD,OAAK,IAAIoF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG0b,MAAM,CAAC/b,MAA7B,EAAqCD,CAAC,GAAGM,GAAzC,EAA8CN,CAAC,EAA/C,EAAmD;EACjD,QAAI6b,QAAQ,CAACrkB,KAAT,CAAewkB,MAAM,CAAChc,CAAD,CAArB,CAAJ,EAA+B;EAC7B,aAAO,IAAP;EACD;EACF;;EAED,SAAO,KAAP;EACD,CArBD;;EAuBO,IAAMkc,gBAAgB,GAAG;EAC9B;EACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCX,sBAAvC,CAFyB;EAG9BY,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;EAI9BC,EAAAA,IAAI,EAAE,EAJwB;EAK9BC,EAAAA,CAAC,EAAE,EAL2B;EAM9BC,EAAAA,EAAE,EAAE,EAN0B;EAO9BC,EAAAA,GAAG,EAAE,EAPyB;EAQ9BC,EAAAA,IAAI,EAAE,EARwB;EAS9BC,EAAAA,GAAG,EAAE,EATyB;EAU9BC,EAAAA,EAAE,EAAE,EAV0B;EAW9BC,EAAAA,EAAE,EAAE,EAX0B;EAY9BC,EAAAA,EAAE,EAAE,EAZ0B;EAa9BC,EAAAA,EAAE,EAAE,EAb0B;EAc9BC,EAAAA,EAAE,EAAE,EAd0B;EAe9BC,EAAAA,EAAE,EAAE,EAf0B;EAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;EAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;EAkB9Bjd,EAAAA,CAAC,EAAE,EAlB2B;EAmB9Bkd,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;EAoB9BC,EAAAA,EAAE,EAAE,EApB0B;EAqB9BC,EAAAA,EAAE,EAAE,EArB0B;EAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;EAuB9BC,EAAAA,GAAG,EAAE,EAvByB;EAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;EAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;EA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;EA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;EA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;EA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;EA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;EA+B9BC,EAAAA,EAAE,EAAE;EA/B0B,CAAzB;EAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;EAAA;;EAC9D,MAAI,CAACF,UAAU,CAAC/d,MAAhB,EAAwB;EACtB,WAAO+d,UAAP;EACD;;EAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;EAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;EACD;;EAED,MAAMG,SAAS,GAAG,IAAIxlB,MAAM,CAACylB,SAAX,EAAlB;EACA,MAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;EACA,MAAMO,aAAa,GAAGlkB,MAAM,CAACC,IAAP,CAAY2jB,SAAZ,CAAtB;;EACA,MAAMO,QAAQ,GAAG,YAAGxX,MAAH,aAAaqX,eAAe,CAACniB,IAAhB,CAAqBmB,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;EAZ8D,6BAcrD2C,CAdqD,EAc9CM,GAd8C;EAAA;;EAe5D,QAAMme,EAAE,GAAGD,QAAQ,CAACxe,CAAD,CAAnB;EACA,QAAM0e,MAAM,GAAGD,EAAE,CAAC3C,QAAH,CAAYrkB,WAAZ,EAAf;;EAEA,QAAI8mB,aAAa,CAACxd,OAAd,CAAsB2d,MAAtB,MAAkC,CAAC,CAAvC,EAA0C;EACxCD,MAAAA,EAAE,CAACvjB,UAAH,CAAcoJ,WAAd,CAA0Bma,EAA1B;EAEA;EACD;;EAED,QAAME,aAAa,GAAG,aAAG3X,MAAH,cAAayX,EAAE,CAAC3Y,UAAhB,CAAtB;;EACA,QAAM8Y,qBAAqB,GAAG,GAAG5X,MAAH,CAAUiX,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACS,MAAD,CAAT,IAAqB,EAArD,CAA9B;EAEAC,IAAAA,aAAa,CAACpkB,OAAd,CAAsB,UAAAohB,IAAI,EAAI;EAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOiD,qBAAP,CAArB,EAAoD;EAClDH,QAAAA,EAAE,CAACpgB,eAAH,CAAmBsd,IAAI,CAACG,QAAxB;EACD;EACF,KAJD;EA3B4D;;EAc9D,OAAK,IAAI9b,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGke,QAAQ,CAACve,MAA/B,EAAuCD,CAAC,GAAGM,GAA3C,EAAgDN,CAAC,EAAjD,EAAqD;EAAA,qBAA5CA,CAA4C;;EAAA,6BAOjD;EAWH;;EAED,SAAOqe,eAAe,CAACniB,IAAhB,CAAqB2iB,SAA5B;EACD;;EClGD;;;;;;EAMA,IAAMjc,MAAI,GAAG,SAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,YAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAMgc,YAAY,GAAG,YAArB;EACA,IAAMC,kBAAkB,GAAG,IAAInkB,MAAJ,aAAqBkkB,YAArB,WAAyC,GAAzC,CAA3B;EACA,IAAME,qBAAqB,GAAG,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAA9B;EAEA,IAAMtW,aAAW,GAAG;EAClBuW,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,QAFQ;EAGlBC,EAAAA,KAAK,EAAE,2BAHW;EAIlBnd,EAAAA,OAAO,EAAE,QAJS;EAKlBod,EAAAA,KAAK,EAAE,iBALW;EAMlBC,EAAAA,IAAI,EAAE,SANY;EAOlBlnB,EAAAA,QAAQ,EAAE,kBAPQ;EAQlB8d,EAAAA,SAAS,EAAE,mBARO;EASlBhQ,EAAAA,MAAM,EAAE,0BATU;EAUlBsL,EAAAA,SAAS,EAAE,0BAVO;EAWlB+N,EAAAA,iBAAiB,EAAE,gBAXD;EAYlB/K,EAAAA,QAAQ,EAAE,kBAZQ;EAalBgL,EAAAA,QAAQ,EAAE,SAbQ;EAclBrB,EAAAA,UAAU,EAAE,iBAdM;EAelBD,EAAAA,SAAS,EAAE,QAfO;EAgBlBxJ,EAAAA,YAAY,EAAE;EAhBI,CAApB;EAmBA,IAAM+K,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAE,MADc;EAEpBC,EAAAA,GAAG,EAAE,KAFe;EAGpBC,EAAAA,KAAK,EAAE,OAHa;EAIpBC,EAAAA,MAAM,EAAE,QAJY;EAKpBC,EAAAA,IAAI,EAAE;EALc,CAAtB;EAQA,IAAM1X,SAAO,GAAG;EACd8W,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,yCACQ,mCADR,GAEQ,yCAJJ;EAKdld,EAAAA,OAAO,EAAE,aALK;EAMdmd,EAAAA,KAAK,EAAE,EANO;EAOdC,EAAAA,KAAK,EAAE,CAPO;EAQdC,EAAAA,IAAI,EAAE,KARQ;EASdlnB,EAAAA,QAAQ,EAAE,KATI;EAUd8d,EAAAA,SAAS,EAAE,KAVG;EAWdhQ,EAAAA,MAAM,EAAE,CAXM;EAYdsL,EAAAA,SAAS,EAAE,KAZG;EAad+N,EAAAA,iBAAiB,EAAE,MAbL;EAcd/K,EAAAA,QAAQ,EAAE,cAdI;EAedgL,EAAAA,QAAQ,EAAE,IAfI;EAgBdrB,EAAAA,UAAU,EAAE,IAhBE;EAiBdD,EAAAA,SAAS,EAAE/B,gBAjBG;EAkBdzH,EAAAA,YAAY,EAAE;EAlBA,CAAhB;EAqBA,IAAMpb,OAAK,GAAG;EACZymB,EAAAA,IAAI,WAAS/c,WADD;EAEZgd,EAAAA,MAAM,aAAWhd,WAFL;EAGZid,EAAAA,IAAI,WAASjd,WAHD;EAIZkd,EAAAA,KAAK,YAAUld,WAJH;EAKZmd,EAAAA,QAAQ,eAAand,WALT;EAMZod,EAAAA,KAAK,YAAUpd,WANH;EAOZqd,EAAAA,OAAO,cAAYrd,WAPP;EAQZsd,EAAAA,QAAQ,eAAatd,WART;EASZud,EAAAA,UAAU,iBAAevd,WATb;EAUZwd,EAAAA,UAAU,iBAAexd;EAVb,CAAd;EAaA,IAAM4U,iBAAe,GAAG,MAAxB;EACA,IAAM6I,gBAAgB,GAAG,OAAzB;EACA,IAAMvQ,iBAAe,GAAG,MAAxB;EAEA,IAAMwQ,gBAAgB,GAAG,MAAzB;EACA,IAAMC,eAAe,GAAG,KAAxB;EAEA,IAAMC,sBAAsB,GAAG,gBAA/B;EAEA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,cAAc,GAAG,QAAvB;EAEA;;;;;;MAMMC;EACJ,mBAAY9oB,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,QAAI,OAAOkb,MAAP,KAAkB,WAAtB,EAAmC;EACjC,YAAM,IAAI9F,SAAJ,CAAc,iEAAd,CAAN;EACD,KAH0B;;;EAM3B,SAAK0R,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,CAAhB;EACA,SAAKC,WAAL,GAAmB,EAAnB;EACA,SAAKC,cAAL,GAAsB,EAAtB;EACA,SAAKzM,OAAL,GAAe,IAAf,CAV2B;;EAa3B,SAAKzc,OAAL,GAAeA,OAAf;EACA,SAAKiC,MAAL,GAAc,KAAKoR,UAAL,CAAgBpR,MAAhB,CAAd;EACA,SAAKknB,GAAL,GAAW,IAAX;;EAEA,SAAKC,aAAL;;EACAzkB,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB,KAAK4d,WAAL,CAAiBhT,QAAvC,EAAiD,IAAjD;EACD;;;;;EAgCD;WAEAye,SAAA,kBAAS;EACP,SAAKN,UAAL,GAAkB,IAAlB;EACD;;WAEDO,UAAA,mBAAU;EACR,SAAKP,UAAL,GAAkB,KAAlB;EACD;;WAEDQ,gBAAA,yBAAgB;EACd,SAAKR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;EACD;;WAED/b,SAAA,gBAAO3F,KAAP,EAAc;EACZ,QAAI,CAAC,KAAK0hB,UAAV,EAAsB;EACpB;EACD;;EAED,QAAI1hB,KAAJ,EAAW;EACT,UAAMmiB,OAAO,GAAG,KAAK5L,WAAL,CAAiBhT,QAAjC;EACA,UAAI6T,OAAO,GAAG9Z,IAAI,CAACG,OAAL,CAAauC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,CAAd;;EAEA,UAAI,CAAC/K,OAAL,EAAc;EACZA,QAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRvW,KAAK,CAACQ,MADE,EAER,KAAK4hB,kBAAL,EAFQ,CAAV;EAIA9kB,QAAAA,IAAI,CAACC,OAAL,CAAayC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,EAAoC/K,OAApC;EACD;;EAEDA,MAAAA,OAAO,CAACyK,cAAR,CAAuBQ,KAAvB,GAA+B,CAACjL,OAAO,CAACyK,cAAR,CAAuBQ,KAAvD;;EAEA,UAAIjL,OAAO,CAACkL,oBAAR,EAAJ,EAAoC;EAClClL,QAAAA,OAAO,CAACmL,MAAR,CAAe,IAAf,EAAqBnL,OAArB;EACD,OAFD,MAEO;EACLA,QAAAA,OAAO,CAACoL,MAAR,CAAe,IAAf,EAAqBpL,OAArB;EACD;EACF,KAnBD,MAmBO;EACL,UAAI,KAAKqL,aAAL,GAAqB9d,SAArB,CAA+BE,QAA/B,CAAwC6L,iBAAxC,CAAJ,EAA8D;EAC5D,aAAK8R,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;EACA;EACD;;EAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF;;WAED9d,UAAA,mBAAU;EACRuJ,IAAAA,YAAY,CAAC,KAAK2T,QAAN,CAAZ;EAEArkB,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK/E,OAArB,EAA8B,KAAK4d,WAAL,CAAiBhT,QAA/C;EAEArD,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKxH,OAAtB,EAA+B,KAAK4d,WAAL,CAAiB/S,SAAhD;EACAtD,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKxH,OAAL,CAAa+L,OAAb,OAAyBuc,gBAAzB,CAAjB,EAA+D,eAA/D,EAAgF,KAAKyB,iBAArF;;EAEA,QAAI,KAAKZ,GAAT,EAAc;EACZ,WAAKA,GAAL,CAASnmB,UAAT,CAAoBoJ,WAApB,CAAgC,KAAK+c,GAArC;EACD;;EAED,SAAKJ,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,WAAL,GAAmB,IAAnB;EACA,SAAKC,cAAL,GAAsB,IAAtB;;EACA,QAAI,KAAKzM,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAae,OAAb;EACD;;EAED,SAAKf,OAAL,GAAe,IAAf;EACA,SAAKzc,OAAL,GAAe,IAAf;EACA,SAAKiC,MAAL,GAAc,IAAd;EACA,SAAKknB,GAAL,GAAW,IAAX;EACD;;WAEDjQ,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAKlZ,OAAL,CAAa+C,KAAb,CAAmBI,OAAnB,KAA+B,MAAnC,EAA2C;EACzC,YAAM,IAAIP,KAAJ,CAAU,qCAAV,CAAN;EACD;;EAED,QAAI,KAAKonB,aAAL,MAAwB,KAAKjB,UAAjC,EAA6C;EAC3C,UAAM7L,SAAS,GAAG3V,YAAY,CAACuC,OAAb,CAAqB,KAAK9J,OAA1B,EAAmC,KAAK4d,WAAL,CAAiBzc,KAAjB,CAAuB2mB,IAA1D,CAAlB;EACA,UAAMmC,UAAU,GAAG5mB,cAAc,CAAC,KAAKrD,OAAN,CAAjC;EACA,UAAMkqB,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAKjqB,OAAL,CAAamqB,aAAb,CAA2B7mB,eAA3B,CAA2C4I,QAA3C,CAAoD,KAAKlM,OAAzD,CADiB,GAEjBiqB,UAAU,CAAC/d,QAAX,CAAoB,KAAKlM,OAAzB,CAFF;;EAIA,UAAIkd,SAAS,CAACvX,gBAAV,IAA8B,CAACukB,UAAnC,EAA+C;EAC7C;EACD;;EAED,UAAMf,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,UAAMM,KAAK,GAAG5qB,MAAM,CAAC,KAAKoe,WAAL,CAAiBlT,IAAlB,CAApB;EAEAye,MAAAA,GAAG,CAAClc,YAAJ,CAAiB,IAAjB,EAAuBmd,KAAvB;EACA,WAAKpqB,OAAL,CAAaiN,YAAb,CAA0B,kBAA1B,EAA8Cmd,KAA9C;EAEA,WAAKC,UAAL;;EAEA,UAAI,KAAKpoB,MAAL,CAAY8kB,SAAhB,EAA2B;EACzBoC,QAAAA,GAAG,CAACnd,SAAJ,CAAc2C,GAAd,CAAkB8Q,iBAAlB;EACD;;EAED,UAAM1B,SAAS,GAAG,OAAO,KAAK9b,MAAL,CAAY8b,SAAnB,KAAiC,UAAjC,GAChB,KAAK9b,MAAL,CAAY8b,SAAZ,CAAsB1e,IAAtB,CAA2B,IAA3B,EAAiC8pB,GAAjC,EAAsC,KAAKnpB,OAA3C,CADgB,GAEhB,KAAKiC,MAAL,CAAY8b,SAFd;;EAIA,UAAMuM,UAAU,GAAG,KAAKC,cAAL,CAAoBxM,SAApB,CAAnB;;EACA,WAAKyM,mBAAL,CAAyBF,UAAzB;;EAEA,UAAMjR,SAAS,GAAG,KAAKoR,aAAL,EAAlB;;EACA9lB,MAAAA,IAAI,CAACC,OAAL,CAAaukB,GAAb,EAAkB,KAAKvL,WAAL,CAAiBhT,QAAnC,EAA6C,IAA7C;;EAEA,UAAI,CAAC,KAAK5K,OAAL,CAAamqB,aAAb,CAA2B7mB,eAA3B,CAA2C4I,QAA3C,CAAoD,KAAKid,GAAzD,CAAL,EAAoE;EAClE9P,QAAAA,SAAS,CAAC8H,WAAV,CAAsBgI,GAAtB;EACD;;EAED5hB,MAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAK9J,OAA1B,EAAmC,KAAK4d,WAAL,CAAiBzc,KAAjB,CAAuB6mB,QAA1D;EAEA,WAAKvL,OAAL,GAAe,IAAIU,MAAJ,CAAW,KAAKnd,OAAhB,EAAyBmpB,GAAzB,EAA8B,KAAK9L,gBAAL,CAAsBiN,UAAtB,CAA9B,CAAf;EAEAnB,MAAAA,GAAG,CAACnd,SAAJ,CAAc2C,GAAd,CAAkBoJ,iBAAlB,EAzC2C;EA4C3C;EACA;EACA;;EACA,UAAI,kBAAkBlY,QAAQ,CAACyD,eAA/B,EAAgD;EAAA;;EAC9C,oBAAGwL,MAAH,aAAajP,QAAQ,CAACmE,IAAT,CAAcgL,QAA3B,EAAqC3M,OAArC,CAA6C,UAAArC,OAAO,EAAI;EACtDuH,UAAAA,YAAY,CAAC+B,EAAb,CAAgBtJ,OAAhB,EAAyB,WAAzB,EAAsC2D,IAAI,EAA1C;EACD,SAFD;EAGD;;EAED,UAAMkW,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,YAAI,KAAI,CAAC5X,MAAL,CAAY8kB,SAAhB,EAA2B;EACzB,UAAA,KAAI,CAAC2D,cAAL;EACD;;EAED,YAAMC,cAAc,GAAG,KAAI,CAAC1B,WAA5B;EACA,QAAA,KAAI,CAACA,WAAL,GAAmB,IAAnB;EAEA1hB,QAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAI,CAAC9J,OAA1B,EAAmC,KAAI,CAAC4d,WAAL,CAAiBzc,KAAjB,CAAuB4mB,KAA1D;;EAEA,YAAI4C,cAAc,KAAKnC,eAAvB,EAAwC;EACtC,UAAA,KAAI,CAACqB,MAAL,CAAY,IAAZ,EAAkB,KAAlB;EACD;EACF,OAbD;;EAeA,UAAI,KAAKV,GAAL,CAASnd,SAAT,CAAmBE,QAAnB,CAA4BuT,iBAA5B,CAAJ,EAAkD;EAChD,YAAM9e,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK2oB,GAAN,CAA3D;EACA5hB,QAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAK4f,GAAtB,EAA2BnqB,cAA3B,EAA2C6a,QAA3C;EACAvY,QAAAA,oBAAoB,CAAC,KAAK6nB,GAAN,EAAWxoB,kBAAX,CAApB;EACD,OAJD,MAIO;EACLkZ,QAAAA,QAAQ;EACT;EACF;EACF;;WAEDZ,OAAA,gBAAO;EAAA;;EACL,QAAMkQ,GAAG,GAAG,KAAKW,aAAL,EAAZ;;EACA,QAAMjQ,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,UAAI,MAAI,CAACoP,WAAL,KAAqBV,gBAArB,IAAyCY,GAAG,CAACnmB,UAAjD,EAA6D;EAC3DmmB,QAAAA,GAAG,CAACnmB,UAAJ,CAAeoJ,WAAf,CAA2B+c,GAA3B;EACD;;EAED,MAAA,MAAI,CAACyB,cAAL;;EACA,MAAA,MAAI,CAAC5qB,OAAL,CAAamG,eAAb,CAA6B,kBAA7B;;EACAoB,MAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAAC9J,OAA1B,EAAmC,MAAI,CAAC4d,WAAL,CAAiBzc,KAAjB,CAAuB0mB,MAA1D;;EACA,MAAA,MAAI,CAACpL,OAAL,CAAae,OAAb;EACD,KATD;;EAWA,QAAMD,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB,KAAK9J,OAA1B,EAAmC,KAAK4d,WAAL,CAAiBzc,KAAjB,CAAuBymB,IAA1D,CAAlB;;EACA,QAAIrK,SAAS,CAAC5X,gBAAd,EAAgC;EAC9B;EACD;;EAEDwjB,IAAAA,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqB8L,iBAArB,EAlBK;EAqBL;;EACA,QAAI,kBAAkBlY,QAAQ,CAACyD,eAA/B,EAAgD;EAAA;;EAC9C,mBAAGwL,MAAH,cAAajP,QAAQ,CAACmE,IAAT,CAAcgL,QAA3B,EACG3M,OADH,CACW,UAAArC,OAAO;EAAA,eAAIuH,YAAY,CAACC,GAAb,CAAiBxH,OAAjB,EAA0B,WAA1B,EAAuC2D,IAAvC,CAAJ;EAAA,OADlB;EAED;;EAED,SAAKulB,cAAL,CAAoBN,aAApB,IAAqC,KAArC;EACA,SAAKM,cAAL,CAAoBP,aAApB,IAAqC,KAArC;EACA,SAAKO,cAAL,CAAoBR,aAApB,IAAqC,KAArC;;EAEA,QAAI,KAAKS,GAAL,CAASnd,SAAT,CAAmBE,QAAnB,CAA4BuT,iBAA5B,CAAJ,EAAkD;EAChD,UAAM9e,kBAAkB,GAAGH,gCAAgC,CAAC2oB,GAAD,CAA3D;EAEA5hB,MAAAA,YAAY,CAACgC,GAAb,CAAiB4f,GAAjB,EAAsBnqB,cAAtB,EAAsC6a,QAAtC;EACAvY,MAAAA,oBAAoB,CAAC6nB,GAAD,EAAMxoB,kBAAN,CAApB;EACD,KALD,MAKO;EACLkZ,MAAAA,QAAQ;EACT;;EAED,SAAKoP,WAAL,GAAmB,EAAnB;EACD;;WAEDxL,SAAA,kBAAS;EACP,QAAI,KAAKhB,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAaiB,cAAb;EACD;EACF;;;WAIDsM,gBAAA,yBAAgB;EACd,WAAOhkB,OAAO,CAAC,KAAK6kB,QAAL,EAAD,CAAd;EACD;;WAEDf,gBAAA,yBAAgB;EACd,QAAI,KAAKX,GAAT,EAAc;EACZ,aAAO,KAAKA,GAAZ;EACD;;EAED,QAAMnpB,OAAO,GAAGH,QAAQ,CAAC4F,aAAT,CAAuB,KAAvB,CAAhB;EACAzF,IAAAA,OAAO,CAAC2mB,SAAR,GAAoB,KAAK1kB,MAAL,CAAY+kB,QAAhC;EAEA,SAAKmC,GAAL,GAAWnpB,OAAO,CAACgP,QAAR,CAAiB,CAAjB,CAAX;EACA,WAAO,KAAKma,GAAZ;EACD;;WAEDkB,aAAA,sBAAa;EACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,SAAKgB,iBAAL,CAAuBjc,cAAc,CAACzJ,OAAf,CAAuBqjB,sBAAvB,EAA+CU,GAA/C,CAAvB,EAA4E,KAAK0B,QAAL,EAA5E;EACA1B,IAAAA,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqBwT,iBAArB,EAAsC1H,iBAAtC;EACD;;WAED+S,oBAAA,2BAAkB9qB,OAAlB,EAA2B+qB,OAA3B,EAAoC;EAClC,QAAI/qB,OAAO,KAAK,IAAhB,EAAsB;EACpB;EACD;;EAED,QAAI,OAAO+qB,OAAP,KAAmB,QAAnB,IAA+B3pB,SAAS,CAAC2pB,OAAD,CAA5C,EAAuD;EACrD,UAAIA,OAAO,CAAC5Q,MAAZ,EAAoB;EAClB4Q,QAAAA,OAAO,GAAGA,OAAO,CAAC,CAAD,CAAjB;EACD,OAHoD;;;EAMrD,UAAI,KAAK9oB,MAAL,CAAYklB,IAAhB,EAAsB;EACpB,YAAI4D,OAAO,CAAC/nB,UAAR,KAAuBhD,OAA3B,EAAoC;EAClCA,UAAAA,OAAO,CAAC2mB,SAAR,GAAoB,EAApB;EACA3mB,UAAAA,OAAO,CAACmhB,WAAR,CAAoB4J,OAApB;EACD;EACF,OALD,MAKO;EACL/qB,QAAAA,OAAO,CAACgrB,WAAR,GAAsBD,OAAO,CAACC,WAA9B;EACD;;EAED;EACD;;EAED,QAAI,KAAK/oB,MAAL,CAAYklB,IAAhB,EAAsB;EACpB,UAAI,KAAKllB,MAAL,CAAYolB,QAAhB,EAA0B;EACxB0D,QAAAA,OAAO,GAAGlF,YAAY,CAACkF,OAAD,EAAU,KAAK9oB,MAAL,CAAY8jB,SAAtB,EAAiC,KAAK9jB,MAAL,CAAY+jB,UAA7C,CAAtB;EACD;;EAEDhmB,MAAAA,OAAO,CAAC2mB,SAAR,GAAoBoE,OAApB;EACD,KAND,MAMO;EACL/qB,MAAAA,OAAO,CAACgrB,WAAR,GAAsBD,OAAtB;EACD;EACF;;WAEDF,WAAA,oBAAW;EACT,QAAI5D,KAAK,GAAG,KAAKjnB,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAZ;;EAEA,QAAI,CAAC+mB,KAAL,EAAY;EACVA,MAAAA,KAAK,GAAG,OAAO,KAAKhlB,MAAL,CAAYglB,KAAnB,KAA6B,UAA7B,GACN,KAAKhlB,MAAL,CAAYglB,KAAZ,CAAkB5nB,IAAlB,CAAuB,KAAKW,OAA5B,CADM,GAEN,KAAKiC,MAAL,CAAYglB,KAFd;EAGD;;EAED,WAAOA,KAAP;EACD;;;WAID5J,mBAAA,0BAAiBiN,UAAjB,EAA6B;EAAA;;EAC3B,QAAMW,eAAe,GAAG;EACtBlN,MAAAA,SAAS,EAAEuM,UADW;EAEtBpM,MAAAA,SAAS,EAAE;EACTnQ,QAAAA,MAAM,EAAE,KAAKiQ,UAAL,EADC;EAET5B,QAAAA,IAAI,EAAE;EACJ8O,UAAAA,QAAQ,EAAE,KAAKjpB,MAAL,CAAYmlB;EADlB,SAFG;EAKT+D,QAAAA,KAAK,EAAE;EACLnrB,UAAAA,OAAO,QAAM,KAAK4d,WAAL,CAAiBlT,IAAvB;EADF,SALE;EAQT0T,QAAAA,eAAe,EAAE;EACfC,UAAAA,iBAAiB,EAAE,KAAKpc,MAAL,CAAYoa;EADhB;EARR,OAFW;EActB+O,MAAAA,QAAQ,EAAE,kBAAA7mB,IAAI,EAAI;EAChB,YAAIA,IAAI,CAAC8mB,iBAAL,KAA2B9mB,IAAI,CAACwZ,SAApC,EAA+C;EAC7C,UAAA,MAAI,CAACuN,4BAAL,CAAkC/mB,IAAlC;EACD;EACF,OAlBqB;EAmBtBgnB,MAAAA,QAAQ,EAAE,kBAAAhnB,IAAI;EAAA,eAAI,MAAI,CAAC+mB,4BAAL,CAAkC/mB,IAAlC,CAAJ;EAAA;EAnBQ,KAAxB;EAsBA,6CACK0mB,eADL,GAEK,KAAKhpB,MAAL,CAAYsa,YAFjB;EAID;;WAEDiO,sBAAA,6BAAoBF,UAApB,EAAgC;EAC9B,SAAKR,aAAL,GAAqB9d,SAArB,CAA+B2C,GAA/B,CAAsCiY,YAAtC,SAAsD0D,UAAtD;EACD;;WAEDtM,aAAA,sBAAa;EAAA;;EACX,QAAMjQ,MAAM,GAAG,EAAf;;EAEA,QAAI,OAAO,KAAK9L,MAAL,CAAY8L,MAAnB,KAA8B,UAAlC,EAA8C;EAC5CA,MAAAA,MAAM,CAAC5G,EAAP,GAAY,UAAA5C,IAAI,EAAI;EAClBA,QAAAA,IAAI,CAAC0Z,OAAL,qCACK1Z,IAAI,CAAC0Z,OADV,GAEK,MAAI,CAAChc,MAAL,CAAY8L,MAAZ,CAAmBxJ,IAAI,CAAC0Z,OAAxB,EAAiC,MAAI,CAACje,OAAtC,KAAkD,EAFvD;EAKA,eAAOuE,IAAP;EACD,OAPD;EAQD,KATD,MASO;EACLwJ,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAK9L,MAAL,CAAY8L,MAA5B;EACD;;EAED,WAAOA,MAAP;EACD;;WAED0c,gBAAA,yBAAgB;EACd,QAAI,KAAKxoB,MAAL,CAAYoX,SAAZ,KAA0B,KAA9B,EAAqC;EACnC,aAAOxZ,QAAQ,CAACmE,IAAhB;EACD;;EAED,QAAI5C,SAAS,CAAC,KAAKa,MAAL,CAAYoX,SAAb,CAAb,EAAsC;EACpC,aAAO,KAAKpX,MAAL,CAAYoX,SAAnB;EACD;;EAED,WAAOxK,cAAc,CAACzJ,OAAf,CAAuB,KAAKnD,MAAL,CAAYoX,SAAnC,CAAP;EACD;;WAEDkR,iBAAA,wBAAexM,SAAf,EAA0B;EACxB,WAAOuJ,aAAa,CAACvJ,SAAS,CAAClb,WAAV,EAAD,CAApB;EACD;;WAEDumB,gBAAA,yBAAgB;EAAA;;EACd,QAAMoC,QAAQ,GAAG,KAAKvpB,MAAL,CAAY6H,OAAZ,CAAoB9I,KAApB,CAA0B,GAA1B,CAAjB;EAEAwqB,IAAAA,QAAQ,CAACnpB,OAAT,CAAiB,UAAAyH,OAAO,EAAI;EAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;EACvBvC,QAAAA,YAAY,CAAC+B,EAAb,CAAgB,MAAI,CAACtJ,OAArB,EACE,MAAI,CAAC4d,WAAL,CAAiBzc,KAAjB,CAAuB8mB,KADzB,EAEE,MAAI,CAAChmB,MAAL,CAAYhC,QAFd,EAGE,UAAAoH,KAAK;EAAA,iBAAI,MAAI,CAAC2F,MAAL,CAAY3F,KAAZ,CAAJ;EAAA,SAHP;EAKD,OAND,MAMO,IAAIyC,OAAO,KAAK+e,cAAhB,EAAgC;EACrC,YAAM4C,OAAO,GAAG3hB,OAAO,KAAK4e,aAAZ,GACd,MAAI,CAAC9K,WAAL,CAAiBzc,KAAjB,CAAuBinB,UADT,GAEd,MAAI,CAACxK,WAAL,CAAiBzc,KAAjB,CAAuB+mB,OAFzB;EAGA,YAAMwD,QAAQ,GAAG5hB,OAAO,KAAK4e,aAAZ,GACf,MAAI,CAAC9K,WAAL,CAAiBzc,KAAjB,CAAuBknB,UADR,GAEf,MAAI,CAACzK,WAAL,CAAiBzc,KAAjB,CAAuBgnB,QAFzB;EAIA5gB,QAAAA,YAAY,CAAC+B,EAAb,CAAgB,MAAI,CAACtJ,OAArB,EACEyrB,OADF,EAEE,MAAI,CAACxpB,MAAL,CAAYhC,QAFd,EAGE,UAAAoH,KAAK;EAAA,iBAAI,MAAI,CAACuiB,MAAL,CAAYviB,KAAZ,CAAJ;EAAA,SAHP;EAKAE,QAAAA,YAAY,CAAC+B,EAAb,CAAgB,MAAI,CAACtJ,OAArB,EACE0rB,QADF,EAEE,MAAI,CAACzpB,MAAL,CAAYhC,QAFd,EAGE,UAAAoH,KAAK;EAAA,iBAAI,MAAI,CAACwiB,MAAL,CAAYxiB,KAAZ,CAAJ;EAAA,SAHP;EAKD;EACF,KA1BD;;EA4BA,SAAK0iB,iBAAL,GAAyB,YAAM;EAC7B,UAAI,MAAI,CAAC/pB,OAAT,EAAkB;EAChB,QAAA,MAAI,CAACiZ,IAAL;EACD;EACF,KAJD;;EAMA1R,IAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKtJ,OAAL,CAAa+L,OAAb,OAAyBuc,gBAAzB,CAAhB,EACE,eADF,EAEE,KAAKyB,iBAFP;;EAKA,QAAI,KAAK9nB,MAAL,CAAYhC,QAAhB,EAA0B;EACxB,WAAKgC,MAAL,qCACK,KAAKA,MADV;EAEE6H,QAAAA,OAAO,EAAE,QAFX;EAGE7J,QAAAA,QAAQ,EAAE;EAHZ;EAKD,KAND,MAMO;EACL,WAAK0rB,SAAL;EACD;EACF;;WAEDA,YAAA,qBAAY;EACV,QAAMC,SAAS,GAAG,OAAO,KAAK5rB,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAzB;;EAEA,QAAI,KAAKF,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KAAsC0rB,SAAS,KAAK,QAAxD,EAAkE;EAChE,WAAK5rB,OAAL,CAAaiN,YAAb,CACE,qBADF,EAEE,KAAKjN,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KAAsC,EAFxC;EAKA,WAAKF,OAAL,CAAaiN,YAAb,CAA0B,OAA1B,EAAmC,EAAnC;EACD;EACF;;WAED2c,SAAA,gBAAOviB,KAAP,EAAcoX,OAAd,EAAuB;EACrB,QAAM+K,OAAO,GAAG,KAAK5L,WAAL,CAAiBhT,QAAjC;EACA6T,IAAAA,OAAO,GAAGA,OAAO,IAAI9Z,IAAI,CAACG,OAAL,CAAauC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,CAArB;;EAEA,QAAI,CAAC/K,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRvW,KAAK,CAACQ,MADE,EAER,KAAK4hB,kBAAL,EAFQ,CAAV;EAIA9kB,MAAAA,IAAI,CAACC,OAAL,CAAayC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,EAAoC/K,OAApC;EACD;;EAED,QAAIpX,KAAJ,EAAW;EACToX,MAAAA,OAAO,CAACyK,cAAR,CACE7hB,KAAK,CAACI,IAAN,KAAe,SAAf,GAA2BkhB,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;EAGD;;EAED,QAAIjK,OAAO,CAACqL,aAAR,GAAwB9d,SAAxB,CAAkCE,QAAlC,CAA2C6L,iBAA3C,KACA0G,OAAO,CAACwK,WAAR,KAAwBV,gBAD5B,EAC8C;EAC5C9J,MAAAA,OAAO,CAACwK,WAAR,GAAsBV,gBAAtB;EACA;EACD;;EAEDlT,IAAAA,YAAY,CAACoJ,OAAO,CAACuK,QAAT,CAAZ;EAEAvK,IAAAA,OAAO,CAACwK,WAAR,GAAsBV,gBAAtB;;EAEA,QAAI,CAAC9J,OAAO,CAACxc,MAAR,CAAeilB,KAAhB,IAAyB,CAACzI,OAAO,CAACxc,MAAR,CAAeilB,KAAf,CAAqBhO,IAAnD,EAAyD;EACvDuF,MAAAA,OAAO,CAACvF,IAAR;EACA;EACD;;EAEDuF,IAAAA,OAAO,CAACuK,QAAR,GAAmBlnB,UAAU,CAAC,YAAM;EAClC,UAAI2c,OAAO,CAACwK,WAAR,KAAwBV,gBAA5B,EAA8C;EAC5C9J,QAAAA,OAAO,CAACvF,IAAR;EACD;EACF,KAJ4B,EAI1BuF,OAAO,CAACxc,MAAR,CAAeilB,KAAf,CAAqBhO,IAJK,CAA7B;EAKD;;WAED2Q,SAAA,gBAAOxiB,KAAP,EAAcoX,OAAd,EAAuB;EACrB,QAAM+K,OAAO,GAAG,KAAK5L,WAAL,CAAiBhT,QAAjC;EACA6T,IAAAA,OAAO,GAAGA,OAAO,IAAI9Z,IAAI,CAACG,OAAL,CAAauC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,CAArB;;EAEA,QAAI,CAAC/K,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRvW,KAAK,CAACQ,MADE,EAER,KAAK4hB,kBAAL,EAFQ,CAAV;EAIA9kB,MAAAA,IAAI,CAACC,OAAL,CAAayC,KAAK,CAACQ,MAAnB,EAA2B2hB,OAA3B,EAAoC/K,OAApC;EACD;;EAED,QAAIpX,KAAJ,EAAW;EACToX,MAAAA,OAAO,CAACyK,cAAR,CACE7hB,KAAK,CAACI,IAAN,KAAe,UAAf,GAA4BkhB,aAA5B,GAA4CD,aAD9C,IAEI,KAFJ;EAGD;;EAED,QAAIjK,OAAO,CAACkL,oBAAR,EAAJ,EAAoC;EAClC;EACD;;EAEDtU,IAAAA,YAAY,CAACoJ,OAAO,CAACuK,QAAT,CAAZ;EAEAvK,IAAAA,OAAO,CAACwK,WAAR,GAAsBT,eAAtB;;EAEA,QAAI,CAAC/J,OAAO,CAACxc,MAAR,CAAeilB,KAAhB,IAAyB,CAACzI,OAAO,CAACxc,MAAR,CAAeilB,KAAf,CAAqBjO,IAAnD,EAAyD;EACvDwF,MAAAA,OAAO,CAACxF,IAAR;EACA;EACD;;EAEDwF,IAAAA,OAAO,CAACuK,QAAR,GAAmBlnB,UAAU,CAAC,YAAM;EAClC,UAAI2c,OAAO,CAACwK,WAAR,KAAwBT,eAA5B,EAA6C;EAC3C/J,QAAAA,OAAO,CAACxF,IAAR;EACD;EACF,KAJ4B,EAI1BwF,OAAO,CAACxc,MAAR,CAAeilB,KAAf,CAAqBjO,IAJK,CAA7B;EAKD;;WAED0Q,uBAAA,gCAAuB;EACrB,SAAK,IAAM7f,OAAX,IAAsB,KAAKof,cAA3B,EAA2C;EACzC,UAAI,KAAKA,cAAL,CAAoBpf,OAApB,CAAJ,EAAkC;EAChC,eAAO,IAAP;EACD;EACF;;EAED,WAAO,KAAP;EACD;;WAEDuJ,aAAA,oBAAWpR,MAAX,EAAmB;EACjB,QAAM4pB,cAAc,GAAGre,WAAW,CAACG,iBAAZ,CAA8B,KAAK3N,OAAnC,CAAvB;EAEAmC,IAAAA,MAAM,CAACC,IAAP,CAAYypB,cAAZ,EACGxpB,OADH,CACW,UAAAypB,QAAQ,EAAI;EACnB,UAAIhF,qBAAqB,CAACje,OAAtB,CAA8BijB,QAA9B,MAA4C,CAAC,CAAjD,EAAoD;EAClD,eAAOD,cAAc,CAACC,QAAD,CAArB;EACD;EACF,KALH;;EAOA,QAAI7pB,MAAM,IAAI,OAAOA,MAAM,CAACoX,SAAd,KAA4B,QAAtC,IAAkDpX,MAAM,CAACoX,SAAP,CAAiBc,MAAvE,EAA+E;EAC7ElY,MAAAA,MAAM,CAACoX,SAAP,GAAmBpX,MAAM,CAACoX,SAAP,CAAiB,CAAjB,CAAnB;EACD;;EAEDpX,IAAAA,MAAM,oDACD,KAAK2b,WAAL,CAAiB3N,OADhB,GAED4b,cAFC,GAGD,OAAO5pB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAH/C,CAAN;;EAMA,QAAI,OAAOA,MAAM,CAACilB,KAAd,KAAwB,QAA5B,EAAsC;EACpCjlB,MAAAA,MAAM,CAACilB,KAAP,GAAe;EACbhO,QAAAA,IAAI,EAAEjX,MAAM,CAACilB,KADA;EAEbjO,QAAAA,IAAI,EAAEhX,MAAM,CAACilB;EAFA,OAAf;EAID;;EAED,QAAI,OAAOjlB,MAAM,CAACglB,KAAd,KAAwB,QAA5B,EAAsC;EACpChlB,MAAAA,MAAM,CAACglB,KAAP,GAAehlB,MAAM,CAACglB,KAAP,CAAa7nB,QAAb,EAAf;EACD;;EAED,QAAI,OAAO6C,MAAM,CAAC8oB,OAAd,KAA0B,QAA9B,EAAwC;EACtC9oB,MAAAA,MAAM,CAAC8oB,OAAP,GAAiB9oB,MAAM,CAAC8oB,OAAP,CAAe3rB,QAAf,EAAjB;EACD;;EAED2C,IAAAA,eAAe,CACb2I,MADa,EAEbzI,MAFa,EAGb,KAAK2b,WAAL,CAAiBpN,WAHJ,CAAf;;EAMA,QAAIvO,MAAM,CAAColB,QAAX,EAAqB;EACnBplB,MAAAA,MAAM,CAAC+kB,QAAP,GAAkBnB,YAAY,CAAC5jB,MAAM,CAAC+kB,QAAR,EAAkB/kB,MAAM,CAAC8jB,SAAzB,EAAoC9jB,MAAM,CAAC+jB,UAA3C,CAA9B;EACD;;EAED,WAAO/jB,MAAP;EACD;;WAEDwnB,qBAAA,8BAAqB;EACnB,QAAMxnB,MAAM,GAAG,EAAf;;EAEA,QAAI,KAAKA,MAAT,EAAiB;EACf,WAAK,IAAMqC,GAAX,IAAkB,KAAKrC,MAAvB,EAA+B;EAC7B,YAAI,KAAK2b,WAAL,CAAiB3N,OAAjB,CAAyB3L,GAAzB,MAAkC,KAAKrC,MAAL,CAAYqC,GAAZ,CAAtC,EAAwD;EACtDrC,UAAAA,MAAM,CAACqC,GAAD,CAAN,GAAc,KAAKrC,MAAL,CAAYqC,GAAZ,CAAd;EACD;EACF;EACF;;EAED,WAAOrC,MAAP;EACD;;WAED2oB,iBAAA,0BAAiB;EACf,QAAMzB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,QAAMiC,QAAQ,GAAG5C,GAAG,CAACjpB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgCunB,kBAAhC,CAAjB;;EACA,QAAIkF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAChkB,MAAT,GAAkB,CAA3C,EAA8C;EAC5CgkB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;EAAA,eAAIA,KAAK,CAAC7rB,IAAN,EAAJ;EAAA,OAAlB,EACGiC,OADH,CACW,UAAA6pB,MAAM;EAAA,eAAI/C,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqBigB,MAArB,CAAJ;EAAA,OADjB;EAED;EACF;;WAEDZ,+BAAA,sCAA6Ba,UAA7B,EAAyC;EACvC,QAAMC,cAAc,GAAGD,UAAU,CAACtnB,QAAlC;EACA,SAAKskB,GAAL,GAAWiD,cAAc,CAACC,MAA1B;;EACA,SAAKzB,cAAL;;EACA,SAAKJ,mBAAL,CAAyB,KAAKD,cAAL,CAAoB4B,UAAU,CAACpO,SAA/B,CAAzB;EACD;;WAED2M,iBAAA,0BAAiB;EACf,QAAMvB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,QAAMwC,mBAAmB,GAAG,KAAKrqB,MAAL,CAAY8kB,SAAxC;;EACA,QAAIoC,GAAG,CAACjpB,YAAJ,CAAiB,aAAjB,MAAoC,IAAxC,EAA8C;EAC5C;EACD;;EAEDipB,IAAAA,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqBwT,iBAArB;EACA,SAAKxd,MAAL,CAAY8kB,SAAZ,GAAwB,KAAxB;EACA,SAAK9N,IAAL;EACA,SAAKC,IAAL;EACA,SAAKjX,MAAL,CAAY8kB,SAAZ,GAAwBuF,mBAAxB;EACD;;;YAIMjgB,kBAAP,yBAAuBpK,MAAvB,EAA+B;EAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;EAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;EACA,UAAMwI,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACsC,IAAD,IAAS,eAAe5B,IAAf,CAAoBV,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAACsC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIukB,OAAJ,CAAY,IAAZ,EAAkB1V,OAAlB,CAAP;EACD;;EAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;EACD;;EAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ;EACD;EACF,KAnBM,CAAP;EAoBD;;YAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;EAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;EACD;;;;0BAvoBoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOsF,SAAP;EACD;;;0BAEiB;EAChB,aAAOvF,MAAP;EACD;;;0BAEqB;EACpB,aAAOE,UAAP;EACD;;;0BAEkB;EACjB,aAAOzJ,OAAP;EACD;;;0BAEsB;EACrB,aAAO0J,WAAP;EACD;;;0BAEwB;EACvB,aAAO2F,aAAP;EACD;;;;;;EAgnBH,IAAMnK,GAAC,GAAGvC,SAAS,EAAnB;EAEA;;;;;;;EAMA;;EACA,IAAIuC,GAAJ,EAAO;EACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;EACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAaoe,OAAO,CAACzc,eAArB;EACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyBmc,OAAzB;;EACAziB,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;EACA,WAAOoc,OAAO,CAACzc,eAAf;EACD,GAHD;EAID;;EClyBD;;;;;;EAMA,IAAM3B,MAAI,GAAG,SAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,YAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAMgc,cAAY,GAAG,YAArB;EACA,IAAMC,oBAAkB,GAAG,IAAInkB,MAAJ,aAAqBkkB,cAArB,WAAyC,GAAzC,CAA3B;;EAEA,IAAM3W,SAAO,qCACR6Y,OAAO,CAAC7Y,OADA;EAEX8N,EAAAA,SAAS,EAAE,OAFA;EAGXjU,EAAAA,OAAO,EAAE,OAHE;EAIXihB,EAAAA,OAAO,EAAE,EAJE;EAKX/D,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,kCAFF,GAGE;EARD,EAAb;;EAWA,IAAMxW,aAAW,qCACZsY,OAAO,CAACtY,WADI;EAEfua,EAAAA,OAAO,EAAE;EAFM,EAAjB;;EAKA,IAAM5pB,OAAK,GAAG;EACZymB,EAAAA,IAAI,WAAS/c,WADD;EAEZgd,EAAAA,MAAM,aAAWhd,WAFL;EAGZid,EAAAA,IAAI,WAASjd,WAHD;EAIZkd,EAAAA,KAAK,YAAUld,WAJH;EAKZmd,EAAAA,QAAQ,eAAand,WALT;EAMZod,EAAAA,KAAK,YAAUpd,WANH;EAOZqd,EAAAA,OAAO,cAAYrd,WAPP;EAQZsd,EAAAA,QAAQ,eAAatd,WART;EASZud,EAAAA,UAAU,iBAAevd,WATb;EAUZwd,EAAAA,UAAU,iBAAexd;EAVb,CAAd;EAaA,IAAM4U,iBAAe,GAAG,MAAxB;EACA,IAAM1H,iBAAe,GAAG,MAAxB;EAEA,IAAMwU,cAAc,GAAG,iBAAvB;EACA,IAAMC,gBAAgB,GAAG,eAAzB;EAEA;;;;;;MAMMC;;;;;;;;;EA+BJ;WAEAzC,gBAAA,yBAAgB;EACd,WAAO,KAAKa,QAAL,MAAmB,KAAK6B,WAAL,EAA1B;EACD;;WAEDrC,aAAA,sBAAa;EACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ,CADW;;EAIX,SAAKgB,iBAAL,CAAuBjc,cAAc,CAACzJ,OAAf,CAAuBmnB,cAAvB,EAAuCpD,GAAvC,CAAvB,EAAoE,KAAK0B,QAAL,EAApE;;EACA,QAAIE,OAAO,GAAG,KAAK2B,WAAL,EAAd;;EACA,QAAI,OAAO3B,OAAP,KAAmB,UAAvB,EAAmC;EACjCA,MAAAA,OAAO,GAAGA,OAAO,CAAC1rB,IAAR,CAAa,KAAKW,OAAlB,CAAV;EACD;;EAED,SAAK8qB,iBAAL,CAAuBjc,cAAc,CAACzJ,OAAf,CAAuBonB,gBAAvB,EAAyCrD,GAAzC,CAAvB,EAAsE4B,OAAtE;EAEA5B,IAAAA,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqBwT,iBAArB,EAAsC1H,iBAAtC;EACD;;WAEDyS,sBAAA,6BAAoBF,UAApB,EAAgC;EAC9B,SAAKR,aAAL,GAAqB9d,SAArB,CAA+B2C,GAA/B,CAAsCiY,cAAtC,SAAsD0D,UAAtD;EACD;;;WAIDoC,cAAA,uBAAc;EACZ,WAAO,KAAK1sB,OAAL,CAAaE,YAAb,CAA0B,cAA1B,KACL,KAAK+B,MAAL,CAAY8oB,OADd;EAED;;WAEDH,iBAAA,0BAAiB;EACf,QAAMzB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,QAAMiC,QAAQ,GAAG5C,GAAG,CAACjpB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgCunB,oBAAhC,CAAjB;;EACA,QAAIkF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAChkB,MAAT,GAAkB,CAA3C,EAA8C;EAC5CgkB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;EAAA,eAAIA,KAAK,CAAC7rB,IAAN,EAAJ;EAAA,OAAlB,EACGiC,OADH,CACW,UAAA6pB,MAAM;EAAA,eAAI/C,GAAG,CAACnd,SAAJ,CAAcC,MAAd,CAAqBigB,MAArB,CAAJ;EAAA,OADjB;EAED;EACF;;;YAIM7f,kBAAP,yBAAuBpK,MAAvB,EAA+B;EAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;EAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;EACA,UAAMwI,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,UAAI,CAACsC,IAAD,IAAS,eAAe5B,IAAf,CAAoBV,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAACsC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIkoB,OAAJ,CAAY,IAAZ,EAAkBrZ,OAAlB,CAAP;EACAzO,QAAAA,IAAI,CAACC,OAAL,CAAa,IAAb,EAAmBgG,UAAnB,EAA6BrG,IAA7B;EACD;;EAED,UAAI,OAAOtC,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;EACD;;EAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ;EACD;EACF,KApBM,CAAP;EAqBD;;YAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;EAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;EACD;;;;EAnGD;0BAEqB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOsF,SAAP;EACD;;;0BAEiB;EAChB,aAAOvF,MAAP;EACD;;;0BAEqB;EACpB,aAAOE,UAAP;EACD;;;0BAEkB;EACjB,aAAOzJ,OAAP;EACD;;;0BAEsB;EACrB,aAAO0J,WAAP;EACD;;;0BAEwB;EACvB,aAAO2F,aAAP;EACD;;;;IA7BmBsY;;EAuGtB,IAAMziB,GAAC,GAAGvC,SAAS,EAAnB;EAEA;;;;;;EAKA;;EACA,IAAIuC,GAAJ,EAAO;EACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;EACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAa+hB,OAAO,CAACpgB,eAArB;EACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyB8f,OAAzB;;EACApmB,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;EACA,WAAO+f,OAAO,CAACpgB,eAAf;EACD,GAHD;EAID;;ECtKD;;;;;;EAMA,IAAM3B,MAAI,GAAG,WAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,cAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAMmF,SAAO,GAAG;EACdlC,EAAAA,MAAM,EAAE,EADM;EAEd4e,EAAAA,MAAM,EAAE,MAFM;EAGd9kB,EAAAA,MAAM,EAAE;EAHM,CAAhB;EAMA,IAAM2I,aAAW,GAAG;EAClBzC,EAAAA,MAAM,EAAE,QADU;EAElB4e,EAAAA,MAAM,EAAE,QAFU;EAGlB9kB,EAAAA,MAAM,EAAE;EAHU,CAApB;EAMA,IAAM+kB,cAAc,gBAAc/hB,WAAlC;EACA,IAAMgiB,YAAY,cAAYhiB,WAA9B;EACA,IAAM2G,qBAAmB,YAAU3G,WAAV,GAAsBC,cAA/C;EAEA,IAAMgiB,wBAAwB,GAAG,eAAjC;EACA,IAAMjgB,mBAAiB,GAAG,QAA1B;EAEA,IAAMkgB,iBAAiB,GAAG,qBAA1B;EACA,IAAMC,uBAAuB,GAAG,mBAAhC;EACA,IAAMC,kBAAkB,GAAG,WAA3B;EACA,IAAMC,kBAAkB,GAAG,WAA3B;EACA,IAAMC,mBAAmB,GAAG,kBAA5B;EACA,IAAMC,iBAAiB,GAAG,WAA1B;EACA,IAAMC,wBAAwB,GAAG,kBAAjC;EAEA,IAAMC,aAAa,GAAG,QAAtB;EACA,IAAMC,eAAe,GAAG,UAAxB;EAEA;;;;;;MAMMC;EACJ,qBAAYxtB,OAAZ,EAAqBiC,MAArB,EAA6B;EAAA;;EAC3B,SAAKsJ,QAAL,GAAgBvL,OAAhB;EACA,SAAKytB,cAAL,GAAsBztB,OAAO,CAACuV,OAAR,KAAoB,MAApB,GAA6B9U,MAA7B,GAAsCT,OAA5D;EACA,SAAKoT,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;EACA,SAAK4W,SAAL,GAAoB,KAAKzF,OAAL,CAAavL,MAAhB,SAA0BolB,kBAA1B,UACQ,KAAK7Z,OAAL,CAAavL,MADrB,SAC+BslB,mBAD/B,WAEQ,KAAK/Z,OAAL,CAAavL,MAFrB,UAEgCilB,wBAFhC,CAAjB;EAGA,SAAKY,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,CAArB;EAEAtmB,IAAAA,YAAY,CAAC+B,EAAb,CAAgB,KAAKmkB,cAArB,EAAqCZ,YAArC,EAAmD,UAAAxlB,KAAK;EAAA,aAAI,KAAI,CAACymB,QAAL,CAAczmB,KAAd,CAAJ;EAAA,KAAxD;EAEA,SAAK0mB,OAAL;;EACA,SAAKD,QAAL;;EAEAnpB,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEAmjB,UAAA,mBAAU;EAAA;;EACR,QAAMC,UAAU,GAAG,KAAKP,cAAL,KAAwB,KAAKA,cAAL,CAAoBhtB,MAA5C,GACjB6sB,aADiB,GAEjBC,eAFF;EAIA,QAAMU,YAAY,GAAG,KAAK7a,OAAL,CAAauZ,MAAb,KAAwB,MAAxB,GACnBqB,UADmB,GAEnB,KAAK5a,OAAL,CAAauZ,MAFf;EAIA,QAAMuB,UAAU,GAAGD,YAAY,KAAKV,eAAjB,GACjB,KAAKY,aAAL,EADiB,GAEjB,CAFF;EAIA,SAAKT,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EAEA,SAAKE,aAAL,GAAqB,KAAKO,gBAAL,EAArB;EAEA,QAAMC,OAAO,GAAGxf,cAAc,CAAC7J,IAAf,CAAoB,KAAK6T,SAAzB,CAAhB;EAEAwV,IAAAA,OAAO,CACJrC,GADH,CACO,UAAAhsB,OAAO,EAAI;EACd,UAAI6H,MAAJ;EACA,UAAMymB,cAAc,GAAGjuB,sBAAsB,CAACL,OAAD,CAA7C;;EAEA,UAAIsuB,cAAJ,EAAoB;EAClBzmB,QAAAA,MAAM,GAAGgH,cAAc,CAACzJ,OAAf,CAAuBkpB,cAAvB,CAAT;EACD;;EAED,UAAIzmB,MAAJ,EAAY;EACV,YAAM0mB,SAAS,GAAG1mB,MAAM,CAACoG,qBAAP,EAAlB;;EACA,YAAIsgB,SAAS,CAACrL,KAAV,IAAmBqL,SAAS,CAACC,MAAjC,EAAyC;EACvC,iBAAO,CACLhhB,WAAW,CAACygB,YAAD,CAAX,CAA0BpmB,MAA1B,EAAkCqG,GAAlC,GAAwCggB,UADnC,EAELI,cAFK,CAAP;EAID;EACF;;EAED,aAAO,IAAP;EACD,KApBH,EAqBGrf,MArBH,CAqBU,UAAAwf,IAAI;EAAA,aAAIA,IAAJ;EAAA,KArBd,EAsBGC,IAtBH,CAsBQ,UAACzK,CAAD,EAAIE,CAAJ;EAAA,aAAUF,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAC,CAAC,CAAD,CAAlB;EAAA,KAtBR,EAuBG9hB,OAvBH,CAuBW,UAAAosB,IAAI,EAAI;EACf,MAAA,MAAI,CAACf,QAAL,CAAcne,IAAd,CAAmBkf,IAAI,CAAC,CAAD,CAAvB;;EACA,MAAA,MAAI,CAACd,QAAL,CAAcpe,IAAd,CAAmBkf,IAAI,CAAC,CAAD,CAAvB;EACD,KA1BH;EA2BD;;WAED3iB,UAAA,mBAAU;EACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;EACArD,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKimB,cAAtB,EAAsC5iB,WAAtC;EAEA,SAAKU,QAAL,GAAgB,IAAhB;EACA,SAAKkiB,cAAL,GAAsB,IAAtB;EACA,SAAKra,OAAL,GAAe,IAAf;EACA,SAAKyF,SAAL,GAAiB,IAAjB;EACA,SAAK6U,QAAL,GAAgB,IAAhB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACD;;;WAIDxa,aAAA,oBAAWpR,MAAX,EAAmB;EACjBA,IAAAA,MAAM,qCACDgO,SADC,GAED,OAAOhO,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAF/C,CAAN;;EAKA,QAAI,OAAOA,MAAM,CAAC4F,MAAd,KAAyB,QAAzB,IAAqCzG,SAAS,CAACa,MAAM,CAAC4F,MAAR,CAAlD,EAAmE;EAAA,UAC3DzD,EAD2D,GACpDnC,MAAM,CAAC4F,MAD6C,CAC3DzD,EAD2D;;EAEjE,UAAI,CAACA,EAAL,EAAS;EACPA,QAAAA,EAAE,GAAG5E,MAAM,CAACkL,MAAD,CAAX;EACAzI,QAAAA,MAAM,CAAC4F,MAAP,CAAczD,EAAd,GAAmBA,EAAnB;EACD;;EAEDnC,MAAAA,MAAM,CAAC4F,MAAP,SAAoBzD,EAApB;EACD;;EAEDrC,IAAAA,eAAe,CAAC2I,MAAD,EAAOzI,MAAP,EAAeuO,aAAf,CAAf;EAEA,WAAOvO,MAAP;EACD;;WAEDksB,gBAAA,yBAAgB;EACd,WAAO,KAAKV,cAAL,KAAwBhtB,MAAxB,GACL,KAAKgtB,cAAL,CAAoBkB,WADf,GAEL,KAAKlB,cAAL,CAAoBtf,SAFtB;EAGD;;WAEDigB,mBAAA,4BAAmB;EACjB,WAAO,KAAKX,cAAL,CAAoBxL,YAApB,IAAoCviB,IAAI,CAACkvB,GAAL,CACzC/uB,QAAQ,CAACmE,IAAT,CAAcie,YAD2B,EAEzCpiB,QAAQ,CAACyD,eAAT,CAAyB2e,YAFgB,CAA3C;EAID;;WAED4M,mBAAA,4BAAmB;EACjB,WAAO,KAAKpB,cAAL,KAAwBhtB,MAAxB,GACLA,MAAM,CAACquB,WADF,GAEL,KAAKrB,cAAL,CAAoBxf,qBAApB,GAA4CugB,MAF9C;EAGD;;WAEDV,WAAA,oBAAW;EACT,QAAM3f,SAAS,GAAG,KAAKggB,aAAL,KAAuB,KAAK/a,OAAL,CAAarF,MAAtD;;EACA,QAAMkU,YAAY,GAAG,KAAKmM,gBAAL,EAArB;;EACA,QAAMW,SAAS,GAAG,KAAK3b,OAAL,CAAarF,MAAb,GAChBkU,YADgB,GAEhB,KAAK4M,gBAAL,EAFF;;EAIA,QAAI,KAAKhB,aAAL,KAAuB5L,YAA3B,EAAyC;EACvC,WAAK8L,OAAL;EACD;;EAED,QAAI5f,SAAS,IAAI4gB,SAAjB,EAA4B;EAC1B,UAAMlnB,MAAM,GAAG,KAAK8lB,QAAL,CAAc,KAAKA,QAAL,CAAc5lB,MAAd,GAAuB,CAArC,CAAf;;EAEA,UAAI,KAAK6lB,aAAL,KAAuB/lB,MAA3B,EAAmC;EACjC,aAAKmnB,SAAL,CAAennB,MAAf;EACD;;EAED;EACD;;EAED,QAAI,KAAK+lB,aAAL,IAAsBzf,SAAS,GAAG,KAAKuf,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;EAC9E,WAAKE,aAAL,GAAqB,IAArB;;EACA,WAAKqB,MAAL;;EACA;EACD;;EAED,SAAK,IAAInnB,CAAC,GAAG,KAAK4lB,QAAL,CAAc3lB,MAA3B,EAAmCD,CAAC,EAApC,GAAyC;EACvC,UAAMonB,cAAc,GAAG,KAAKtB,aAAL,KAAuB,KAAKD,QAAL,CAAc7lB,CAAd,CAAvB,IACnBqG,SAAS,IAAI,KAAKuf,QAAL,CAAc5lB,CAAd,CADM,KAElB,OAAO,KAAK4lB,QAAL,CAAc5lB,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IACGqG,SAAS,GAAG,KAAKuf,QAAL,CAAc5lB,CAAC,GAAG,CAAlB,CAHG,CAAvB;;EAKA,UAAIonB,cAAJ,EAAoB;EAClB,aAAKF,SAAL,CAAe,KAAKrB,QAAL,CAAc7lB,CAAd,CAAf;EACD;EACF;EACF;;WAEDknB,YAAA,mBAAUnnB,MAAV,EAAkB;EAChB,SAAK+lB,aAAL,GAAqB/lB,MAArB;;EAEA,SAAKonB,MAAL;;EAEA,QAAME,OAAO,GAAG,KAAKtW,SAAL,CAAe7X,KAAf,CAAqB,GAArB,EACbgrB,GADa,CACT,UAAA/rB,QAAQ;EAAA,aAAOA,QAAP,uBAAgC4H,MAAhC,YAA4C5H,QAA5C,gBAA8D4H,MAA9D;EAAA,KADC,CAAhB;;EAGA,QAAMunB,IAAI,GAAGvgB,cAAc,CAACzJ,OAAf,CAAuB+pB,OAAO,CAACE,IAAR,CAAa,GAAb,CAAvB,CAAb;;EAEA,QAAID,IAAI,CAACpjB,SAAL,CAAeE,QAAf,CAAwB4gB,wBAAxB,CAAJ,EAAuD;EACrDje,MAAAA,cAAc,CACXzJ,OADH,CACWioB,wBADX,EACqC+B,IAAI,CAACrjB,OAAL,CAAaqhB,iBAAb,CADrC,EAEGphB,SAFH,CAEa2C,GAFb,CAEiB9B,mBAFjB;EAIAuiB,MAAAA,IAAI,CAACpjB,SAAL,CAAe2C,GAAf,CAAmB9B,mBAAnB;EACD,KAND,MAMO;EACL;EACAuiB,MAAAA,IAAI,CAACpjB,SAAL,CAAe2C,GAAf,CAAmB9B,mBAAnB;EAEAgC,MAAAA,cAAc,CACXM,OADH,CACWigB,IADX,EACiBpC,uBADjB,EAEG3qB,OAFH,CAEW,UAAAitB,SAAS,EAAI;EACpB;EACA;EACAzgB,QAAAA,cAAc,CAACW,IAAf,CAAoB8f,SAApB,EAAkCrC,kBAAlC,UAAyDE,mBAAzD,EACG9qB,OADH,CACW,UAAAosB,IAAI;EAAA,iBAAIA,IAAI,CAACziB,SAAL,CAAe2C,GAAf,CAAmB9B,mBAAnB,CAAJ;EAAA,SADf,EAHoB;;EAOpBgC,QAAAA,cAAc,CAACW,IAAf,CAAoB8f,SAApB,EAA+BpC,kBAA/B,EACG7qB,OADH,CACW,UAAAktB,OAAO,EAAI;EAClB1gB,UAAAA,cAAc,CAACG,QAAf,CAAwBugB,OAAxB,EAAiCtC,kBAAjC,EACG5qB,OADH,CACW,UAAAosB,IAAI;EAAA,mBAAIA,IAAI,CAACziB,SAAL,CAAe2C,GAAf,CAAmB9B,mBAAnB,CAAJ;EAAA,WADf;EAED,SAJH;EAKD,OAdH;EAeD;;EAEDtF,IAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAK2jB,cAA1B,EAA0Cb,cAA1C,EAA0D;EACxD3W,MAAAA,aAAa,EAAEpO;EADyC,KAA1D;EAGD;;WAEDonB,SAAA,kBAAS;EACPpgB,IAAAA,cAAc,CAAC7J,IAAf,CAAoB,KAAK6T,SAAzB,EACG5J,MADH,CACU,UAAAugB,IAAI;EAAA,aAAIA,IAAI,CAACxjB,SAAL,CAAeE,QAAf,CAAwBW,mBAAxB,CAAJ;EAAA,KADd,EAEGxK,OAFH,CAEW,UAAAmtB,IAAI;EAAA,aAAIA,IAAI,CAACxjB,SAAL,CAAeC,MAAf,CAAsBY,mBAAtB,CAAJ;EAAA,KAFf;EAGD;;;cAIMR,kBAAP,yBAAuBpK,MAAvB,EAA+B;EAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;EAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;EACA,UAAMwI,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACsC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIipB,SAAJ,CAAc,IAAd,EAAoBpa,OAApB,CAAP;EACD;;EAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;EACD;;EAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ;EACD;EACF,KAfM,CAAP;EAgBD;;cAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;EAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;EACD;;;;0BAjOoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOsF,SAAP;EACD;;;;;EA8NH;;;;;;;EAMA1I,YAAY,CAAC+B,EAAb,CAAgB7I,MAAhB,EAAwB+Q,qBAAxB,EAA6C,YAAM;EACjD3C,EAAAA,cAAc,CAAC7J,IAAf,CAAoB+nB,iBAApB,EACG1qB,OADH,CACW,UAAAotB,GAAG;EAAA,WAAI,IAAIjC,SAAJ,CAAciC,GAAd,EAAmBjiB,WAAW,CAACG,iBAAZ,CAA8B8hB,GAA9B,CAAnB,CAAJ;EAAA,GADd;EAED,CAHD;EAKA,IAAMppB,GAAC,GAAGvC,SAAS,EAAnB;EAEA;;;;;;EAKA;;EACA,IAAIuC,GAAJ,EAAO;EACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;EACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAa8iB,SAAS,CAACnhB,eAAvB;EACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyB6gB,SAAzB;;EACAnnB,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;EACA,WAAO8gB,SAAS,CAACnhB,eAAjB;EACD,GAHD;EAID;;ECtUD;;;;;;EAMA,IAAM3B,MAAI,GAAG,KAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,QAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAM+M,YAAU,YAAUhN,WAA1B;EACA,IAAMiN,cAAY,cAAYjN,WAA9B;EACA,IAAM8M,YAAU,YAAU9M,WAA1B;EACA,IAAM+M,aAAW,aAAW/M,WAA5B;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAM4kB,wBAAwB,GAAG,eAAjC;EACA,IAAM7iB,mBAAiB,GAAG,QAA1B;EACA,IAAMsO,qBAAmB,GAAG,UAA5B;EACA,IAAMsE,iBAAe,GAAG,MAAxB;EACA,IAAM1H,iBAAe,GAAG,MAAxB;EAEA,IAAMqV,mBAAiB,GAAG,WAA1B;EACA,IAAMJ,yBAAuB,GAAG,mBAAhC;EACA,IAAMhb,iBAAe,GAAG,SAAxB;EACA,IAAM2d,kBAAkB,GAAG,uBAA3B;EACA,IAAM7iB,sBAAoB,GAAG,iEAA7B;EACA,IAAMugB,0BAAwB,GAAG,kBAAjC;EACA,IAAMuC,8BAA8B,GAAG,iCAAvC;EAEA;;;;;;MAMMC;EACJ,eAAY7vB,OAAZ,EAAqB;EACnB,SAAKuL,QAAL,GAAgBvL,OAAhB;EAEA2E,IAAAA,IAAI,CAACC,OAAL,CAAa,KAAK2G,QAAlB,EAA4BX,UAA5B,EAAsC,IAAtC;EACD;;;;;EAQD;WAEAsO,OAAA,gBAAO;EAAA;;EACL,QAAK,KAAK3N,QAAL,CAAcvI,UAAd,IACH,KAAKuI,QAAL,CAAcvI,UAAd,CAAyB3B,QAAzB,KAAsCgO,IAAI,CAACC,YADxC,IAEH,KAAK/D,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCW,mBAAjC,CAFE,IAGF,KAAKtB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCiP,qBAAjC,CAHF,EAGyD;EACvD;EACD;;EAED,QAAI1L,QAAJ;EACA,QAAM5H,MAAM,GAAGtH,sBAAsB,CAAC,KAAKgL,QAAN,CAArC;;EACA,QAAMukB,WAAW,GAAG,KAAKvkB,QAAL,CAAcQ,OAAd,CAAsBihB,yBAAtB,CAApB;;EAEA,QAAI8C,WAAJ,EAAiB;EACf,UAAMC,YAAY,GAAGD,WAAW,CAAClM,QAAZ,KAAyB,IAAzB,IAAiCkM,WAAW,CAAClM,QAAZ,KAAyB,IAA1D,GAAiE+L,kBAAjE,GAAsF3d,iBAA3G;EACAvC,MAAAA,QAAQ,GAAGZ,cAAc,CAAC7J,IAAf,CAAoB+qB,YAApB,EAAkCD,WAAlC,CAAX;EACArgB,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAAC1H,MAAT,GAAkB,CAAnB,CAAnB;EACD;;EAED,QAAIwV,SAAS,GAAG,IAAhB;;EAEA,QAAI9N,QAAJ,EAAc;EACZ8N,MAAAA,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB2F,QAArB,EAA+BoI,YAA/B,EAA2C;EACrD5B,QAAAA,aAAa,EAAE,KAAK1K;EADiC,OAA3C,CAAZ;EAGD;;EAED,QAAM2R,SAAS,GAAG3V,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCoM,YAApC,EAAgD;EAChE1B,MAAAA,aAAa,EAAExG;EADiD,KAAhD,CAAlB;;EAIA,QAAIyN,SAAS,CAACvX,gBAAV,IACD4X,SAAS,KAAK,IAAd,IAAsBA,SAAS,CAAC5X,gBADnC,EACsD;EACpD;EACD;;EAED,SAAKqpB,SAAL,CACE,KAAKzjB,QADP,EAEEukB,WAFF;;EAKA,QAAMjW,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrBtS,MAAAA,YAAY,CAACuC,OAAb,CAAqB2F,QAArB,EAA+BqI,cAA/B,EAA6C;EAC3C7B,QAAAA,aAAa,EAAE,KAAI,CAAC1K;EADuB,OAA7C;EAGAhE,MAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAI,CAACyB,QAA1B,EAAoCqM,aAApC,EAAiD;EAC/C3B,QAAAA,aAAa,EAAExG;EADgC,OAAjD;EAGD,KAPD;;EASA,QAAI5H,MAAJ,EAAY;EACV,WAAKmnB,SAAL,CAAennB,MAAf,EAAuBA,MAAM,CAAC7E,UAA9B,EAA0C6W,QAA1C;EACD,KAFD,MAEO;EACLA,MAAAA,QAAQ;EACT;EACF;;WAED/N,UAAA,mBAAU;EACRnH,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;EACA,SAAKW,QAAL,GAAgB,IAAhB;EACD;;;WAIDyjB,YAAA,mBAAUhvB,OAAV,EAAmBqZ,SAAnB,EAA8BqI,QAA9B,EAAwC;EAAA;;EACtC,QAAMsO,cAAc,GAAG3W,SAAS,KAAKA,SAAS,CAACuK,QAAV,KAAuB,IAAvB,IAA+BvK,SAAS,CAACuK,QAAV,KAAuB,IAA3D,CAAT,GACrB/U,cAAc,CAAC7J,IAAf,CAAoB2qB,kBAApB,EAAwCtW,SAAxC,CADqB,GAErBxK,cAAc,CAACG,QAAf,CAAwBqK,SAAxB,EAAmCrH,iBAAnC,CAFF;EAIA,QAAMie,MAAM,GAAGD,cAAc,CAAC,CAAD,CAA7B;EACA,QAAM/V,eAAe,GAAGyH,QAAQ,IAC7BuO,MAAM,IAAIA,MAAM,CAACjkB,SAAP,CAAiBE,QAAjB,CAA0BuT,iBAA1B,CADb;;EAGA,QAAM5F,QAAQ,GAAG,SAAXA,QAAW;EAAA,aAAM,MAAI,CAACqW,mBAAL,CACrBlwB,OADqB,EAErBiwB,MAFqB,EAGrBvO,QAHqB,CAAN;EAAA,KAAjB;;EAMA,QAAIuO,MAAM,IAAIhW,eAAd,EAA+B;EAC7B,UAAMtZ,kBAAkB,GAAGH,gCAAgC,CAACyvB,MAAD,CAA3D;EACAA,MAAAA,MAAM,CAACjkB,SAAP,CAAiBC,MAAjB,CAAwB8L,iBAAxB;EAEAxQ,MAAAA,YAAY,CAACgC,GAAb,CAAiB0mB,MAAjB,EAAyBjxB,cAAzB,EAAyC6a,QAAzC;EACAvY,MAAAA,oBAAoB,CAAC2uB,MAAD,EAAStvB,kBAAT,CAApB;EACD,KAND,MAMO;EACLkZ,MAAAA,QAAQ;EACT;EACF;;WAEDqW,sBAAA,6BAAoBlwB,OAApB,EAA6BiwB,MAA7B,EAAqCvO,QAArC,EAA+C;EAC7C,QAAIuO,MAAJ,EAAY;EACVA,MAAAA,MAAM,CAACjkB,SAAP,CAAiBC,MAAjB,CAAwBY,mBAAxB;EAEA,UAAMsjB,aAAa,GAAGthB,cAAc,CAACzJ,OAAf,CAAuBwqB,8BAAvB,EAAuDK,MAAM,CAACjtB,UAA9D,CAAtB;;EAEA,UAAImtB,aAAJ,EAAmB;EACjBA,QAAAA,aAAa,CAACnkB,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;EACD;;EAED,UAAIojB,MAAM,CAAC/vB,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;EACzC+vB,QAAAA,MAAM,CAAChjB,YAAP,CAAoB,eAApB,EAAqC,KAArC;EACD;EACF;;EAEDjN,IAAAA,OAAO,CAACgM,SAAR,CAAkB2C,GAAlB,CAAsB9B,mBAAtB;;EACA,QAAI7M,OAAO,CAACE,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;EAC1CF,MAAAA,OAAO,CAACiN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAEDrJ,IAAAA,MAAM,CAAC5D,OAAD,CAAN;;EAEA,QAAIA,OAAO,CAACgM,SAAR,CAAkBE,QAAlB,CAA2BuT,iBAA3B,CAAJ,EAAiD;EAC/Czf,MAAAA,OAAO,CAACgM,SAAR,CAAkB2C,GAAlB,CAAsBoJ,iBAAtB;EACD;;EAED,QAAI/X,OAAO,CAACgD,UAAR,IAAsBhD,OAAO,CAACgD,UAAR,CAAmBgJ,SAAnB,CAA6BE,QAA7B,CAAsCwjB,wBAAtC,CAA1B,EAA2F;EACzF,UAAMU,eAAe,GAAGpwB,OAAO,CAAC+L,OAAR,CAAgBqhB,mBAAhB,CAAxB;;EAEA,UAAIgD,eAAJ,EAAqB;EACnBvhB,QAAAA,cAAc,CAAC7J,IAAf,CAAoBqoB,0BAApB,EACGhrB,OADH,CACW,UAAAguB,QAAQ;EAAA,iBAAIA,QAAQ,CAACrkB,SAAT,CAAmB2C,GAAnB,CAAuB9B,mBAAvB,CAAJ;EAAA,SADnB;EAED;;EAED7M,MAAAA,OAAO,CAACiN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAED,QAAIyU,QAAJ,EAAc;EACZA,MAAAA,QAAQ;EACT;EACF;;;QAIMrV,kBAAP,yBAAuBpK,MAAvB,EAA+B;EAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;EAC3B,UAAM/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,KAAgC,IAAIilB,GAAJ,CAAQ,IAAR,CAA7C;;EAEA,UAAI,OAAO5tB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;EACD;;EAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;QAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;EAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;EACD;;;;0BA3JoB;EACnB,aAAOD,SAAP;EACD;;;;;EA4JH;;;;;;;EAMApD,YAAY,CAAC+B,EAAb,CAAgBzJ,QAAhB,EAA0BqL,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUzF,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAAC3B,cAAN;EAEA,MAAMnB,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,KAAgC,IAAIilB,GAAJ,CAAQ,IAAR,CAA7C;EACAtrB,EAAAA,IAAI,CAAC2U,IAAL;EACD,CALD;EAOA,IAAM7S,GAAC,GAAGvC,SAAS,EAAnB;EAEA;;;;;;;EAMA;;EACA,IAAIuC,GAAJ,EAAO;EACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;EACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAamlB,GAAG,CAACxjB,eAAjB;EACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyBkjB,GAAzB;;EACAxpB,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;EACA,WAAOmjB,GAAG,CAACxjB,eAAX;EACD,GAHD;EAID;;EC3OD;;;;;;EAMA,IAAM3B,MAAI,GAAG,OAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,UAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EAEA,IAAMsU,qBAAmB,qBAAmBrU,WAA5C;EACA,IAAMgN,YAAU,YAAUhN,WAA1B;EACA,IAAMiN,cAAY,cAAYjN,WAA9B;EACA,IAAM8M,YAAU,YAAU9M,WAA1B;EACA,IAAM+M,aAAW,aAAW/M,WAA5B;EAEA,IAAM4U,iBAAe,GAAG,MAAxB;EACA,IAAM6Q,eAAe,GAAG,MAAxB;EACA,IAAMvY,iBAAe,GAAG,MAAxB;EACA,IAAMwY,kBAAkB,GAAG,SAA3B;EAEA,IAAM/f,aAAW,GAAG;EAClBuW,EAAAA,SAAS,EAAE,SADO;EAElByJ,EAAAA,QAAQ,EAAE,SAFQ;EAGlBtJ,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,IAAMjX,SAAO,GAAG;EACd8W,EAAAA,SAAS,EAAE,IADG;EAEdyJ,EAAAA,QAAQ,EAAE,IAFI;EAGdtJ,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA,IAAMrH,uBAAqB,GAAG,wBAA9B;EAEA;;;;;;MAMM4Q;EACJ,iBAAYzwB,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAKsJ,QAAL,GAAgBvL,OAAhB;EACA,SAAKoT,OAAL,GAAe,KAAKC,UAAL,CAAgBpR,MAAhB,CAAf;EACA,SAAK+mB,QAAL,GAAgB,IAAhB;;EACA,SAAKI,aAAL;;EACAzkB,IAAAA,IAAI,CAACC,OAAL,CAAa5E,OAAb,EAAsB4K,UAAtB,EAAgC,IAAhC;EACD;;;;;EAgBD;WAEAsO,OAAA,gBAAO;EAAA;;EACL,QAAMgE,SAAS,GAAG3V,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCoM,YAApC,CAAlB;;EAEA,QAAIuF,SAAS,CAACvX,gBAAd,EAAgC;EAC9B;EACD;;EAED,QAAI,KAAKyN,OAAL,CAAa2T,SAAjB,EAA4B;EAC1B,WAAKxb,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4B8Q,iBAA5B;EACD;;EAED,QAAM5F,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,KAAI,CAACtO,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BskB,kBAA/B;;EACA,MAAA,KAAI,CAAChlB,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4BoJ,iBAA5B;;EAEAxQ,MAAAA,YAAY,CAACuC,OAAb,CAAqB,KAAI,CAACyB,QAA1B,EAAoCqM,aAApC;;EAEA,UAAI,KAAI,CAACxE,OAAL,CAAaod,QAAjB,EAA2B;EACzB,QAAA,KAAI,CAACxH,QAAL,GAAgBlnB,UAAU,CAAC,YAAM;EAC/B,UAAA,KAAI,CAACmX,IAAL;EACD,SAFyB,EAEvB,KAAI,CAAC7F,OAAL,CAAa8T,KAFU,CAA1B;EAGD;EACF,KAXD;;EAaA,SAAK3b,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BqkB,eAA/B;;EACA1sB,IAAAA,MAAM,CAAC,KAAK2H,QAAN,CAAN;;EACA,SAAKA,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4B4hB,kBAA5B;;EACA,QAAI,KAAKnd,OAAL,CAAa2T,SAAjB,EAA4B;EAC1B,UAAMpmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK+K,QAAN,CAA3D;EAEAhE,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD6a,QAAhD;EACAvY,MAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgB5K,kBAAhB,CAApB;EACD,KALD,MAKO;EACLkZ,MAAAA,QAAQ;EACT;EACF;;WAEDZ,OAAA,gBAAO;EAAA;;EACL,QAAI,CAAC,KAAK1N,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,iBAAjC,CAAL,EAAwD;EACtD;EACD;;EAED,QAAMwF,SAAS,GAAGhW,YAAY,CAACuC,OAAb,CAAqB,KAAKyB,QAA1B,EAAoCsM,YAApC,CAAlB;;EAEA,QAAI0F,SAAS,CAAC5X,gBAAd,EAAgC;EAC9B;EACD;;EAED,QAAMkU,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAACtO,QAAL,CAAcS,SAAd,CAAwB2C,GAAxB,CAA4B2hB,eAA5B;;EACA/oB,MAAAA,YAAY,CAACuC,OAAb,CAAqB,MAAI,CAACyB,QAA1B,EAAoCuM,cAApC;EACD,KAHD;;EAKA,SAAKvM,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B8L,iBAA/B;;EACA,QAAI,KAAK3E,OAAL,CAAa2T,SAAjB,EAA4B;EAC1B,UAAMpmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK+K,QAAN,CAA3D;EAEAhE,MAAAA,YAAY,CAACgC,GAAb,CAAiB,KAAKgC,QAAtB,EAAgCvM,cAAhC,EAAgD6a,QAAhD;EACAvY,MAAAA,oBAAoB,CAAC,KAAKiK,QAAN,EAAgB5K,kBAAhB,CAApB;EACD,KALD,MAKO;EACLkZ,MAAAA,QAAQ;EACT;EACF;;WAED/N,UAAA,mBAAU;EACRuJ,IAAAA,YAAY,CAAC,KAAK2T,QAAN,CAAZ;EACA,SAAKA,QAAL,GAAgB,IAAhB;;EAEA,QAAI,KAAKzd,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC6L,iBAAjC,CAAJ,EAAuD;EACrD,WAAKxM,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B8L,iBAA/B;EACD;;EAEDxQ,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+D,QAAtB,EAAgC2T,qBAAhC;EACAva,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKwG,QAArB,EAA+BX,UAA/B;EAEA,SAAKW,QAAL,GAAgB,IAAhB;EACA,SAAK6H,OAAL,GAAe,IAAf;EACD;;;WAIDC,aAAA,oBAAWpR,MAAX,EAAmB;EACjBA,IAAAA,MAAM,oDACDgO,SADC,GAEDzC,WAAW,CAACG,iBAAZ,CAA8B,KAAKpC,QAAnC,CAFC,GAGD,OAAOtJ,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAH/C,CAAN;EAMAF,IAAAA,eAAe,CACb2I,MADa,EAEbzI,MAFa,EAGb,KAAK2b,WAAL,CAAiBpN,WAHJ,CAAf;EAMA,WAAOvO,MAAP;EACD;;WAEDmnB,gBAAA,yBAAgB;EAAA;;EACd7hB,IAAAA,YAAY,CAAC+B,EAAb,CACE,KAAKiC,QADP,EAEE2T,qBAFF,EAGEW,uBAHF,EAIE;EAAA,aAAM,MAAI,CAAC5G,IAAL,EAAN;EAAA,KAJF;EAMD;;;UAIM5M,kBAAP,yBAAuBpK,MAAvB,EAA+B;EAC7B,WAAO,KAAKqK,IAAL,CAAU,YAAY;EAC3B,UAAI/H,IAAI,GAAGI,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmB8F,UAAnB,CAAX;;EACA,UAAMwI,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACsC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIksB,KAAJ,CAAU,IAAV,EAAgBrd,OAAhB,CAAP;EACD;;EAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOsC,IAAI,CAACtC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIoV,SAAJ,wBAAkCpV,MAAlC,QAAN;EACD;;EAEDsC,QAAAA,IAAI,CAACtC,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAfM,CAAP;EAgBD;;UAEMwK,cAAP,qBAAmBzM,OAAnB,EAA4B;EAC1B,WAAO2E,IAAI,CAACG,OAAL,CAAa9E,OAAb,EAAsB4K,UAAtB,CAAP;EACD;;;;0BA/IoB;EACnB,aAAOD,SAAP;EACD;;;0BAEwB;EACvB,aAAO6F,aAAP;EACD;;;0BAEoB;EACnB,aAAOP,SAAP;EACD;;;;;;EAwIH,IAAM5J,GAAC,GAAGvC,SAAS,EAAnB;EAEA;;;;;;;EAMA;;EACA,IAAIuC,GAAJ,EAAO;EACL,MAAMqG,oBAAkB,GAAGrG,GAAC,CAACc,EAAF,CAAKuD,MAAL,CAA3B;EACArE,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAa+lB,KAAK,CAACpkB,eAAnB;EACAhG,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWiC,WAAX,GAAyB8jB,KAAzB;;EACApqB,EAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BvG,IAAAA,GAAC,CAACc,EAAF,CAAKuD,MAAL,IAAagC,oBAAb;EACA,WAAO+jB,KAAK,CAACpkB,eAAb;EACD,GAHD;EAID;;EC3OD;;;;;;AAmBA,kBAAe;EACbf,EAAAA,KAAK,EAALA,KADa;EAEbyB,EAAAA,MAAM,EAANA,MAFa;EAGb4F,EAAAA,QAAQ,EAARA,QAHa;EAIb2F,EAAAA,QAAQ,EAARA,QAJa;EAKbkE,EAAAA,QAAQ,EAARA,QALa;EAMbwD,EAAAA,KAAK,EAALA,KANa;EAObyM,EAAAA,OAAO,EAAPA,OAPa;EAQbe,EAAAA,SAAS,EAATA,SARa;EASbqC,EAAAA,GAAG,EAAHA,GATa;EAUbY,EAAAA,KAAK,EAALA,KAVa;EAWb3H,EAAAA,OAAO,EAAPA;EAXa,CAAf;;;;;;;;"}