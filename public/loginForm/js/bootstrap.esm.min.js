/*!
  * Bootstrap v5.0.0-alpha1 (https://getbootstrap.com/)
  * Copyright 2011-2020 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */
import <PERSON><PERSON> from"popper.js";function _defineProperties(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),e}function _defineProperty(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){_defineProperty(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function _inheritsLoose(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var MAX_UID=1e6,MILLISECONDS_MULTIPLIER=1e3,TRANSITION_END="transitionend",toType=function(e){return null==e?""+e:{}.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase()},getUID=function(e){do{e+=Math.floor(Math.random()*MAX_UID)}while(document.getElementById(e));return e},getSelector=function(e){var t=e.getAttribute("data-target");if(!t||"#"===t){var n=e.getAttribute("href");t=n&&"#"!==n?n.trim():null}return t},getSelectorFromElement=function(e){var t=getSelector(e);return t&&document.querySelector(t)?t:null},getElementFromSelector=function(e){var t=getSelector(e);return t?document.querySelector(t):null},getTransitionDurationFromElement=function(e){if(!e)return 0;var t=window.getComputedStyle(e),n=t.transitionDuration,i=t.transitionDelay,r=parseFloat(n),o=parseFloat(i);return r||o?(n=n.split(",")[0],i=i.split(",")[0],(parseFloat(n)+parseFloat(i))*MILLISECONDS_MULTIPLIER):0},triggerTransitionEnd=function(e){e.dispatchEvent(new Event(TRANSITION_END))},isElement=function(e){return(e[0]||e).nodeType},emulateTransitionEnd=function(e,t){var n=!1,i=t+5;e.addEventListener(TRANSITION_END,(function t(){n=!0,e.removeEventListener(TRANSITION_END,t)})),setTimeout((function(){n||triggerTransitionEnd(e)}),i)},typeCheckConfig=function(e,t,n){Object.keys(n).forEach((function(i){var r=n[i],o=t[i],a=o&&isElement(o)?"element":toType(o);if(!new RegExp(r).test(a))throw new Error(e.toUpperCase()+': Option "'+i+'" provided type "'+a+'" but expected type "'+r+'".')}))},isVisible=function(e){if(!e)return!1;if(e.style&&e.parentNode&&e.parentNode.style){var t=getComputedStyle(e),n=getComputedStyle(e.parentNode);return"none"!==t.display&&"none"!==n.display&&"hidden"!==t.visibility}return!1},findShadowRoot=function e(t){if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){var n=t.getRootNode();return n instanceof ShadowRoot?n:null}return t instanceof ShadowRoot?t:t.parentNode?e(t.parentNode):null},noop=function(){return function(){}},reflow=function(e){return e.offsetHeight},getjQuery=function(){var e=window.jQuery;return e&&!document.body.hasAttribute("data-no-jquery")?e:null},mapData=function(){var e={},t=1;return{set:function(n,i,r){void 0===n.key&&(n.key={key:i,id:t},t++),e[n.key.id]=r},get:function(t,n){if(!t||void 0===t.key)return null;var i=t.key;return i.key===n?e[i.id]:null},delete:function(t,n){if(void 0!==t.key){var i=t.key;i.key===n&&(delete e[i.id],delete t.key)}}}}(),Data={setData:function(e,t,n){mapData.set(e,t,n)},getData:function(e,t){return mapData.get(e,t)},removeData:function(e,t){mapData.delete(e,t)}},find=Element.prototype.querySelectorAll,findOne=Element.prototype.querySelector,defaultPreventedPreservedOnDispatch=function(){var e=new CustomEvent("Bootstrap",{cancelable:!0}),t=document.createElement("div");return t.addEventListener("Bootstrap",(function(){return null})),e.preventDefault(),t.dispatchEvent(e),e.defaultPrevented}(),scopeSelectorRegex=/:scope\b/,supportScopeQuery=function(){var e=document.createElement("div");try{e.querySelectorAll(":scope *")}catch(e){return!1}return!0}();supportScopeQuery||(find=function(e){if(!scopeSelectorRegex.test(e))return this.querySelectorAll(e);var t=Boolean(this.id);t||(this.id=getUID("scope"));var n=null;try{e=e.replace(scopeSelectorRegex,"#"+this.id),n=this.querySelectorAll(e)}finally{t||this.removeAttribute("id")}return n},findOne=function(e){if(!scopeSelectorRegex.test(e))return this.querySelector(e);var t=find.call(this,e);return void 0!==t[0]?t[0]:null});var $=getjQuery(),namespaceRegex=/[^.]*(?=\..*)\.|.*/,stripNameRegex=/\..*/,stripUidRegex=/::\d+$/,eventRegistry={},uidEvent=1,customEvents={mouseenter:"mouseover",mouseleave:"mouseout"},nativeEvents=["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"];function getUidEvent(e,t){return t&&t+"::"+uidEvent++||e.uidEvent||uidEvent++}function getEvent(e){var t=getUidEvent(e);return e.uidEvent=t,eventRegistry[t]=eventRegistry[t]||{},eventRegistry[t]}function bootstrapHandler(e,t){return function n(i){return n.oneOff&&EventHandler.off(e,i.type,t),t.apply(e,[i])}}function bootstrapDelegationHandler(e,t,n){return function i(r){for(var o=e.querySelectorAll(t),a=r.target;a&&a!==this;a=a.parentNode)for(var s=o.length;s--;)if(o[s]===a)return i.oneOff&&EventHandler.off(e,r.type,n),n.apply(a,[r]);return null}}function findHandler(e,t,n){void 0===n&&(n=null);for(var i=Object.keys(e),r=0,o=i.length;r<o;r++){var a=e[i[r]];if(a.originalHandler===t&&a.delegationSelector===n)return a}return null}function normalizeParams(e,t,n){var i="string"==typeof t,r=i?n:t,o=e.replace(stripNameRegex,""),a=customEvents[o];return a&&(o=a),nativeEvents.indexOf(o)>-1||(o=e),[i,r,o]}function addHandler(e,t,n,i,r){if("string"==typeof t&&e){n||(n=i,i=null);var o=normalizeParams(t,n,i),a=o[0],s=o[1],l=o[2],E=getEvent(e),_=E[l]||(E[l]={}),c=findHandler(_,s,a?n:null);if(c)c.oneOff=c.oneOff&&r;else{var u=getUidEvent(s,t.replace(namespaceRegex,"")),f=a?bootstrapDelegationHandler(e,n,i):bootstrapHandler(e,n);f.delegationSelector=a?n:null,f.originalHandler=s,f.oneOff=r,f.uidEvent=u,_[u]=f,e.addEventListener(l,f,a)}}}function removeHandler(e,t,n,i,r){var o=findHandler(t[n],i,r);o&&(e.removeEventListener(n,o,Boolean(r)),delete t[n][o.uidEvent])}function removeNamespacedHandlers(e,t,n,i){var r=t[n]||{};Object.keys(r).forEach((function(o){if(o.indexOf(i)>-1){var a=r[o];removeHandler(e,t,n,a.originalHandler,a.delegationSelector)}}))}var EventHandler={on:function(e,t,n,i){addHandler(e,t,n,i,!1)},one:function(e,t,n,i){addHandler(e,t,n,i,!0)},off:function(e,t,n,i){if("string"==typeof t&&e){var r=normalizeParams(t,n,i),o=r[0],a=r[1],s=r[2],l=s!==t,E=getEvent(e),_="."===t.charAt(0);if(void 0===a){_&&Object.keys(E).forEach((function(n){removeNamespacedHandlers(e,E,n,t.slice(1))}));var c=E[s]||{};Object.keys(c).forEach((function(n){var i=n.replace(stripUidRegex,"");if(!l||t.indexOf(i)>-1){var r=c[n];removeHandler(e,E,s,r.originalHandler,r.delegationSelector)}}))}else{if(!E||!E[s])return;removeHandler(e,E,s,a,o?n:null)}}},trigger:function(e,t,n){if("string"!=typeof t||!e)return null;var i,r=t.replace(stripNameRegex,""),o=t!==r,a=nativeEvents.indexOf(r)>-1,s=!0,l=!0,E=!1,_=null;return o&&$&&(i=$.Event(t,n),$(e).trigger(i),s=!i.isPropagationStopped(),l=!i.isImmediatePropagationStopped(),E=i.isDefaultPrevented()),a?(_=document.createEvent("HTMLEvents")).initEvent(r,s,!0):_=new CustomEvent(t,{bubbles:s,cancelable:!0}),void 0!==n&&Object.keys(n).forEach((function(e){Object.defineProperty(_,e,{get:function(){return n[e]}})})),E&&(_.preventDefault(),defaultPreventedPreservedOnDispatch||Object.defineProperty(_,"defaultPrevented",{get:function(){return!0}})),l&&e.dispatchEvent(_),_.defaultPrevented&&void 0!==i&&i.preventDefault(),_}},NAME="alert",VERSION="5.0.0-alpha1",DATA_KEY="bs.alert",EVENT_KEY="."+DATA_KEY,DATA_API_KEY=".data-api",SELECTOR_DISMISS='[data-dismiss="alert"]',EVENT_CLOSE="close"+EVENT_KEY,EVENT_CLOSED="closed"+EVENT_KEY,EVENT_CLICK_DATA_API="click"+EVENT_KEY+DATA_API_KEY,CLASSNAME_ALERT="alert",CLASSNAME_FADE="fade",CLASSNAME_SHOW="show",Alert=function(){function e(e){this._element=e,this._element&&Data.setData(e,DATA_KEY,this)}var t=e.prototype;return t.close=function(e){var t=this._element;e&&(t=this._getRootElement(e));var n=this._triggerCloseEvent(t);null===n||n.defaultPrevented||this._removeElement(t)},t.dispose=function(){Data.removeData(this._element,DATA_KEY),this._element=null},t._getRootElement=function(e){return getElementFromSelector(e)||e.closest("."+CLASSNAME_ALERT)},t._triggerCloseEvent=function(e){return EventHandler.trigger(e,EVENT_CLOSE)},t._removeElement=function(e){var t=this;if(e.classList.remove(CLASSNAME_SHOW),e.classList.contains(CLASSNAME_FADE)){var n=getTransitionDurationFromElement(e);EventHandler.one(e,TRANSITION_END,(function(){return t._destroyElement(e)})),emulateTransitionEnd(e,n)}else this._destroyElement(e)},t._destroyElement=function(e){e.parentNode&&e.parentNode.removeChild(e),EventHandler.trigger(e,EVENT_CLOSED)},e.jQueryInterface=function(t){return this.each((function(){var n=Data.getData(this,DATA_KEY);n||(n=new e(this)),"close"===t&&n[t](this)}))},e.handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},e.getInstance=function(e){return Data.getData(e,DATA_KEY)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION}}]),e}();EventHandler.on(document,EVENT_CLICK_DATA_API,SELECTOR_DISMISS,Alert.handleDismiss(new Alert));var $$1=getjQuery();if($$1){var JQUERY_NO_CONFLICT=$$1.fn[NAME];$$1.fn[NAME]=Alert.jQueryInterface,$$1.fn[NAME].Constructor=Alert,$$1.fn[NAME].noConflict=function(){return $$1.fn[NAME]=JQUERY_NO_CONFLICT,Alert.jQueryInterface}}var NAME$1="button",VERSION$1="5.0.0-alpha1",DATA_KEY$1="bs.button",EVENT_KEY$1="."+DATA_KEY$1,DATA_API_KEY$1=".data-api",CLASS_NAME_ACTIVE="active",SELECTOR_DATA_TOGGLE='[data-toggle="button"]',EVENT_CLICK_DATA_API$1="click"+EVENT_KEY$1+DATA_API_KEY$1,Button=function(){function e(e){this._element=e,Data.setData(e,DATA_KEY$1,this)}var t=e.prototype;return t.toggle=function(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(CLASS_NAME_ACTIVE))},t.dispose=function(){Data.removeData(this._element,DATA_KEY$1),this._element=null},e.jQueryInterface=function(t){return this.each((function(){var n=Data.getData(this,DATA_KEY$1);n||(n=new e(this)),"toggle"===t&&n[t]()}))},e.getInstance=function(e){return Data.getData(e,DATA_KEY$1)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION$1}}]),e}();EventHandler.on(document,EVENT_CLICK_DATA_API$1,SELECTOR_DATA_TOGGLE,(function(e){e.preventDefault();var t=e.target.closest(SELECTOR_DATA_TOGGLE),n=Data.getData(t,DATA_KEY$1);n||(n=new Button(t)),n.toggle()}));var $$2=getjQuery();if($$2){var JQUERY_NO_CONFLICT$1=$$2.fn[NAME$1];$$2.fn[NAME$1]=Button.jQueryInterface,$$2.fn[NAME$1].Constructor=Button,$$2.fn[NAME$1].noConflict=function(){return $$2.fn[NAME$1]=JQUERY_NO_CONFLICT$1,Button.jQueryInterface}}function normalizeData(e){return"true"===e||"false"!==e&&(e===Number(e).toString()?Number(e):""===e||"null"===e?null:e)}function normalizeDataKey(e){return e.replace(/[A-Z]/g,(function(e){return"-"+e.toLowerCase()}))}var Manipulator={setDataAttribute:function(e,t,n){e.setAttribute("data-"+normalizeDataKey(t),n)},removeDataAttribute:function(e,t){e.removeAttribute("data-"+normalizeDataKey(t))},getDataAttributes:function(e){if(!e)return{};var t=_objectSpread2({},e.dataset);return Object.keys(t).forEach((function(e){t[e]=normalizeData(t[e])})),t},getDataAttribute:function(e,t){return normalizeData(e.getAttribute("data-"+normalizeDataKey(t)))},offset:function(e){var t=e.getBoundingClientRect();return{top:t.top+document.body.scrollTop,left:t.left+document.body.scrollLeft}},position:function(e){return{top:e.offsetTop,left:e.offsetLeft}},toggleClass:function(e,t){e&&(e.classList.contains(t)?e.classList.remove(t):e.classList.add(t))}},NODE_TEXT=3,SelectorEngine={matches:function(e,t){return e.matches(t)},find:function(e,t){var n;return void 0===t&&(t=document.documentElement),(n=[]).concat.apply(n,find.call(t,e))},findOne:function(e,t){return void 0===t&&(t=document.documentElement),findOne.call(t,e)},children:function(e,t){var n,i=(n=[]).concat.apply(n,e.children);return i.filter((function(e){return e.matches(t)}))},parents:function(e,t){for(var n=[],i=e.parentNode;i&&i.nodeType===Node.ELEMENT_NODE&&i.nodeType!==NODE_TEXT;)this.matches(i,t)&&n.push(i),i=i.parentNode;return n},prev:function(e,t){for(var n=e.previousElementSibling;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next:function(e,t){for(var n=e.nextElementSibling;n;){if(this.matches(n,t))return[n];n=n.nextElementSibling}return[]}},NAME$2="carousel",VERSION$2="5.0.0-alpha1",DATA_KEY$2="bs.carousel",EVENT_KEY$2="."+DATA_KEY$2,DATA_API_KEY$2=".data-api",ARROW_LEFT_KEY="ArrowLeft",ARROW_RIGHT_KEY="ArrowRight",TOUCHEVENT_COMPAT_WAIT=500,SWIPE_THRESHOLD=40,Default={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},DefaultType={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},DIRECTION_NEXT="next",DIRECTION_PREV="prev",DIRECTION_LEFT="left",DIRECTION_RIGHT="right",EVENT_SLIDE="slide"+EVENT_KEY$2,EVENT_SLID="slid"+EVENT_KEY$2,EVENT_KEYDOWN="keydown"+EVENT_KEY$2,EVENT_MOUSEENTER="mouseenter"+EVENT_KEY$2,EVENT_MOUSELEAVE="mouseleave"+EVENT_KEY$2,EVENT_TOUCHSTART="touchstart"+EVENT_KEY$2,EVENT_TOUCHMOVE="touchmove"+EVENT_KEY$2,EVENT_TOUCHEND="touchend"+EVENT_KEY$2,EVENT_POINTERDOWN="pointerdown"+EVENT_KEY$2,EVENT_POINTERUP="pointerup"+EVENT_KEY$2,EVENT_DRAG_START="dragstart"+EVENT_KEY$2,EVENT_LOAD_DATA_API="load"+EVENT_KEY$2+DATA_API_KEY$2,EVENT_CLICK_DATA_API$2="click"+EVENT_KEY$2+DATA_API_KEY$2,CLASS_NAME_CAROUSEL="carousel",CLASS_NAME_ACTIVE$1="active",CLASS_NAME_SLIDE="slide",CLASS_NAME_RIGHT="carousel-item-right",CLASS_NAME_LEFT="carousel-item-left",CLASS_NAME_NEXT="carousel-item-next",CLASS_NAME_PREV="carousel-item-prev",CLASS_NAME_POINTER_EVENT="pointer-event",SELECTOR_ACTIVE=".active",SELECTOR_ACTIVE_ITEM=".active.carousel-item",SELECTOR_ITEM=".carousel-item",SELECTOR_ITEM_IMG=".carousel-item img",SELECTOR_NEXT_PREV=".carousel-item-next, .carousel-item-prev",SELECTOR_INDICATORS=".carousel-indicators",SELECTOR_DATA_SLIDE="[data-slide], [data-slide-to]",SELECTOR_DATA_RIDE='[data-ride="carousel"]',PointerType={TOUCH:"touch",PEN:"pen"},Carousel=function(){function e(e,t){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._element=e,this._indicatorsElement=SelectorEngine.findOne(SELECTOR_INDICATORS,this._element),this._touchSupported="ontouchstart"in document.documentElement||navigator.maxTouchPoints>0,this._pointerEvent=Boolean(window.PointerEvent),this._addEventListeners(),Data.setData(e,DATA_KEY$2,this)}var t=e.prototype;return t.next=function(){this._isSliding||this._slide(DIRECTION_NEXT)},t.nextWhenVisible=function(){!document.hidden&&isVisible(this._element)&&this.next()},t.prev=function(){this._isSliding||this._slide(DIRECTION_PREV)},t.pause=function(e){e||(this._isPaused=!0),SelectorEngine.findOne(SELECTOR_NEXT_PREV,this._element)&&(triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},t.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config&&this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},t.to=function(e){var t=this;this._activeElement=SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element);var n=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)EventHandler.one(this._element,EVENT_SLID,(function(){return t.to(e)}));else{if(n===e)return this.pause(),void this.cycle();var i=e>n?DIRECTION_NEXT:DIRECTION_PREV;this._slide(i,this._items[e])}},t.dispose=function(){EventHandler.off(this._element,EVENT_KEY$2),Data.removeData(this._element,DATA_KEY$2),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},t._getConfig=function(e){return e=_objectSpread2(_objectSpread2({},Default),e),typeCheckConfig(NAME$2,e,DefaultType),e},t._handleSwipe=function(){var e=Math.abs(this.touchDeltaX);if(!(e<=SWIPE_THRESHOLD)){var t=e/this.touchDeltaX;this.touchDeltaX=0,t>0&&this.prev(),t<0&&this.next()}},t._addEventListeners=function(){var e=this;this._config.keyboard&&EventHandler.on(this._element,EVENT_KEYDOWN,(function(t){return e._keydown(t)})),"hover"===this._config.pause&&(EventHandler.on(this._element,EVENT_MOUSEENTER,(function(t){return e.pause(t)})),EventHandler.on(this._element,EVENT_MOUSELEAVE,(function(t){return e.cycle(t)}))),this._config.touch&&this._touchSupported&&this._addTouchEventListeners()},t._addTouchEventListeners=function(){var e=this,t=function(t){e._pointerEvent&&PointerType[t.pointerType.toUpperCase()]?e.touchStartX=t.clientX:e._pointerEvent||(e.touchStartX=t.touches[0].clientX)},n=function(t){e._pointerEvent&&PointerType[t.pointerType.toUpperCase()]&&(e.touchDeltaX=t.clientX-e.touchStartX),e._handleSwipe(),"hover"===e._config.pause&&(e.pause(),e.touchTimeout&&clearTimeout(e.touchTimeout),e.touchTimeout=setTimeout((function(t){return e.cycle(t)}),TOUCHEVENT_COMPAT_WAIT+e._config.interval))};SelectorEngine.find(SELECTOR_ITEM_IMG,this._element).forEach((function(e){EventHandler.on(e,EVENT_DRAG_START,(function(e){return e.preventDefault()}))})),this._pointerEvent?(EventHandler.on(this._element,EVENT_POINTERDOWN,(function(e){return t(e)})),EventHandler.on(this._element,EVENT_POINTERUP,(function(e){return n(e)})),this._element.classList.add(CLASS_NAME_POINTER_EVENT)):(EventHandler.on(this._element,EVENT_TOUCHSTART,(function(e){return t(e)})),EventHandler.on(this._element,EVENT_TOUCHMOVE,(function(t){return function(t){t.touches&&t.touches.length>1?e.touchDeltaX=0:e.touchDeltaX=t.touches[0].clientX-e.touchStartX}(t)})),EventHandler.on(this._element,EVENT_TOUCHEND,(function(e){return n(e)})))},t._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.key){case ARROW_LEFT_KEY:e.preventDefault(),this.prev();break;case ARROW_RIGHT_KEY:e.preventDefault(),this.next()}},t._getItemIndex=function(e){return this._items=e&&e.parentNode?SelectorEngine.find(SELECTOR_ITEM,e.parentNode):[],this._items.indexOf(e)},t._getItemByDirection=function(e,t){var n=e===DIRECTION_NEXT,i=e===DIRECTION_PREV,r=this._getItemIndex(t),o=this._items.length-1;if((i&&0===r||n&&r===o)&&!this._config.wrap)return t;var a=(r+(e===DIRECTION_PREV?-1:1))%this._items.length;return-1===a?this._items[this._items.length-1]:this._items[a]},t._triggerSlideEvent=function(e,t){var n=this._getItemIndex(e),i=this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element));return EventHandler.trigger(this._element,EVENT_SLIDE,{relatedTarget:e,direction:t,from:i,to:n})},t._setActiveIndicatorElement=function(e){if(this._indicatorsElement){for(var t=SelectorEngine.find(SELECTOR_ACTIVE,this._indicatorsElement),n=0;n<t.length;n++)t[n].classList.remove(CLASS_NAME_ACTIVE$1);var i=this._indicatorsElement.children[this._getItemIndex(e)];i&&i.classList.add(CLASS_NAME_ACTIVE$1)}},t._slide=function(e,t){var n,i,r,o=this,a=SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element),s=this._getItemIndex(a),l=t||a&&this._getItemByDirection(e,a),E=this._getItemIndex(l),_=Boolean(this._interval);if(e===DIRECTION_NEXT?(n=CLASS_NAME_LEFT,i=CLASS_NAME_NEXT,r=DIRECTION_LEFT):(n=CLASS_NAME_RIGHT,i=CLASS_NAME_PREV,r=DIRECTION_RIGHT),l&&l.classList.contains(CLASS_NAME_ACTIVE$1))this._isSliding=!1;else if(!this._triggerSlideEvent(l,r).defaultPrevented&&a&&l){if(this._isSliding=!0,_&&this.pause(),this._setActiveIndicatorElement(l),this._element.classList.contains(CLASS_NAME_SLIDE)){l.classList.add(i),reflow(l),a.classList.add(n),l.classList.add(n);var c=parseInt(l.getAttribute("data-interval"),10);c?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=c):this._config.interval=this._config.defaultInterval||this._config.interval;var u=getTransitionDurationFromElement(a);EventHandler.one(a,TRANSITION_END,(function(){l.classList.remove(n,i),l.classList.add(CLASS_NAME_ACTIVE$1),a.classList.remove(CLASS_NAME_ACTIVE$1,i,n),o._isSliding=!1,setTimeout((function(){EventHandler.trigger(o._element,EVENT_SLID,{relatedTarget:l,direction:r,from:s,to:E})}),0)})),emulateTransitionEnd(a,u)}else a.classList.remove(CLASS_NAME_ACTIVE$1),l.classList.add(CLASS_NAME_ACTIVE$1),this._isSliding=!1,EventHandler.trigger(this._element,EVENT_SLID,{relatedTarget:l,direction:r,from:s,to:E});_&&this.cycle()}},e.carouselInterface=function(t,n){var i=Data.getData(t,DATA_KEY$2),r=_objectSpread2(_objectSpread2({},Default),Manipulator.getDataAttributes(t));"object"==typeof n&&(r=_objectSpread2(_objectSpread2({},r),n));var o="string"==typeof n?n:r.slide;if(i||(i=new e(t,r)),"number"==typeof n)i.to(n);else if("string"==typeof o){if(void 0===i[o])throw new TypeError('No method named "'+o+'"');i[o]()}else r.interval&&r.ride&&(i.pause(),i.cycle())},e.jQueryInterface=function(t){return this.each((function(){e.carouselInterface(this,t)}))},e.dataApiClickHandler=function(t){var n=getElementFromSelector(this);if(n&&n.classList.contains(CLASS_NAME_CAROUSEL)){var i=_objectSpread2(_objectSpread2({},Manipulator.getDataAttributes(n)),Manipulator.getDataAttributes(this)),r=this.getAttribute("data-slide-to");r&&(i.interval=!1),e.carouselInterface(n,i),r&&Data.getData(n,DATA_KEY$2).to(r),t.preventDefault()}},e.getInstance=function(e){return Data.getData(e,DATA_KEY$2)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION$2}},{key:"Default",get:function(){return Default}}]),e}();EventHandler.on(document,EVENT_CLICK_DATA_API$2,SELECTOR_DATA_SLIDE,Carousel.dataApiClickHandler),EventHandler.on(window,EVENT_LOAD_DATA_API,(function(){for(var e=SelectorEngine.find(SELECTOR_DATA_RIDE),t=0,n=e.length;t<n;t++)Carousel.carouselInterface(e[t],Data.getData(e[t],DATA_KEY$2))}));var $$3=getjQuery();if($$3){var JQUERY_NO_CONFLICT$2=$$3.fn[NAME$2];$$3.fn[NAME$2]=Carousel.jQueryInterface,$$3.fn[NAME$2].Constructor=Carousel,$$3.fn[NAME$2].noConflict=function(){return $$3.fn[NAME$2]=JQUERY_NO_CONFLICT$2,Carousel.jQueryInterface}}var NAME$3="collapse",VERSION$3="5.0.0-alpha1",DATA_KEY$3="bs.collapse",EVENT_KEY$3="."+DATA_KEY$3,DATA_API_KEY$3=".data-api",Default$1={toggle:!0,parent:""},DefaultType$1={toggle:"boolean",parent:"(string|element)"},EVENT_SHOW="show"+EVENT_KEY$3,EVENT_SHOWN="shown"+EVENT_KEY$3,EVENT_HIDE="hide"+EVENT_KEY$3,EVENT_HIDDEN="hidden"+EVENT_KEY$3,EVENT_CLICK_DATA_API$3="click"+EVENT_KEY$3+DATA_API_KEY$3,CLASS_NAME_SHOW="show",CLASS_NAME_COLLAPSE="collapse",CLASS_NAME_COLLAPSING="collapsing",CLASS_NAME_COLLAPSED="collapsed",WIDTH="width",HEIGHT="height",SELECTOR_ACTIVES=".show, .collapsing",SELECTOR_DATA_TOGGLE$1='[data-toggle="collapse"]',Collapse=function(){function e(e,t){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(t),this._triggerArray=SelectorEngine.find(SELECTOR_DATA_TOGGLE$1+'[href="#'+e.id+'"],'+SELECTOR_DATA_TOGGLE$1+'[data-target="#'+e.id+'"]');for(var n=SelectorEngine.find(SELECTOR_DATA_TOGGLE$1),i=0,r=n.length;i<r;i++){var o=n[i],a=getSelectorFromElement(o),s=SelectorEngine.find(a).filter((function(t){return t===e}));null!==a&&s.length&&(this._selector=a,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle(),Data.setData(e,DATA_KEY$3,this)}var t=e.prototype;return t.toggle=function(){this._element.classList.contains(CLASS_NAME_SHOW)?this.hide():this.show()},t.show=function(){var t=this;if(!this._isTransitioning&&!this._element.classList.contains(CLASS_NAME_SHOW)){var n,i;this._parent&&0===(n=SelectorEngine.find(SELECTOR_ACTIVES,this._parent).filter((function(e){return"string"==typeof t._config.parent?e.getAttribute("data-parent")===t._config.parent:e.classList.contains(CLASS_NAME_COLLAPSE)}))).length&&(n=null);var r=SelectorEngine.findOne(this._selector);if(n){var o=n.filter((function(e){return r!==e}));if((i=o[0]?Data.getData(o[0],DATA_KEY$3):null)&&i._isTransitioning)return}if(!EventHandler.trigger(this._element,EVENT_SHOW).defaultPrevented){n&&n.forEach((function(t){r!==t&&e.collapseInterface(t,"hide"),i||Data.setData(t,DATA_KEY$3,null)}));var a=this._getDimension();this._element.classList.remove(CLASS_NAME_COLLAPSE),this._element.classList.add(CLASS_NAME_COLLAPSING),this._element.style[a]=0,this._triggerArray.length&&this._triggerArray.forEach((function(e){e.classList.remove(CLASS_NAME_COLLAPSED),e.setAttribute("aria-expanded",!0)})),this.setTransitioning(!0);var s="scroll"+(a[0].toUpperCase()+a.slice(1)),l=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,(function(){t._element.classList.remove(CLASS_NAME_COLLAPSING),t._element.classList.add(CLASS_NAME_COLLAPSE,CLASS_NAME_SHOW),t._element.style[a]="",t.setTransitioning(!1),EventHandler.trigger(t._element,EVENT_SHOWN)})),emulateTransitionEnd(this._element,l),this._element.style[a]=this._element[s]+"px"}}},t.hide=function(){var e=this;if(!this._isTransitioning&&this._element.classList.contains(CLASS_NAME_SHOW)&&!EventHandler.trigger(this._element,EVENT_HIDE).defaultPrevented){var t=this._getDimension();this._element.style[t]=this._element.getBoundingClientRect()[t]+"px",reflow(this._element),this._element.classList.add(CLASS_NAME_COLLAPSING),this._element.classList.remove(CLASS_NAME_COLLAPSE,CLASS_NAME_SHOW);var n=this._triggerArray.length;if(n>0)for(var i=0;i<n;i++){var r=this._triggerArray[i],o=getElementFromSelector(r);o&&!o.classList.contains(CLASS_NAME_SHOW)&&(r.classList.add(CLASS_NAME_COLLAPSED),r.setAttribute("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[t]="";var a=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,(function(){e.setTransitioning(!1),e._element.classList.remove(CLASS_NAME_COLLAPSING),e._element.classList.add(CLASS_NAME_COLLAPSE),EventHandler.trigger(e._element,EVENT_HIDDEN)})),emulateTransitionEnd(this._element,a)}},t.setTransitioning=function(e){this._isTransitioning=e},t.dispose=function(){Data.removeData(this._element,DATA_KEY$3),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},t._getConfig=function(e){return(e=_objectSpread2(_objectSpread2({},Default$1),e)).toggle=Boolean(e.toggle),typeCheckConfig(NAME$3,e,DefaultType$1),e},t._getDimension=function(){return this._element.classList.contains(WIDTH)?WIDTH:HEIGHT},t._getParent=function(){var e=this,t=this._config.parent;isElement(t)?void 0===t.jquery&&void 0===t[0]||(t=t[0]):t=SelectorEngine.findOne(t);var n=SELECTOR_DATA_TOGGLE$1+'[data-parent="'+t+'"]';return SelectorEngine.find(n,t).forEach((function(t){var n=getElementFromSelector(t);e._addAriaAndCollapsedClass(n,[t])})),t},t._addAriaAndCollapsedClass=function(e,t){if(e){var n=e.classList.contains(CLASS_NAME_SHOW);t.length&&t.forEach((function(e){n?e.classList.remove(CLASS_NAME_COLLAPSED):e.classList.add(CLASS_NAME_COLLAPSED),e.setAttribute("aria-expanded",n)}))}},e.collapseInterface=function(t,n){var i=Data.getData(t,DATA_KEY$3),r=_objectSpread2(_objectSpread2(_objectSpread2({},Default$1),Manipulator.getDataAttributes(t)),"object"==typeof n&&n?n:{});if(!i&&r.toggle&&"string"==typeof n&&/show|hide/.test(n)&&(r.toggle=!1),i||(i=new e(t,r)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}},e.jQueryInterface=function(t){return this.each((function(){e.collapseInterface(this,t)}))},e.getInstance=function(e){return Data.getData(e,DATA_KEY$3)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION$3}},{key:"Default",get:function(){return Default$1}}]),e}();EventHandler.on(document,EVENT_CLICK_DATA_API$3,SELECTOR_DATA_TOGGLE$1,(function(e){"A"===e.target.tagName&&e.preventDefault();var t=Manipulator.getDataAttributes(this),n=getSelectorFromElement(this);SelectorEngine.find(n).forEach((function(e){var n,i=Data.getData(e,DATA_KEY$3);i?(null===i._parent&&"string"==typeof t.parent&&(i._config.parent=t.parent,i._parent=i._getParent()),n="toggle"):n=t,Collapse.collapseInterface(e,n)}))}));var $$4=getjQuery();if($$4){var JQUERY_NO_CONFLICT$3=$$4.fn[NAME$3];$$4.fn[NAME$3]=Collapse.jQueryInterface,$$4.fn[NAME$3].Constructor=Collapse,$$4.fn[NAME$3].noConflict=function(){return $$4.fn[NAME$3]=JQUERY_NO_CONFLICT$3,Collapse.jQueryInterface}}var NAME$4="dropdown",VERSION$4="5.0.0-alpha1",DATA_KEY$4="bs.dropdown",EVENT_KEY$4="."+DATA_KEY$4,DATA_API_KEY$4=".data-api",ESCAPE_KEY="Escape",SPACE_KEY="Space",TAB_KEY="Tab",ARROW_UP_KEY="ArrowUp",ARROW_DOWN_KEY="ArrowDown",RIGHT_MOUSE_BUTTON=2,REGEXP_KEYDOWN=new RegExp(ARROW_UP_KEY+"|"+ARROW_DOWN_KEY+"|"+ESCAPE_KEY),EVENT_HIDE$1="hide"+EVENT_KEY$4,EVENT_HIDDEN$1="hidden"+EVENT_KEY$4,EVENT_SHOW$1="show"+EVENT_KEY$4,EVENT_SHOWN$1="shown"+EVENT_KEY$4,EVENT_CLICK="click"+EVENT_KEY$4,EVENT_CLICK_DATA_API$4="click"+EVENT_KEY$4+DATA_API_KEY$4,EVENT_KEYDOWN_DATA_API="keydown"+EVENT_KEY$4+DATA_API_KEY$4,EVENT_KEYUP_DATA_API="keyup"+EVENT_KEY$4+DATA_API_KEY$4,CLASS_NAME_DISABLED="disabled",CLASS_NAME_SHOW$1="show",CLASS_NAME_DROPUP="dropup",CLASS_NAME_DROPRIGHT="dropright",CLASS_NAME_DROPLEFT="dropleft",CLASS_NAME_MENURIGHT="dropdown-menu-right",CLASS_NAME_NAVBAR="navbar",CLASS_NAME_POSITION_STATIC="position-static",SELECTOR_DATA_TOGGLE$2='[data-toggle="dropdown"]',SELECTOR_FORM_CHILD=".dropdown form",SELECTOR_MENU=".dropdown-menu",SELECTOR_NAVBAR_NAV=".navbar-nav",SELECTOR_VISIBLE_ITEMS=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",PLACEMENT_TOP="top-start",PLACEMENT_TOPEND="top-end",PLACEMENT_BOTTOM="bottom-start",PLACEMENT_BOTTOMEND="bottom-end",PLACEMENT_RIGHT="right-start",PLACEMENT_LEFT="left-start",Default$2={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},DefaultType$2={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},Dropdown=function(){function e(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners(),Data.setData(e,DATA_KEY$4,this)}var t=e.prototype;return t.toggle=function(){if(!this._element.disabled&&!this._element.classList.contains(CLASS_NAME_DISABLED)){var t=this._element.classList.contains(CLASS_NAME_SHOW$1);e.clearMenus(),t||this.show()}},t.show=function(){if(!(this._element.disabled||this._element.classList.contains(CLASS_NAME_DISABLED)||this._menu.classList.contains(CLASS_NAME_SHOW$1))){var t=e.getParentFromElement(this._element),n={relatedTarget:this._element};if(!EventHandler.trigger(this._element,EVENT_SHOW$1,n).defaultPrevented){if(!this._inNavbar){if(void 0===Popper)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org)");var i=this._element;"parent"===this._config.reference?i=t:isElement(this._config.reference)&&(i=this._config.reference,void 0!==this._config.reference.jquery&&(i=this._config.reference[0])),"scrollParent"!==this._config.boundary&&t.classList.add(CLASS_NAME_POSITION_STATIC),this._popper=new Popper(i,this._menu,this._getPopperConfig())}var r;if("ontouchstart"in document.documentElement&&!t.closest(SELECTOR_NAVBAR_NAV))(r=[]).concat.apply(r,document.body.children).forEach((function(e){return EventHandler.on(e,"mouseover",null,noop())}));this._element.focus(),this._element.setAttribute("aria-expanded",!0),Manipulator.toggleClass(this._menu,CLASS_NAME_SHOW$1),Manipulator.toggleClass(this._element,CLASS_NAME_SHOW$1),EventHandler.trigger(t,EVENT_SHOWN$1,n)}}},t.hide=function(){if(!this._element.disabled&&!this._element.classList.contains(CLASS_NAME_DISABLED)&&this._menu.classList.contains(CLASS_NAME_SHOW$1)){var t=e.getParentFromElement(this._element),n={relatedTarget:this._element};EventHandler.trigger(t,EVENT_HIDE$1,n).defaultPrevented||(this._popper&&this._popper.destroy(),Manipulator.toggleClass(this._menu,CLASS_NAME_SHOW$1),Manipulator.toggleClass(this._element,CLASS_NAME_SHOW$1),EventHandler.trigger(t,EVENT_HIDDEN$1,n))}},t.dispose=function(){Data.removeData(this._element,DATA_KEY$4),EventHandler.off(this._element,EVENT_KEY$4),this._element=null,this._menu=null,this._popper&&(this._popper.destroy(),this._popper=null)},t.update=function(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.scheduleUpdate()},t._addEventListeners=function(){var e=this;EventHandler.on(this._element,EVENT_CLICK,(function(t){t.preventDefault(),t.stopPropagation(),e.toggle()}))},t._getConfig=function(e){return e=_objectSpread2(_objectSpread2(_objectSpread2({},this.constructor.Default),Manipulator.getDataAttributes(this._element)),e),typeCheckConfig(NAME$4,e,this.constructor.DefaultType),e},t._getMenuElement=function(){return SelectorEngine.next(this._element,SELECTOR_MENU)[0]},t._getPlacement=function(){var e=this._element.parentNode,t=PLACEMENT_BOTTOM;return e.classList.contains(CLASS_NAME_DROPUP)?(t=PLACEMENT_TOP,this._menu.classList.contains(CLASS_NAME_MENURIGHT)&&(t=PLACEMENT_TOPEND)):e.classList.contains(CLASS_NAME_DROPRIGHT)?t=PLACEMENT_RIGHT:e.classList.contains(CLASS_NAME_DROPLEFT)?t=PLACEMENT_LEFT:this._menu.classList.contains(CLASS_NAME_MENURIGHT)&&(t=PLACEMENT_BOTTOMEND),t},t._detectNavbar=function(){return Boolean(this._element.closest("."+CLASS_NAME_NAVBAR))},t._getOffset=function(){var e=this,t={};return"function"==typeof this._config.offset?t.fn=function(t){return t.offsets=_objectSpread2(_objectSpread2({},t.offsets),e._config.offset(t.offsets,e._element)||{}),t}:t.offset=this._config.offset,t},t._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(e.modifiers.applyStyle={enabled:!1}),_objectSpread2(_objectSpread2({},e),this._config.popperConfig)},e.dropdownInterface=function(t,n){var i=Data.getData(t,DATA_KEY$4);if(i||(i=new e(t,"object"==typeof n?n:null)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}},e.jQueryInterface=function(t){return this.each((function(){e.dropdownInterface(this,t)}))},e.clearMenus=function(t){if(!t||t.button!==RIGHT_MOUSE_BUTTON&&("keyup"!==t.type||t.key===TAB_KEY))for(var n=SelectorEngine.find(SELECTOR_DATA_TOGGLE$2),i=0,r=n.length;i<r;i++){var o=e.getParentFromElement(n[i]),a=Data.getData(n[i],DATA_KEY$4),s={relatedTarget:n[i]};if(t&&"click"===t.type&&(s.clickEvent=t),a){var l=a._menu;if(n[i].classList.contains(CLASS_NAME_SHOW$1))if(!(t&&("click"===t.type&&/input|textarea/i.test(t.target.tagName)||"keyup"===t.type&&t.key===TAB_KEY)&&l.contains(t.target)))if(!EventHandler.trigger(o,EVENT_HIDE$1,s).defaultPrevented){var E;if("ontouchstart"in document.documentElement)(E=[]).concat.apply(E,document.body.children).forEach((function(e){return EventHandler.off(e,"mouseover",null,noop())}));n[i].setAttribute("aria-expanded","false"),a._popper&&a._popper.destroy(),l.classList.remove(CLASS_NAME_SHOW$1),n[i].classList.remove(CLASS_NAME_SHOW$1),EventHandler.trigger(o,EVENT_HIDDEN$1,s)}}}},e.getParentFromElement=function(e){return getElementFromSelector(e)||e.parentNode},e.dataApiKeydownHandler=function(t){if(!(/input|textarea/i.test(t.target.tagName)?t.key===SPACE_KEY||t.key!==ESCAPE_KEY&&(t.key!==ARROW_DOWN_KEY&&t.key!==ARROW_UP_KEY||t.target.closest(SELECTOR_MENU)):!REGEXP_KEYDOWN.test(t.key))&&(t.preventDefault(),t.stopPropagation(),!this.disabled&&!this.classList.contains(CLASS_NAME_DISABLED))){var n=e.getParentFromElement(this),i=this.classList.contains(CLASS_NAME_SHOW$1);if(t.key===ESCAPE_KEY)return(this.matches(SELECTOR_DATA_TOGGLE$2)?this:SelectorEngine.prev(this,SELECTOR_DATA_TOGGLE$2)[0]).focus(),void e.clearMenus();if(i&&t.key!==SPACE_KEY){var r=SelectorEngine.find(SELECTOR_VISIBLE_ITEMS,n).filter(isVisible);if(r.length){var o=r.indexOf(t.target);t.key===ARROW_UP_KEY&&o>0&&o--,t.key===ARROW_DOWN_KEY&&o<r.length-1&&o++,r[o=-1===o?0:o].focus()}}else e.clearMenus()}},e.getInstance=function(e){return Data.getData(e,DATA_KEY$4)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION$4}},{key:"Default",get:function(){return Default$2}},{key:"DefaultType",get:function(){return DefaultType$2}}]),e}();EventHandler.on(document,EVENT_KEYDOWN_DATA_API,SELECTOR_DATA_TOGGLE$2,Dropdown.dataApiKeydownHandler),EventHandler.on(document,EVENT_KEYDOWN_DATA_API,SELECTOR_MENU,Dropdown.dataApiKeydownHandler),EventHandler.on(document,EVENT_CLICK_DATA_API$4,Dropdown.clearMenus),EventHandler.on(document,EVENT_KEYUP_DATA_API,Dropdown.clearMenus),EventHandler.on(document,EVENT_CLICK_DATA_API$4,SELECTOR_DATA_TOGGLE$2,(function(e){e.preventDefault(),e.stopPropagation(),Dropdown.dropdownInterface(this,"toggle")})),EventHandler.on(document,EVENT_CLICK_DATA_API$4,SELECTOR_FORM_CHILD,(function(e){return e.stopPropagation()}));var $$5=getjQuery();if($$5){var JQUERY_NO_CONFLICT$4=$$5.fn[NAME$4];$$5.fn[NAME$4]=Dropdown.jQueryInterface,$$5.fn[NAME$4].Constructor=Dropdown,$$5.fn[NAME$4].noConflict=function(){return $$5.fn[NAME$4]=JQUERY_NO_CONFLICT$4,Dropdown.jQueryInterface}}var NAME$5="modal",VERSION$5="5.0.0-alpha1",DATA_KEY$5="bs.modal",EVENT_KEY$5="."+DATA_KEY$5,DATA_API_KEY$5=".data-api",ESCAPE_KEY$1="Escape",Default$3={backdrop:!0,keyboard:!0,focus:!0,show:!0},DefaultType$3={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},EVENT_HIDE$2="hide"+EVENT_KEY$5,EVENT_HIDE_PREVENTED="hidePrevented"+EVENT_KEY$5,EVENT_HIDDEN$2="hidden"+EVENT_KEY$5,EVENT_SHOW$2="show"+EVENT_KEY$5,EVENT_SHOWN$2="shown"+EVENT_KEY$5,EVENT_FOCUSIN="focusin"+EVENT_KEY$5,EVENT_RESIZE="resize"+EVENT_KEY$5,EVENT_CLICK_DISMISS="click.dismiss"+EVENT_KEY$5,EVENT_KEYDOWN_DISMISS="keydown.dismiss"+EVENT_KEY$5,EVENT_MOUSEUP_DISMISS="mouseup.dismiss"+EVENT_KEY$5,EVENT_MOUSEDOWN_DISMISS="mousedown.dismiss"+EVENT_KEY$5,EVENT_CLICK_DATA_API$5="click"+EVENT_KEY$5+DATA_API_KEY$5,CLASS_NAME_SCROLLBAR_MEASURER="modal-scrollbar-measure",CLASS_NAME_BACKDROP="modal-backdrop",CLASS_NAME_OPEN="modal-open",CLASS_NAME_FADE="fade",CLASS_NAME_SHOW$2="show",CLASS_NAME_STATIC="modal-static",SELECTOR_DIALOG=".modal-dialog",SELECTOR_MODAL_BODY=".modal-body",SELECTOR_DATA_TOGGLE$3='[data-toggle="modal"]',SELECTOR_DATA_DISMISS='[data-dismiss="modal"]',SELECTOR_FIXED_CONTENT=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",SELECTOR_STICKY_CONTENT=".sticky-top",Modal=function(){function e(e,t){this._config=this._getConfig(t),this._element=e,this._dialog=SelectorEngine.findOne(SELECTOR_DIALOG,e),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0,Data.setData(e,DATA_KEY$5,this)}var t=e.prototype;return t.toggle=function(e){return this._isShown?this.hide():this.show(e)},t.show=function(e){var t=this;if(!this._isShown&&!this._isTransitioning){this._element.classList.contains(CLASS_NAME_FADE)&&(this._isTransitioning=!0);var n=EventHandler.trigger(this._element,EVENT_SHOW$2,{relatedTarget:e});this._isShown||n.defaultPrevented||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),EventHandler.on(this._element,EVENT_CLICK_DISMISS,SELECTOR_DATA_DISMISS,(function(e){return t.hide(e)})),EventHandler.on(this._dialog,EVENT_MOUSEDOWN_DISMISS,(function(){EventHandler.one(t._element,EVENT_MOUSEUP_DISMISS,(function(e){e.target===t._element&&(t._ignoreBackdropClick=!0)}))})),this._showBackdrop((function(){return t._showElement(e)})))}},t.hide=function(e){var t=this;if((e&&e.preventDefault(),this._isShown&&!this._isTransitioning)&&!EventHandler.trigger(this._element,EVENT_HIDE$2).defaultPrevented){this._isShown=!1;var n=this._element.classList.contains(CLASS_NAME_FADE);if(n&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),EventHandler.off(document,EVENT_FOCUSIN),this._element.classList.remove(CLASS_NAME_SHOW$2),EventHandler.off(this._element,EVENT_CLICK_DISMISS),EventHandler.off(this._dialog,EVENT_MOUSEDOWN_DISMISS),n){var i=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,(function(e){return t._hideModal(e)})),emulateTransitionEnd(this._element,i)}else this._hideModal()}},t.dispose=function(){[window,this._element,this._dialog].forEach((function(e){return EventHandler.off(e,EVENT_KEY$5)})),EventHandler.off(document,EVENT_FOCUSIN),Data.removeData(this._element,DATA_KEY$5),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},t.handleUpdate=function(){this._adjustDialog()},t._getConfig=function(e){return e=_objectSpread2(_objectSpread2({},Default$3),e),typeCheckConfig(NAME$5,e,DefaultType$3),e},t._showElement=function(e){var t=this,n=this._element.classList.contains(CLASS_NAME_FADE),i=SelectorEngine.findOne(SELECTOR_MODAL_BODY,this._dialog);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0,i&&(i.scrollTop=0),n&&reflow(this._element),this._element.classList.add(CLASS_NAME_SHOW$2),this._config.focus&&this._enforceFocus();var r=function(){t._config.focus&&t._element.focus(),t._isTransitioning=!1,EventHandler.trigger(t._element,EVENT_SHOWN$2,{relatedTarget:e})};if(n){var o=getTransitionDurationFromElement(this._dialog);EventHandler.one(this._dialog,TRANSITION_END,r),emulateTransitionEnd(this._dialog,o)}else r()},t._enforceFocus=function(){var e=this;EventHandler.off(document,EVENT_FOCUSIN),EventHandler.on(document,EVENT_FOCUSIN,(function(t){document===t.target||e._element===t.target||e._element.contains(t.target)||e._element.focus()}))},t._setEscapeEvent=function(){var e=this;this._isShown?EventHandler.on(this._element,EVENT_KEYDOWN_DISMISS,(function(t){e._config.keyboard&&t.key===ESCAPE_KEY$1?(t.preventDefault(),e.hide()):e._config.keyboard||t.key!==ESCAPE_KEY$1||e._triggerBackdropTransition()})):EventHandler.off(this._element,EVENT_KEYDOWN_DISMISS)},t._setResizeEvent=function(){var e=this;this._isShown?EventHandler.on(window,EVENT_RESIZE,(function(){return e._adjustDialog()})):EventHandler.off(window,EVENT_RESIZE)},t._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop((function(){document.body.classList.remove(CLASS_NAME_OPEN),e._resetAdjustments(),e._resetScrollbar(),EventHandler.trigger(e._element,EVENT_HIDDEN$2)}))},t._removeBackdrop=function(){this._backdrop.parentNode.removeChild(this._backdrop),this._backdrop=null},t._showBackdrop=function(e){var t=this,n=this._element.classList.contains(CLASS_NAME_FADE)?CLASS_NAME_FADE:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className=CLASS_NAME_BACKDROP,n&&this._backdrop.classList.add(n),document.body.appendChild(this._backdrop),EventHandler.on(this._element,EVENT_CLICK_DISMISS,(function(e){t._ignoreBackdropClick?t._ignoreBackdropClick=!1:e.target===e.currentTarget&&t._triggerBackdropTransition()})),n&&reflow(this._backdrop),this._backdrop.classList.add(CLASS_NAME_SHOW$2),!n)return void e();var i=getTransitionDurationFromElement(this._backdrop);EventHandler.one(this._backdrop,TRANSITION_END,e),emulateTransitionEnd(this._backdrop,i)}else if(!this._isShown&&this._backdrop){this._backdrop.classList.remove(CLASS_NAME_SHOW$2);var r=function(){t._removeBackdrop(),e()};if(this._element.classList.contains(CLASS_NAME_FADE)){var o=getTransitionDurationFromElement(this._backdrop);EventHandler.one(this._backdrop,TRANSITION_END,r),emulateTransitionEnd(this._backdrop,o)}else r()}else e()},t._triggerBackdropTransition=function(){var e=this;if("static"===this._config.backdrop){if(EventHandler.trigger(this._element,EVENT_HIDE_PREVENTED).defaultPrevented)return;this._element.classList.add(CLASS_NAME_STATIC);var t=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,(function(){e._element.classList.remove(CLASS_NAME_STATIC)})),emulateTransitionEnd(this._element,t),this._element.focus()}else this.hide()},t._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},t._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},t._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(e.left+e.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},t._setScrollbar=function(){var e=this;if(this._isBodyOverflowing){SelectorEngine.find(SELECTOR_FIXED_CONTENT).forEach((function(t){var n=t.style.paddingRight,i=window.getComputedStyle(t)["padding-right"];Manipulator.setDataAttribute(t,"padding-right",n),t.style.paddingRight=parseFloat(i)+e._scrollbarWidth+"px"})),SelectorEngine.find(SELECTOR_STICKY_CONTENT).forEach((function(t){var n=t.style.marginRight,i=window.getComputedStyle(t)["margin-right"];Manipulator.setDataAttribute(t,"margin-right",n),t.style.marginRight=parseFloat(i)-e._scrollbarWidth+"px"}));var t=document.body.style.paddingRight,n=window.getComputedStyle(document.body)["padding-right"];Manipulator.setDataAttribute(document.body,"padding-right",t),document.body.style.paddingRight=parseFloat(n)+this._scrollbarWidth+"px"}document.body.classList.add(CLASS_NAME_OPEN)},t._resetScrollbar=function(){SelectorEngine.find(SELECTOR_FIXED_CONTENT).forEach((function(e){var t=Manipulator.getDataAttribute(e,"padding-right");void 0!==t&&(Manipulator.removeDataAttribute(e,"padding-right"),e.style.paddingRight=t)})),SelectorEngine.find(""+SELECTOR_STICKY_CONTENT).forEach((function(e){var t=Manipulator.getDataAttribute(e,"margin-right");void 0!==t&&(Manipulator.removeDataAttribute(e,"margin-right"),e.style.marginRight=t)}));var e=Manipulator.getDataAttribute(document.body,"padding-right");void 0===e?document.body.style.paddingRight="":(Manipulator.removeDataAttribute(document.body,"padding-right"),document.body.style.paddingRight=e)},t._getScrollbarWidth=function(){var e=document.createElement("div");e.className=CLASS_NAME_SCROLLBAR_MEASURER,document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},e.jQueryInterface=function(t,n){return this.each((function(){var i=Data.getData(this,DATA_KEY$5),r=_objectSpread2(_objectSpread2(_objectSpread2({},Default$3),Manipulator.getDataAttributes(this)),"object"==typeof t&&t?t:{});if(i||(i=new e(this,r)),"string"==typeof t){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t](n)}else r.show&&i.show(n)}))},e.getInstance=function(e){return Data.getData(e,DATA_KEY$5)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION$5}},{key:"Default",get:function(){return Default$3}}]),e}();EventHandler.on(document,EVENT_CLICK_DATA_API$5,SELECTOR_DATA_TOGGLE$3,(function(e){var t=this,n=getElementFromSelector(this);"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault(),EventHandler.one(n,EVENT_SHOW$2,(function(e){e.defaultPrevented||EventHandler.one(n,EVENT_HIDDEN$2,(function(){isVisible(t)&&t.focus()}))}));var i=Data.getData(n,DATA_KEY$5);if(!i){var r=_objectSpread2(_objectSpread2({},Manipulator.getDataAttributes(n)),Manipulator.getDataAttributes(this));i=new Modal(n,r)}i.show(this)}));var $$6=getjQuery();if($$6){var JQUERY_NO_CONFLICT$5=$$6.fn[NAME$5];$$6.fn[NAME$5]=Modal.jQueryInterface,$$6.fn[NAME$5].Constructor=Modal,$$6.fn[NAME$5].noConflict=function(){return $$6.fn[NAME$5]=JQUERY_NO_CONFLICT$5,Modal.jQueryInterface}}var uriAttrs=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],ARIA_ATTRIBUTE_PATTERN=/^aria-[\w-]*$/i,SAFE_URL_PATTERN=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,DATA_URL_PATTERN=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i,allowedAttribute=function(e,t){var n=e.nodeName.toLowerCase();if(-1!==t.indexOf(n))return-1===uriAttrs.indexOf(n)||Boolean(e.nodeValue.match(SAFE_URL_PATTERN)||e.nodeValue.match(DATA_URL_PATTERN));for(var i=t.filter((function(e){return e instanceof RegExp})),r=0,o=i.length;r<o;r++)if(n.match(i[r]))return!0;return!1},DefaultWhitelist={"*":["class","dir","id","lang","role",ARIA_ATTRIBUTE_PATTERN],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};function sanitizeHtml(e,t,n){var i;if(!e.length)return e;if(n&&"function"==typeof n)return n(e);for(var r=(new window.DOMParser).parseFromString(e,"text/html"),o=Object.keys(t),a=(i=[]).concat.apply(i,r.body.querySelectorAll("*")),s=function(e,n){var i,r=a[e],s=r.nodeName.toLowerCase();if(-1===o.indexOf(s))return r.parentNode.removeChild(r),"continue";var l=(i=[]).concat.apply(i,r.attributes),E=[].concat(t["*"]||[],t[s]||[]);l.forEach((function(e){allowedAttribute(e,E)||r.removeAttribute(e.nodeName)}))},l=0,E=a.length;l<E;l++)s(l);return r.body.innerHTML}var NAME$6="tooltip",VERSION$6="5.0.0-alpha1",DATA_KEY$6="bs.tooltip",EVENT_KEY$6="."+DATA_KEY$6,CLASS_PREFIX="bs-tooltip",BSCLS_PREFIX_REGEX=new RegExp("(^|\\s)"+CLASS_PREFIX+"\\S+","g"),DISALLOWED_ATTRIBUTES=["sanitize","whiteList","sanitizeFn"],DefaultType$4={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},AttachmentMap={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Default$4={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:DefaultWhitelist,popperConfig:null},Event$1={HIDE:"hide"+EVENT_KEY$6,HIDDEN:"hidden"+EVENT_KEY$6,SHOW:"show"+EVENT_KEY$6,SHOWN:"shown"+EVENT_KEY$6,INSERTED:"inserted"+EVENT_KEY$6,CLICK:"click"+EVENT_KEY$6,FOCUSIN:"focusin"+EVENT_KEY$6,FOCUSOUT:"focusout"+EVENT_KEY$6,MOUSEENTER:"mouseenter"+EVENT_KEY$6,MOUSELEAVE:"mouseleave"+EVENT_KEY$6},CLASS_NAME_FADE$1="fade",CLASS_NAME_MODAL="modal",CLASS_NAME_SHOW$3="show",HOVER_STATE_SHOW="show",HOVER_STATE_OUT="out",SELECTOR_TOOLTIP_INNER=".tooltip-inner",TRIGGER_HOVER="hover",TRIGGER_FOCUS="focus",TRIGGER_CLICK="click",TRIGGER_MANUAL="manual",Tooltip=function(){function e(e,t){if(void 0===Popper)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(t),this.tip=null,this._setListeners(),Data.setData(e,this.constructor.DATA_KEY,this)}var t=e.prototype;return t.enable=function(){this._isEnabled=!0},t.disable=function(){this._isEnabled=!1},t.toggleEnabled=function(){this._isEnabled=!this._isEnabled},t.toggle=function(e){if(this._isEnabled)if(e){var t=this.constructor.DATA_KEY,n=Data.getData(e.target,t);n||(n=new this.constructor(e.target,this._getDelegateConfig()),Data.setData(e.target,t,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)}else{if(this.getTipElement().classList.contains(CLASS_NAME_SHOW$3))return void this._leave(null,this);this._enter(null,this)}},t.dispose=function(){clearTimeout(this._timeout),Data.removeData(this.element,this.constructor.DATA_KEY),EventHandler.off(this.element,this.constructor.EVENT_KEY),EventHandler.off(this.element.closest("."+CLASS_NAME_MODAL),"hide.bs.modal",this._hideModalHandler),this.tip&&this.tip.parentNode.removeChild(this.tip),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},t.show=function(){var e=this;if("none"===this.element.style.display)throw new Error("Please use show on visible elements");if(this.isWithContent()&&this._isEnabled){var t=EventHandler.trigger(this.element,this.constructor.Event.SHOW),n=findShadowRoot(this.element),i=null===n?this.element.ownerDocument.documentElement.contains(this.element):n.contains(this.element);if(t.defaultPrevented||!i)return;var r=this.getTipElement(),o=getUID(this.constructor.NAME);r.setAttribute("id",o),this.element.setAttribute("aria-describedby",o),this.setContent(),this.config.animation&&r.classList.add(CLASS_NAME_FADE$1);var a="function"==typeof this.config.placement?this.config.placement.call(this,r,this.element):this.config.placement,s=this._getAttachment(a);this._addAttachmentClass(s);var l,E=this._getContainer();if(Data.setData(r,this.constructor.DATA_KEY,this),this.element.ownerDocument.documentElement.contains(this.tip)||E.appendChild(r),EventHandler.trigger(this.element,this.constructor.Event.INSERTED),this._popper=new Popper(this.element,r,this._getPopperConfig(s)),r.classList.add(CLASS_NAME_SHOW$3),"ontouchstart"in document.documentElement)(l=[]).concat.apply(l,document.body.children).forEach((function(e){EventHandler.on(e,"mouseover",noop())}));var _=function(){e.config.animation&&e._fixTransition();var t=e._hoverState;e._hoverState=null,EventHandler.trigger(e.element,e.constructor.Event.SHOWN),t===HOVER_STATE_OUT&&e._leave(null,e)};if(this.tip.classList.contains(CLASS_NAME_FADE$1)){var c=getTransitionDurationFromElement(this.tip);EventHandler.one(this.tip,TRANSITION_END,_),emulateTransitionEnd(this.tip,c)}else _()}},t.hide=function(){var e=this,t=this.getTipElement(),n=function(){e._hoverState!==HOVER_STATE_SHOW&&t.parentNode&&t.parentNode.removeChild(t),e._cleanTipClass(),e.element.removeAttribute("aria-describedby"),EventHandler.trigger(e.element,e.constructor.Event.HIDDEN),e._popper.destroy()};if(!EventHandler.trigger(this.element,this.constructor.Event.HIDE).defaultPrevented){var i;if(t.classList.remove(CLASS_NAME_SHOW$3),"ontouchstart"in document.documentElement)(i=[]).concat.apply(i,document.body.children).forEach((function(e){return EventHandler.off(e,"mouseover",noop)}));if(this._activeTrigger[TRIGGER_CLICK]=!1,this._activeTrigger[TRIGGER_FOCUS]=!1,this._activeTrigger[TRIGGER_HOVER]=!1,this.tip.classList.contains(CLASS_NAME_FADE$1)){var r=getTransitionDurationFromElement(t);EventHandler.one(t,TRANSITION_END,n),emulateTransitionEnd(t,r)}else n();this._hoverState=""}},t.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},t.isWithContent=function(){return Boolean(this.getTitle())},t.getTipElement=function(){if(this.tip)return this.tip;var e=document.createElement("div");return e.innerHTML=this.config.template,this.tip=e.children[0],this.tip},t.setContent=function(){var e=this.getTipElement();this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER,e),this.getTitle()),e.classList.remove(CLASS_NAME_FADE$1,CLASS_NAME_SHOW$3)},t.setElementContent=function(e,t){if(null!==e)return"object"==typeof t&&isElement(t)?(t.jquery&&(t=t[0]),void(this.config.html?t.parentNode!==e&&(e.innerHTML="",e.appendChild(t)):e.textContent=t.textContent)):void(this.config.html?(this.config.sanitize&&(t=sanitizeHtml(t,this.config.whiteList,this.config.sanitizeFn)),e.innerHTML=t):e.textContent=t)},t.getTitle=function(){var e=this.element.getAttribute("data-original-title");return e||(e="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),e},t._getPopperConfig=function(e){var t=this;return _objectSpread2(_objectSpread2({},{placement:e,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:"."+this.constructor.NAME+"-arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t._handlePopperPlacementChange(e)},onUpdate:function(e){return t._handlePopperPlacementChange(e)}}),this.config.popperConfig)},t._addAttachmentClass=function(e){this.getTipElement().classList.add(CLASS_PREFIX+"-"+e)},t._getOffset=function(){var e=this,t={};return"function"==typeof this.config.offset?t.fn=function(t){return t.offsets=_objectSpread2(_objectSpread2({},t.offsets),e.config.offset(t.offsets,e.element)||{}),t}:t.offset=this.config.offset,t},t._getContainer=function(){return!1===this.config.container?document.body:isElement(this.config.container)?this.config.container:SelectorEngine.findOne(this.config.container)},t._getAttachment=function(e){return AttachmentMap[e.toUpperCase()]},t._setListeners=function(){var e=this;this.config.trigger.split(" ").forEach((function(t){if("click"===t)EventHandler.on(e.element,e.constructor.Event.CLICK,e.config.selector,(function(t){return e.toggle(t)}));else if(t!==TRIGGER_MANUAL){var n=t===TRIGGER_HOVER?e.constructor.Event.MOUSEENTER:e.constructor.Event.FOCUSIN,i=t===TRIGGER_HOVER?e.constructor.Event.MOUSELEAVE:e.constructor.Event.FOCUSOUT;EventHandler.on(e.element,n,e.config.selector,(function(t){return e._enter(t)})),EventHandler.on(e.element,i,e.config.selector,(function(t){return e._leave(t)}))}})),this._hideModalHandler=function(){e.element&&e.hide()},EventHandler.on(this.element.closest("."+CLASS_NAME_MODAL),"hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=_objectSpread2(_objectSpread2({},this.config),{},{trigger:"manual",selector:""}):this._fixTitle()},t._fixTitle=function(){var e=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==e)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},t._enter=function(e,t){var n=this.constructor.DATA_KEY;(t=t||Data.getData(e.target,n))||(t=new this.constructor(e.target,this._getDelegateConfig()),Data.setData(e.target,n,t)),e&&(t._activeTrigger["focusin"===e.type?TRIGGER_FOCUS:TRIGGER_HOVER]=!0),t.getTipElement().classList.contains(CLASS_NAME_SHOW$3)||t._hoverState===HOVER_STATE_SHOW?t._hoverState=HOVER_STATE_SHOW:(clearTimeout(t._timeout),t._hoverState=HOVER_STATE_SHOW,t.config.delay&&t.config.delay.show?t._timeout=setTimeout((function(){t._hoverState===HOVER_STATE_SHOW&&t.show()}),t.config.delay.show):t.show())},t._leave=function(e,t){var n=this.constructor.DATA_KEY;(t=t||Data.getData(e.target,n))||(t=new this.constructor(e.target,this._getDelegateConfig()),Data.setData(e.target,n,t)),e&&(t._activeTrigger["focusout"===e.type?TRIGGER_FOCUS:TRIGGER_HOVER]=!1),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState=HOVER_STATE_OUT,t.config.delay&&t.config.delay.hide?t._timeout=setTimeout((function(){t._hoverState===HOVER_STATE_OUT&&t.hide()}),t.config.delay.hide):t.hide())},t._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},t._getConfig=function(e){var t=Manipulator.getDataAttributes(this.element);return Object.keys(t).forEach((function(e){-1!==DISALLOWED_ATTRIBUTES.indexOf(e)&&delete t[e]})),e&&"object"==typeof e.container&&e.container.jquery&&(e.container=e.container[0]),"number"==typeof(e=_objectSpread2(_objectSpread2(_objectSpread2({},this.constructor.Default),t),"object"==typeof e&&e?e:{})).delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),typeCheckConfig(NAME$6,e,this.constructor.DefaultType),e.sanitize&&(e.template=sanitizeHtml(e.template,e.whiteList,e.sanitizeFn)),e},t._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},t._cleanTipClass=function(){var e=this.getTipElement(),t=e.getAttribute("class").match(BSCLS_PREFIX_REGEX);null!==t&&t.length>0&&t.map((function(e){return e.trim()})).forEach((function(t){return e.classList.remove(t)}))},t._handlePopperPlacementChange=function(e){var t=e.instance;this.tip=t.popper,this._cleanTipClass(),this._addAttachmentClass(this._getAttachment(e.placement))},t._fixTransition=function(){var e=this.getTipElement(),t=this.config.animation;null===e.getAttribute("x-placement")&&(e.classList.remove(CLASS_NAME_FADE$1),this.config.animation=!1,this.hide(),this.show(),this.config.animation=t)},e.jQueryInterface=function(t){return this.each((function(){var n=Data.getData(this,DATA_KEY$6),i="object"==typeof t&&t;if((n||!/dispose|hide/.test(t))&&(n||(n=new e(this,i)),"string"==typeof t)){if(void 0===n[t])throw new TypeError('No method named "'+t+'"');n[t]()}}))},e.getInstance=function(e){return Data.getData(e,DATA_KEY$6)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION$6}},{key:"Default",get:function(){return Default$4}},{key:"NAME",get:function(){return NAME$6}},{key:"DATA_KEY",get:function(){return DATA_KEY$6}},{key:"Event",get:function(){return Event$1}},{key:"EVENT_KEY",get:function(){return EVENT_KEY$6}},{key:"DefaultType",get:function(){return DefaultType$4}}]),e}(),$$7=getjQuery();if($$7){var JQUERY_NO_CONFLICT$6=$$7.fn[NAME$6];$$7.fn[NAME$6]=Tooltip.jQueryInterface,$$7.fn[NAME$6].Constructor=Tooltip,$$7.fn[NAME$6].noConflict=function(){return $$7.fn[NAME$6]=JQUERY_NO_CONFLICT$6,Tooltip.jQueryInterface}}var NAME$7="popover",VERSION$7="5.0.0-alpha1",DATA_KEY$7="bs.popover",EVENT_KEY$7="."+DATA_KEY$7,CLASS_PREFIX$1="bs-popover",BSCLS_PREFIX_REGEX$1=new RegExp("(^|\\s)"+CLASS_PREFIX$1+"\\S+","g"),Default$5=_objectSpread2(_objectSpread2({},Tooltip.Default),{},{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),DefaultType$5=_objectSpread2(_objectSpread2({},Tooltip.DefaultType),{},{content:"(string|element|function)"}),Event$2={HIDE:"hide"+EVENT_KEY$7,HIDDEN:"hidden"+EVENT_KEY$7,SHOW:"show"+EVENT_KEY$7,SHOWN:"shown"+EVENT_KEY$7,INSERTED:"inserted"+EVENT_KEY$7,CLICK:"click"+EVENT_KEY$7,FOCUSIN:"focusin"+EVENT_KEY$7,FOCUSOUT:"focusout"+EVENT_KEY$7,MOUSEENTER:"mouseenter"+EVENT_KEY$7,MOUSELEAVE:"mouseleave"+EVENT_KEY$7},CLASS_NAME_FADE$2="fade",CLASS_NAME_SHOW$4="show",SELECTOR_TITLE=".popover-header",SELECTOR_CONTENT=".popover-body",Popover=function(e){function t(){return e.apply(this,arguments)||this}_inheritsLoose(t,e);var n=t.prototype;return n.isWithContent=function(){return this.getTitle()||this._getContent()},n.setContent=function(){var e=this.getTipElement();this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE,e),this.getTitle());var t=this._getContent();"function"==typeof t&&(t=t.call(this.element)),this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT,e),t),e.classList.remove(CLASS_NAME_FADE$2,CLASS_NAME_SHOW$4)},n._addAttachmentClass=function(e){this.getTipElement().classList.add(CLASS_PREFIX$1+"-"+e)},n._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},n._cleanTipClass=function(){var e=this.getTipElement(),t=e.getAttribute("class").match(BSCLS_PREFIX_REGEX$1);null!==t&&t.length>0&&t.map((function(e){return e.trim()})).forEach((function(t){return e.classList.remove(t)}))},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$7),i="object"==typeof e?e:null;if((n||!/dispose|hide/.test(e))&&(n||(n=new t(this,i),Data.setData(this,DATA_KEY$7,n)),"string"==typeof e)){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}}))},t.getInstance=function(e){return Data.getData(e,DATA_KEY$7)},_createClass(t,null,[{key:"VERSION",get:function(){return VERSION$7}},{key:"Default",get:function(){return Default$5}},{key:"NAME",get:function(){return NAME$7}},{key:"DATA_KEY",get:function(){return DATA_KEY$7}},{key:"Event",get:function(){return Event$2}},{key:"EVENT_KEY",get:function(){return EVENT_KEY$7}},{key:"DefaultType",get:function(){return DefaultType$5}}]),t}(Tooltip),$$8=getjQuery();if($$8){var JQUERY_NO_CONFLICT$7=$$8.fn[NAME$7];$$8.fn[NAME$7]=Popover.jQueryInterface,$$8.fn[NAME$7].Constructor=Popover,$$8.fn[NAME$7].noConflict=function(){return $$8.fn[NAME$7]=JQUERY_NO_CONFLICT$7,Popover.jQueryInterface}}var NAME$8="scrollspy",VERSION$8="5.0.0-alpha1",DATA_KEY$8="bs.scrollspy",EVENT_KEY$8="."+DATA_KEY$8,DATA_API_KEY$6=".data-api",Default$6={offset:10,method:"auto",target:""},DefaultType$6={offset:"number",method:"string",target:"(string|element)"},EVENT_ACTIVATE="activate"+EVENT_KEY$8,EVENT_SCROLL="scroll"+EVENT_KEY$8,EVENT_LOAD_DATA_API$1="load"+EVENT_KEY$8+DATA_API_KEY$6,CLASS_NAME_DROPDOWN_ITEM="dropdown-item",CLASS_NAME_ACTIVE$2="active",SELECTOR_DATA_SPY='[data-spy="scroll"]',SELECTOR_NAV_LIST_GROUP=".nav, .list-group",SELECTOR_NAV_LINKS=".nav-link",SELECTOR_NAV_ITEMS=".nav-item",SELECTOR_LIST_ITEMS=".list-group-item",SELECTOR_DROPDOWN=".dropdown",SELECTOR_DROPDOWN_TOGGLE=".dropdown-toggle",METHOD_OFFSET="offset",METHOD_POSITION="position",ScrollSpy=function(){function e(e,t){var n=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(t),this._selector=this._config.target+" "+SELECTOR_NAV_LINKS+","+this._config.target+" "+SELECTOR_LIST_ITEMS+","+this._config.target+" ."+CLASS_NAME_DROPDOWN_ITEM,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,EventHandler.on(this._scrollElement,EVENT_SCROLL,(function(e){return n._process(e)})),this.refresh(),this._process(),Data.setData(e,DATA_KEY$8,this)}var t=e.prototype;return t.refresh=function(){var e=this,t=this._scrollElement===this._scrollElement.window?METHOD_OFFSET:METHOD_POSITION,n="auto"===this._config.method?t:this._config.method,i=n===METHOD_POSITION?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),SelectorEngine.find(this._selector).map((function(e){var t,r=getSelectorFromElement(e);if(r&&(t=SelectorEngine.findOne(r)),t){var o=t.getBoundingClientRect();if(o.width||o.height)return[Manipulator[n](t).top+i,r]}return null})).filter((function(e){return e})).sort((function(e,t){return e[0]-t[0]})).forEach((function(t){e._offsets.push(t[0]),e._targets.push(t[1])}))},t.dispose=function(){Data.removeData(this._element,DATA_KEY$8),EventHandler.off(this._scrollElement,EVENT_KEY$8),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},t._getConfig=function(e){if("string"!=typeof(e=_objectSpread2(_objectSpread2({},Default$6),"object"==typeof e&&e?e:{})).target&&isElement(e.target)){var t=e.target.id;t||(t=getUID(NAME$8),e.target.id=t),e.target="#"+t}return typeCheckConfig(NAME$8,e,DefaultType$6),e},t._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},t._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},t._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},t._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),n=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),e>=n){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&e<this._offsets[0]&&this._offsets[0]>0)return this._activeTarget=null,void this._clear();for(var r=this._offsets.length;r--;){this._activeTarget!==this._targets[r]&&e>=this._offsets[r]&&(void 0===this._offsets[r+1]||e<this._offsets[r+1])&&this._activate(this._targets[r])}}},t._activate=function(e){this._activeTarget=e,this._clear();var t=this._selector.split(",").map((function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'})),n=SelectorEngine.findOne(t.join(","));n.classList.contains(CLASS_NAME_DROPDOWN_ITEM)?(SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE,n.closest(SELECTOR_DROPDOWN)).classList.add(CLASS_NAME_ACTIVE$2),n.classList.add(CLASS_NAME_ACTIVE$2)):(n.classList.add(CLASS_NAME_ACTIVE$2),SelectorEngine.parents(n,SELECTOR_NAV_LIST_GROUP).forEach((function(e){SelectorEngine.prev(e,SELECTOR_NAV_LINKS+", "+SELECTOR_LIST_ITEMS).forEach((function(e){return e.classList.add(CLASS_NAME_ACTIVE$2)})),SelectorEngine.prev(e,SELECTOR_NAV_ITEMS).forEach((function(e){SelectorEngine.children(e,SELECTOR_NAV_LINKS).forEach((function(e){return e.classList.add(CLASS_NAME_ACTIVE$2)}))}))}))),EventHandler.trigger(this._scrollElement,EVENT_ACTIVATE,{relatedTarget:e})},t._clear=function(){SelectorEngine.find(this._selector).filter((function(e){return e.classList.contains(CLASS_NAME_ACTIVE$2)})).forEach((function(e){return e.classList.remove(CLASS_NAME_ACTIVE$2)}))},e.jQueryInterface=function(t){return this.each((function(){var n=Data.getData(this,DATA_KEY$8);if(n||(n=new e(this,"object"==typeof t&&t)),"string"==typeof t){if(void 0===n[t])throw new TypeError('No method named "'+t+'"');n[t]()}}))},e.getInstance=function(e){return Data.getData(e,DATA_KEY$8)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION$8}},{key:"Default",get:function(){return Default$6}}]),e}();EventHandler.on(window,EVENT_LOAD_DATA_API$1,(function(){SelectorEngine.find(SELECTOR_DATA_SPY).forEach((function(e){return new ScrollSpy(e,Manipulator.getDataAttributes(e))}))}));var $$9=getjQuery();if($$9){var JQUERY_NO_CONFLICT$8=$$9.fn[NAME$8];$$9.fn[NAME$8]=ScrollSpy.jQueryInterface,$$9.fn[NAME$8].Constructor=ScrollSpy,$$9.fn[NAME$8].noConflict=function(){return $$9.fn[NAME$8]=JQUERY_NO_CONFLICT$8,ScrollSpy.jQueryInterface}}var NAME$9="tab",VERSION$9="5.0.0-alpha1",DATA_KEY$9="bs.tab",EVENT_KEY$9="."+DATA_KEY$9,DATA_API_KEY$7=".data-api",EVENT_HIDE$3="hide"+EVENT_KEY$9,EVENT_HIDDEN$3="hidden"+EVENT_KEY$9,EVENT_SHOW$3="show"+EVENT_KEY$9,EVENT_SHOWN$3="shown"+EVENT_KEY$9,EVENT_CLICK_DATA_API$6="click"+EVENT_KEY$9+DATA_API_KEY$7,CLASS_NAME_DROPDOWN_MENU="dropdown-menu",CLASS_NAME_ACTIVE$3="active",CLASS_NAME_DISABLED$1="disabled",CLASS_NAME_FADE$3="fade",CLASS_NAME_SHOW$5="show",SELECTOR_DROPDOWN$1=".dropdown",SELECTOR_NAV_LIST_GROUP$1=".nav, .list-group",SELECTOR_ACTIVE$1=".active",SELECTOR_ACTIVE_UL=":scope > li > .active",SELECTOR_DATA_TOGGLE$4='[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',SELECTOR_DROPDOWN_TOGGLE$1=".dropdown-toggle",SELECTOR_DROPDOWN_ACTIVE_CHILD=":scope > .dropdown-menu .active",Tab=function(){function e(e){this._element=e,Data.setData(this._element,DATA_KEY$9,this)}var t=e.prototype;return t.show=function(){var e=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&this._element.classList.contains(CLASS_NAME_ACTIVE$3)||this._element.classList.contains(CLASS_NAME_DISABLED$1))){var t,n=getElementFromSelector(this._element),i=this._element.closest(SELECTOR_NAV_LIST_GROUP$1);if(i){var r="UL"===i.nodeName||"OL"===i.nodeName?SELECTOR_ACTIVE_UL:SELECTOR_ACTIVE$1;t=(t=SelectorEngine.find(r,i))[t.length-1]}var o=null;if(t&&(o=EventHandler.trigger(t,EVENT_HIDE$3,{relatedTarget:this._element})),!(EventHandler.trigger(this._element,EVENT_SHOW$3,{relatedTarget:t}).defaultPrevented||null!==o&&o.defaultPrevented)){this._activate(this._element,i);var a=function(){EventHandler.trigger(t,EVENT_HIDDEN$3,{relatedTarget:e._element}),EventHandler.trigger(e._element,EVENT_SHOWN$3,{relatedTarget:t})};n?this._activate(n,n.parentNode,a):a()}}},t.dispose=function(){Data.removeData(this._element,DATA_KEY$9),this._element=null},t._activate=function(e,t,n){var i=this,r=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?SelectorEngine.children(t,SELECTOR_ACTIVE$1):SelectorEngine.find(SELECTOR_ACTIVE_UL,t))[0],o=n&&r&&r.classList.contains(CLASS_NAME_FADE$3),a=function(){return i._transitionComplete(e,r,n)};if(r&&o){var s=getTransitionDurationFromElement(r);r.classList.remove(CLASS_NAME_SHOW$5),EventHandler.one(r,TRANSITION_END,a),emulateTransitionEnd(r,s)}else a()},t._transitionComplete=function(e,t,n){if(t){t.classList.remove(CLASS_NAME_ACTIVE$3);var i=SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD,t.parentNode);i&&i.classList.remove(CLASS_NAME_ACTIVE$3),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!1)}(e.classList.add(CLASS_NAME_ACTIVE$3),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),reflow(e),e.classList.contains(CLASS_NAME_FADE$3)&&e.classList.add(CLASS_NAME_SHOW$5),e.parentNode&&e.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU))&&(e.closest(SELECTOR_DROPDOWN$1)&&SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE$1).forEach((function(e){return e.classList.add(CLASS_NAME_ACTIVE$3)})),e.setAttribute("aria-expanded",!0));n&&n()},e.jQueryInterface=function(t){return this.each((function(){var n=Data.getData(this,DATA_KEY$9)||new e(this);if("string"==typeof t){if(void 0===n[t])throw new TypeError('No method named "'+t+'"');n[t]()}}))},e.getInstance=function(e){return Data.getData(e,DATA_KEY$9)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION$9}}]),e}();EventHandler.on(document,EVENT_CLICK_DATA_API$6,SELECTOR_DATA_TOGGLE$4,(function(e){e.preventDefault(),(Data.getData(this,DATA_KEY$9)||new Tab(this)).show()}));var $$a=getjQuery();if($$a){var JQUERY_NO_CONFLICT$9=$$a.fn[NAME$9];$$a.fn[NAME$9]=Tab.jQueryInterface,$$a.fn[NAME$9].Constructor=Tab,$$a.fn[NAME$9].noConflict=function(){return $$a.fn[NAME$9]=JQUERY_NO_CONFLICT$9,Tab.jQueryInterface}}var NAME$a="toast",VERSION$a="5.0.0-alpha1",DATA_KEY$a="bs.toast",EVENT_KEY$a="."+DATA_KEY$a,EVENT_CLICK_DISMISS$1="click.dismiss"+EVENT_KEY$a,EVENT_HIDE$4="hide"+EVENT_KEY$a,EVENT_HIDDEN$4="hidden"+EVENT_KEY$a,EVENT_SHOW$4="show"+EVENT_KEY$a,EVENT_SHOWN$4="shown"+EVENT_KEY$a,CLASS_NAME_FADE$4="fade",CLASS_NAME_HIDE="hide",CLASS_NAME_SHOW$6="show",CLASS_NAME_SHOWING="showing",DefaultType$7={animation:"boolean",autohide:"boolean",delay:"number"},Default$7={animation:!0,autohide:!0,delay:500},SELECTOR_DATA_DISMISS$1='[data-dismiss="toast"]',Toast=function(){function e(e,t){this._element=e,this._config=this._getConfig(t),this._timeout=null,this._setListeners(),Data.setData(e,DATA_KEY$a,this)}var t=e.prototype;return t.show=function(){var e=this;if(!EventHandler.trigger(this._element,EVENT_SHOW$4).defaultPrevented){this._config.animation&&this._element.classList.add(CLASS_NAME_FADE$4);var t=function(){e._element.classList.remove(CLASS_NAME_SHOWING),e._element.classList.add(CLASS_NAME_SHOW$6),EventHandler.trigger(e._element,EVENT_SHOWN$4),e._config.autohide&&(e._timeout=setTimeout((function(){e.hide()}),e._config.delay))};if(this._element.classList.remove(CLASS_NAME_HIDE),reflow(this._element),this._element.classList.add(CLASS_NAME_SHOWING),this._config.animation){var n=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,t),emulateTransitionEnd(this._element,n)}else t()}},t.hide=function(){var e=this;if(this._element.classList.contains(CLASS_NAME_SHOW$6)&&!EventHandler.trigger(this._element,EVENT_HIDE$4).defaultPrevented){var t=function(){e._element.classList.add(CLASS_NAME_HIDE),EventHandler.trigger(e._element,EVENT_HIDDEN$4)};if(this._element.classList.remove(CLASS_NAME_SHOW$6),this._config.animation){var n=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,t),emulateTransitionEnd(this._element,n)}else t()}},t.dispose=function(){clearTimeout(this._timeout),this._timeout=null,this._element.classList.contains(CLASS_NAME_SHOW$6)&&this._element.classList.remove(CLASS_NAME_SHOW$6),EventHandler.off(this._element,EVENT_CLICK_DISMISS$1),Data.removeData(this._element,DATA_KEY$a),this._element=null,this._config=null},t._getConfig=function(e){return e=_objectSpread2(_objectSpread2(_objectSpread2({},Default$7),Manipulator.getDataAttributes(this._element)),"object"==typeof e&&e?e:{}),typeCheckConfig(NAME$a,e,this.constructor.DefaultType),e},t._setListeners=function(){var e=this;EventHandler.on(this._element,EVENT_CLICK_DISMISS$1,SELECTOR_DATA_DISMISS$1,(function(){return e.hide()}))},e.jQueryInterface=function(t){return this.each((function(){var n=Data.getData(this,DATA_KEY$a);if(n||(n=new e(this,"object"==typeof t&&t)),"string"==typeof t){if(void 0===n[t])throw new TypeError('No method named "'+t+'"');n[t](this)}}))},e.getInstance=function(e){return Data.getData(e,DATA_KEY$a)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION$a}},{key:"DefaultType",get:function(){return DefaultType$7}},{key:"Default",get:function(){return Default$7}}]),e}(),$$b=getjQuery();if($$b){var JQUERY_NO_CONFLICT$a=$$b.fn[NAME$a];$$b.fn[NAME$a]=Toast.jQueryInterface,$$b.fn[NAME$a].Constructor=Toast,$$b.fn[NAME$a].noConflict=function(){return $$b.fn[NAME$a]=JQUERY_NO_CONFLICT$a,Toast.jQueryInterface}}export{Alert,Button,Carousel,Collapse,Dropdown,Modal,Popover,ScrollSpy,Tab,Toast,Tooltip};
//# sourceMappingURL=bootstrap.esm.min.js.map