{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/polyfill.js", "../../js/src/dom/event-handler.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/popper.js/dist/esm/popper.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["storeData", "id", "e", "element", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "_window$getComputedSt", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "nodeType", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "call", "match", "toLowerCase", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "mapData", "set", "key", "data", "get", "keyProperties", "delete", "Data", "instance", "find", "Element", "prototype", "querySelectorAll", "findOne", "defaultPreventedPreservedOnDispatch", "CustomEvent", "cancelable", "createElement", "preventDefault", "defaultPrevented", "scopeSelectorRegex", "_", "this", "hasId", "Boolean", "nodeList", "replace", "removeAttribute", "matches", "$", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "handler", "delegationSelector", "uidEventList", "i", "len", "length", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "custom", "indexOf", "add<PERSON><PERSON><PERSON>", "oneOff", "_normalizeParams", "handlers", "previousFn", "fn", "dom<PERSON><PERSON>s", "target", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "on", "one", "_normalizeParams2", "inNamespace", "isNamespace", "char<PERSON>t", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "isNative", "bubbles", "nativeDispatch", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "defineProperty", "NAME", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "dispose", "closest", "_this", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "getInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "Number", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "getDataAttributes", "attributes", "_objectSpread2", "dataset", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "toggleClass", "className", "add", "SelectorEngine", "_ref", "documentElement", "concat", "findFn", "children", "_ref2", "filter", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "EVENT_KEY", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_this2", "_keydown", "_addTouchEventListeners", "_this3", "start", "pointerType", "clientX", "touches", "end", "clearTimeout", "itemImg", "move", "tagName", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "directionalClassName", "orderClassName", "_this4", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "nextElementInterval", "parseInt", "defaultInterval", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "SELECTOR_DATA_TOGGLE", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "<PERSON><PERSON><PERSON><PERSON>", "timeoutDuration", "longerTimeoutBrowsers", "userAgent", "debounce", "Promise", "resolve", "then", "scheduled", "isFunction", "functionToCheck", "getStyleComputedProperty", "css", "ownerDocument", "defaultView", "getParentNode", "nodeName", "host", "getScrollParent", "_getStyleComputedProp", "overflow", "overflowX", "overflowY", "getReferenceNode", "reference", "referenceNode", "isIE11", "MSInputMethodContext", "documentMode", "isIE10", "isIE", "version", "getOffsetParent", "noOffsetParent", "offsetParent", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "DOCUMENT_POSITION_FOLLOWING", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "isOffsetContainer", "element1root", "getScroll", "side", "arguments", "undefined", "upperSide", "html", "scrollingElement", "includeScroll", "subtract", "modifier", "bottom", "right", "getBordersSize", "styles", "axis", "sideA", "sideB", "getSize", "computedStyle", "max", "getWindowSizes", "height", "width", "classCallCheck", "createClass", "defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "protoProps", "staticProps", "_extends", "assign", "source", "hasOwnProperty", "getClientRect", "offsets", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "getOffsetRectRelativeToArbitraryNode", "fixedPosition", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "excludeScroll", "relativeOffset", "innerWidth", "innerHeight", "isFixed", "getFixedPositionOffsetParent", "parentElement", "el", "getBoundaries", "popper", "padding", "boundariesElement", "boundaries", "boundariesNode", "_getWindowSizes", "isPaddingNumber", "getArea", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "map", "area", "sort", "a", "b", "filtered<PERSON><PERSON>s", "computedPlacement", "variation", "getReferenceOffsets", "state", "commonOffsetParent", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "arr", "check", "Array", "runModifiers", "modifiers", "ends", "prop", "findIndex", "cur", "console", "warn", "enabled", "update", "isDestroyed", "arrowStyles", "flipped", "options", "positionFixed", "flip", "originalPlacement", "isCreated", "onUpdate", "onCreate", "isModifierEnabled", "modifierName", "some", "name", "getSupportedPropertyName", "prefixes", "upperProp", "to<PERSON><PERSON><PERSON>", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "disableEventListeners", "removeOnDestroy", "getWindow", "setupEventListeners", "updateBound", "passive", "scrollElement", "attachToScrollParents", "callback", "scrollParents", "isBody", "eventsEnabled", "enableEventListeners", "scheduleUpdate", "cancelAnimationFrame", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "isFirefox", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "_requesting", "requested", "placements", "validPlacements", "clockwise", "counter", "reverse", "BEHAVIORS", "parseOffset", "basePlacement", "useHeight", "fragments", "frag", "divider", "search", "splitRegex", "ops", "op", "mergeWithPrevious", "reduce", "str", "toValue", "index2", "De<PERSON>ults", "shift", "shiftvariation", "_data$offsets", "isVertical", "shiftOffsets", "preventOverflow", "transformProp", "popperStyles", "transform", "priority", "primary", "escapeWithReference", "secondary", "min", "keepTogether", "opSide", "arrow", "_data$offsets$arrow", "arrowElement", "sideCapitalized", "altSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "round", "placementOpposite", "flipOrder", "behavior", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariationByRef", "flipVariations", "flippedVariationByContent", "flipVariationsByContent", "flippedVariation", "getOppositeVariation", "inner", "subtractLength", "bound", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "shouldRound", "noRound", "v", "referenceWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isVariation", "horizontalToInteger", "verticalToInteger", "getRoundedOffsets", "devicePixelRatio", "prefixedProperty", "invertTop", "invertLeft", "x-placement", "applyStyle", "setAttributes", "onLoad", "modifierOptions", "<PERSON><PERSON>", "requestAnimationFrame", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "REGEXP_KEYDOWN", "ARROW_UP_KEY", "boundary", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "referenceElement", "_getPopperConfig", "focus", "stopPropagation", "constructor", "_getPlacement", "parentDropdown", "_getOffset", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "showEvent", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_this5", "_triggerBackdropTransition", "_this6", "_this7", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "_this8", "animate", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "_this9", "modalTransitionDuration", "isModalOverflowing", "scrollHeight", "paddingLeft", "paddingRight", "_getScrollbarWidth", "_this10", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "margin", "scrollDiv", "scrollbarWidth", "_this11", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "*", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "whiteList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "whitelist<PERSON><PERSON>s", "elements", "_loop", "el<PERSON>ame", "attributeList", "whitelistedAttributes", "attr", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "allowedAttribute", "innerHTML", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "fallbackPlacement", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "DATA_KEY", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "findShadowRoot", "attachShadow", "getRootNode", "root", "ShadowRoot", "isInTheDom", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "complete", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "_handlePopperPlacementChange", "CLASS_PREFIX", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "popperData", "popperInstance", "initConfigAnimation", "Popover", "_getContent", "method", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "item", "pageYOffset", "_getOffsetHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "SELECTOR_NAV_LINKS", "navItem", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdown", "autohide", "Toast"], "mappings": ";;;;;slCAOA,ICOQA,EACFC,ECCEC,EAIAC,EFMFC,EAAS,SAAAC,GACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnBC,SAASC,eAAeL,IAEjC,OAAOA,GAGHM,EAAc,SAAAR,GAClB,IAAIS,EAAWT,EAAQU,aAAa,eAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAME,EAAWX,EAAQU,aAAa,QAEtCD,EAAWE,GAAyB,MAAbA,EAAmBA,EAASC,OAAS,KAG9D,OAAOH,GAGHI,EAAyB,SAAAb,GAC7B,IAAMS,EAAWD,EAAYR,GAE7B,OAAIS,GACKH,SAASQ,cAAcL,GAAYA,EAGrC,MAGHM,EAAyB,SAAAf,GAC7B,IAAMS,EAAWD,EAAYR,GAE7B,OAAOS,EAAWH,SAASQ,cAAcL,GAAY,MAGjDO,EAAmC,SAAAhB,GACvC,IAAKA,EACH,OAAO,EAFyC,IAAAiB,EAS9CC,OAAOC,iBAAiBnB,GAF1BoB,EAPgDH,EAOhDG,mBACAC,EARgDJ,EAQhDI,gBAGIC,EAA0BC,WAAWH,GACrCI,EAAuBD,WAAWF,GAGxC,OAAKC,GAA4BE,GAKjCJ,EAAqBA,EAAmBK,MAAM,KAAK,GACnDJ,EAAkBA,EAAgBI,MAAM,KAAK,GA3Ef,KA6EtBF,WAAWH,GAAsBG,WAAWF,KAP3C,GAULK,EAAuB,SAAA1B,GAC3BA,EAAQ2B,cAAc,IAAIC,MAhFL,mBAmFjBC,EAAY,SAAAC,GAAG,OAAKA,EAAI,IAAMA,GAAKC,UAEnCC,EAAuB,SAAChC,EAASiC,GACrC,IAAIC,GAAS,EAEPC,EAAmBF,EADD,EAOxBjC,EAAQoC,iBA9Fa,iBAyFrB,SAASC,IACPH,GAAS,EACTlC,EAAQsC,oBA3FW,gBA2FyBD,MAI9CE,YAAW,WACJL,GACHR,EAAqB1B,KAEtBmC,IAGCK,EAAkB,SAACC,EAAeC,EAAQC,GAC9CC,OAAOC,KAAKF,GACTG,SAAQ,SAAAC,GACP,IAtGSjB,EAsGHkB,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASpB,EAAUoB,GACnC,UAxGFnB,OADSA,EA0GAmB,GAxGX,GAAUnB,EAGL,GAAGqB,SAASC,KAAKtB,GAAKuB,MAAM,eAAe,GAAGC,cAuGjD,IAAK,IAAIC,OAAOP,GAAeQ,KAAKN,GAClC,MAAM,IAAIO,MACLhB,EAAciB,cAAdjB,aACQM,EADX,oBACuCG,EADpCT,wBAEmBO,EAFtB,UAOJW,EAAY,SAAA3D,GAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQ4D,OAAS5D,EAAQ6D,YAAc7D,EAAQ6D,WAAWD,MAAO,CACnE,IAAME,EAAe3C,iBAAiBnB,GAChC+D,EAAkB5C,iBAAiBnB,EAAQ6D,YAEjD,MAAgC,SAAzBC,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GA0BHC,EAAO,WAAA,OAAM,cAEbC,EAAS,SAAAnE,GAAO,OAAIA,EAAQoE,cAE5BC,EAAY,WAAM,IACdC,EAAWpD,OAAXoD,OAER,OAAIA,IAAWhE,SAASiE,KAAKC,aAAa,kBACjCF,EAGF,MCvKHG,GACE5E,EAAY,GACdC,EAAK,EACF,CACL4E,IADK,SACD1E,EAAS2E,EAAKC,QACW,IAAhB5E,EAAQ2E,MACjB3E,EAAQ2E,IAAM,CACZA,IAAAA,EACA7E,GAAAA,GAEFA,KAGFD,EAAUG,EAAQ2E,IAAI7E,IAAM8E,GAE9BC,IAZK,SAYD7E,EAAS2E,GACX,IAAK3E,QAAkC,IAAhBA,EAAQ2E,IAC7B,OAAO,KAGT,IAAMG,EAAgB9E,EAAQ2E,IAC9B,OAAIG,EAAcH,MAAQA,EACjB9E,EAAUiF,EAAchF,IAG1B,MAETiF,OAxBK,SAwBE/E,EAAS2E,GACd,QAA2B,IAAhB3E,EAAQ2E,IAAnB,CAIA,IAAMG,EAAgB9E,EAAQ2E,IAC1BG,EAAcH,MAAQA,WACjB9E,EAAUiF,EAAchF,WACxBE,EAAQ2E,SAMjBK,EAAO,SACHC,EAAUN,EAAKC,GACrBH,EAAQC,IAAIO,EAAUN,EAAKC,IAFzBI,EAAO,SAIHC,EAAUN,GAChB,OAAOF,EAAQI,IAAII,EAAUN,IAL3BK,EAAO,SAOAC,EAAUN,GACnBF,EAAQM,OAAOE,EAAUN,ICnDzBO,EAAOC,QAAQC,UAAUC,iBACzBC,EAAUH,QAAQC,UAAUtE,cAG1ByE,GACExF,EAAI,IAAIyF,YAAY,YAAa,CACrCC,YAAY,KAGRzF,EAAUM,SAASoF,cAAc,QAC/BtD,iBAAiB,aAAa,WAAA,OAAM,QAE5CrC,EAAE4F,iBACF3F,EAAQ2B,cAAc5B,GACfA,EAAE6F,kBAGLC,EAAqB,YACA,WACzB,IAAM7F,EAAUM,SAASoF,cAAc,OAEvC,IACE1F,EAAQqF,iBAAiB,YACzB,MAAOS,GACP,OAAO,EAGT,OAAO,GATkB,KAazBZ,EAAO,SAAUzE,GACf,IAAKoF,EAAmBrC,KAAK/C,GAC3B,OAAOsF,KAAKV,iBAAiB5E,GAG/B,IAAMuF,EAAQC,QAAQF,KAAKjG,IAEtBkG,IACHD,KAAKjG,GAAKG,EAAO,UAGnB,IAAIiG,EAAW,KACf,IACEzF,EAAWA,EAAS0F,QAAQN,EAAjB,IAAyCE,KAAKjG,IACzDoG,EAAWH,KAAKV,iBAAiB5E,GAFnC,QAIOuF,GACHD,KAAKK,gBAAgB,MAIzB,OAAOF,GAGTZ,EAAU,SAAU7E,GAClB,IAAKoF,EAAmBrC,KAAK/C,GAC3B,OAAOsF,KAAKjF,cAAcL,GAG5B,IAAM4F,EAAUnB,EAAK9B,KAAK2C,KAAMtF,GAEhC,YAA0B,IAAf4F,EAAQ,GACVA,EAAQ,GAGV,OC7DX,IAAMC,EAAIjC,IACJkC,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GAClBC,EAAW,EACTC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAe,CACnB,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,UASF,SAASC,EAAYhH,EAASiH,GAC5B,OAAQA,GAAUA,EAAP,KAAeN,KAAiB3G,EAAQ2G,UAAYA,IAGjE,SAASO,EAASlH,GAChB,IAAMiH,EAAMD,EAAYhH,GAKxB,OAHAA,EAAQ2G,SAAWM,EACnBP,EAAcO,GAAOP,EAAcO,IAAQ,GAEpCP,EAAcO,GAkCvB,SAASE,EAAYC,EAAQC,EAASC,QAA2B,IAA3BA,IAAAA,EAAqB,MAGzD,IAFA,IAAMC,EAAe3E,OAAOC,KAAKuE,GAExBI,EAAI,EAAGC,EAAMF,EAAaG,OAAQF,EAAIC,EAAKD,IAAK,CACvD,IAAMG,EAAQP,EAAOG,EAAaC,IAElC,GAAIG,EAAMC,kBAAoBP,GAAWM,EAAML,qBAAuBA,EACpE,OAAOK,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBT,EAASU,GACnD,IAAMC,EAAgC,iBAAZX,EACpBO,EAAkBI,EAAaD,EAAeV,EAGhDY,EAAYH,EAAkB3B,QAAQK,EAAgB,IACpD0B,EAAStB,EAAaqB,GAY5B,OAVIC,IACFD,EAAYC,GAGGnB,EAAaoB,QAAQF,IAAc,IAGlDA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,EAAWpI,EAAS8H,EAAmBT,EAASU,EAAcM,GACrE,GAAiC,iBAAtBP,GAAmC9H,EAA9C,CAIKqH,IACHA,EAAUU,EACVA,EAAe,MAP4D,IAAAO,EAU5BT,EAAgBC,EAAmBT,EAASU,GAAtFC,EAVsEM,EAAA,GAU1DV,EAV0DU,EAAA,GAUzCL,EAVyCK,EAAA,GAWvElB,EAASF,EAASlH,GAClBuI,EAAWnB,EAAOa,KAAeb,EAAOa,GAAa,IACrDO,EAAarB,EAAYoB,EAAUX,EAAiBI,EAAaX,EAAU,MAEjF,GAAImB,EACFA,EAAWH,OAASG,EAAWH,QAAUA,MAD3C,CAMA,IAAMpB,EAAMD,EAAYY,EAAiBE,EAAkB3B,QAAQI,EAAgB,KAC7EkC,EAAKT,EA9Eb,SAAoChI,EAASS,EAAUgI,GACrD,OAAO,SAASpB,EAAQM,GAGtB,IAFA,IAAMe,EAAc1I,EAAQqF,iBAAiB5E,GAElCkI,EAAWhB,EAAXgB,OAAkBA,GAAUA,IAAW5C,KAAM4C,EAASA,EAAO9E,WACtE,IAAK,IAAI2D,EAAIkB,EAAYhB,OAAQF,KAC/B,GAAIkB,EAAYlB,KAAOmB,EAKrB,OAJItB,EAAQgB,QACVO,EAAaC,IAAI7I,EAAS2H,EAAMmB,KAAML,GAGjCA,EAAGM,MAAMJ,EAAQ,CAAChB,IAM/B,OAAO,MA8DPqB,CAA2BhJ,EAASqH,EAASU,GAzFjD,SAA0B/H,EAASyI,GACjC,OAAO,SAASpB,EAAQM,GAKtB,OAJIN,EAAQgB,QACVO,EAAaC,IAAI7I,EAAS2H,EAAMmB,KAAML,GAGjCA,EAAGM,MAAM/I,EAAS,CAAC2H,KAoF1BsB,CAAiBjJ,EAASqH,GAE5BoB,EAAGnB,mBAAqBU,EAAaX,EAAU,KAC/CoB,EAAGb,gBAAkBA,EACrBa,EAAGJ,OAASA,EACZI,EAAG9B,SAAWM,EACdsB,EAAStB,GAAOwB,EAEhBzI,EAAQoC,iBAAiB6F,EAAWQ,EAAIT,KAG1C,SAASkB,EAAclJ,EAASoH,EAAQa,EAAWZ,EAASC,GAC1D,IAAMmB,EAAKtB,EAAYC,EAAOa,GAAYZ,EAASC,GAE9CmB,IAILzI,EAAQsC,oBAAoB2F,EAAWQ,EAAIxC,QAAQqB,WAC5CF,EAAOa,GAAWQ,EAAG9B,WAgB9B,IAAMiC,EAAe,CACnBO,GADmB,SAChBnJ,EAAS2H,EAAON,EAASU,GAC1BK,EAAWpI,EAAS2H,EAAON,EAASU,GAAc,IAGpDqB,IALmB,SAKfpJ,EAAS2H,EAAON,EAASU,GAC3BK,EAAWpI,EAAS2H,EAAON,EAASU,GAAc,IAGpDc,IATmB,SASf7I,EAAS8H,EAAmBT,EAASU,GACvC,GAAiC,iBAAtBD,GAAmC9H,EAA9C,CADqD,IAAAqJ,EAKJxB,EAAgBC,EAAmBT,EAASU,GAAtFC,EAL8CqB,EAAA,GAKlCzB,EALkCyB,EAAA,GAKjBpB,EALiBoB,EAAA,GAM/CC,EAAcrB,IAAcH,EAC5BV,EAASF,EAASlH,GAClBuJ,EAA8C,MAAhCzB,EAAkB0B,OAAO,GAE7C,QAA+B,IAApB5B,EAAX,CAUI2B,GACF3G,OAAOC,KAAKuE,GACTtE,SAAQ,SAAA2G,IA5CjB,SAAkCzJ,EAASoH,EAAQa,EAAWyB,GAC5D,IAAMC,EAAoBvC,EAAOa,IAAc,GAE/CrF,OAAOC,KAAK8G,GACT7G,SAAQ,SAAA8G,GACP,GAAIA,EAAWzB,QAAQuB,IAAc,EAAG,CACtC,IAAM/B,EAAQgC,EAAkBC,GAEhCV,EAAclJ,EAASoH,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,wBAqCrEuC,CAAyB7J,EAASoH,EAAQqC,EAAc3B,EAAkBgC,MAAM,OAItF,IAAMH,EAAoBvC,EAAOa,IAAc,GAC/CrF,OAAOC,KAAK8G,GACT7G,SAAQ,SAAAiH,GACP,IAAMH,EAAaG,EAAY5D,QAAQM,EAAe,IAEtD,IAAK6C,GAAexB,EAAkBK,QAAQyB,IAAe,EAAG,CAC9D,IAAMjC,EAAQgC,EAAkBI,GAEhCb,EAAclJ,EAASoH,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,4BAzB7E,CAEE,IAAKF,IAAWA,EAAOa,GACrB,OAGFiB,EAAclJ,EAASoH,EAAQa,EAAWL,EAAiBI,EAAaX,EAAU,SAwBtF2C,QAjDmB,SAiDXhK,EAAS2H,EAAOsC,GACtB,GAAqB,iBAAVtC,IAAuB3H,EAChC,OAAO,KAGT,IAIIkK,EAJEjC,EAAYN,EAAMxB,QAAQK,EAAgB,IAC1C8C,EAAc3B,IAAUM,EACxBkC,EAAWpD,EAAaoB,QAAQF,IAAc,EAGhDmC,GAAU,EACVC,GAAiB,EACjBzE,GAAmB,EACnB0E,EAAM,KAmDV,OAjDIhB,GAAehD,IACjB4D,EAAc5D,EAAE1E,MAAM+F,EAAOsC,GAE7B3D,EAAEtG,GAASgK,QAAQE,GACnBE,GAAWF,EAAYK,uBACvBF,GAAkBH,EAAYM,gCAC9B5E,EAAmBsE,EAAYO,sBAG7BN,GACFG,EAAMhK,SAASoK,YAAY,eACvBC,UAAU1C,EAAWmC,GAAS,GAElCE,EAAM,IAAI9E,YAAYmC,EAAO,CAC3ByC,QAAAA,EACA3E,YAAY,SAKI,IAATwE,GACTrH,OAAOC,KAAKoH,GACTnH,SAAQ,SAAA6B,GACP/B,OAAOgI,eAAeN,EAAK3F,EAAK,CAC9BE,IAD8B,WAE5B,OAAOoF,EAAKtF,SAMlBiB,IACF0E,EAAI3E,iBAECJ,GACH3C,OAAOgI,eAAeN,EAAK,mBAAoB,CAC7CzF,IAAK,WAAA,OAAM,MAKbwF,GACFrK,EAAQ2B,cAAc2I,GAGpBA,EAAI1E,uBAA2C,IAAhBsE,GACjCA,EAAYvE,iBAGP2E,ICrTLO,EAAO,QAsBPC,EAAAA,WACJ,SAAAA,EAAY9K,GACV+F,KAAKgF,SAAW/K,EAEZ+F,KAAKgF,UACP/F,EAAahF,EAzBF,WAyBqB+F,iCAYpCiF,MAAA,SAAMhL,GACJ,IAAIiL,EAAclF,KAAKgF,SACnB/K,IACFiL,EAAclF,KAAKmF,gBAAgBlL,IAGrC,IAAMmL,EAAcpF,KAAKqF,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAYvF,kBAIxCG,KAAKsF,eAAeJ,MAGtBK,QAAA,WACEtG,EAAgBe,KAAKgF,SArDR,YAsDbhF,KAAKgF,SAAW,QAKlBG,gBAAA,SAAgBlL,GACd,OAAOe,EAAuBf,IAAYA,EAAQuL,QAAR,aAG5CH,mBAAA,SAAmBpL,GACjB,OAAO4I,EAAaoB,QAAQhK,EA1Df,qBA6DfqL,eAAA,SAAerL,GAAS,IAAAwL,EAAAzF,KAGtB,GAFA/F,EAAQyL,UAAUC,OAxDC,QA0Dd1L,EAAQyL,UAAUE,SA3DJ,QA2DnB,CAKA,IAAMvK,EAAqBJ,EAAiChB,GAE5D4I,EACGQ,IAAIpJ,EJ9FY,iBI8Fa,WAAA,OAAMwL,EAAKI,gBAAgB5L,MAC3DgC,EAAqBhC,EAASoB,QAR5B2E,KAAK6F,gBAAgB5L,MAWzB4L,gBAAA,SAAgB5L,GACVA,EAAQ6D,YACV7D,EAAQ6D,WAAWgI,YAAY7L,GAGjC4I,EAAaoB,QAAQhK,EAhFP,sBAqFT8L,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KA9Fb,YAgGNnB,IACHA,EAAO,IAAIkG,EAAM/E,OAGJ,UAAXrD,GACFkC,EAAKlC,GAAQqD,YAKZiG,cAAP,SAAqBC,GACnB,OAAO,SAAUtE,GACXA,GACFA,EAAMhC,iBAGRsG,EAAcjB,MAAMjF,UAIjBmG,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EArHP,qDAgCb,MAjCY,qBAqBV8K,GA0GNlC,EACGO,GAAG7I,SAvHoB,0BAJD,yBA2H+BwK,EAAMkB,cAAc,IAAIlB,IAEhF,IAAMxE,EAAIjC,IAUV,GAAIiC,EAAG,CACL,IAAM6F,EAAqB7F,EAAEmC,GAAGoC,GAChCvE,EAAEmC,GAAGoC,GAAQC,EAAMgB,gBACnBxF,EAAEmC,GAAGoC,GAAMuB,YAActB,EACzBxE,EAAEmC,GAAGoC,GAAMwB,WAAa,WAEtB,OADA/F,EAAEmC,GAAGoC,GAAQsB,EACNrB,EAAMgB,iBCzJjB,IAkBMQ,EAAAA,WACJ,SAAAA,EAAYtM,GACV+F,KAAKgF,SAAW/K,EAChBgF,EAAahF,EAnBA,YAmBmB+F,iCAWlCwG,OAAA,WAEExG,KAAKgF,SAASyB,aAAa,eAAgBzG,KAAKgF,SAASU,UAAUc,OA5B7C,cA+BxBjB,QAAA,WACEtG,EAAgBe,KAAKgF,SApCR,aAqCbhF,KAAKgF,SAAW,QAKXe,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KA5Cb,aA8CNnB,IACHA,EAAO,IAAI0H,EAAOvG,OAGL,WAAXrD,GACFkC,EAAKlC,WAKJwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EAzDP,sDAyBb,MA1BY,qBAiBVsM,GAmDN1D,EAAaO,GAAG7I,SA3DU,2BAFG,0BA6DyC,SAAAqH,GACpEA,EAAMhC,iBAEN,IAAM8G,EAAS9E,EAAMgB,OAAO4C,QAhED,0BAkEvB3G,EAAOI,EAAayH,EAxET,aAyEV7H,IACHA,EAAO,IAAI0H,EAAOG,IAGpB7H,EAAK2H,YAGP,IAAMjG,EAAIjC,IASV,GAAIiC,EAAG,CACL,IAAM6F,EAAqB7F,EAAEmC,GAAF,OAC3BnC,EAAEmC,GAAF,OAAa6D,EAAOR,gBACpBxF,EAAEmC,GAAF,OAAW2D,YAAcE,EAEzBhG,EAAEmC,GAAF,OAAW4D,WAAa,WAEtB,OADA/F,EAAEmC,GAAF,OAAa0D,EACNG,EAAOR,iBC5GlB,SAASY,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQC,OAAOD,GAAKxJ,WACfyJ,OAAOD,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASE,EAAiBlI,GACxB,OAAOA,EAAIwB,QAAQ,UAAU,SAAA2G,GAAG,MAAA,IAAQA,EAAIxJ,iBAG9C,IAAMyJ,GAAc,CAClBC,iBADkB,SACDhN,EAAS2E,EAAK1B,GAC7BjD,EAAQwM,aAAR,QAA6BK,EAAiBlI,GAAQ1B,IAGxDgK,oBALkB,SAKEjN,EAAS2E,GAC3B3E,EAAQoG,gBAAR,QAAgCyG,EAAiBlI,KAGnDuI,kBATkB,SASAlN,GAChB,IAAKA,EACH,MAAO,GAGT,IAAMmN,EAAUC,EAAA,GACXpN,EAAQqN,SAOb,OAJAzK,OAAOC,KAAKsK,GAAYrK,SAAQ,SAAA6B,GAC9BwI,EAAWxI,GAAO+H,EAAcS,EAAWxI,OAGtCwI,GAGTG,iBAzBkB,SAyBDtN,EAAS2E,GACxB,OAAO+H,EAAc1M,EAAQU,aAAR,QAA6BmM,EAAiBlI,MAGrE4I,OA7BkB,SA6BXvN,GACL,IAAMwN,EAAOxN,EAAQyN,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAMpN,SAASiE,KAAKoJ,UAC9BC,KAAMJ,EAAKI,KAAOtN,SAASiE,KAAKsJ,aAIpCC,SAtCkB,SAsCT9N,GACP,MAAO,CACL0N,IAAK1N,EAAQ+N,UACbH,KAAM5N,EAAQgO,aAIlBC,YA7CkB,SA6CNjO,EAASkO,GACdlO,IAIDA,EAAQyL,UAAUE,SAASuC,GAC7BlO,EAAQyL,UAAUC,OAAOwC,GAEzBlO,EAAQyL,UAAU0C,IAAID,MCnEtBE,GAAiB,CACrB/H,QADqB,SACbrG,EAASS,GACf,OAAOT,EAAQqG,QAAQ5F,IAGzByE,KALqB,SAKhBzE,EAAUT,GAAoC,IAAAqO,EACjD,YADiD,IAApCrO,IAAAA,EAAUM,SAASgO,kBACzBD,EAAA,IAAGE,OAAHxF,MAAAsF,EAAaG,EAAOpL,KAAKpD,EAASS,KAG3C6E,QATqB,SASb7E,EAAUT,GAChB,YADoD,IAApCA,IAAAA,EAAUM,SAASgO,iBAC5BhJ,EAAQlC,KAAKpD,EAASS,IAG/BgO,SAbqB,SAaZzO,EAASS,GAAU,IAAAiO,EACpBD,GAAWC,EAAA,IAAGH,OAAHxF,MAAA2F,EAAa1O,EAAQyO,UAEtC,OAAOA,EAASE,QAAO,SAAAC,GAAK,OAAIA,EAAMvI,QAAQ5F,OAGhDoO,QAnBqB,SAmBb7O,EAASS,GAKf,IAJA,IAAMoO,EAAU,GAEZC,EAAW9O,EAAQ6D,WAEhBiL,GAAYA,EAAS/M,WAAagN,KAAKC,cA1BhC,IA0BgDF,EAAS/M,UACjEgE,KAAKM,QAAQyI,EAAUrO,IACzBoO,EAAQI,KAAKH,GAGfA,EAAWA,EAASjL,WAGtB,OAAOgL,GAGTK,KAnCqB,SAmChBlP,EAASS,GAGZ,IAFA,IAAI0O,EAAWnP,EAAQoP,uBAEhBD,GAAU,CACf,GAAIA,EAAS9I,QAAQ5F,GACnB,MAAO,CAAC0O,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAjDqB,SAiDhBrP,EAASS,GAGZ,IAFA,IAAI4O,EAAOrP,EAAQsP,mBAEZD,GAAM,CACX,GAAItJ,KAAKM,QAAQgJ,EAAM5O,GACrB,MAAO,CAAC4O,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KChDLzE,GAAO,WAGP0E,GAAS,eAQTC,GAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,GAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAwCHE,GAAc,CAClBC,MAAO,QACPC,IAAK,OAQDC,GAAAA,WACJ,SAAAA,EAAYnQ,EAAS0C,GACnBqD,KAAKqK,OAAS,KACdrK,KAAKsK,UAAY,KACjBtK,KAAKuK,eAAiB,KACtBvK,KAAKwK,WAAY,EACjBxK,KAAKyK,YAAa,EAClBzK,KAAK0K,aAAe,KACpB1K,KAAK2K,YAAc,EACnB3K,KAAK4K,YAAc,EAEnB5K,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAKgF,SAAW/K,EAChB+F,KAAK+K,mBAAqB1C,GAAe9I,QA3BjB,uBA2B8CS,KAAKgF,UAC3EhF,KAAKgL,gBAAkB,iBAAkBzQ,SAASgO,iBAAmB0C,UAAUC,eAAiB,EAChGlL,KAAKmL,cAAgBjL,QAAQ/E,OAAOiQ,cAEpCpL,KAAKqL,qBACLpM,EAAahF,EA5FA,cA4FmB+F,iCAelCsJ,KAAA,WACOtJ,KAAKyK,YACRzK,KAAKsL,OAlFY,WAsFrBC,gBAAA,YAGOhR,SAASiR,QAAU5N,EAAUoC,KAAKgF,WACrChF,KAAKsJ,UAITH,KAAA,WACOnJ,KAAKyK,YACRzK,KAAKsL,OA/FY,WAmGrBzB,MAAA,SAAMjI,GACCA,IACH5B,KAAKwK,WAAY,GAGfnC,GAAe9I,QAzEI,2CAyEwBS,KAAKgF,YAClDrJ,EAAqBqE,KAAKgF,UAC1BhF,KAAKyL,OAAM,IAGbC,cAAc1L,KAAKsK,WACnBtK,KAAKsK,UAAY,QAGnBmB,MAAA,SAAM7J,GACCA,IACH5B,KAAKwK,WAAY,GAGfxK,KAAKsK,YACPoB,cAAc1L,KAAKsK,WACnBtK,KAAKsK,UAAY,MAGftK,KAAK6K,SAAW7K,KAAK6K,QAAQnB,WAAa1J,KAAKwK,YACjDxK,KAAKsK,UAAYqB,aACdpR,SAASqR,gBAAkB5L,KAAKuL,gBAAkBvL,KAAKsJ,MAAMuC,KAAK7L,MACnEA,KAAK6K,QAAQnB,cAKnBoC,GAAA,SAAGC,GAAO,IAAAtG,EAAAzF,KACRA,KAAKuK,eAAiBlC,GAAe9I,QAxGZ,wBAwG0CS,KAAKgF,UACxE,IAAMgH,EAAchM,KAAKiM,cAAcjM,KAAKuK,gBAE5C,KAAIwB,EAAQ/L,KAAKqK,OAAO1I,OAAS,GAAKoK,EAAQ,GAI9C,GAAI/L,KAAKyK,WACP5H,EAAaQ,IAAIrD,KAAKgF,SAvIZ,oBAuIkC,WAAA,OAAMS,EAAKqG,GAAGC,UAD5D,CAKA,GAAIC,IAAgBD,EAGlB,OAFA/L,KAAK6J,aACL7J,KAAKyL,QAIP,IAAMS,EAAYH,EAAQC,EAvJP,OACA,OA0JnBhM,KAAKsL,OAAOY,EAAWlM,KAAKqK,OAAO0B,QAGrCxG,QAAA,WACE1C,EAAaC,IAAI9C,KAAKgF,SAAUwE,IAChCvK,EAAgBe,KAAKgF,SA3LR,eA6LbhF,KAAKqK,OAAS,KACdrK,KAAK6K,QAAU,KACf7K,KAAKgF,SAAW,KAChBhF,KAAKsK,UAAY,KACjBtK,KAAKwK,UAAY,KACjBxK,KAAKyK,WAAa,KAClBzK,KAAKuK,eAAiB,KACtBvK,KAAK+K,mBAAqB,QAK5BD,WAAA,SAAWnO,GAMT,OALAA,EAAM0K,EAAAA,EAAA,GACDoC,IACA9M,GAELF,EAAgBqI,GAAMnI,EAAQqN,IACvBrN,KAGTwP,aAAA,WACE,IAAMC,EAAYhS,KAAKiS,IAAIrM,KAAK4K,aAEhC,KAAIwB,GA9MgB,IA8MpB,CAIA,IAAMF,EAAYE,EAAYpM,KAAK4K,YAEnC5K,KAAK4K,YAAc,EAGfsB,EAAY,GACdlM,KAAKmJ,OAIH+C,EAAY,GACdlM,KAAKsJ,WAIT+B,mBAAA,WAAqB,IAAAiB,EAAAtM,KACfA,KAAK6K,QAAQlB,UACf9G,EACGO,GAAGpD,KAAKgF,SAzME,uBAyMuB,SAAApD,GAAK,OAAI0K,EAAKC,SAAS3K,MAGlC,UAAvB5B,KAAK6K,QAAQhB,QACfhH,EACGO,GAAGpD,KAAKgF,SA7MK,0BA6MuB,SAAApD,GAAK,OAAI0K,EAAKzC,MAAMjI,MAC3DiB,EACGO,GAAGpD,KAAKgF,SA9MK,0BA8MuB,SAAApD,GAAK,OAAI0K,EAAKb,MAAM7J,OAGzD5B,KAAK6K,QAAQd,OAAS/J,KAAKgL,iBAC7BhL,KAAKwM,6BAITA,wBAAA,WAA0B,IAAAC,EAAAzM,KAClB0M,EAAQ,SAAA9K,GACR6K,EAAKtB,eAAiBlB,GAAYrI,EAAM+K,YAAYhP,eACtD8O,EAAK9B,YAAc/I,EAAMgL,QACfH,EAAKtB,gBACfsB,EAAK9B,YAAc/I,EAAMiL,QAAQ,GAAGD,UAalCE,EAAM,SAAAlL,GACN6K,EAAKtB,eAAiBlB,GAAYrI,EAAM+K,YAAYhP,iBACtD8O,EAAK7B,YAAchJ,EAAMgL,QAAUH,EAAK9B,aAG1C8B,EAAKN,eACsB,UAAvBM,EAAK5B,QAAQhB,QASf4C,EAAK5C,QACD4C,EAAK/B,cACPqC,aAAaN,EAAK/B,cAGpB+B,EAAK/B,aAAelO,YAAW,SAAAoF,GAAK,OAAI6K,EAAKhB,MAAM7J,KA1R5B,IA0R6D6K,EAAK5B,QAAQnB,YAIrGrB,GAAelJ,KA1OO,qBA0OiBa,KAAKgF,UAAUjI,SAAQ,SAAAiQ,GAC5DnK,EAAaO,GAAG4J,EA3PA,yBA2P2B,SAAAhT,GAAC,OAAIA,EAAE4F,uBAGhDI,KAAKmL,eACPtI,EAAaO,GAAGpD,KAAKgF,SAjQJ,2BAiQiC,SAAApD,GAAK,OAAI8K,EAAM9K,MACjEiB,EAAaO,GAAGpD,KAAKgF,SAjQN,yBAiQiC,SAAApD,GAAK,OAAIkL,EAAIlL,MAE7D5B,KAAKgF,SAASU,UAAU0C,IAvPG,mBAyP3BvF,EAAaO,GAAGpD,KAAKgF,SAzQL,0BAyQiC,SAAApD,GAAK,OAAI8K,EAAM9K,MAChEiB,EAAaO,GAAGpD,KAAKgF,SAzQN,yBAyQiC,SAAApD,GAAK,OA5C1C,SAAAA,GAEPA,EAAMiL,SAAWjL,EAAMiL,QAAQlL,OAAS,EAC1C8K,EAAK7B,YAAc,EAEnB6B,EAAK7B,YAAchJ,EAAMiL,QAAQ,GAAGD,QAAUH,EAAK9B,YAuCIsC,CAAKrL,MAC9DiB,EAAaO,GAAGpD,KAAKgF,SAzQP,wBAyQiC,SAAApD,GAAK,OAAIkL,EAAIlL,UAIhE2K,SAAA,SAAS3K,GACP,IAAI,kBAAkBnE,KAAKmE,EAAMgB,OAAOsK,SAIxC,OAAQtL,EAAMhD,KACZ,IAtTiB,YAuTfgD,EAAMhC,iBACNI,KAAKmJ,OACL,MACF,IAzTkB,aA0ThBvH,EAAMhC,iBACNI,KAAKsJ,WAMX2C,cAAA,SAAchS,GAKZ,OAJA+F,KAAKqK,OAASpQ,GAAWA,EAAQ6D,WAC/BuK,GAAelJ,KA/QC,iBA+QmBlF,EAAQ6D,YAC3C,GAEKkC,KAAKqK,OAAOjI,QAAQnI,MAG7BkT,oBAAA,SAAoBjB,EAAWkB,GAC7B,IAAMC,EApTa,SAoTKnB,EAClBoB,EApTa,SAoTKpB,EAClBF,EAAchM,KAAKiM,cAAcmB,GACjCG,EAAgBvN,KAAKqK,OAAO1I,OAAS,EAI3C,IAHuB2L,GAAmC,IAAhBtB,GACjBqB,GAAmBrB,IAAgBuB,KAEtCvN,KAAK6K,QAAQf,KACjC,OAAOsD,EAGT,IACMI,GAAaxB,GA/TA,SA8TLE,GAAgC,EAAI,IACRlM,KAAKqK,OAAO1I,OAEtD,OAAsB,IAAf6L,EACLxN,KAAKqK,OAAOrK,KAAKqK,OAAO1I,OAAS,GACjC3B,KAAKqK,OAAOmD,MAGhBC,mBAAA,SAAmBC,EAAeC,GAChC,IAAMC,EAAc5N,KAAKiM,cAAcyB,GACjCG,EAAY7N,KAAKiM,cAAc5D,GAAe9I,QA5S3B,wBA4SyDS,KAAKgF,WAEvF,OAAOnC,EAAaoB,QAAQjE,KAAKgF,SAtUpB,oBAsU2C,CACtD0I,cAAAA,EACAxB,UAAWyB,EACXG,KAAMD,EACN/B,GAAI8B,OAIRG,2BAAA,SAA2B9T,GACzB,GAAI+F,KAAK+K,mBAAoB,CAE3B,IADA,IAAMiD,EAAa3F,GAAelJ,KAzThB,UAyTsCa,KAAK+K,oBACpDtJ,EAAI,EAAGA,EAAIuM,EAAWrM,OAAQF,IACrCuM,EAAWvM,GAAGiE,UAAUC,OAnUN,UAsUpB,IAAMsI,EAAgBjO,KAAK+K,mBAAmBrC,SAC5C1I,KAAKiM,cAAchS,IAGjBgU,GACFA,EAAcvI,UAAU0C,IA3UN,cAgVxBkD,OAAA,SAAOY,EAAWjS,GAAS,IASrBiU,EACAC,EACAR,EAXqBS,EAAApO,KACnBoN,EAAgB/E,GAAe9I,QAxUZ,wBAwU0CS,KAAKgF,UAClEqJ,EAAqBrO,KAAKiM,cAAcmB,GACxCkB,EAAcrU,GAAYmT,GAC9BpN,KAAKmN,oBAAoBjB,EAAWkB,GAEhCmB,EAAmBvO,KAAKiM,cAAcqC,GACtCE,EAAYtO,QAAQF,KAAKsK,WAgB/B,GA3XmB,SAiXf4B,GACFgC,EA3VkB,qBA4VlBC,EA3VkB,qBA4VlBR,EAlXiB,SAoXjBO,EAhWmB,sBAiWnBC,EA9VkB,qBA+VlBR,EArXkB,SAwXhBW,GAAeA,EAAY5I,UAAUE,SAvWnB,UAwWpB5F,KAAKyK,YAAa,OAKpB,IADmBzK,KAAKyN,mBAAmBa,EAAaX,GACzC9N,kBAIVuN,GAAkBkB,EAAvB,CAaA,GARAtO,KAAKyK,YAAa,EAEd+D,GACFxO,KAAK6J,QAGP7J,KAAK+N,2BAA2BO,GAE5BtO,KAAKgF,SAASU,UAAUE,SA7XP,SA6XmC,CACtD0I,EAAY5I,UAAU0C,IAAI+F,GAE1B/P,EAAOkQ,GAEPlB,EAAc1H,UAAU0C,IAAI8F,GAC5BI,EAAY5I,UAAU0C,IAAI8F,GAE1B,IAAMO,EAAsBC,SAASJ,EAAY3T,aAAa,iBAAkB,IAC5E8T,GACFzO,KAAK6K,QAAQ8D,gBAAkB3O,KAAK6K,QAAQ8D,iBAAmB3O,KAAK6K,QAAQnB,SAC5E1J,KAAK6K,QAAQnB,SAAW+E,GAExBzO,KAAK6K,QAAQnB,SAAW1J,KAAK6K,QAAQ8D,iBAAmB3O,KAAK6K,QAAQnB,SAGvE,IAAMrO,EAAqBJ,EAAiCmS,GAE5DvK,EACGQ,IAAI+J,ERtdU,iBQsdqB,WAClCkB,EAAY5I,UAAUC,OAAOuI,EAAsBC,GACnDG,EAAY5I,UAAU0C,IAnZN,UAqZhBgF,EAAc1H,UAAUC,OArZR,SAqZkCwI,EAAgBD,GAElEE,EAAK3D,YAAa,EAElBjO,YAAW,WACTqG,EAAaoB,QAAQmK,EAAKpJ,SAxatB,mBAwa4C,CAC9C0I,cAAeY,EACfpC,UAAWyB,EACXG,KAAMO,EACNvC,GAAIyC,MAEL,MAGPtS,EAAqBmR,EAAe/R,QAEpC+R,EAAc1H,UAAUC,OAraJ,UAsapB2I,EAAY5I,UAAU0C,IAtaF,UAwapBpI,KAAKyK,YAAa,EAClB5H,EAAaoB,QAAQjE,KAAKgF,SAvbhB,mBAubsC,CAC9C0I,cAAeY,EACfpC,UAAWyB,EACXG,KAAMO,EACNvC,GAAIyC,IAIJC,GACFxO,KAAKyL,YAMFmD,kBAAP,SAAyB3U,EAAS0C,GAChC,IAAIkC,EAAOI,EAAahF,EAxeX,eAyeT4Q,EAAOxD,EAAAA,EAAA,GACNoC,IACAzC,GAAYG,kBAAkBlN,IAGb,iBAAX0C,IACTkO,EAAOxD,EAAAA,EAAA,GACFwD,GACAlO,IAIP,IAAMkS,EAA2B,iBAAXlS,EAAsBA,EAASkO,EAAQjB,MAM7D,GAJK/K,IACHA,EAAO,IAAIuL,EAASnQ,EAAS4Q,IAGT,iBAAXlO,EACTkC,EAAKiN,GAAGnP,QACH,GAAsB,iBAAXkS,EAAqB,CACrC,QAA4B,IAAjBhQ,EAAKgQ,GACd,MAAM,IAAIC,UAAJ,oBAAkCD,EAAlC,KAGRhQ,EAAKgQ,UACIhE,EAAQnB,UAAYmB,EAAQkE,OACrClQ,EAAKgL,QACLhL,EAAK4M,YAIF1F,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACfoE,EAASwE,kBAAkB5O,KAAMrD,SAI9BqS,oBAAP,SAA2BpN,GACzB,IAAMgB,EAAS5H,EAAuBgF,MAEtC,GAAK4C,GAAWA,EAAO8C,UAAUE,SApeT,YAoexB,CAIA,IAAMjJ,EAAM0K,EAAAA,EAAA,GACPL,GAAYG,kBAAkBvE,IAC9BoE,GAAYG,kBAAkBnH,OAE7BiP,EAAajP,KAAKrF,aAAa,iBAEjCsU,IACFtS,EAAO+M,UAAW,GAGpBU,EAASwE,kBAAkBhM,EAAQjG,GAE/BsS,GACFhQ,EAAa2D,EAniBF,eAmiBoBkJ,GAAGmD,GAGpCrN,EAAMhC,qBAGDuG,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EA1iBP,wDAkGb,MAnGY,+CAuGZ,OAAOwP,SA5BLW,GA0eNvH,EACGO,GAAG7I,SAzgBoB,6BAiBE,gCAwf+B6P,GAAS4E,qBAEpEnM,EAAaO,GAAGjI,OA5gBS,6BA4gBoB,WAG3C,IAFA,IAAM+T,EAAY7G,GAAelJ,KA1fR,0BA4fhBsC,EAAI,EAAGC,EAAMwN,EAAUvN,OAAQF,EAAIC,EAAKD,IAC/C2I,GAASwE,kBAAkBM,EAAUzN,GAAIxC,EAAaiQ,EAAUzN,GA3jBnD,mBA+jBjB,IAAMlB,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQsF,GAASrE,gBACtBxF,GAAEmC,GAAGoC,IAAMuB,YAAc+D,GACzB7J,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACNgE,GAASrE,iBChlBpB,IAAMjB,GAAO,WAMP2E,GAAU,CACdjD,QAAQ,EACR2I,OAAQ,IAGJnF,GAAc,CAClBxD,OAAQ,UACR2I,OAAQ,oBA0BJC,GAAAA,WACJ,SAAAA,EAAYnV,EAAS0C,GACnBqD,KAAKqP,kBAAmB,EACxBrP,KAAKgF,SAAW/K,EAChB+F,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAKsP,cAAgBjH,GAAelJ,KAC/BoQ,mCAA+BtV,EAAQF,GAAvCwV,6CACsCtV,EAAQF,GADjD,MAMF,IAFA,IAAMyV,EAAanH,GAAelJ,KAlBT,4BAoBhBsC,EAAI,EAAGC,EAAM8N,EAAW7N,OAAQF,EAAIC,EAAKD,IAAK,CACrD,IAAMgO,EAAOD,EAAW/N,GAClB/G,EAAWI,EAAuB2U,GAClCC,EAAgBrH,GAAelJ,KAAKzE,GACvCkO,QAAO,SAAA+G,GAAS,OAAIA,IAAc1V,KAEpB,OAAbS,GAAqBgV,EAAc/N,SACrC3B,KAAK4P,UAAYlV,EACjBsF,KAAKsP,cAAcpG,KAAKuG,IAI5BzP,KAAK6P,QAAU7P,KAAK6K,QAAQsE,OAASnP,KAAK8P,aAAe,KAEpD9P,KAAK6K,QAAQsE,QAChBnP,KAAK+P,0BAA0B/P,KAAKgF,SAAUhF,KAAKsP,eAGjDtP,KAAK6K,QAAQrE,QACfxG,KAAKwG,SAGPvH,EAAahF,EAvEA,cAuEmB+F,iCAelCwG,OAAA,WACMxG,KAAKgF,SAASU,UAAUE,SAnER,QAoElB5F,KAAKgQ,OAELhQ,KAAKiQ,UAITA,KAAA,WAAO,IAAAxK,EAAAzF,KACL,IAAIA,KAAKqP,mBACPrP,KAAKgF,SAASU,UAAUE,SA5EN,QA2EpB,CAKA,IAAIsK,EACAC,EAEAnQ,KAAK6P,SAUgB,KATvBK,EAAU7H,GAAelJ,KA5EN,qBA4E6Ba,KAAK6P,SAClDjH,QAAO,SAAA6G,GACN,MAAmC,iBAAxBhK,EAAKoF,QAAQsE,OACfM,EAAK9U,aAAa,iBAAmB8K,EAAKoF,QAAQsE,OAGpDM,EAAK/J,UAAUE,SAzFJ,gBA4FVjE,SACVuO,EAAU,MAId,IAAME,EAAY/H,GAAe9I,QAAQS,KAAK4P,WAC9C,GAAIM,EAAS,CACX,IAAMG,EAAiBH,EAAQtH,QAAO,SAAA6G,GAAI,OAAIW,IAAcX,KAG5D,IAFAU,EAAcE,EAAe,GAAKpR,EAAaoR,EAAe,GAzHnD,eAyHmE,OAE3DF,EAAYd,iBAC7B,OAKJ,IADmBxM,EAAaoB,QAAQjE,KAAKgF,SAlHjC,oBAmHGnF,iBAAf,CAIIqQ,GACFA,EAAQnT,SAAQ,SAAAuT,GACVF,IAAcE,GAChBlB,EAASmB,kBAAkBD,EAAY,QAGpCH,GACHlR,EAAaqR,EA5IN,cA4I4B,SAKzC,IAAME,EAAYxQ,KAAKyQ,gBAEvBzQ,KAAKgF,SAASU,UAAUC,OA9HA,YA+HxB3F,KAAKgF,SAASU,UAAU0C,IA9HE,cAgI1BpI,KAAKgF,SAASnH,MAAM2S,GAAa,EAE7BxQ,KAAKsP,cAAc3N,QACrB3B,KAAKsP,cAAcvS,SAAQ,SAAA9C,GACzBA,EAAQyL,UAAUC,OAnIG,aAoIrB1L,EAAQwM,aAAa,iBAAiB,MAI1CzG,KAAK0Q,kBAAiB,GAEtB,IAYMC,EAAU,UADaH,EAAU,GAAG7S,cAAgB6S,EAAUzM,MAAM,IAEpE1I,EAAqBJ,EAAiC+E,KAAKgF,UAEjEnC,EAAaQ,IAAIrD,KAAKgF,STtMH,iBSuLF,WACfS,EAAKT,SAASU,UAAUC,OA5IA,cA6IxBF,EAAKT,SAASU,UAAU0C,IA9IF,WADJ,QAiJlB3C,EAAKT,SAASnH,MAAM2S,GAAa,GAEjC/K,EAAKiL,kBAAiB,GAEtB7N,EAAaoB,QAAQwB,EAAKT,SA1Jf,wBAmKb/I,EAAqB+D,KAAKgF,SAAU3J,GACpC2E,KAAKgF,SAASnH,MAAM2S,GAAgBxQ,KAAKgF,SAAS2L,GAAlD,UAGFX,KAAA,WAAO,IAAA1D,EAAAtM,KACL,IAAIA,KAAKqP,kBACNrP,KAAKgF,SAASU,UAAUE,SApKP,UAwKD/C,EAAaoB,QAAQjE,KAAKgF,SA5KjC,oBA6KGnF,iBAAf,CAIA,IAAM2Q,EAAYxQ,KAAKyQ,gBAEvBzQ,KAAKgF,SAASnH,MAAM2S,GAAgBxQ,KAAKgF,SAAS0C,wBAAwB8I,GAA1E,KAEApS,EAAO4B,KAAKgF,UAEZhF,KAAKgF,SAASU,UAAU0C,IAjLE,cAkL1BpI,KAAKgF,SAASU,UAAUC,OAnLA,WADJ,QAsLpB,IAAMiL,EAAqB5Q,KAAKsP,cAAc3N,OAC9C,GAAIiP,EAAqB,EACvB,IAAK,IAAInP,EAAI,EAAGA,EAAImP,EAAoBnP,IAAK,CAC3C,IAAMwC,EAAUjE,KAAKsP,cAAc7N,GAC7BgO,EAAOzU,EAAuBiJ,GAEhCwL,IAASA,EAAK/J,UAAUE,SA5LZ,UA6Ld3B,EAAQyB,UAAU0C,IA1LC,aA2LnBnE,EAAQwC,aAAa,iBAAiB,IAK5CzG,KAAK0Q,kBAAiB,GAStB1Q,KAAKgF,SAASnH,MAAM2S,GAAa,GACjC,IAAMnV,EAAqBJ,EAAiC+E,KAAKgF,UAEjEnC,EAAaQ,IAAIrD,KAAKgF,STzPH,iBS+OF,WACfsH,EAAKoE,kBAAiB,GACtBpE,EAAKtH,SAASU,UAAUC,OArMA,cAsMxB2G,EAAKtH,SAASU,UAAU0C,IAvMF,YAwMtBvF,EAAaoB,QAAQqI,EAAKtH,SA5Md,yBAmNd/I,EAAqB+D,KAAKgF,SAAU3J,OAGtCqV,iBAAA,SAAiBG,GACf7Q,KAAKqP,iBAAmBwB,KAG1BtL,QAAA,WACEtG,EAAgBe,KAAKgF,SA5OR,eA8ObhF,KAAK6K,QAAU,KACf7K,KAAK6P,QAAU,KACf7P,KAAKgF,SAAW,KAChBhF,KAAKsP,cAAgB,KACrBtP,KAAKqP,iBAAmB,QAK1BvE,WAAA,SAAWnO,GAOT,OANAA,EAAM0K,EAAAA,EAAA,GACDoC,IACA9M,IAEE6J,OAAStG,QAAQvD,EAAO6J,QAC/B/J,EAAgBqI,GAAMnI,EAAQqN,IACvBrN,KAGT8T,cAAA,WAEE,OADiBzQ,KAAKgF,SAASU,UAAUE,SAzO/B,SAAA,QACC,YA4ObkK,WAAA,WAAa,IAAArD,EAAAzM,KACLmP,EAAWnP,KAAK6K,QAAhBsE,OAEFrT,EAAUqT,QAEiB,IAAlBA,EAAO2B,aAA+C,IAAd3B,EAAO,KACxDA,EAASA,EAAO,IAGlBA,EAAS9G,GAAe9I,QAAQ4P,GAGlC,IAAMzU,EAAc6U,yCAAqCJ,EAA3C,KAYd,OAVA9G,GAAelJ,KAAKzE,EAAUyU,GAC3BpS,SAAQ,SAAA9C,GACP,IAAM8W,EAAW/V,EAAuBf,GAExCwS,EAAKsD,0BACHgB,EACA,CAAC9W,OAIAkV,KAGTY,0BAAA,SAA0B9V,EAAS+W,GACjC,GAAI/W,EAAS,CACX,IAAMgX,EAAShX,EAAQyL,UAAUE,SA/Qf,QAiRdoL,EAAarP,QACfqP,EAAajU,SAAQ,SAAA0S,GACfwB,EACFxB,EAAK/J,UAAUC,OAjRE,aAmRjB8J,EAAK/J,UAAU0C,IAnRE,aAsRnBqH,EAAKhJ,aAAa,gBAAiBwK,UAQpCV,kBAAP,SAAyBtW,EAAS0C,GAChC,IAAIkC,EAAOI,EAAahF,EAtTX,eAuTP4Q,EAAOxD,EAAAA,EAAAA,EAAA,GACRoC,IACAzC,GAAYG,kBAAkBlN,IACZ,iBAAX0C,GAAuBA,EAASA,EAAS,IAWrD,IARKkC,GAAQgM,EAAQrE,QAA4B,iBAAX7J,GAAuB,YAAYc,KAAKd,KAC5EkO,EAAQrE,QAAS,GAGd3H,IACHA,EAAO,IAAIuQ,EAASnV,EAAS4Q,IAGT,iBAAXlO,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,SAIFoJ,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACfoJ,EAASmB,kBAAkBvQ,KAAMrD,SAI9BwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EArVP,wDA6Eb,MA9EY,+CAkFZ,OAAOwP,SA5CL2F,GA0TNvM,EAAaO,GAAG7I,SA7UU,6BAWG,4BAkUyC,SAAUqH,GAEjD,MAAzBA,EAAMgB,OAAOsK,SACftL,EAAMhC,iBAGR,IAAMsR,EAAclK,GAAYG,kBAAkBnH,MAC5CtF,EAAWI,EAAuBkF,MACfqI,GAAelJ,KAAKzE,GAE5BqC,SAAQ,SAAA9C,GACvB,IACI0C,EADEkC,EAAOI,EAAahF,EA1Wb,eA4WT4E,GAEmB,OAAjBA,EAAKgR,SAAkD,iBAAvBqB,EAAY/B,SAC9CtQ,EAAKgM,QAAQsE,OAAS+B,EAAY/B,OAClCtQ,EAAKgR,QAAUhR,EAAKiR,cAGtBnT,EAAS,UAETA,EAASuU,EAGX9B,GAASmB,kBAAkBtW,EAAS0C,SAIxC,IAAM4D,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQsK,GAASrJ,gBACtBxF,GAAEmC,GAAGoC,IAAMuB,YAAc+I,GACzB7O,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACNgJ,GAASrJ,iBClZpB,IAAIoL,GAA8B,oBAAXhW,QAA8C,oBAAbZ,UAAiD,oBAAd0Q,UAEvFmG,GAAkB,WAEpB,IADA,IAAIC,EAAwB,CAAC,OAAQ,UAAW,WACvC5P,EAAI,EAAGA,EAAI4P,EAAsB1P,OAAQF,GAAK,EACrD,GAAI0P,IAAalG,UAAUqG,UAAUlP,QAAQiP,EAAsB5P,KAAO,EACxE,OAAO,EAGX,OAAO,EAPa,GAqCtB,IAWI8P,GAXqBJ,IAAahW,OAAOqW,QA3B7C,SAA2B9O,GACzB,IAAIvG,GAAS,EACb,OAAO,WACDA,IAGJA,GAAS,EACThB,OAAOqW,QAAQC,UAAUC,MAAK,WAC5BvV,GAAS,EACTuG,UAKN,SAAsBA,GACpB,IAAIiP,GAAY,EAChB,OAAO,WACAA,IACHA,GAAY,EACZnV,YAAW,WACTmV,GAAY,EACZjP,MACC0O,OAyBT,SAASQ,GAAWC,GAElB,OAAOA,GAA8D,sBADvD,GACoBzU,SAASC,KAAKwU,GAUlD,SAASC,GAAyB7X,EAAS+C,GACzC,GAAyB,IAArB/C,EAAQ+B,SACV,MAAO,GAGT,IACI+V,EADS9X,EAAQ+X,cAAcC,YAClB7W,iBAAiBnB,EAAS,MAC3C,OAAO+C,EAAW+U,EAAI/U,GAAY+U,EAUpC,SAASG,GAAcjY,GACrB,MAAyB,SAArBA,EAAQkY,SACHlY,EAEFA,EAAQ6D,YAAc7D,EAAQmY,KAUvC,SAASC,GAAgBpY,GAEvB,IAAKA,EACH,OAAOM,SAASiE,KAGlB,OAAQvE,EAAQkY,UACd,IAAK,OACL,IAAK,OACH,OAAOlY,EAAQ+X,cAAcxT,KAC/B,IAAK,YACH,OAAOvE,EAAQuE,KAKnB,IAAI8T,EAAwBR,GAAyB7X,GACjDsY,EAAWD,EAAsBC,SACjCC,EAAYF,EAAsBE,UAClCC,EAAYH,EAAsBG,UAEtC,MAAI,wBAAwBhV,KAAK8U,EAAWE,EAAYD,GAC/CvY,EAGFoY,GAAgBH,GAAcjY,IAUvC,SAASyY,GAAiBC,GACxB,OAAOA,GAAaA,EAAUC,cAAgBD,EAAUC,cAAgBD,EAG1E,IAAIE,GAAS1B,OAAgBhW,OAAO2X,uBAAwBvY,SAASwY,cACjEC,GAAS7B,IAAa,UAAU1T,KAAKwN,UAAUqG,WASnD,SAAS2B,GAAKC,GACZ,OAAgB,KAAZA,EACKL,GAEO,KAAZK,EACKF,GAEFH,IAAUG,GAUnB,SAASG,GAAgBlZ,GACvB,IAAKA,EACH,OAAOM,SAASgO,gBAQlB,IALA,IAAI6K,EAAiBH,GAAK,IAAM1Y,SAASiE,KAAO,KAG5C6U,EAAepZ,EAAQoZ,cAAgB,KAEpCA,IAAiBD,GAAkBnZ,EAAQsP,oBAChD8J,GAAgBpZ,EAAUA,EAAQsP,oBAAoB8J,aAGxD,IAAIlB,EAAWkB,GAAgBA,EAAalB,SAE5C,OAAKA,GAAyB,SAAbA,GAAoC,SAAbA,GAMsB,IAA1D,CAAC,KAAM,KAAM,SAAS/P,QAAQiR,EAAalB,WAA2E,WAAvDL,GAAyBuB,EAAc,YACjGF,GAAgBE,GAGlBA,EATEpZ,EAAUA,EAAQ+X,cAAczJ,gBAAkBhO,SAASgO,gBA4BtE,SAAS+K,GAAQC,GACf,OAAwB,OAApBA,EAAKzV,WACAwV,GAAQC,EAAKzV,YAGfyV,EAWT,SAASC,GAAuBC,EAAUC,GAExC,KAAKD,GAAaA,EAASzX,UAAa0X,GAAaA,EAAS1X,UAC5D,OAAOzB,SAASgO,gBAIlB,IAAIoL,EAAQF,EAASG,wBAAwBF,GAAY1K,KAAK6K,4BAC1DnH,EAAQiH,EAAQF,EAAWC,EAC3B5G,EAAM6G,EAAQD,EAAWD,EAGzBK,EAAQvZ,SAASwZ,cACrBD,EAAME,SAAStH,EAAO,GACtBoH,EAAMG,OAAOnH,EAAK,GAClB,IAAIoH,EAA0BJ,EAAMI,wBAIpC,GAAIT,IAAaS,GAA2BR,IAAaQ,GAA2BxH,EAAM9G,SAASkH,GACjG,OApDJ,SAA2B7S,GACzB,IAAIkY,EAAWlY,EAAQkY,SAEvB,MAAiB,SAAbA,IAGgB,SAAbA,GAAuBgB,GAAgBlZ,EAAQka,qBAAuBla,GA8CvEma,CAAkBF,GACbA,EAGFf,GAAgBe,GAIzB,IAAIG,EAAef,GAAQG,GAC3B,OAAIY,EAAajC,KACRoB,GAAuBa,EAAajC,KAAMsB,GAE1CF,GAAuBC,EAAUH,GAAQI,GAAUtB,MAY9D,SAASkC,GAAUra,GACjB,IAAIsa,EAAOC,UAAU7S,OAAS,QAAsB8S,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,MAE3EE,EAAqB,QAATH,EAAiB,YAAc,aAC3CpC,EAAWlY,EAAQkY,SAEvB,GAAiB,SAAbA,GAAoC,SAAbA,EAAqB,CAC9C,IAAIwC,EAAO1a,EAAQ+X,cAAczJ,gBAC7BqM,EAAmB3a,EAAQ+X,cAAc4C,kBAAoBD,EACjE,OAAOC,EAAiBF,GAG1B,OAAOza,EAAQya,GAYjB,SAASG,GAAcpN,EAAMxN,GAC3B,IAAI6a,EAAWN,UAAU7S,OAAS,QAAsB8S,IAAjBD,UAAU,IAAmBA,UAAU,GAE1E5M,EAAY0M,GAAUra,EAAS,OAC/B6N,EAAawM,GAAUra,EAAS,QAChC8a,EAAWD,GAAY,EAAI,EAK/B,OAJArN,EAAKE,KAAOC,EAAYmN,EACxBtN,EAAKuN,QAAUpN,EAAYmN,EAC3BtN,EAAKI,MAAQC,EAAaiN,EAC1BtN,EAAKwN,OAASnN,EAAaiN,EACpBtN,EAaT,SAASyN,GAAeC,EAAQC,GAC9B,IAAIC,EAAiB,MAATD,EAAe,OAAS,MAChCE,EAAkB,SAAVD,EAAmB,QAAU,SAEzC,OAAO7Z,WAAW2Z,EAAO,SAAWE,EAAQ,SAAU,IAAM7Z,WAAW2Z,EAAO,SAAWG,EAAQ,SAAU,IAG7G,SAASC,GAAQH,EAAM5W,EAAMmW,EAAMa,GACjC,OAAOpb,KAAKqb,IAAIjX,EAAK,SAAW4W,GAAO5W,EAAK,SAAW4W,GAAOT,EAAK,SAAWS,GAAOT,EAAK,SAAWS,GAAOT,EAAK,SAAWS,GAAOnC,GAAK,IAAMvE,SAASiG,EAAK,SAAWS,IAAS1G,SAAS8G,EAAc,UAAqB,WAATJ,EAAoB,MAAQ,UAAY1G,SAAS8G,EAAc,UAAqB,WAATJ,EAAoB,SAAW,WAAa,GAG5U,SAASM,GAAenb,GACtB,IAAIiE,EAAOjE,EAASiE,KAChBmW,EAAOpa,EAASgO,gBAChBiN,EAAgBvC,GAAK,KAAO7X,iBAAiBuZ,GAEjD,MAAO,CACLgB,OAAQJ,GAAQ,SAAU/W,EAAMmW,EAAMa,GACtCI,MAAOL,GAAQ,QAAS/W,EAAMmW,EAAMa,IAIxC,IAAIK,GAAiB,SAAU3W,EAAUmH,GACvC,KAAMnH,aAAoBmH,GACxB,MAAM,IAAIyI,UAAU,sCAIpBgH,GAAc,WAChB,SAASC,EAAiBnT,EAAQoT,GAChC,IAAK,IAAIvU,EAAI,EAAGA,EAAIuU,EAAMrU,OAAQF,IAAK,CACrC,IAAIwU,EAAaD,EAAMvU,GACvBwU,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjDvZ,OAAOgI,eAAejC,EAAQqT,EAAWrX,IAAKqX,IAIlD,OAAO,SAAU5P,EAAagQ,EAAYC,GAGxC,OAFID,GAAYN,EAAiB1P,EAAYhH,UAAWgX,GACpDC,GAAaP,EAAiB1P,EAAaiQ,GACxCjQ,GAdO,GAsBdxB,GAAiB,SAAU9I,EAAK6C,EAAK1B,GAYvC,OAXI0B,KAAO7C,EACTc,OAAOgI,eAAe9I,EAAK6C,EAAK,CAC9B1B,MAAOA,EACPgZ,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZra,EAAI6C,GAAO1B,EAGNnB,GAGLwa,GAAW1Z,OAAO2Z,QAAU,SAAU5T,GACxC,IAAK,IAAInB,EAAI,EAAGA,EAAI+S,UAAU7S,OAAQF,IAAK,CACzC,IAAIgV,EAASjC,UAAU/S,GAEvB,IAAK,IAAI7C,KAAO6X,EACV5Z,OAAOwC,UAAUqX,eAAerZ,KAAKoZ,EAAQ7X,KAC/CgE,EAAOhE,GAAO6X,EAAO7X,IAK3B,OAAOgE,GAUT,SAAS+T,GAAcC,GACrB,OAAOL,GAAS,GAAIK,EAAS,CAC3B3B,MAAO2B,EAAQ/O,KAAO+O,EAAQhB,MAC9BZ,OAAQ4B,EAAQjP,IAAMiP,EAAQjB,SAWlC,SAASjO,GAAsBzN,GAC7B,IAAIwN,EAAO,GAKX,IACE,GAAIwL,GAAK,IAAK,CACZxL,EAAOxN,EAAQyN,wBACf,IAAIE,EAAY0M,GAAUra,EAAS,OAC/B6N,EAAawM,GAAUra,EAAS,QACpCwN,EAAKE,KAAOC,EACZH,EAAKI,MAAQC,EACbL,EAAKuN,QAAUpN,EACfH,EAAKwN,OAASnN,OAEdL,EAAOxN,EAAQyN,wBAEjB,MAAO1N,IAET,IAAI6c,EAAS,CACXhP,KAAMJ,EAAKI,KACXF,IAAKF,EAAKE,IACViO,MAAOnO,EAAKwN,MAAQxN,EAAKI,KACzB8N,OAAQlO,EAAKuN,OAASvN,EAAKE,KAIzBmP,EAA6B,SAArB7c,EAAQkY,SAAsBuD,GAAezb,EAAQ+X,eAAiB,GAC9E4D,EAAQkB,EAAMlB,OAAS3b,EAAQ8c,aAAeF,EAAOjB,MACrDD,EAASmB,EAAMnB,QAAU1b,EAAQ+c,cAAgBH,EAAOlB,OAExDsB,EAAiBhd,EAAQid,YAActB,EACvCuB,EAAgBld,EAAQoE,aAAesX,EAI3C,GAAIsB,GAAkBE,EAAe,CACnC,IAAIhC,EAASrD,GAAyB7X,GACtCgd,GAAkB/B,GAAeC,EAAQ,KACzCgC,GAAiBjC,GAAeC,EAAQ,KAExC0B,EAAOjB,OAASqB,EAChBJ,EAAOlB,QAAUwB,EAGnB,OAAOR,GAAcE,GAGvB,SAASO,GAAqC1O,EAAUyG,GACtD,IAAIkI,EAAgB7C,UAAU7S,OAAS,QAAsB8S,IAAjBD,UAAU,IAAmBA,UAAU,GAE/ExB,EAASC,GAAK,IACdqE,EAA6B,SAApBnI,EAAOgD,SAChBoF,EAAe7P,GAAsBgB,GACrC8O,EAAa9P,GAAsByH,GACnCsI,EAAepF,GAAgB3J,GAE/ByM,EAASrD,GAAyB3C,GAClCuI,EAAiBlc,WAAW2Z,EAAOuC,eAAgB,IACnDC,EAAkBnc,WAAW2Z,EAAOwC,gBAAiB,IAGrDN,GAAiBC,IACnBE,EAAW7P,IAAMvN,KAAKqb,IAAI+B,EAAW7P,IAAK,GAC1C6P,EAAW3P,KAAOzN,KAAKqb,IAAI+B,EAAW3P,KAAM,IAE9C,IAAI+O,EAAUD,GAAc,CAC1BhP,IAAK4P,EAAa5P,IAAM6P,EAAW7P,IAAM+P,EACzC7P,KAAM0P,EAAa1P,KAAO2P,EAAW3P,KAAO8P,EAC5C/B,MAAO2B,EAAa3B,MACpBD,OAAQ4B,EAAa5B,SASvB,GAPAiB,EAAQgB,UAAY,EACpBhB,EAAQiB,WAAa,GAMhB7E,GAAUsE,EAAQ,CACrB,IAAIM,EAAYpc,WAAW2Z,EAAOyC,UAAW,IACzCC,EAAarc,WAAW2Z,EAAO0C,WAAY,IAE/CjB,EAAQjP,KAAO+P,EAAiBE,EAChChB,EAAQ5B,QAAU0C,EAAiBE,EACnChB,EAAQ/O,MAAQ8P,EAAkBE,EAClCjB,EAAQ3B,OAAS0C,EAAkBE,EAGnCjB,EAAQgB,UAAYA,EACpBhB,EAAQiB,WAAaA,EAOvB,OAJI7E,IAAWqE,EAAgBlI,EAAOvJ,SAAS6R,GAAgBtI,IAAWsI,GAA0C,SAA1BA,EAAatF,YACrGyE,EAAU/B,GAAc+B,EAASzH,IAG5ByH,EAGT,SAASkB,GAA8C7d,GACrD,IAAI8d,EAAgBvD,UAAU7S,OAAS,QAAsB8S,IAAjBD,UAAU,IAAmBA,UAAU,GAE/EG,EAAO1a,EAAQ+X,cAAczJ,gBAC7ByP,EAAiBZ,GAAqCnd,EAAS0a,GAC/DiB,EAAQxb,KAAKqb,IAAId,EAAKoC,YAAa5b,OAAO8c,YAAc,GACxDtC,EAASvb,KAAKqb,IAAId,EAAKqC,aAAc7b,OAAO+c,aAAe,GAE3DtQ,EAAamQ,EAAkC,EAAlBzD,GAAUK,GACvC7M,EAAciQ,EAA0C,EAA1BzD,GAAUK,EAAM,QAE9CnN,EAAS,CACXG,IAAKC,EAAYoQ,EAAerQ,IAAMqQ,EAAeJ,UACrD/P,KAAMC,EAAakQ,EAAenQ,KAAOmQ,EAAeH,WACxDjC,MAAOA,EACPD,OAAQA,GAGV,OAAOgB,GAAcnP,GAWvB,SAAS2Q,GAAQle,GACf,IAAIkY,EAAWlY,EAAQkY,SACvB,GAAiB,SAAbA,GAAoC,SAAbA,EACzB,OAAO,EAET,GAAsD,UAAlDL,GAAyB7X,EAAS,YACpC,OAAO,EAET,IAAI6D,EAAaoU,GAAcjY,GAC/B,QAAK6D,GAGEqa,GAAQra,GAWjB,SAASsa,GAA6Bne,GAEpC,IAAKA,IAAYA,EAAQoe,eAAiBpF,KACxC,OAAO1Y,SAASgO,gBAGlB,IADA,IAAI+P,EAAKre,EAAQoe,cACVC,GAAoD,SAA9CxG,GAAyBwG,EAAI,cACxCA,EAAKA,EAAGD,cAEV,OAAOC,GAAM/d,SAASgO,gBAcxB,SAASgQ,GAAcC,EAAQ7F,EAAW8F,EAASC,GACjD,IAAIrB,EAAgB7C,UAAU7S,OAAS,QAAsB8S,IAAjBD,UAAU,IAAmBA,UAAU,GAI/EmE,EAAa,CAAEhR,IAAK,EAAGE,KAAM,GAC7BwL,EAAegE,EAAgBe,GAA6BI,GAAUhF,GAAuBgF,EAAQ9F,GAAiBC,IAG1H,GAA0B,aAAtB+F,EACFC,EAAab,GAA8CzE,EAAcgE,OACpE,CAEL,IAAIuB,OAAiB,EACK,iBAAtBF,EAE8B,UADhCE,EAAiBvG,GAAgBH,GAAcS,KAC5BR,WACjByG,EAAiBJ,EAAOxG,cAAczJ,iBAGxCqQ,EAD+B,WAAtBF,EACQF,EAAOxG,cAAczJ,gBAErBmQ,EAGnB,IAAI9B,EAAUQ,GAAqCwB,EAAgBvF,EAAcgE,GAGjF,GAAgC,SAA5BuB,EAAezG,UAAwBgG,GAAQ9E,GAWjDsF,EAAa/B,MAXmD,CAChE,IAAIiC,EAAkBnD,GAAe8C,EAAOxG,eACxC2D,EAASkD,EAAgBlD,OACzBC,EAAQiD,EAAgBjD,MAE5B+C,EAAWhR,KAAOiP,EAAQjP,IAAMiP,EAAQgB,UACxCe,EAAW3D,OAASW,EAASiB,EAAQjP,IACrCgR,EAAW9Q,MAAQ+O,EAAQ/O,KAAO+O,EAAQiB,WAC1Cc,EAAW1D,MAAQW,EAAQgB,EAAQ/O,MASvC,IAAIiR,EAAqC,iBADzCL,EAAUA,GAAW,GAOrB,OALAE,EAAW9Q,MAAQiR,EAAkBL,EAAUA,EAAQ5Q,MAAQ,EAC/D8Q,EAAWhR,KAAOmR,EAAkBL,EAAUA,EAAQ9Q,KAAO,EAC7DgR,EAAW1D,OAAS6D,EAAkBL,EAAUA,EAAQxD,OAAS,EACjE0D,EAAW3D,QAAU8D,EAAkBL,EAAUA,EAAQzD,QAAU,EAE5D2D,EAGT,SAASI,GAAQzQ,GAIf,OAHYA,EAAKsN,MACJtN,EAAKqN,OAcpB,SAASqD,GAAqBC,EAAWC,EAASV,EAAQ7F,EAAW+F,GACnE,IAAID,EAAUjE,UAAU7S,OAAS,QAAsB8S,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,EAElF,IAAmC,IAA/ByE,EAAU7W,QAAQ,QACpB,OAAO6W,EAGT,IAAIN,EAAaJ,GAAcC,EAAQ7F,EAAW8F,EAASC,GAEvDS,EAAQ,CACVxR,IAAK,CACHiO,MAAO+C,EAAW/C,MAClBD,OAAQuD,EAAQvR,IAAMgR,EAAWhR,KAEnCsN,MAAO,CACLW,MAAO+C,EAAW1D,MAAQiE,EAAQjE,MAClCU,OAAQgD,EAAWhD,QAErBX,OAAQ,CACNY,MAAO+C,EAAW/C,MAClBD,OAAQgD,EAAW3D,OAASkE,EAAQlE,QAEtCnN,KAAM,CACJ+N,MAAOsD,EAAQrR,KAAO8Q,EAAW9Q,KACjC8N,OAAQgD,EAAWhD,SAInByD,EAAcvc,OAAOC,KAAKqc,GAAOE,KAAI,SAAUza,GACjD,OAAO2X,GAAS,CACd3X,IAAKA,GACJua,EAAMva,GAAM,CACb0a,KAAMP,GAAQI,EAAMva,SAErB2a,MAAK,SAAUC,EAAGC,GACnB,OAAOA,EAAEH,KAAOE,EAAEF,QAGhBI,EAAgBN,EAAYxQ,QAAO,SAAUD,GAC/C,IAAIiN,EAAQjN,EAAMiN,MACdD,EAAShN,EAAMgN,OACnB,OAAOC,GAAS4C,EAAOzB,aAAepB,GAAU6C,EAAOxB,gBAGrD2C,EAAoBD,EAAc/X,OAAS,EAAI+X,EAAc,GAAG9a,IAAMwa,EAAY,GAAGxa,IAErFgb,EAAYX,EAAUvd,MAAM,KAAK,GAErC,OAAOie,GAAqBC,EAAY,IAAMA,EAAY,IAa5D,SAASC,GAAoBC,EAAOtB,EAAQ7F,GAC1C,IAAI0E,EAAgB7C,UAAU7S,OAAS,QAAsB8S,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,KAEpFuF,EAAqB1C,EAAgBe,GAA6BI,GAAUhF,GAAuBgF,EAAQ9F,GAAiBC,IAChI,OAAOyE,GAAqCzE,EAAWoH,EAAoB1C,GAU7E,SAAS2C,GAAc/f,GACrB,IACIkb,EADSlb,EAAQ+X,cAAcC,YACf7W,iBAAiBnB,GACjCggB,EAAIze,WAAW2Z,EAAOyC,WAAa,GAAKpc,WAAW2Z,EAAO+E,cAAgB,GAC1EC,EAAI3e,WAAW2Z,EAAO0C,YAAc,GAAKrc,WAAW2Z,EAAOiF,aAAe,GAK9E,MAJa,CACXxE,MAAO3b,EAAQid,YAAciD,EAC7BxE,OAAQ1b,EAAQoE,aAAe4b,GAYnC,SAASI,GAAqBpB,GAC5B,IAAIqB,EAAO,CAAEzS,KAAM,QAASoN,MAAO,OAAQD,OAAQ,MAAOrN,IAAK,UAC/D,OAAOsR,EAAU7Y,QAAQ,0BAA0B,SAAUma,GAC3D,OAAOD,EAAKC,MAchB,SAASC,GAAiBhC,EAAQiC,EAAkBxB,GAClDA,EAAYA,EAAUvd,MAAM,KAAK,GAGjC,IAAIgf,EAAaV,GAAcxB,GAG3BmC,EAAgB,CAClB/E,MAAO8E,EAAW9E,MAClBD,OAAQ+E,EAAW/E,QAIjBiF,GAAoD,IAA1C,CAAC,QAAS,QAAQxY,QAAQ6W,GACpC4B,EAAWD,EAAU,MAAQ,OAC7BE,EAAgBF,EAAU,OAAS,MACnCG,EAAcH,EAAU,SAAW,QACnCI,EAAwBJ,EAAqB,QAAX,SAStC,OAPAD,EAAcE,GAAYJ,EAAiBI,GAAYJ,EAAiBM,GAAe,EAAIL,EAAWK,GAAe,EAEnHJ,EAAcG,GADZ7B,IAAc6B,EACeL,EAAiBK,GAAiBJ,EAAWM,GAE7CP,EAAiBJ,GAAqBS,IAGhEH,EAYT,SAASxb,GAAK8b,EAAKC,GAEjB,OAAIC,MAAM9b,UAAUF,KACX8b,EAAI9b,KAAK+b,GAIXD,EAAIrS,OAAOsS,GAAO,GAqC3B,SAASE,GAAaC,EAAWxc,EAAMyc,GAoBrC,YAnB8B7G,IAAT6G,EAAqBD,EAAYA,EAAUtX,MAAM,EA1BxE,SAAmBkX,EAAKM,EAAMre,GAE5B,GAAIie,MAAM9b,UAAUmc,UAClB,OAAOP,EAAIO,WAAU,SAAUC,GAC7B,OAAOA,EAAIF,KAAUre,KAKzB,IAAII,EAAQ6B,GAAK8b,GAAK,SAAUlf,GAC9B,OAAOA,EAAIwf,KAAUre,KAEvB,OAAO+d,EAAI7Y,QAAQ9E,GAcsDke,CAAUH,EAAW,OAAQC,KAEvFve,SAAQ,SAAUgY,GAC3BA,EAAmB,UAErB2G,QAAQC,KAAK,yDAEf,IAAIjZ,EAAKqS,EAAmB,UAAKA,EAASrS,GACtCqS,EAAS6G,SAAWhK,GAAWlP,KAIjC7D,EAAK+X,QAAQ4B,OAAS7B,GAAc9X,EAAK+X,QAAQ4B,QACjD3Z,EAAK+X,QAAQjE,UAAYgE,GAAc9X,EAAK+X,QAAQjE,WAEpD9T,EAAO6D,EAAG7D,EAAMkW,OAIblW,EAUT,SAASgd,KAEP,IAAI7b,KAAK8Z,MAAMgC,YAAf,CAIA,IAAIjd,EAAO,CACTK,SAAUc,KACVmV,OAAQ,GACR4G,YAAa,GACb3U,WAAY,GACZ4U,SAAS,EACTpF,QAAS,IAIX/X,EAAK+X,QAAQjE,UAAYkH,GAAoB7Z,KAAK8Z,MAAO9Z,KAAKwY,OAAQxY,KAAK2S,UAAW3S,KAAKic,QAAQC,eAKnGrd,EAAKoa,UAAYD,GAAqBhZ,KAAKic,QAAQhD,UAAWpa,EAAK+X,QAAQjE,UAAW3S,KAAKwY,OAAQxY,KAAK2S,UAAW3S,KAAKic,QAAQZ,UAAUc,KAAKzD,kBAAmB1Y,KAAKic,QAAQZ,UAAUc,KAAK1D,SAG9L5Z,EAAKud,kBAAoBvd,EAAKoa,UAE9Bpa,EAAKqd,cAAgBlc,KAAKic,QAAQC,cAGlCrd,EAAK+X,QAAQ4B,OAASgC,GAAiBxa,KAAKwY,OAAQ3Z,EAAK+X,QAAQjE,UAAW9T,EAAKoa,WAEjFpa,EAAK+X,QAAQ4B,OAAOzQ,SAAW/H,KAAKic,QAAQC,cAAgB,QAAU,WAGtErd,EAAOuc,GAAapb,KAAKqb,UAAWxc,GAI/BmB,KAAK8Z,MAAMuC,UAIdrc,KAAKic,QAAQK,SAASzd,IAHtBmB,KAAK8Z,MAAMuC,WAAY,EACvBrc,KAAKic,QAAQM,SAAS1d,KAY1B,SAAS2d,GAAkBnB,EAAWoB,GACpC,OAAOpB,EAAUqB,MAAK,SAAUpU,GAC9B,IAAIqU,EAAOrU,EAAKqU,KAEhB,OADcrU,EAAKsT,SACDe,IAASF,KAW/B,SAASG,GAAyB5f,GAIhC,IAHA,IAAI6f,EAAW,EAAC,EAAO,KAAM,SAAU,MAAO,KAC1CC,EAAY9f,EAASyG,OAAO,GAAG9F,cAAgBX,EAAS+G,MAAM,GAEzDtC,EAAI,EAAGA,EAAIob,EAASlb,OAAQF,IAAK,CACxC,IAAItH,EAAS0iB,EAASpb,GAClBsb,EAAU5iB,EAAS,GAAKA,EAAS2iB,EAAY9f,EACjD,QAA4C,IAAjCzC,SAASiE,KAAKX,MAAMkf,GAC7B,OAAOA,EAGX,OAAO,KAQT,SAASC,KAsBP,OArBAhd,KAAK8Z,MAAMgC,aAAc,EAGrBU,GAAkBxc,KAAKqb,UAAW,gBACpCrb,KAAKwY,OAAOnY,gBAAgB,eAC5BL,KAAKwY,OAAO3a,MAAMkK,SAAW,GAC7B/H,KAAKwY,OAAO3a,MAAM8J,IAAM,GACxB3H,KAAKwY,OAAO3a,MAAMgK,KAAO,GACzB7H,KAAKwY,OAAO3a,MAAMoX,MAAQ,GAC1BjV,KAAKwY,OAAO3a,MAAMmX,OAAS,GAC3BhV,KAAKwY,OAAO3a,MAAMof,WAAa,GAC/Bjd,KAAKwY,OAAO3a,MAAM+e,GAAyB,cAAgB,IAG7D5c,KAAKkd,wBAIDld,KAAKic,QAAQkB,iBACfnd,KAAKwY,OAAO1a,WAAWgI,YAAY9F,KAAKwY,QAEnCxY,KAQT,SAASod,GAAUnjB,GACjB,IAAI+X,EAAgB/X,EAAQ+X,cAC5B,OAAOA,EAAgBA,EAAcC,YAAc9W,OAoBrD,SAASkiB,GAAoB1K,EAAWsJ,EAASnC,EAAOwD,GAEtDxD,EAAMwD,YAAcA,EACpBF,GAAUzK,GAAWtW,iBAAiB,SAAUyd,EAAMwD,YAAa,CAAEC,SAAS,IAG9E,IAAIC,EAAgBnL,GAAgBM,GAKpC,OA5BF,SAAS8K,EAAsBhG,EAAc7V,EAAO8b,EAAUC,GAC5D,IAAIC,EAAmC,SAA1BnG,EAAatF,SACtBvP,EAASgb,EAASnG,EAAazF,cAAcC,YAAcwF,EAC/D7U,EAAOvG,iBAAiBuF,EAAO8b,EAAU,CAAEH,SAAS,IAE/CK,GACHH,EAAsBpL,GAAgBzP,EAAO9E,YAAa8D,EAAO8b,EAAUC,GAE7EA,EAAczU,KAAKtG,GAgBnB6a,CAAsBD,EAAe,SAAU1D,EAAMwD,YAAaxD,EAAM6D,eACxE7D,EAAM0D,cAAgBA,EACtB1D,EAAM+D,eAAgB,EAEf/D,EAST,SAASgE,KACF9d,KAAK8Z,MAAM+D,gBACd7d,KAAK8Z,MAAQuD,GAAoBrd,KAAK2S,UAAW3S,KAAKic,QAASjc,KAAK8Z,MAAO9Z,KAAK+d,iBAkCpF,SAASb,KAxBT,IAA8BvK,EAAWmH,EAyBnC9Z,KAAK8Z,MAAM+D,gBACbG,qBAAqBhe,KAAK+d,gBAC1B/d,KAAK8Z,OA3BqBnH,EA2BQ3S,KAAK2S,UA3BFmH,EA2Ba9Z,KAAK8Z,MAzBzDsD,GAAUzK,GAAWpW,oBAAoB,SAAUud,EAAMwD,aAGzDxD,EAAM6D,cAAc5gB,SAAQ,SAAU6F,GACpCA,EAAOrG,oBAAoB,SAAUud,EAAMwD,gBAI7CxD,EAAMwD,YAAc,KACpBxD,EAAM6D,cAAgB,GACtB7D,EAAM0D,cAAgB,KACtB1D,EAAM+D,eAAgB,EACf/D,IAwBT,SAASmE,GAAUC,GACjB,MAAa,KAANA,IAAaC,MAAM3iB,WAAW0iB,KAAOE,SAASF,GAWvD,SAASG,GAAUpkB,EAASkb,GAC1BtY,OAAOC,KAAKqY,GAAQpY,SAAQ,SAAUwe,GACpC,IAAI+C,EAAO,IAEkE,IAAzE,CAAC,QAAS,SAAU,MAAO,QAAS,SAAU,QAAQlc,QAAQmZ,IAAgB0C,GAAU9I,EAAOoG,MACjG+C,EAAO,MAETrkB,EAAQ4D,MAAM0d,GAAQpG,EAAOoG,GAAQ+C,KAgIzC,IAAIC,GAAYpN,IAAa,WAAW1T,KAAKwN,UAAUqG,WA8GvD,SAASkN,GAAmBnD,EAAWoD,EAAgBC,GACrD,IAAIC,EAAaxf,GAAKkc,GAAW,SAAU/S,GAEzC,OADWA,EAAKqU,OACA8B,KAGdG,IAAeD,GAActD,EAAUqB,MAAK,SAAU3H,GACxD,OAAOA,EAAS4H,OAAS+B,GAAiB3J,EAAS6G,SAAW7G,EAASpB,MAAQgL,EAAWhL,SAG5F,IAAKiL,EAAY,CACf,IAAIC,EAAc,IAAMJ,EAAiB,IACrCK,EAAY,IAAMJ,EAAgB,IACtChD,QAAQC,KAAKmD,EAAY,4BAA8BD,EAAc,4DAA8DA,EAAc,KAEnJ,OAAOD,EAoIT,IAAIG,GAAa,CAAC,aAAc,OAAQ,WAAY,YAAa,MAAO,UAAW,cAAe,QAAS,YAAa,aAAc,SAAU,eAAgB,WAAY,OAAQ,cAGhLC,GAAkBD,GAAWhb,MAAM,GAYvC,SAASkb,GAAUhG,GACjB,IAAIiG,EAAU1K,UAAU7S,OAAS,QAAsB8S,IAAjBD,UAAU,IAAmBA,UAAU,GAEzEzI,EAAQiT,GAAgB5c,QAAQ6W,GAChCgC,EAAM+D,GAAgBjb,MAAMgI,EAAQ,GAAGvD,OAAOwW,GAAgBjb,MAAM,EAAGgI,IAC3E,OAAOmT,EAAUjE,EAAIkE,UAAYlE,EAGnC,IAAImE,GACI,OADJA,GAES,YAFTA,GAGgB,mBAiMpB,SAASC,GAAY7X,EAAQmT,EAAeF,EAAkB6E,GAC5D,IAAI1I,EAAU,CAAC,EAAG,GAKd2I,GAA0D,IAA9C,CAAC,QAAS,QAAQnd,QAAQkd,GAItCE,EAAYhY,EAAO9L,MAAM,WAAW2d,KAAI,SAAUoG,GACpD,OAAOA,EAAK5kB,UAKV6kB,EAAUF,EAAUpd,QAAQjD,GAAKqgB,GAAW,SAAUC,GACxD,OAAgC,IAAzBA,EAAKE,OAAO,YAGjBH,EAAUE,KAAiD,IAArCF,EAAUE,GAAStd,QAAQ,MACnDsZ,QAAQC,KAAK,gFAKf,IAAIiE,EAAa,cACbC,GAAmB,IAAbH,EAAiB,CAACF,EAAUzb,MAAM,EAAG2b,GAASlX,OAAO,CAACgX,EAAUE,GAAShkB,MAAMkkB,GAAY,KAAM,CAACJ,EAAUE,GAAShkB,MAAMkkB,GAAY,IAAIpX,OAAOgX,EAAUzb,MAAM2b,EAAU,KAAO,CAACF,GAqC9L,OAlCAK,EAAMA,EAAIxG,KAAI,SAAUyG,EAAI/T,GAE1B,IAAIgP,GAAyB,IAAVhP,GAAewT,EAAYA,GAAa,SAAW,QAClEQ,GAAoB,EACxB,OAAOD,EAGNE,QAAO,SAAUxG,EAAGC,GACnB,MAAwB,KAApBD,EAAEA,EAAE7X,OAAS,KAAwC,IAA3B,CAAC,IAAK,KAAKS,QAAQqX,IAC/CD,EAAEA,EAAE7X,OAAS,GAAK8X,EAClBsG,GAAoB,EACbvG,GACEuG,GACTvG,EAAEA,EAAE7X,OAAS,IAAM8X,EACnBsG,GAAoB,EACbvG,GAEAA,EAAEhR,OAAOiR,KAEjB,IAEFJ,KAAI,SAAU4G,GACb,OAxGN,SAAiBA,EAAKlF,EAAaJ,EAAeF,GAEhD,IAAI/e,EAAQukB,EAAI3iB,MAAM,6BAClBJ,GAASxB,EAAM,GACf4iB,EAAO5iB,EAAM,GAGjB,IAAKwB,EACH,OAAO+iB,EAGT,GAA0B,IAAtB3B,EAAKlc,QAAQ,KAAY,CAC3B,IAAInI,OAAU,EACd,OAAQqkB,GACN,IAAK,KACHrkB,EAAU0gB,EACV,MACF,IAAK,IACL,IAAK,KACL,QACE1gB,EAAUwgB,EAId,OADW9D,GAAc1c,GACb8gB,GAAe,IAAM7d,EAC5B,GAAa,OAATohB,GAA0B,OAATA,EAAe,CAQzC,OALa,OAATA,EACKlkB,KAAKqb,IAAIlb,SAASgO,gBAAgByO,aAAc7b,OAAO+c,aAAe,GAEtE9d,KAAKqb,IAAIlb,SAASgO,gBAAgBwO,YAAa5b,OAAO8c,YAAc,IAE/D,IAAM/a,EAIpB,OAAOA,EAmEEgjB,CAAQD,EAAKlF,EAAaJ,EAAeF,UAKhD1d,SAAQ,SAAU+iB,EAAI/T,GACxB+T,EAAG/iB,SAAQ,SAAU0iB,EAAMU,GACrBlC,GAAUwB,KACZ7I,EAAQ7K,IAAU0T,GAA2B,MAAnBK,EAAGK,EAAS,IAAc,EAAI,UAIvDvJ,EA2OT,IAkWIwJ,GAAW,CAKbnH,UAAW,SAMXiD,eAAe,EAMf2B,eAAe,EAOfV,iBAAiB,EAQjBZ,SAAU,aAUVD,SAAU,aAOVjB,UAnZc,CASdgF,MAAO,CAEL1M,MAAO,IAEPiI,SAAS,EAETlZ,GA9HJ,SAAe7D,GACb,IAAIoa,EAAYpa,EAAKoa,UACjBqG,EAAgBrG,EAAUvd,MAAM,KAAK,GACrC4kB,EAAiBrH,EAAUvd,MAAM,KAAK,GAG1C,GAAI4kB,EAAgB,CAClB,IAAIC,EAAgB1hB,EAAK+X,QACrBjE,EAAY4N,EAAc5N,UAC1B6F,EAAS+H,EAAc/H,OAEvBgI,GAA2D,IAA9C,CAAC,SAAU,OAAOpe,QAAQkd,GACvC/K,EAAOiM,EAAa,OAAS,MAC7BzF,EAAcyF,EAAa,QAAU,SAErCC,EAAe,CACjB/T,MAAO7H,GAAe,GAAI0P,EAAM5B,EAAU4B,IAC1CzH,IAAKjI,GAAe,GAAI0P,EAAM5B,EAAU4B,GAAQ5B,EAAUoI,GAAevC,EAAOuC,KAGlFlc,EAAK+X,QAAQ4B,OAASjC,GAAS,GAAIiC,EAAQiI,EAAaH,IAG1D,OAAOzhB,IAgJP2I,OAAQ,CAENmM,MAAO,IAEPiI,SAAS,EAETlZ,GA7RJ,SAAgB7D,EAAMyJ,GACpB,IAAId,EAASc,EAAKd,OACdyR,EAAYpa,EAAKoa,UACjBsH,EAAgB1hB,EAAK+X,QACrB4B,EAAS+H,EAAc/H,OACvB7F,EAAY4N,EAAc5N,UAE1B2M,EAAgBrG,EAAUvd,MAAM,KAAK,GAErCkb,OAAU,EAsBd,OApBEA,EADEqH,IAAWzW,GACH,EAAEA,EAAQ,GAEV6X,GAAY7X,EAAQgR,EAAQ7F,EAAW2M,GAG7B,SAAlBA,GACF9G,EAAO7Q,KAAOiP,EAAQ,GACtB4B,EAAO3Q,MAAQ+O,EAAQ,IACI,UAAlB0I,GACT9G,EAAO7Q,KAAOiP,EAAQ,GACtB4B,EAAO3Q,MAAQ+O,EAAQ,IACI,QAAlB0I,GACT9G,EAAO3Q,MAAQ+O,EAAQ,GACvB4B,EAAO7Q,KAAOiP,EAAQ,IACK,WAAlB0I,IACT9G,EAAO3Q,MAAQ+O,EAAQ,GACvB4B,EAAO7Q,KAAOiP,EAAQ,IAGxB/X,EAAK2Z,OAASA,EACP3Z,GAkQL2I,OAAQ,GAoBVkZ,gBAAiB,CAEf/M,MAAO,IAEPiI,SAAS,EAETlZ,GAlRJ,SAAyB7D,EAAMod,GAC7B,IAAIvD,EAAoBuD,EAAQvD,mBAAqBvF,GAAgBtU,EAAKK,SAASsZ,QAK/E3Z,EAAKK,SAASyT,YAAc+F,IAC9BA,EAAoBvF,GAAgBuF,IAMtC,IAAIiI,EAAgB/D,GAAyB,aACzCgE,EAAe/hB,EAAKK,SAASsZ,OAAO3a,MACpC8J,EAAMiZ,EAAajZ,IACnBE,EAAO+Y,EAAa/Y,KACpBgZ,EAAYD,EAAaD,GAE7BC,EAAajZ,IAAM,GACnBiZ,EAAa/Y,KAAO,GACpB+Y,EAAaD,GAAiB,GAE9B,IAAIhI,EAAaJ,GAAc1Z,EAAKK,SAASsZ,OAAQ3Z,EAAKK,SAASyT,UAAWsJ,EAAQxD,QAASC,EAAmB7Z,EAAKqd,eAIvH0E,EAAajZ,IAAMA,EACnBiZ,EAAa/Y,KAAOA,EACpB+Y,EAAaD,GAAiBE,EAE9B5E,EAAQtD,WAAaA,EAErB,IAAIhF,EAAQsI,EAAQ6E,SAChBtI,EAAS3Z,EAAK+X,QAAQ4B,OAEtB0C,EAAQ,CACV6F,QAAS,SAAiB9H,GACxB,IAAI/b,EAAQsb,EAAOS,GAInB,OAHIT,EAAOS,GAAaN,EAAWM,KAAegD,EAAQ+E,sBACxD9jB,EAAQ9C,KAAKqb,IAAI+C,EAAOS,GAAYN,EAAWM,KAE1CpU,GAAe,GAAIoU,EAAW/b,IAEvC+jB,UAAW,SAAmBhI,GAC5B,IAAI4B,EAAyB,UAAd5B,EAAwB,OAAS,MAC5C/b,EAAQsb,EAAOqC,GAInB,OAHIrC,EAAOS,GAAaN,EAAWM,KAAegD,EAAQ+E,sBACxD9jB,EAAQ9C,KAAK8mB,IAAI1I,EAAOqC,GAAWlC,EAAWM,IAA4B,UAAdA,EAAwBT,EAAO5C,MAAQ4C,EAAO7C,UAErG9Q,GAAe,GAAIgW,EAAU3d,KAWxC,OAPAyW,EAAM5W,SAAQ,SAAUkc,GACtB,IAAI1E,GAA+C,IAAxC,CAAC,OAAQ,OAAOnS,QAAQ6W,GAAoB,UAAY,YACnET,EAASjC,GAAS,GAAIiC,EAAQ0C,EAAM3G,GAAM0E,OAG5Cpa,EAAK+X,QAAQ4B,OAASA,EAEf3Z,GA2NLiiB,SAAU,CAAC,OAAQ,QAAS,MAAO,UAOnCrI,QAAS,EAMTC,kBAAmB,gBAYrByI,aAAc,CAEZxN,MAAO,IAEPiI,SAAS,EAETlZ,GAlgBJ,SAAsB7D,GACpB,IAAI0hB,EAAgB1hB,EAAK+X,QACrB4B,EAAS+H,EAAc/H,OACvB7F,EAAY4N,EAAc5N,UAE1BsG,EAAYpa,EAAKoa,UAAUvd,MAAM,KAAK,GACtCrB,EAAQD,KAAKC,MACbmmB,GAAuD,IAA1C,CAAC,MAAO,UAAUpe,QAAQ6W,GACvC1E,EAAOiM,EAAa,QAAU,SAC9BY,EAASZ,EAAa,OAAS,MAC/BzF,EAAcyF,EAAa,QAAU,SASzC,OAPIhI,EAAOjE,GAAQla,EAAMsY,EAAUyO,MACjCviB,EAAK+X,QAAQ4B,OAAO4I,GAAU/mB,EAAMsY,EAAUyO,IAAW5I,EAAOuC,IAE9DvC,EAAO4I,GAAU/mB,EAAMsY,EAAU4B,MACnC1V,EAAK+X,QAAQ4B,OAAO4I,GAAU/mB,EAAMsY,EAAU4B,KAGzC1V,IA4fPwiB,MAAO,CAEL1N,MAAO,IAEPiI,SAAS,EAETlZ,GApxBJ,SAAe7D,EAAMod,GACnB,IAAIqF,EAGJ,IAAK9C,GAAmB3f,EAAKK,SAASmc,UAAW,QAAS,gBACxD,OAAOxc,EAGT,IAAI0iB,EAAetF,EAAQhiB,QAG3B,GAA4B,iBAAjBsnB,GAIT,KAHAA,EAAe1iB,EAAKK,SAASsZ,OAAOzd,cAAcwmB,IAIhD,OAAO1iB,OAKT,IAAKA,EAAKK,SAASsZ,OAAO5S,SAAS2b,GAEjC,OADA7F,QAAQC,KAAK,iEACN9c,EAIX,IAAIoa,EAAYpa,EAAKoa,UAAUvd,MAAM,KAAK,GACtC6kB,EAAgB1hB,EAAK+X,QACrB4B,EAAS+H,EAAc/H,OACvB7F,EAAY4N,EAAc5N,UAE1B6N,GAAuD,IAA1C,CAAC,OAAQ,SAASpe,QAAQ6W,GAEvCvX,EAAM8e,EAAa,SAAW,QAC9BgB,EAAkBhB,EAAa,MAAQ,OACvCjM,EAAOiN,EAAgBjkB,cACvBkkB,EAAUjB,EAAa,OAAS,MAChCY,EAASZ,EAAa,SAAW,QACjCkB,EAAmB1H,GAAcuH,GAAc7f,GAQ/CiR,EAAUyO,GAAUM,EAAmBlJ,EAAOjE,KAChD1V,EAAK+X,QAAQ4B,OAAOjE,IAASiE,EAAOjE,IAAS5B,EAAUyO,GAAUM,IAG/D/O,EAAU4B,GAAQmN,EAAmBlJ,EAAO4I,KAC9CviB,EAAK+X,QAAQ4B,OAAOjE,IAAS5B,EAAU4B,GAAQmN,EAAmBlJ,EAAO4I,IAE3EviB,EAAK+X,QAAQ4B,OAAS7B,GAAc9X,EAAK+X,QAAQ4B,QAGjD,IAAImJ,EAAShP,EAAU4B,GAAQ5B,EAAUjR,GAAO,EAAIggB,EAAmB,EAInE3P,EAAMD,GAAyBjT,EAAKK,SAASsZ,QAC7CoJ,EAAmBpmB,WAAWuW,EAAI,SAAWyP,GAAkB,IAC/DK,EAAmBrmB,WAAWuW,EAAI,SAAWyP,EAAkB,SAAU,IACzEM,EAAYH,EAAS9iB,EAAK+X,QAAQ4B,OAAOjE,GAAQqN,EAAmBC,EAQxE,OALAC,EAAY1nB,KAAKqb,IAAIrb,KAAK8mB,IAAI1I,EAAO9W,GAAOggB,EAAkBI,GAAY,GAE1EjjB,EAAK0iB,aAAeA,EACpB1iB,EAAK+X,QAAQyK,OAAmCxc,GAA1Byc,EAAsB,GAAwC/M,EAAMna,KAAK2nB,MAAMD,IAAajd,GAAeyc,EAAqBG,EAAS,IAAKH,GAE7JziB,GA8sBL5E,QAAS,aAcXkiB,KAAM,CAEJxI,MAAO,IAEPiI,SAAS,EAETlZ,GA5oBJ,SAAc7D,EAAMod,GAElB,GAAIO,GAAkB3d,EAAKK,SAASmc,UAAW,SAC7C,OAAOxc,EAGT,GAAIA,EAAKmd,SAAWnd,EAAKoa,YAAcpa,EAAKud,kBAE1C,OAAOvd,EAGT,IAAI8Z,EAAaJ,GAAc1Z,EAAKK,SAASsZ,OAAQ3Z,EAAKK,SAASyT,UAAWsJ,EAAQxD,QAASwD,EAAQvD,kBAAmB7Z,EAAKqd,eAE3HjD,EAAYpa,EAAKoa,UAAUvd,MAAM,KAAK,GACtCsmB,EAAoB3H,GAAqBpB,GACzCW,EAAY/a,EAAKoa,UAAUvd,MAAM,KAAK,IAAM,GAE5CumB,EAAY,GAEhB,OAAQhG,EAAQiG,UACd,KAAK9C,GACH6C,EAAY,CAAChJ,EAAW+I,GACxB,MACF,KAAK5C,GACH6C,EAAYhD,GAAUhG,GACtB,MACF,KAAKmG,GACH6C,EAAYhD,GAAUhG,GAAW,GACjC,MACF,QACEgJ,EAAYhG,EAAQiG,SAyDxB,OAtDAD,EAAUllB,SAAQ,SAAUolB,EAAMpW,GAChC,GAAIkN,IAAckJ,GAAQF,EAAUtgB,SAAWoK,EAAQ,EACrD,OAAOlN,EAGToa,EAAYpa,EAAKoa,UAAUvd,MAAM,KAAK,GACtCsmB,EAAoB3H,GAAqBpB,GAEzC,IAAI0B,EAAgB9b,EAAK+X,QAAQ4B,OAC7B4J,EAAavjB,EAAK+X,QAAQjE,UAG1BtY,EAAQD,KAAKC,MACbgoB,EAA4B,SAAdpJ,GAAwB5e,EAAMsgB,EAAc1F,OAAS5a,EAAM+nB,EAAWva,OAAuB,UAAdoR,GAAyB5e,EAAMsgB,EAAc9S,MAAQxN,EAAM+nB,EAAWnN,QAAwB,QAAdgE,GAAuB5e,EAAMsgB,EAAc3F,QAAU3a,EAAM+nB,EAAWza,MAAsB,WAAdsR,GAA0B5e,EAAMsgB,EAAchT,KAAOtN,EAAM+nB,EAAWpN,QAEjUsN,EAAgBjoB,EAAMsgB,EAAc9S,MAAQxN,EAAMse,EAAW9Q,MAC7D0a,EAAiBloB,EAAMsgB,EAAc1F,OAAS5a,EAAMse,EAAW1D,OAC/DuN,EAAenoB,EAAMsgB,EAAchT,KAAOtN,EAAMse,EAAWhR,KAC3D8a,EAAkBpoB,EAAMsgB,EAAc3F,QAAU3a,EAAMse,EAAW3D,QAEjE0N,EAAoC,SAAdzJ,GAAwBqJ,GAA+B,UAAdrJ,GAAyBsJ,GAAgC,QAAdtJ,GAAuBuJ,GAA8B,WAAdvJ,GAA0BwJ,EAG3KjC,GAAuD,IAA1C,CAAC,MAAO,UAAUpe,QAAQ6W,GAGvC0J,IAA0B1G,EAAQ2G,iBAAmBpC,GAA4B,UAAd5G,GAAyB0I,GAAiB9B,GAA4B,QAAd5G,GAAuB2I,IAAmB/B,GAA4B,UAAd5G,GAAyB4I,IAAiBhC,GAA4B,QAAd5G,GAAuB6I,GAGlQI,IAA8B5G,EAAQ6G,0BAA4BtC,GAA4B,UAAd5G,GAAyB2I,GAAkB/B,GAA4B,QAAd5G,GAAuB0I,IAAkB9B,GAA4B,UAAd5G,GAAyB6I,IAAoBjC,GAA4B,QAAd5G,GAAuB4I,GAElRO,EAAmBJ,GAAyBE,GAE5CR,GAAeK,GAAuBK,KAExClkB,EAAKmd,SAAU,GAEXqG,GAAeK,KACjBzJ,EAAYgJ,EAAUlW,EAAQ,IAG5BgX,IACFnJ,EAvJR,SAA8BA,GAC5B,MAAkB,QAAdA,EACK,QACgB,UAAdA,EACF,MAEFA,EAiJWoJ,CAAqBpJ,IAGnC/a,EAAKoa,UAAYA,GAAaW,EAAY,IAAMA,EAAY,IAI5D/a,EAAK+X,QAAQ4B,OAASjC,GAAS,GAAI1X,EAAK+X,QAAQ4B,OAAQgC,GAAiB3b,EAAKK,SAASsZ,OAAQ3Z,EAAK+X,QAAQjE,UAAW9T,EAAKoa,YAE5Hpa,EAAOuc,GAAavc,EAAKK,SAASmc,UAAWxc,EAAM,YAGhDA,GA4jBLqjB,SAAU,OAKVzJ,QAAS,EAOTC,kBAAmB,WAQnBkK,gBAAgB,EAQhBE,yBAAyB,GAU3BG,MAAO,CAELtP,MAAO,IAEPiI,SAAS,EAETlZ,GArQJ,SAAe7D,GACb,IAAIoa,EAAYpa,EAAKoa,UACjBqG,EAAgBrG,EAAUvd,MAAM,KAAK,GACrC6kB,EAAgB1hB,EAAK+X,QACrB4B,EAAS+H,EAAc/H,OACvB7F,EAAY4N,EAAc5N,UAE1BiI,GAAwD,IAA9C,CAAC,OAAQ,SAASxY,QAAQkd,GAEpC4D,GAA6D,IAA5C,CAAC,MAAO,QAAQ9gB,QAAQkd,GAO7C,OALA9G,EAAOoC,EAAU,OAAS,OAASjI,EAAU2M,IAAkB4D,EAAiB1K,EAAOoC,EAAU,QAAU,UAAY,GAEvH/b,EAAKoa,UAAYoB,GAAqBpB,GACtCpa,EAAK+X,QAAQ4B,OAAS7B,GAAc6B,GAE7B3Z,IAkQPmR,KAAM,CAEJ2D,MAAO,IAEPiI,SAAS,EAETlZ,GA9TJ,SAAc7D,GACZ,IAAK2f,GAAmB3f,EAAKK,SAASmc,UAAW,OAAQ,mBACvD,OAAOxc,EAGT,IAAIqa,EAAUra,EAAK+X,QAAQjE,UACvBwQ,EAAQhkB,GAAKN,EAAKK,SAASmc,WAAW,SAAUtG,GAClD,MAAyB,oBAAlBA,EAAS4H,QACfhE,WAEH,GAAIO,EAAQlE,OAASmO,EAAMxb,KAAOuR,EAAQrR,KAAOsb,EAAMlO,OAASiE,EAAQvR,IAAMwb,EAAMnO,QAAUkE,EAAQjE,MAAQkO,EAAMtb,KAAM,CAExH,IAAkB,IAAdhJ,EAAKmR,KACP,OAAOnR,EAGTA,EAAKmR,MAAO,EACZnR,EAAKuI,WAAW,uBAAyB,OACpC,CAEL,IAAkB,IAAdvI,EAAKmR,KACP,OAAOnR,EAGTA,EAAKmR,MAAO,EACZnR,EAAKuI,WAAW,wBAAyB,EAG3C,OAAOvI,IAoTPukB,aAAc,CAEZzP,MAAO,IAEPiI,SAAS,EAETlZ,GAtgCJ,SAAsB7D,EAAMod,GAC1B,IAAIhC,EAAIgC,EAAQhC,EACZE,EAAI8B,EAAQ9B,EACZ3B,EAAS3Z,EAAK+X,QAAQ4B,OAItB6K,EAA8BlkB,GAAKN,EAAKK,SAASmc,WAAW,SAAUtG,GACxE,MAAyB,eAAlBA,EAAS4H,QACf2G,qBACiC7O,IAAhC4O,GACF3H,QAAQC,KAAK,iIAEf,IAAI2H,OAAkD7O,IAAhC4O,EAA4CA,EAA8BpH,EAAQqH,gBAEpGjQ,EAAeF,GAAgBtU,EAAKK,SAASsZ,QAC7C+K,EAAmB7b,GAAsB2L,GAGzC8B,EAAS,CACXpN,SAAUyQ,EAAOzQ,UAGf6O,EA9DN,SAA2B/X,EAAM2kB,GAC/B,IAAIjD,EAAgB1hB,EAAK+X,QACrB4B,EAAS+H,EAAc/H,OACvB7F,EAAY4N,EAAc5N,UAC1BoP,EAAQ3nB,KAAK2nB,MACb1nB,EAAQD,KAAKC,MAEbopB,EAAU,SAAiBC,GAC7B,OAAOA,GAGLC,EAAiB5B,EAAMpP,EAAUiD,OACjCgO,EAAc7B,EAAMvJ,EAAO5C,OAE3B4K,GAA4D,IAA/C,CAAC,OAAQ,SAASpe,QAAQvD,EAAKoa,WAC5C4K,GAA+C,IAAjChlB,EAAKoa,UAAU7W,QAAQ,KAIrC0hB,EAAuBN,EAAwBhD,GAAcqD,GAH3CF,EAAiB,GAAMC,EAAc,EAGuC7B,EAAQ1nB,EAAjEopB,EACrCM,EAAqBP,EAAwBzB,EAAV0B,EAEvC,MAAO,CACL5b,KAAMic,EANWH,EAAiB,GAAM,GAAKC,EAAc,GAAM,IAMtBC,GAAeL,EAAchL,EAAO3Q,KAAO,EAAI2Q,EAAO3Q,MACjGF,IAAKoc,EAAkBvL,EAAO7Q,KAC9BqN,OAAQ+O,EAAkBvL,EAAOxD,QACjCC,MAAO6O,EAAoBtL,EAAOvD,QAoCtB+O,CAAkBnlB,EAAM1D,OAAO8oB,iBAAmB,IAAM1F,IAElElJ,EAAc,WAAN4E,EAAiB,MAAQ,SACjC3E,EAAc,UAAN6E,EAAgB,OAAS,QAKjC+J,EAAmBtH,GAAyB,aAW5C/U,OAAO,EACPF,OAAM,EAqBV,GAhBIA,EAJU,WAAV0N,EAG4B,SAA1BhC,EAAalB,UACRkB,EAAa2D,aAAeJ,EAAQ5B,QAEpCuO,EAAiB5N,OAASiB,EAAQ5B,OAGrC4B,EAAQjP,IAIZE,EAFU,UAAVyN,EAC4B,SAA1BjC,EAAalB,UACPkB,EAAa0D,YAAcH,EAAQ3B,OAEnCsO,EAAiB3N,MAAQgB,EAAQ3B,MAGpC2B,EAAQ/O,KAEbyb,GAAmBY,EACrB/O,EAAO+O,GAAoB,eAAiBrc,EAAO,OAASF,EAAM,SAClEwN,EAAOE,GAAS,EAChBF,EAAOG,GAAS,EAChBH,EAAO8H,WAAa,gBACf,CAEL,IAAIkH,EAAsB,WAAV9O,GAAsB,EAAI,EACtC+O,EAAuB,UAAV9O,GAAqB,EAAI,EAC1CH,EAAOE,GAAS1N,EAAMwc,EACtBhP,EAAOG,GAASzN,EAAOuc,EACvBjP,EAAO8H,WAAa5H,EAAQ,KAAOC,EAIrC,IAAIlO,EAAa,CACfid,cAAexlB,EAAKoa,WAQtB,OAJApa,EAAKuI,WAAamP,GAAS,GAAInP,EAAYvI,EAAKuI,YAChDvI,EAAKsW,OAASoB,GAAS,GAAIpB,EAAQtW,EAAKsW,QACxCtW,EAAKkd,YAAcxF,GAAS,GAAI1X,EAAK+X,QAAQyK,MAAOxiB,EAAKkd,aAElDld,GAo7BLykB,iBAAiB,EAMjBrJ,EAAG,SAMHE,EAAG,SAkBLmK,WAAY,CAEV3Q,MAAO,IAEPiI,SAAS,EAETlZ,GAzpCJ,SAAoB7D,GAgBlB,OAXAwf,GAAUxf,EAAKK,SAASsZ,OAAQ3Z,EAAKsW,QAzBvC,SAAuBlb,EAASmN,GAC9BvK,OAAOC,KAAKsK,GAAYrK,SAAQ,SAAUwe,IAE1B,IADFnU,EAAWmU,GAErBthB,EAAQwM,aAAa8U,EAAMnU,EAAWmU,IAEtCthB,EAAQoG,gBAAgBkb,MAuB5BgJ,CAAc1lB,EAAKK,SAASsZ,OAAQ3Z,EAAKuI,YAGrCvI,EAAK0iB,cAAgB1kB,OAAOC,KAAK+B,EAAKkd,aAAapa,QACrD0c,GAAUxf,EAAK0iB,aAAc1iB,EAAKkd,aAG7Bld,GA2oCL2lB,OA9nCJ,SAA0B7R,EAAW6F,EAAQyD,EAASwI,EAAiB3K,GAErE,IAAIW,EAAmBZ,GAAoBC,EAAOtB,EAAQ7F,EAAWsJ,EAAQC,eAKzEjD,EAAYD,GAAqBiD,EAAQhD,UAAWwB,EAAkBjC,EAAQ7F,EAAWsJ,EAAQZ,UAAUc,KAAKzD,kBAAmBuD,EAAQZ,UAAUc,KAAK1D,SAQ9J,OANAD,EAAO/R,aAAa,cAAewS,GAInCoF,GAAU7F,EAAQ,CAAEzQ,SAAUkU,EAAQC,cAAgB,QAAU,aAEzDD,GAsnCLqH,qBAAiB7O,KAuGjBiQ,GAAS,WASX,SAASA,EAAO/R,EAAW6F,GACzB,IAAI/S,EAAQzF,KAERic,EAAUzH,UAAU7S,OAAS,QAAsB8S,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,GAClFqB,GAAe7V,KAAM0kB,GAErB1kB,KAAK+d,eAAiB,WACpB,OAAO4G,sBAAsBlf,EAAMoW,SAIrC7b,KAAK6b,OAAStK,GAASvR,KAAK6b,OAAOhQ,KAAK7L,OAGxCA,KAAKic,QAAU1F,GAAS,GAAImO,EAAOtE,SAAUnE,GAG7Cjc,KAAK8Z,MAAQ,CACXgC,aAAa,EACbO,WAAW,EACXsB,cAAe,IAIjB3d,KAAK2S,UAAYA,GAAaA,EAAU7B,OAAS6B,EAAU,GAAKA,EAChE3S,KAAKwY,OAASA,GAAUA,EAAO1H,OAAS0H,EAAO,GAAKA,EAGpDxY,KAAKic,QAAQZ,UAAY,GACzBxe,OAAOC,KAAKyZ,GAAS,GAAImO,EAAOtE,SAAS/E,UAAWY,EAAQZ,YAAYte,SAAQ,SAAU4f,GACxFlX,EAAMwW,QAAQZ,UAAUsB,GAAQpG,GAAS,GAAImO,EAAOtE,SAAS/E,UAAUsB,IAAS,GAAIV,EAAQZ,UAAYY,EAAQZ,UAAUsB,GAAQ,OAIpI3c,KAAKqb,UAAYxe,OAAOC,KAAKkD,KAAKic,QAAQZ,WAAWhC,KAAI,SAAUsD,GACjE,OAAOpG,GAAS,CACdoG,KAAMA,GACLlX,EAAMwW,QAAQZ,UAAUsB,OAG5BpD,MAAK,SAAUC,EAAGC,GACjB,OAAOD,EAAE7F,MAAQ8F,EAAE9F,SAOrB3T,KAAKqb,UAAUte,SAAQ,SAAU0nB,GAC3BA,EAAgB7I,SAAWhK,GAAW6S,EAAgBD,SACxDC,EAAgBD,OAAO/e,EAAMkN,UAAWlN,EAAM+S,OAAQ/S,EAAMwW,QAASwI,EAAiBhf,EAAMqU,UAKhG9Z,KAAK6b,SAEL,IAAIgC,EAAgB7d,KAAKic,QAAQ4B,cAC7BA,GAEF7d,KAAK8d,uBAGP9d,KAAK8Z,MAAM+D,cAAgBA,EAqD7B,OA9CA/H,GAAY4O,EAAQ,CAAC,CACnB9lB,IAAK,SACL1B,MAAO,WACL,OAAO2e,GAAOxe,KAAK2C,QAEpB,CACDpB,IAAK,UACL1B,MAAO,WACL,OAAO8f,GAAQ3f,KAAK2C,QAErB,CACDpB,IAAK,uBACL1B,MAAO,WACL,OAAO4gB,GAAqBzgB,KAAK2C,QAElC,CACDpB,IAAK,wBACL1B,MAAO,WACL,OAAOggB,GAAsB7f,KAAK2C,UA4B/B0kB,EA7HI,GAqJbA,GAAOE,OAA2B,oBAAXzpB,OAAyBA,OAAS0pB,QAAQC,YACjEJ,GAAO3F,WAAaA,GACpB2F,GAAOtE,SAAWA,GCzhFlB,IAAMtb,GAAO,WAaPigB,GAAiB,IAAIvnB,OAAUwnB,4BAiC/Bvb,GAAU,CACdjC,OAAQ,EACR2U,MAAM,EACN8I,SAAU,eACVtS,UAAW,SACX1U,QAAS,UACTinB,aAAc,MAGVlb,GAAc,CAClBxC,OAAQ,2BACR2U,KAAM,UACN8I,SAAU,mBACVtS,UAAW,mBACX1U,QAAS,SACTinB,aAAc,iBASVC,GAAAA,WACJ,SAAAA,EAAYlrB,EAAS0C,GACnBqD,KAAKgF,SAAW/K,EAChB+F,KAAKolB,QAAU,KACfplB,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAKqlB,MAAQrlB,KAAKslB,kBAClBtlB,KAAKulB,UAAYvlB,KAAKwlB,gBAEtBxlB,KAAKqL,qBACLpM,EAAahF,EA7EA,cA6EmB+F,iCAmBlCwG,OAAA,WACE,IAAIxG,KAAKgF,SAASygB,WAAYzlB,KAAKgF,SAASU,UAAUE,SA3E9B,YA2ExB,CAIA,IAAM8f,EAAW1lB,KAAKgF,SAASU,UAAUE,SA9ErB,QAgFpBuf,EAASQ,aAELD,GAIJ1lB,KAAKiQ,WAGPA,KAAA,WACE,KAAIjQ,KAAKgF,SAASygB,UAAYzlB,KAAKgF,SAASU,UAAUE,SA3F9B,aA2F+D5F,KAAKqlB,MAAM3f,UAAUE,SA1FxF,SA0FpB,CAIA,IAAMuJ,EAASgW,EAASS,qBAAqB5lB,KAAKgF,UAC5C0I,EAAgB,CACpBA,cAAe1N,KAAKgF,UAKtB,IAFkBnC,EAAaoB,QAAQjE,KAAKgF,SA3GhC,mBA2GsD0I,GAEpD7N,iBAAd,CAKA,IAAKG,KAAKulB,UAAW,CACnB,QAAsB,IAAXb,GACT,MAAM,IAAI5V,UAAU,mEAGtB,IAAI+W,EAAmB7lB,KAAKgF,SAEG,WAA3BhF,KAAK6K,QAAQ8H,UACfkT,EAAmB1W,EACVrT,EAAUkE,KAAK6K,QAAQ8H,aAChCkT,EAAmB7lB,KAAK6K,QAAQ8H,eAGa,IAAlC3S,KAAK6K,QAAQ8H,UAAU7B,SAChC+U,EAAmB7lB,KAAK6K,QAAQ8H,UAAU,KAOhB,iBAA1B3S,KAAK6K,QAAQoa,UACf9V,EAAOzJ,UAAU0C,IA1HU,mBA6H7BpI,KAAKolB,QAAU,IAAIV,GAAOmB,EAAkB7lB,KAAKqlB,MAAOrlB,KAAK8lB,oBAQvB,IAAAxd,EADxC,GAAI,iBAAkB/N,SAASgO,kBAC5B4G,EAAO3J,QAhIc,gBAiItB8C,EAAA,IAAGE,OAAHxF,MAAAsF,EAAa/N,SAASiE,KAAKkK,UACxB3L,SAAQ,SAAA0S,GAAI,OAAI5M,EAAaO,GAAGqM,EAAM,YAAa,MXxBzC,kBW2BfzP,KAAKgF,SAAS+gB,QACd/lB,KAAKgF,SAASyB,aAAa,iBAAiB,GAE5CO,GAAYkB,YAAYlI,KAAKqlB,MAnJT,QAoJpBre,GAAYkB,YAAYlI,KAAKgF,SApJT,QAqJpBnC,EAAaoB,QAAQkL,EA5JR,oBA4J6BzB,QAG5CsC,KAAA,WACE,IAAIhQ,KAAKgF,SAASygB,WAAYzlB,KAAKgF,SAASU,UAAUE,SA1J9B,aA0JgE5F,KAAKqlB,MAAM3f,UAAUE,SAzJzF,QAyJpB,CAIA,IAAMuJ,EAASgW,EAASS,qBAAqB5lB,KAAKgF,UAC5C0I,EAAgB,CACpBA,cAAe1N,KAAKgF,UAGJnC,EAAaoB,QAAQkL,EA5K3B,mBA4K+CzB,GAE7C7N,mBAIVG,KAAKolB,SACPplB,KAAKolB,QAAQpI,UAGfhW,GAAYkB,YAAYlI,KAAKqlB,MA5KT,QA6KpBre,GAAYkB,YAAYlI,KAAKgF,SA7KT,QA8KpBnC,EAAaoB,QAAQkL,EAvLP,qBAuL6BzB,QAG7CnI,QAAA,WACEtG,EAAgBe,KAAKgF,SAzMR,eA0MbnC,EAAaC,IAAI9C,KAAKgF,SAzMX,gBA0MXhF,KAAKgF,SAAW,KAChBhF,KAAKqlB,MAAQ,KACTrlB,KAAKolB,UACPplB,KAAKolB,QAAQpI,UACbhd,KAAKolB,QAAU,SAInBvJ,OAAA,WACE7b,KAAKulB,UAAYvlB,KAAKwlB,gBAClBxlB,KAAKolB,SACPplB,KAAKolB,QAAQrH,oBAMjB1S,mBAAA,WAAqB,IAAA5F,EAAAzF,KACnB6C,EAAaO,GAAGpD,KAAKgF,SA5MR,qBA4M+B,SAAApD,GAC1CA,EAAMhC,iBACNgC,EAAMokB,kBACNvgB,EAAKe,eAITsE,WAAA,SAAWnO,GAaT,OAZAA,EAAM0K,EAAAA,EAAAA,EAAA,GACDrH,KAAKimB,YAAYxc,SACjBzC,GAAYG,kBAAkBnH,KAAKgF,WACnCrI,GAGLF,EACEqI,GACAnI,EACAqD,KAAKimB,YAAYjc,aAGZrN,KAGT2oB,gBAAA,WACE,OAAOjd,GAAeiB,KAAKtJ,KAAKgF,SApNd,kBAoNuC,MAG3DkhB,cAAA,WACE,IAAMC,EAAiBnmB,KAAKgF,SAASlH,WACjCmb,EAnNiB,eAmOrB,OAbIkN,EAAezgB,UAAUE,SArOP,WAsOpBqT,EAzNgB,YA0NZjZ,KAAKqlB,MAAM3f,UAAUE,SApOF,yBAqOrBqT,EA1NiB,YA4NVkN,EAAezgB,UAAUE,SAzOX,aA0OvBqT,EA1NkB,cA2NTkN,EAAezgB,UAAUE,SA1OZ,YA2OtBqT,EA3NiB,aA4NRjZ,KAAKqlB,MAAM3f,UAAUE,SA3OP,yBA4OvBqT,EA/NsB,cAkOjBA,KAGTuM,cAAA,WACE,OAAOtlB,QAAQF,KAAKgF,SAASQ,QAAd,eAGjB4gB,WAAA,WAAa,IAAA9Z,EAAAtM,KACLwH,EAAS,GAef,MAbmC,mBAAxBxH,KAAK6K,QAAQrD,OACtBA,EAAO9E,GAAK,SAAA7D,GAMV,OALAA,EAAK+X,QAALvP,EAAAA,EAAA,GACKxI,EAAK+X,SACLtK,EAAKzB,QAAQrD,OAAO3I,EAAK+X,QAAStK,EAAKtH,WAAa,IAGlDnG,GAGT2I,EAAOA,OAASxH,KAAK6K,QAAQrD,OAGxBA,KAGTse,iBAAA,WACE,IAAMZ,EAAe,CACnBjM,UAAWjZ,KAAKkmB,gBAChB7K,UAAW,CACT7T,OAAQxH,KAAKomB,aACbjK,KAAM,CACJP,QAAS5b,KAAK6K,QAAQsR,MAExBuE,gBAAiB,CACfhI,kBAAmB1Y,KAAK6K,QAAQoa,YAYtC,MAN6B,WAAzBjlB,KAAK6K,QAAQ5M,UACfinB,EAAa7J,UAAUiJ,WAAa,CAClC1I,SAAS,IAIbvU,EAAAA,EAAA,GACK6d,GACAllB,KAAK6K,QAAQqa,iBAMbmB,kBAAP,SAAyBpsB,EAAS0C,GAChC,IAAIkC,EAAOI,EAAahF,EAlUX,eAyUb,GAJK4E,IACHA,EAAO,IAAIsmB,EAASlrB,EAHY,iBAAX0C,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,SAIFoJ,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACfmf,EAASkB,kBAAkBrmB,KAAMrD,SAI9BgpB,WAAP,SAAkB/jB,GAChB,IAAIA,GAhVmB,IAgVTA,EAAM8E,SACF,UAAf9E,EAAMmB,MApVG,QAoViBnB,EAAMhD,KAMnC,IAFA,IAAM0nB,EAAUje,GAAelJ,KA/TN,4BAiUhBsC,EAAI,EAAGC,EAAM4kB,EAAQ3kB,OAAQF,EAAIC,EAAKD,IAAK,CAClD,IAAM0N,EAASgW,EAASS,qBAAqBU,EAAQ7kB,IAC/C8kB,EAAUtnB,EAAaqnB,EAAQ7kB,GAlW1B,eAmWLiM,EAAgB,CACpBA,cAAe4Y,EAAQ7kB,IAOzB,GAJIG,GAAwB,UAAfA,EAAMmB,OACjB2K,EAAc8Y,WAAa5kB,GAGxB2kB,EAAL,CAIA,IAAME,EAAeF,EAAQlB,MAC7B,GAAKiB,EAAQ7kB,GAAGiE,UAAUE,SAzVR,QA6VlB,KAAIhE,IAA0B,UAAfA,EAAMmB,MACjB,kBAAkBtF,KAAKmE,EAAMgB,OAAOsK,UACpB,UAAftL,EAAMmB,MAhXD,QAgXqBnB,EAAMhD,MACjC6nB,EAAa7gB,SAAShE,EAAMgB,SAKhC,IADkBC,EAAaoB,QAAQkL,EA9W7B,mBA8WiDzB,GAC7C7N,iBAAd,CAMgD,IAAA8I,EAAhD,GAAI,iBAAkBpO,SAASgO,iBAC7BI,EAAA,IAAGH,OAAHxF,MAAA2F,EAAapO,SAASiE,KAAKkK,UACxB3L,SAAQ,SAAA0S,GAAI,OAAI5M,EAAaC,IAAI2M,EAAM,YAAa,MXxP5C,kBW2Pb6W,EAAQ7kB,GAAGgF,aAAa,gBAAiB,SAErC8f,EAAQnB,SACVmB,EAAQnB,QAAQpI,UAGlByJ,EAAa/gB,UAAUC,OAtXL,QAuXlB2gB,EAAQ7kB,GAAGiE,UAAUC,OAvXH,QAwXlB9C,EAAaoB,QAAQkL,EAjYT,qBAiY+BzB,SAIxCkY,qBAAP,SAA4B3rB,GAC1B,OAAOe,EAAuBf,IAAYA,EAAQ6D,cAG7C4oB,sBAAP,SAA6B9kB,GAQ3B,KAAI,kBAAkBnE,KAAKmE,EAAMgB,OAAOsK,SA1Z1B,UA2ZZtL,EAAMhD,KA5ZO,WA4ZegD,EAAMhD,MAxZjB,cAyZfgD,EAAMhD,KA1ZO,YA0ZmBgD,EAAMhD,KACtCgD,EAAMgB,OAAO4C,QAjYC,oBAkYfuf,GAAetnB,KAAKmE,EAAMhD,QAI7BgD,EAAMhC,iBACNgC,EAAMokB,mBAEFhmB,KAAKylB,WAAYzlB,KAAK0F,UAAUE,SApZZ,aAoZxB,CAIA,IAAMuJ,EAASgW,EAASS,qBAAqB5lB,MACvC0lB,EAAW1lB,KAAK0F,UAAUE,SAxZZ,QA0ZpB,GA7ae,WA6aXhE,EAAMhD,IAIR,OAHeoB,KAAKM,QAnZG,4BAmZ6BN,KAAOqI,GAAec,KAAKnJ,KAnZxD,4BAmZoF,IACpG+lB,aACPZ,EAASQ,aAIX,GAAKD,GAnbS,UAmbG9jB,EAAMhD,IAAvB,CAKA,IAAM+nB,EAAQte,GAAelJ,KA1ZF,8DA0Z+BgQ,GACvDvG,OAAOhL,GAEV,GAAK+oB,EAAMhlB,OAAX,CAIA,IAAIoK,EAAQ4a,EAAMvkB,QAAQR,EAAMgB,QA7bf,YA+bbhB,EAAMhD,KAAwBmN,EAAQ,GACxCA,IA/biB,cAkcfnK,EAAMhD,KAA0BmN,EAAQ4a,EAAMhlB,OAAS,GACzDoK,IAMF4a,EAFA5a,GAAmB,IAAXA,EAAe,EAAIA,GAEdga,cAxBXZ,EAASQ,iBA2BNxf,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EArdP,wDAmFb,MApFY,+CAwFZ,OAAOwP,uCAIP,OAAOO,SAvBLmb,GA2ZNtiB,EAAaO,GAAG7I,SA5cY,+BAYC,2BAgc2C4qB,GAASuB,uBACjF7jB,EAAaO,GAAG7I,SA7cY,+BAcN,iBA+b2C4qB,GAASuB,uBAC1E7jB,EAAaO,GAAG7I,SA/cU,6BA+csB4qB,GAASQ,YACzD9iB,EAAaO,GAAG7I,SA9cU,6BA8csB4qB,GAASQ,YACzD9iB,EAAaO,GAAG7I,SAjdU,6BAaG,4BAocyC,SAAUqH,GAC9EA,EAAMhC,iBACNgC,EAAMokB,kBACNb,GAASkB,kBAAkBrmB,KAAM,aAEnC6C,EACGO,GAAG7I,SAvdoB,6BAcE,kBAyc+B,SAAAP,GAAC,OAAIA,EAAEgsB,qBAElE,IAAMzlB,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQqgB,GAASpf,gBACtBxF,GAAEmC,GAAGoC,IAAMuB,YAAc8e,GACzB5kB,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACN+e,GAASpf,iBC3fpB,IAOM0D,GAAU,CACdmd,UAAU,EACVjd,UAAU,EACVoc,OAAO,EACP9V,MAAM,GAGFjG,GAAc,CAClB4c,SAAU,mBACVjd,SAAU,UACVoc,MAAO,UACP9V,KAAM,WAoCF4W,GAAAA,WACJ,SAAAA,EAAY5sB,EAAS0C,GACnBqD,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAKgF,SAAW/K,EAChB+F,KAAK8mB,QAAUze,GAAe9I,QAjBV,gBAiBmCtF,GACvD+F,KAAK+mB,UAAY,KACjB/mB,KAAKgnB,UAAW,EAChBhnB,KAAKinB,oBAAqB,EAC1BjnB,KAAKknB,sBAAuB,EAC5BlnB,KAAKqP,kBAAmB,EACxBrP,KAAKmnB,gBAAkB,EACvBloB,EAAahF,EA/DA,WA+DmB+F,iCAelCwG,OAAA,SAAOkH,GACL,OAAO1N,KAAKgnB,SAAWhnB,KAAKgQ,OAAShQ,KAAKiQ,KAAKvC,MAGjDuC,KAAA,SAAKvC,GAAe,IAAAjI,EAAAzF,KAClB,IAAIA,KAAKgnB,WAAYhnB,KAAKqP,iBAA1B,CAIIrP,KAAKgF,SAASU,UAAUE,SApDR,UAqDlB5F,KAAKqP,kBAAmB,GAG1B,IAAM+X,EAAYvkB,EAAaoB,QAAQjE,KAAKgF,SArEhC,gBAqEsD,CAChE0I,cAAAA,IAGE1N,KAAKgnB,UAAYI,EAAUvnB,mBAI/BG,KAAKgnB,UAAW,EAEhBhnB,KAAKqnB,kBACLrnB,KAAKsnB,gBAELtnB,KAAKunB,gBAELvnB,KAAKwnB,kBACLxnB,KAAKynB,kBAEL5kB,EAAaO,GAAGpD,KAAKgF,SAnFA,yBAgBK,0BAsExB,SAAApD,GAAK,OAAI6D,EAAKuK,KAAKpO,MAGrBiB,EAAaO,GAAGpD,KAAK8mB,QAtFI,8BAsF8B,WACrDjkB,EAAaQ,IAAIoC,EAAKT,SAxFD,4BAwFkC,SAAApD,GACjDA,EAAMgB,SAAW6C,EAAKT,WACxBS,EAAKyhB,sBAAuB,SAKlClnB,KAAK0nB,eAAc,WAAA,OAAMjiB,EAAKkiB,aAAaja,WAG7CsC,KAAA,SAAKpO,GAAO,IAAA0K,EAAAtM,KAKV,IAJI4B,GACFA,EAAMhC,iBAGHI,KAAKgnB,WAAYhnB,KAAKqP,oBAITxM,EAAaoB,QAAQjE,KAAKgF,SApHhC,iBAsHEnF,iBAAd,CAIAG,KAAKgnB,UAAW,EAChB,IAAMY,EAAa5nB,KAAKgF,SAASU,UAAUE,SA3GvB,QA2HpB,GAdIgiB,IACF5nB,KAAKqP,kBAAmB,GAG1BrP,KAAKwnB,kBACLxnB,KAAKynB,kBAEL5kB,EAAaC,IAAIvI,SA/HF,oBAiIfyF,KAAKgF,SAASU,UAAUC,OArHJ,QAuHpB9C,EAAaC,IAAI9C,KAAKgF,SAjID,0BAkIrBnC,EAAaC,IAAI9C,KAAK8mB,QA/HG,8BAiIrBc,EAAY,CACd,IAAMvsB,EAAqBJ,EAAiC+E,KAAKgF,UAEjEnC,EAAaQ,IAAIrD,KAAKgF,SZtLL,iBYsL+B,SAAApD,GAAK,OAAI0K,EAAKub,WAAWjmB,MACzE3F,EAAqB+D,KAAKgF,SAAU3J,QAEpC2E,KAAK6nB,iBAITtiB,QAAA,WACE,CAACpK,OAAQ6E,KAAKgF,SAAUhF,KAAK8mB,SAC1B/pB,SAAQ,SAAA+qB,GAAW,OAAIjlB,EAAaC,IAAIglB,EAzKhC,gBAgLXjlB,EAAaC,IAAIvI,SAzJF,oBA2Jf0E,EAAgBe,KAAKgF,SAnLR,YAqLbhF,KAAK6K,QAAU,KACf7K,KAAKgF,SAAW,KAChBhF,KAAK8mB,QAAU,KACf9mB,KAAK+mB,UAAY,KACjB/mB,KAAKgnB,SAAW,KAChBhnB,KAAKinB,mBAAqB,KAC1BjnB,KAAKknB,qBAAuB,KAC5BlnB,KAAKqP,iBAAmB,KACxBrP,KAAKmnB,gBAAkB,QAGzBY,aAAA,WACE/nB,KAAKunB,mBAKPzc,WAAA,SAAWnO,GAMT,OALAA,EAAM0K,EAAAA,EAAA,GACDoC,IACA9M,GAELF,EA7MS,QA6MaE,EAAQqN,IACvBrN,KAGTgrB,aAAA,SAAaja,GAAe,IAAAjB,EAAAzM,KACpB4nB,EAAa5nB,KAAKgF,SAASU,UAAUE,SA7KvB,QA8KdoiB,EAAY3f,GAAe9I,QAzKT,cAyKsCS,KAAK8mB,SAE9D9mB,KAAKgF,SAASlH,YACfkC,KAAKgF,SAASlH,WAAW9B,WAAagN,KAAKC,cAE7C1O,SAASiE,KAAKypB,YAAYjoB,KAAKgF,UAGjChF,KAAKgF,SAASnH,MAAMI,QAAU,QAC9B+B,KAAKgF,SAAS3E,gBAAgB,eAC9BL,KAAKgF,SAASyB,aAAa,cAAc,GACzCzG,KAAKgF,SAASyB,aAAa,OAAQ,UACnCzG,KAAKgF,SAAS4C,UAAY,EAEtBogB,IACFA,EAAUpgB,UAAY,GAGpBggB,GACFxpB,EAAO4B,KAAKgF,UAGdhF,KAAKgF,SAASU,UAAU0C,IAnMJ,QAqMhBpI,KAAK6K,QAAQkb,OACf/lB,KAAKkoB,gBAGP,IAAMC,EAAqB,WACrB1b,EAAK5B,QAAQkb,OACftZ,EAAKzH,SAAS+gB,QAGhBtZ,EAAK4C,kBAAmB,EACxBxM,EAAaoB,QAAQwI,EAAKzH,SA5Nf,iBA4NsC,CAC/C0I,cAAAA,KAIJ,GAAIka,EAAY,CACd,IAAMvsB,EAAqBJ,EAAiC+E,KAAK8mB,SAEjEjkB,EAAaQ,IAAIrD,KAAK8mB,QZhRL,gBYgR8BqB,GAC/ClsB,EAAqB+D,KAAK8mB,QAASzrB,QAEnC8sB,OAIJD,cAAA,WAAgB,IAAA9Z,EAAApO,KACd6C,EAAaC,IAAIvI,SA3OF,oBA4OfsI,EAAaO,GAAG7I,SA5OD,oBA4O0B,SAAAqH,GACnCrH,WAAaqH,EAAMgB,QACnBwL,EAAKpJ,WAAapD,EAAMgB,QACvBwL,EAAKpJ,SAASY,SAAShE,EAAMgB,SAChCwL,EAAKpJ,SAAS+gB,cAKpByB,gBAAA,WAAkB,IAAAY,EAAApoB,KACZA,KAAKgnB,SACPnkB,EAAaO,GAAGpD,KAAKgF,SApPA,4BAoPiC,SAAApD,GAChDwmB,EAAKvd,QAAQlB,UA7QN,WA6QkB/H,EAAMhD,KACjCgD,EAAMhC,iBACNwoB,EAAKpY,QACKoY,EAAKvd,QAAQlB,UAhRd,WAgR0B/H,EAAMhD,KACzCwpB,EAAKC,gCAITxlB,EAAaC,IAAI9C,KAAKgF,SA7PD,+BAiQzByiB,gBAAA,WAAkB,IAAAa,EAAAtoB,KACZA,KAAKgnB,SACPnkB,EAAaO,GAAGjI,OArQJ,mBAqQ0B,WAAA,OAAMmtB,EAAKf,mBAEjD1kB,EAAaC,IAAI3H,OAvQL,sBA2QhB0sB,WAAA,WAAa,IAAAU,EAAAvoB,KACXA,KAAKgF,SAASnH,MAAMI,QAAU,OAC9B+B,KAAKgF,SAASyB,aAAa,eAAe,GAC1CzG,KAAKgF,SAAS3E,gBAAgB,cAC9BL,KAAKgF,SAAS3E,gBAAgB,QAC9BL,KAAKqP,kBAAmB,EACxBrP,KAAK0nB,eAAc,WACjBntB,SAASiE,KAAKkH,UAAUC,OAzQN,cA0QlB4iB,EAAKC,oBACLD,EAAKE,kBACL5lB,EAAaoB,QAAQskB,EAAKvjB,SAzRd,yBA6RhB0jB,gBAAA,WACE1oB,KAAK+mB,UAAUjpB,WAAWgI,YAAY9F,KAAK+mB,WAC3C/mB,KAAK+mB,UAAY,QAGnBW,cAAA,SAAchK,GAAU,IAAAiL,EAAA3oB,KAChB4oB,EAAU5oB,KAAKgF,SAASU,UAAUE,SArRpB,QAAA,OAuRlB,GAEF,GAAI5F,KAAKgnB,UAAYhnB,KAAK6K,QAAQ+b,SAAU,CA6B1C,GA5BA5mB,KAAK+mB,UAAYxsB,SAASoF,cAAc,OACxCK,KAAK+mB,UAAU5e,UA7RO,iBA+RlBygB,GACF5oB,KAAK+mB,UAAUrhB,UAAU0C,IAAIwgB,GAG/BruB,SAASiE,KAAKypB,YAAYjoB,KAAK+mB,WAE/BlkB,EAAaO,GAAGpD,KAAKgF,SA5SF,0BA4SiC,SAAApD,GAC9C+mB,EAAKzB,qBACPyB,EAAKzB,sBAAuB,EAI1BtlB,EAAMgB,SAAWhB,EAAMinB,eAI3BF,EAAKN,gCAGHO,GACFxqB,EAAO4B,KAAK+mB,WAGd/mB,KAAK+mB,UAAUrhB,UAAU0C,IAnTP,SAqTbwgB,EAEH,YADAlL,IAIF,IAAMoL,EAA6B7tB,EAAiC+E,KAAK+mB,WAEzElkB,EAAaQ,IAAIrD,KAAK+mB,UZrXL,gBYqXgCrJ,GACjDzhB,EAAqB+D,KAAK+mB,UAAW+B,QAChC,IAAK9oB,KAAKgnB,UAAYhnB,KAAK+mB,UAAW,CAC3C/mB,KAAK+mB,UAAUrhB,UAAUC,OA/TP,QAiUlB,IAAMojB,EAAiB,WACrBJ,EAAKD,kBACLhL,KAGF,GAAI1d,KAAKgF,SAASU,UAAUE,SAvUV,QAuUqC,CACrD,IAAMkjB,EAA6B7tB,EAAiC+E,KAAK+mB,WACzElkB,EAAaQ,IAAIrD,KAAK+mB,UZjYP,gBYiYkCgC,GACjD9sB,EAAqB+D,KAAK+mB,UAAW+B,QAErCC,SAGFrL,OAIJ2K,2BAAA,WAA6B,IAAAW,EAAAhpB,KAC3B,GAA8B,WAA1BA,KAAK6K,QAAQ+b,SAAuB,CAEtC,GADkB/jB,EAAaoB,QAAQjE,KAAKgF,SApWxB,0BAqWNnF,iBACZ,OAGFG,KAAKgF,SAASU,UAAU0C,IAxVJ,gBAyVpB,IAAM6gB,EAA0BhuB,EAAiC+E,KAAKgF,UACtEnC,EAAaQ,IAAIrD,KAAKgF,SZpZL,iBYoZ+B,WAC9CgkB,EAAKhkB,SAASU,UAAUC,OA3VN,mBA6VpB1J,EAAqB+D,KAAKgF,SAAUikB,GACpCjpB,KAAKgF,SAAS+gB,aAEd/lB,KAAKgQ,UAQTuX,cAAA,WACE,IAAM2B,EACJlpB,KAAKgF,SAASmkB,aAAe5uB,SAASgO,gBAAgByO,cAEnDhX,KAAKinB,oBAAsBiC,IAC9BlpB,KAAKgF,SAASnH,MAAMurB,YAAiBppB,KAAKmnB,gBAA1C,MAGEnnB,KAAKinB,qBAAuBiC,IAC9BlpB,KAAKgF,SAASnH,MAAMwrB,aAAkBrpB,KAAKmnB,gBAA3C,SAIJqB,kBAAA,WACExoB,KAAKgF,SAASnH,MAAMurB,YAAc,GAClCppB,KAAKgF,SAASnH,MAAMwrB,aAAe,MAGrChC,gBAAA,WACE,IAAM5f,EAAOlN,SAASiE,KAAKkJ,wBAC3B1H,KAAKinB,mBAAqB7sB,KAAK2nB,MAAMta,EAAKI,KAAOJ,EAAKwN,OAAS9Z,OAAO8c,WACtEjY,KAAKmnB,gBAAkBnnB,KAAKspB,wBAG9BhC,cAAA,WAAgB,IAAAiC,EAAAvpB,KACd,GAAIA,KAAKinB,mBAAoB,CAK3B5e,GAAelJ,KAhYU,qDAiYtBpC,SAAQ,SAAA9C,GACP,IAAMuvB,EAAgBvvB,EAAQ4D,MAAMwrB,aAC9BI,EAAoBtuB,OAAOC,iBAAiBnB,GAAS,iBAC3D+M,GAAYC,iBAAiBhN,EAAS,gBAAiBuvB,GACvDvvB,EAAQ4D,MAAMwrB,aAAkB7tB,WAAWiuB,GAAqBF,EAAKpC,gBAArE,QAIJ9e,GAAelJ,KAxYW,eAyYvBpC,SAAQ,SAAA9C,GACP,IAAMyvB,EAAezvB,EAAQ4D,MAAMuc,YAC7BuP,EAAmBxuB,OAAOC,iBAAiBnB,GAAS,gBAC1D+M,GAAYC,iBAAiBhN,EAAS,eAAgByvB,GACtDzvB,EAAQ4D,MAAMuc,YAAiB5e,WAAWmuB,GAAoBJ,EAAKpC,gBAAnE,QAIJ,IAAMqC,EAAgBjvB,SAASiE,KAAKX,MAAMwrB,aACpCI,EAAoBtuB,OAAOC,iBAAiBb,SAASiE,MAAM,iBAEjEwI,GAAYC,iBAAiB1M,SAASiE,KAAM,gBAAiBgrB,GAC7DjvB,SAASiE,KAAKX,MAAMwrB,aAAkB7tB,WAAWiuB,GAAqBzpB,KAAKmnB,gBAA3E,KAGF5sB,SAASiE,KAAKkH,UAAU0C,IAlaJ,iBAqatBqgB,gBAAA,WAEEpgB,GAAelJ,KA9ZY,qDA+ZxBpC,SAAQ,SAAA9C,GACP,IAAMwe,EAAUzR,GAAYO,iBAAiBtN,EAAS,sBAC/B,IAAZwe,IACTzR,GAAYE,oBAAoBjN,EAAS,iBACzCA,EAAQ4D,MAAMwrB,aAAe5Q,MAKnCpQ,GAAelJ,KAvaa,eAwazBpC,SAAQ,SAAA9C,GACP,IAAM2vB,EAAS5iB,GAAYO,iBAAiBtN,EAAS,qBAC/B,IAAX2vB,IACT5iB,GAAYE,oBAAoBjN,EAAS,gBACzCA,EAAQ4D,MAAMuc,YAAcwP,MAKlC,IAAMnR,EAAUzR,GAAYO,iBAAiBhN,SAASiE,KAAM,sBACrC,IAAZia,EACTle,SAASiE,KAAKX,MAAMwrB,aAAe,IAEnCriB,GAAYE,oBAAoB3M,SAASiE,KAAM,iBAC/CjE,SAASiE,KAAKX,MAAMwrB,aAAe5Q,MAIvC6Q,mBAAA,WACE,IAAMO,EAAYtvB,SAASoF,cAAc,OACzCkqB,EAAU1hB,UAxcwB,0BAyclC5N,SAASiE,KAAKypB,YAAY4B,GAC1B,IAAMC,EAAiBD,EAAUniB,wBAAwBkO,MAAQiU,EAAU9S,YAE3E,OADAxc,SAASiE,KAAKsH,YAAY+jB,GACnBC,KAKF/jB,gBAAP,SAAuBpJ,EAAQ+Q,GAC7B,OAAO1N,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KAnfb,YAofL6K,EAAOxD,EAAAA,EAAAA,EAAA,GACRoC,IACAzC,GAAYG,kBAAkBnH,OACZ,iBAAXrD,GAAuBA,EAASA,EAAS,IAOrD,GAJKkC,IACHA,EAAO,IAAIgoB,EAAM7mB,KAAM6K,IAGH,iBAAXlO,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,GAAQ+Q,QACJ7C,EAAQoF,MACjBpR,EAAKoR,KAAKvC,SAKTvH,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EA3gBP,qDAqEb,MAtEY,+CA0EZ,OAAOwP,SArBLod,GAieNhkB,EAAaO,GAAG7I,SAvfU,0BAWG,yBA4eyC,SAAUqH,GAAO,IAAAmoB,EAAA/pB,KAC/E4C,EAAS5H,EAAuBgF,MAEjB,MAAjBA,KAAKkN,SAAoC,SAAjBlN,KAAKkN,SAC/BtL,EAAMhC,iBAGRiD,EAAaQ,IAAIT,EAtgBH,iBAsgBuB,SAAAwkB,GAC/BA,EAAUvnB,kBAKdgD,EAAaQ,IAAIT,EA7gBH,mBA6gByB,WACjChF,EAAUmsB,IACZA,EAAKhE,cAKX,IAAIlnB,EAAOI,EAAa2D,EAziBT,YA0iBf,IAAK/D,EAAM,CACT,IAAMlC,EAAM0K,EAAAA,EAAA,GACPL,GAAYG,kBAAkBvE,IAC9BoE,GAAYG,kBAAkBnH,OAGnCnB,EAAO,IAAIgoB,GAAMjkB,EAAQjG,GAG3BkC,EAAKoR,KAAKjQ,SAGZ,IAAMO,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAF,MAC3BnC,GAAEmC,GAAF,MAAamkB,GAAM9gB,gBACnBxF,GAAEmC,GAAF,MAAW2D,YAAcwgB,GACzBtmB,GAAEmC,GAAF,MAAW4D,WAAa,WAEtB,OADA/F,GAAEmC,GAAF,MAAa0D,GACNygB,GAAM9gB,iBC5lBjB,IAAMikB,GAAW,CACf,aACA,OACA,OACA,WACA,WACA,SACA,MACA,cAUIC,GAAmB,8DAOnBC,GAAmB,qIAyBZC,GAAmB,CAE9BC,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7B5Q,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BF,KAAM,GACNG,EAAG,GACH4Q,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJvpB,EAAG,GACHwpB,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAGC,SAASC,GAAaC,EAAYC,EAAWC,GAAY,IAAA3jB,EAC9D,IAAKyjB,EAAWpqB,OACd,OAAOoqB,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAQpB,IALA,IACMG,GADY,IAAI/wB,OAAOgxB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgBxvB,OAAOC,KAAKkvB,GAC5BM,GAAWhkB,EAAA,IAAGE,OAAHxF,MAAAsF,EAAa4jB,EAAgB1tB,KAAKc,iBAAiB,MAZNitB,EAAA,SAcrD9qB,EAAOC,GAd8C,IAAAiH,EAetD2P,EAAKgU,EAAS7qB,GACd+qB,EAASlU,EAAGnG,SAAS5U,cAE3B,IAAuC,IAAnC8uB,EAAcjqB,QAAQoqB,GAGxB,OAFAlU,EAAGxa,WAAWgI,YAAYwS,GAE1B,WAGF,IAAMmU,GAAgB9jB,EAAA,IAAGH,OAAHxF,MAAA2F,EAAa2P,EAAGlR,YAChCslB,EAAwB,GAAGlkB,OAAOwjB,EAAU,MAAQ,GAAIA,EAAUQ,IAAW,IAEnFC,EAAc1vB,SAAQ,SAAA4vB,IApFD,SAACA,EAAMC,GAC9B,IAAMC,EAAWF,EAAKxa,SAAS5U,cAE/B,IAAgD,IAA5CqvB,EAAqBxqB,QAAQyqB,GAC/B,OAAoC,IAAhC7C,GAAS5nB,QAAQyqB,IACZ3sB,QAAQysB,EAAKG,UAAUxvB,MAAM2sB,KAAqB0C,EAAKG,UAAUxvB,MAAM4sB,KASlF,IAHA,IAAM6C,EAASH,EAAqBhkB,QAAO,SAAAokB,GAAS,OAAIA,aAAqBxvB,UAGpEiE,EAAI,EAAGC,EAAMqrB,EAAOprB,OAAQF,EAAIC,EAAKD,IAC5C,GAAIorB,EAASvvB,MAAMyvB,EAAOtrB,IACxB,OAAO,EAIX,OAAO,GAiEEwrB,CAAiBN,EAAMD,IAC1BpU,EAAGjY,gBAAgBssB,EAAKxa,cAfrB1Q,EAAI,EAAGC,EAAM4qB,EAAS3qB,OAAQF,EAAIC,EAAKD,IAAK8qB,EAA5C9qB,GAoBT,OAAOyqB,EAAgB1tB,KAAK0uB,UC3F9B,IAAMpoB,GAAO,UAKPqoB,GAAqB,IAAI3vB,OAAJ,wBAAyC,KAC9D4vB,GAAwB,CAAC,WAAY,YAAa,cAElDpjB,GAAc,CAClBqjB,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPtpB,QAAS,SACTupB,MAAO,kBACP7Y,KAAM,UACNja,SAAU,mBACVue,UAAW,oBACXzR,OAAQ,2BACR4I,UAAW,2BACXqd,kBAAmB,iBACnBxI,SAAU,mBACVyI,SAAU,UACVzB,WAAY,kBACZD,UAAW,SACX9G,aAAc,iBAGVyI,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAO,QACPC,OAAQ,SACRC,KAAM,QAGFvkB,GAAU,CACd4jB,WAAW,EACXC,SAAU,+GAGVrpB,QAAS,cACTspB,MAAO,GACPC,MAAO,EACP7Y,MAAM,EACNja,UAAU,EACVue,UAAW,MACXzR,OAAQ,EACR4I,WAAW,EACXqd,kBAAmB,OACnBxI,SAAU,eACVyI,UAAU,EACVzB,WAAY,KACZD,UAAW7B,GACXjF,aAAc,MAGVrpB,GAAQ,CACZoyB,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAuBNC,GAAAA,WACJ,SAAAA,EAAY10B,EAAS0C,GACnB,QAAsB,IAAX+nB,GACT,MAAM,IAAI5V,UAAU,kEAItB9O,KAAK4uB,YAAa,EAClB5uB,KAAK6uB,SAAW,EAChB7uB,KAAK8uB,YAAc,GACnB9uB,KAAK+uB,eAAiB,GACtB/uB,KAAKolB,QAAU,KAGfplB,KAAK/F,QAAUA,EACf+F,KAAKrD,OAASqD,KAAK8K,WAAWnO,GAC9BqD,KAAKgvB,IAAM,KAEXhvB,KAAKivB,gBACLhwB,EAAahF,EAAS+F,KAAKimB,YAAYiJ,SAAUlvB,iCAmCnDmvB,OAAA,WACEnvB,KAAK4uB,YAAa,KAGpBQ,QAAA,WACEpvB,KAAK4uB,YAAa,KAGpBS,cAAA,WACErvB,KAAK4uB,YAAc5uB,KAAK4uB,cAG1BpoB,OAAA,SAAO5E,GACL,GAAK5B,KAAK4uB,WAIV,GAAIhtB,EAAO,CACT,IAAM0tB,EAAUtvB,KAAKimB,YAAYiJ,SAC7B3I,EAAUtnB,EAAa2C,EAAMgB,OAAQ0sB,GAEpC/I,IACHA,EAAU,IAAIvmB,KAAKimB,YACjBrkB,EAAMgB,OACN5C,KAAKuvB,sBAEPtwB,EAAa2C,EAAMgB,OAAQ0sB,EAAS/I,IAGtCA,EAAQwI,eAAeS,OAASjJ,EAAQwI,eAAeS,MAEnDjJ,EAAQkJ,uBACVlJ,EAAQmJ,OAAO,KAAMnJ,GAErBA,EAAQoJ,OAAO,KAAMpJ,OAElB,CACL,GAAIvmB,KAAK4vB,gBAAgBlqB,UAAUE,SA7GjB,QA+GhB,YADA5F,KAAK2vB,OAAO,KAAM3vB,MAIpBA,KAAK0vB,OAAO,KAAM1vB,UAItBuF,QAAA,WACEwH,aAAa/M,KAAK6uB,UAElB5vB,EAAgBe,KAAK/F,QAAS+F,KAAKimB,YAAYiJ,UAE/CrsB,EAAaC,IAAI9C,KAAK/F,QAAS+F,KAAKimB,YAAYzc,WAChD3G,EAAaC,IAAI9C,KAAK/F,QAAQuL,QAAb,UAA8C,gBAAiBxF,KAAK6vB,mBAEjF7vB,KAAKgvB,KACPhvB,KAAKgvB,IAAIlxB,WAAWgI,YAAY9F,KAAKgvB,KAGvChvB,KAAK4uB,WAAa,KAClB5uB,KAAK6uB,SAAW,KAChB7uB,KAAK8uB,YAAc,KACnB9uB,KAAK+uB,eAAiB,KAClB/uB,KAAKolB,SACPplB,KAAKolB,QAAQpI,UAGfhd,KAAKolB,QAAU,KACfplB,KAAK/F,QAAU,KACf+F,KAAKrD,OAAS,KACdqD,KAAKgvB,IAAM,QAGb/e,KAAA,WAAO,IAAAxK,EAAAzF,KACL,GAAmC,SAA/BA,KAAK/F,QAAQ4D,MAAMI,QACrB,MAAM,IAAIP,MAAM,uCAGlB,GAAIsC,KAAK8vB,iBAAmB9vB,KAAK4uB,WAAY,CAC3C,IAAMxH,EAAYvkB,EAAaoB,QAAQjE,KAAK/F,QAAS+F,KAAKimB,YAAYpqB,MAAMsyB,MACtE4B,Ed9GW,SAAjBC,EAAiB/1B,GACrB,IAAKM,SAASgO,gBAAgB0nB,aAC5B,OAAO,KAIT,GAAmC,mBAAxBh2B,EAAQi2B,YAA4B,CAC7C,IAAMC,EAAOl2B,EAAQi2B,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIl2B,aAAmBm2B,WACdn2B,EAIJA,EAAQ6D,WAINkyB,EAAe/1B,EAAQ6D,YAHrB,Kc6FckyB,CAAehwB,KAAK/F,SACjCo2B,EAA4B,OAAfN,EACjB/vB,KAAK/F,QAAQ+X,cAAczJ,gBAAgB3C,SAAS5F,KAAK/F,SACzD81B,EAAWnqB,SAAS5F,KAAK/F,SAE3B,GAAImtB,EAAUvnB,mBAAqBwwB,EACjC,OAGF,IAAMrB,EAAMhvB,KAAK4vB,gBACXU,EAAQp2B,EAAO8F,KAAKimB,YAAYnhB,MAEtCkqB,EAAIvoB,aAAa,KAAM6pB,GACvBtwB,KAAK/F,QAAQwM,aAAa,mBAAoB6pB,GAE9CtwB,KAAKuwB,aAEDvwB,KAAKrD,OAAO0wB,WACd2B,EAAItpB,UAAU0C,IA3KE,QA8KlB,IAAM6Q,EAA6C,mBAA1BjZ,KAAKrD,OAAOsc,UACnCjZ,KAAKrD,OAAOsc,UAAU5b,KAAK2C,KAAMgvB,EAAKhvB,KAAK/F,SAC3C+F,KAAKrD,OAAOsc,UAERuX,EAAaxwB,KAAKywB,eAAexX,GACvCjZ,KAAK0wB,oBAAoBF,GAEzB,IAiBgDloB,EAjB1C8H,EAAYpQ,KAAK2wB,gBAiBvB,GAhBA1xB,EAAa+vB,EAAKhvB,KAAKimB,YAAYiJ,SAAUlvB,MAExCA,KAAK/F,QAAQ+X,cAAczJ,gBAAgB3C,SAAS5F,KAAKgvB,MAC5D5e,EAAU6X,YAAY+G,GAGxBnsB,EAAaoB,QAAQjE,KAAK/F,QAAS+F,KAAKimB,YAAYpqB,MAAMwyB,UAE1DruB,KAAKolB,QAAU,IAAIV,GAAO1kB,KAAK/F,QAAS+0B,EAAKhvB,KAAK8lB,iBAAiB0K,IAEnExB,EAAItpB,UAAU0C,IA9LI,QAoMd,iBAAkB7N,SAASgO,iBAC7BD,EAAA,IAAGE,OAAHxF,MAAAsF,EAAa/N,SAASiE,KAAKkK,UAAU3L,SAAQ,SAAA9C,GAC3C4I,EAAaO,GAAGnJ,EAAS,adtIhB,kBc0Ib,IAAM22B,EAAW,WACXnrB,EAAK9I,OAAO0wB,WACd5nB,EAAKorB,iBAGP,IAAMC,EAAiBrrB,EAAKqpB,YAC5BrpB,EAAKqpB,YAAc,KAEnBjsB,EAAaoB,QAAQwB,EAAKxL,QAASwL,EAAKwgB,YAAYpqB,MAAMuyB,OA/M1C,QAiNZ0C,GACFrrB,EAAKkqB,OAAO,KAAMlqB,IAItB,GAAIzF,KAAKgvB,IAAItpB,UAAUE,SA3NL,QA2NgC,CAChD,IAAMvK,EAAqBJ,EAAiC+E,KAAKgvB,KACjEnsB,EAAaQ,IAAIrD,KAAKgvB,Id3TP,gBc2T4B4B,GAC3C30B,EAAqB+D,KAAKgvB,IAAK3zB,QAE/Bu1B,QAKN5gB,KAAA,WAAO,IAAA1D,EAAAtM,KACCgvB,EAAMhvB,KAAK4vB,gBACXgB,EAAW,WAnOI,SAoOftkB,EAAKwiB,aAAoCE,EAAIlxB,YAC/CkxB,EAAIlxB,WAAWgI,YAAYkpB,GAG7B1iB,EAAKykB,iBACLzkB,EAAKrS,QAAQoG,gBAAgB,oBAC7BwC,EAAaoB,QAAQqI,EAAKrS,QAASqS,EAAK2Z,YAAYpqB,MAAMqyB,QAC1D5hB,EAAK8Y,QAAQpI,WAIf,IADkBna,EAAaoB,QAAQjE,KAAK/F,QAAS+F,KAAKimB,YAAYpqB,MAAMoyB,MAC9DpuB,iBAAd,CAQgD,IAAA8I,EAAhD,GAJAqmB,EAAItpB,UAAUC,OArPM,QAyPhB,iBAAkBpL,SAASgO,iBAC7BI,EAAA,IAAGH,OAAHxF,MAAA2F,EAAapO,SAASiE,KAAKkK,UACxB3L,SAAQ,SAAA9C,GAAO,OAAI4I,EAAaC,IAAI7I,EAAS,YAAakE,MAO/D,GAJA6B,KAAK+uB,eAAL,OAAqC,EACrC/uB,KAAK+uB,eAAL,OAAqC,EACrC/uB,KAAK+uB,eAAL,OAAqC,EAEjC/uB,KAAKgvB,IAAItpB,UAAUE,SApQH,QAoQ8B,CAChD,IAAMvK,EAAqBJ,EAAiC+zB,GAE5DnsB,EAAaQ,IAAI2rB,EdrWA,gBcqWqB4B,GACtC30B,EAAqB+yB,EAAK3zB,QAE1Bu1B,IAGF5wB,KAAK8uB,YAAc,OAGrBjT,OAAA,WACuB,OAAjB7b,KAAKolB,SACPplB,KAAKolB,QAAQrH,oBAMjB+R,cAAA,WACE,OAAO5vB,QAAQF,KAAKgxB,eAGtBpB,cAAA,WACE,GAAI5vB,KAAKgvB,IACP,OAAOhvB,KAAKgvB,IAGd,IAAM/0B,EAAUM,SAASoF,cAAc,OAIvC,OAHA1F,EAAQizB,UAAYltB,KAAKrD,OAAO2wB,SAEhCttB,KAAKgvB,IAAM/0B,EAAQyO,SAAS,GACrB1I,KAAKgvB,OAGduB,WAAA,WACE,IAAMvB,EAAMhvB,KAAK4vB,gBACjB5vB,KAAKixB,kBAAkB5oB,GAAe9I,QAnSX,iBAmS2CyvB,GAAMhvB,KAAKgxB,YACjFhC,EAAItpB,UAAUC,OA3SM,OAEA,WA4StBsrB,kBAAA,SAAkBh3B,EAASi3B,GACzB,GAAgB,OAAZj3B,EAIJ,MAAuB,iBAAZi3B,GAAwBp1B,EAAUo1B,IACvCA,EAAQpgB,SACVogB,EAAUA,EAAQ,SAIhBlxB,KAAKrD,OAAOgY,KACVuc,EAAQpzB,aAAe7D,IACzBA,EAAQizB,UAAY,GACpBjzB,EAAQguB,YAAYiJ,IAGtBj3B,EAAQk3B,YAAcD,EAAQC,mBAM9BnxB,KAAKrD,OAAOgY,MACV3U,KAAKrD,OAAO+wB,WACdwD,EAAUpF,GAAaoF,EAASlxB,KAAKrD,OAAOqvB,UAAWhsB,KAAKrD,OAAOsvB,aAGrEhyB,EAAQizB,UAAYgE,GAEpBj3B,EAAQk3B,YAAcD,MAI1BF,SAAA,WACE,IAAIzD,EAAQvtB,KAAK/F,QAAQU,aAAa,uBAQtC,OANK4yB,IACHA,EAAqC,mBAAtBvtB,KAAKrD,OAAO4wB,MACzBvtB,KAAKrD,OAAO4wB,MAAMlwB,KAAK2C,KAAK/F,SAC5B+F,KAAKrD,OAAO4wB,OAGTA,KAKTzH,iBAAA,SAAiB0K,GAAY,IAAA/jB,EAAAzM,KAuB3B,OAAAqH,EAAAA,EAAA,GAtBwB,CACtB4R,UAAWuX,EACXnV,UAAW,CACT7T,OAAQxH,KAAKomB,aACbjK,KAAM,CACJ+F,SAAUliB,KAAKrD,OAAO8wB,mBAExBpM,MAAO,CACLpnB,QAAO,IAAM+F,KAAKimB,YAAYnhB,KAAvB,UAET4b,gBAAiB,CACfhI,kBAAmB1Y,KAAKrD,OAAOsoB,WAGnC1I,SAAU,SAAA1d,GACJA,EAAKud,oBAAsBvd,EAAKoa,WAClCxM,EAAK2kB,6BAA6BvyB,IAGtCyd,SAAU,SAAAzd,GAAI,OAAI4N,EAAK2kB,6BAA6BvyB,MAKjDmB,KAAKrD,OAAOuoB,iBAInBwL,oBAAA,SAAoBF,GAClBxwB,KAAK4vB,gBAAgBlqB,UAAU0C,IAAOipB,cAAgBb,MAGxDpK,WAAA,WAAa,IAAAhY,EAAApO,KACLwH,EAAS,GAef,MAbkC,mBAAvBxH,KAAKrD,OAAO6K,OACrBA,EAAO9E,GAAK,SAAA7D,GAMV,OALAA,EAAK+X,QAALvP,EAAAA,EAAA,GACKxI,EAAK+X,SACLxI,EAAKzR,OAAO6K,OAAO3I,EAAK+X,QAASxI,EAAKnU,UAAY,IAGhD4E,GAGT2I,EAAOA,OAASxH,KAAKrD,OAAO6K,OAGvBA,KAGTmpB,cAAA,WACE,OAA8B,IAA1B3wB,KAAKrD,OAAOyT,UACP7V,SAASiE,KAGd1C,EAAUkE,KAAKrD,OAAOyT,WACjBpQ,KAAKrD,OAAOyT,UAGd/H,GAAe9I,QAAQS,KAAKrD,OAAOyT,cAG5CqgB,eAAA,SAAexX,GACb,OAAO0U,GAAc1U,EAAUtb,kBAGjCsxB,cAAA,WAAgB,IAAA7G,EAAApoB,KACGA,KAAKrD,OAAOsH,QAAQvI,MAAM,KAElCqB,SAAQ,SAAAkH,GACf,GAAgB,UAAZA,EACFpB,EAAaO,GAAGglB,EAAKnuB,QACnBmuB,EAAKnC,YAAYpqB,MAAMyyB,MACvBlG,EAAKzrB,OAAOjC,UACZ,SAAAkH,GAAK,OAAIwmB,EAAK5hB,OAAO5E,WAElB,GAhaU,WAgaNqC,EAA4B,CACrC,IAAMqtB,EApaQ,UAoaErtB,EACdmkB,EAAKnC,YAAYpqB,MAAM4yB,WACvBrG,EAAKnC,YAAYpqB,MAAM0yB,QACnBgD,EAvaQ,UAuaGttB,EACfmkB,EAAKnC,YAAYpqB,MAAM6yB,WACvBtG,EAAKnC,YAAYpqB,MAAM2yB,SAEzB3rB,EAAaO,GAAGglB,EAAKnuB,QACnBq3B,EACAlJ,EAAKzrB,OAAOjC,UACZ,SAAAkH,GAAK,OAAIwmB,EAAKsH,OAAO9tB,MAEvBiB,EAAaO,GAAGglB,EAAKnuB,QACnBs3B,EACAnJ,EAAKzrB,OAAOjC,UACZ,SAAAkH,GAAK,OAAIwmB,EAAKuH,OAAO/tB,UAK3B5B,KAAK6vB,kBAAoB,WACnBzH,EAAKnuB,SACPmuB,EAAKpY,QAITnN,EAAaO,GAAGpD,KAAK/F,QAAQuL,QAAb,UACd,gBACAxF,KAAK6vB,mBAGH7vB,KAAKrD,OAAOjC,SACdsF,KAAKrD,OAAL0K,EAAAA,EAAA,GACKrH,KAAKrD,QADV,GAAA,CAEEsH,QAAS,SACTvJ,SAAU,KAGZsF,KAAKwxB,eAITA,UAAA,WACE,IAAMC,SAAmBzxB,KAAK/F,QAAQU,aAAa,wBAE/CqF,KAAK/F,QAAQU,aAAa,UAA0B,WAAd82B,KACxCzxB,KAAK/F,QAAQwM,aACX,sBACAzG,KAAK/F,QAAQU,aAAa,UAAY,IAGxCqF,KAAK/F,QAAQwM,aAAa,QAAS,QAIvCipB,OAAA,SAAO9tB,EAAO2kB,GACZ,IAAM+I,EAAUtvB,KAAKimB,YAAYiJ,UACjC3I,EAAUA,GAAWtnB,EAAa2C,EAAMgB,OAAQ0sB,MAG9C/I,EAAU,IAAIvmB,KAAKimB,YACjBrkB,EAAMgB,OACN5C,KAAKuvB,sBAEPtwB,EAAa2C,EAAMgB,OAAQ0sB,EAAS/I,IAGlC3kB,IACF2kB,EAAQwI,eACS,YAAfntB,EAAMmB,KAxeQ,QADA,UA0eZ,GAGFwjB,EAAQqJ,gBAAgBlqB,UAAUE,SApflB,SAEC,SAmfjB2gB,EAAQuI,YACVvI,EAAQuI,YApfW,QAwfrB/hB,aAAawZ,EAAQsI,UAErBtI,EAAQuI,YA1fa,OA4fhBvI,EAAQ5pB,OAAO6wB,OAAUjH,EAAQ5pB,OAAO6wB,MAAMvd,KAKnDsW,EAAQsI,SAAWryB,YAAW,WAjgBT,SAkgBf+pB,EAAQuI,aACVvI,EAAQtW,SAETsW,EAAQ5pB,OAAO6wB,MAAMvd,MARtBsW,EAAQtW,WAWZ0f,OAAA,SAAO/tB,EAAO2kB,GACZ,IAAM+I,EAAUtvB,KAAKimB,YAAYiJ,UACjC3I,EAAUA,GAAWtnB,EAAa2C,EAAMgB,OAAQ0sB,MAG9C/I,EAAU,IAAIvmB,KAAKimB,YACjBrkB,EAAMgB,OACN5C,KAAKuvB,sBAEPtwB,EAAa2C,EAAMgB,OAAQ0sB,EAAS/I,IAGlC3kB,IACF2kB,EAAQwI,eACS,aAAfntB,EAAMmB,KAhhBQ,QADA,UAkhBZ,GAGFwjB,EAAQkJ,yBAIZ1iB,aAAawZ,EAAQsI,UAErBtI,EAAQuI,YA/hBY,MAiiBfvI,EAAQ5pB,OAAO6wB,OAAUjH,EAAQ5pB,OAAO6wB,MAAMxd,KAKnDuW,EAAQsI,SAAWryB,YAAW,WAtiBV,QAuiBd+pB,EAAQuI,aACVvI,EAAQvW,SAETuW,EAAQ5pB,OAAO6wB,MAAMxd,MARtBuW,EAAQvW,WAWZyf,qBAAA,WACE,IAAK,IAAMxrB,KAAWjE,KAAK+uB,eACzB,GAAI/uB,KAAK+uB,eAAe9qB,GACtB,OAAO,EAIX,OAAO,KAGT6G,WAAA,SAAWnO,GACT,IAAM+0B,EAAiB1qB,GAAYG,kBAAkBnH,KAAK/F,SA4C1D,OA1CA4C,OAAOC,KAAK40B,GACT30B,SAAQ,SAAA40B,IAC0C,IAA7CvE,GAAsBhrB,QAAQuvB,WACzBD,EAAeC,MAIxBh1B,GAAsC,iBAArBA,EAAOyT,WAA0BzT,EAAOyT,UAAUU,SACrEnU,EAAOyT,UAAYzT,EAAOyT,UAAU,IASV,iBAN5BzT,EAAM0K,EAAAA,EAAAA,EAAA,GACDrH,KAAKimB,YAAYxc,SACjBioB,GACkB,iBAAX/0B,GAAuBA,EAASA,EAAS,KAGnC6wB,QAChB7wB,EAAO6wB,MAAQ,CACbvd,KAAMtT,EAAO6wB,MACbxd,KAAMrT,EAAO6wB,QAIW,iBAAjB7wB,EAAO4wB,QAChB5wB,EAAO4wB,MAAQ5wB,EAAO4wB,MAAMnwB,YAGA,iBAAnBT,EAAOu0B,UAChBv0B,EAAOu0B,QAAUv0B,EAAOu0B,QAAQ9zB,YAGlCX,EACEqI,GACAnI,EACAqD,KAAKimB,YAAYjc,aAGfrN,EAAO+wB,WACT/wB,EAAO2wB,SAAWxB,GAAanvB,EAAO2wB,SAAU3wB,EAAOqvB,UAAWrvB,EAAOsvB,aAGpEtvB,KAGT4yB,mBAAA,WACE,IAAM5yB,EAAS,GAEf,GAAIqD,KAAKrD,OACP,IAAK,IAAMiC,KAAOoB,KAAKrD,OACjBqD,KAAKimB,YAAYxc,QAAQ7K,KAASoB,KAAKrD,OAAOiC,KAChDjC,EAAOiC,GAAOoB,KAAKrD,OAAOiC,IAKhC,OAAOjC,KAGTo0B,eAAA,WACE,IAAM/B,EAAMhvB,KAAK4vB,gBACXgC,EAAW5C,EAAIr0B,aAAa,SAAS2C,MAAM6vB,IAChC,OAAbyE,GAAqBA,EAASjwB,OAAS,GACzCiwB,EAASvY,KAAI,SAAAwY,GAAK,OAAIA,EAAMh3B,UACzBkC,SAAQ,SAAA+0B,GAAM,OAAI9C,EAAItpB,UAAUC,OAAOmsB,SAI9CV,6BAAA,SAA6BW,GAC3B,IAAMC,EAAiBD,EAAW7yB,SAClCc,KAAKgvB,IAAMgD,EAAexZ,OAC1BxY,KAAK+wB,iBACL/wB,KAAK0wB,oBAAoB1wB,KAAKywB,eAAesB,EAAW9Y,eAG1D4X,eAAA,WACE,IAAM7B,EAAMhvB,KAAK4vB,gBACXqC,EAAsBjyB,KAAKrD,OAAO0wB,UACA,OAApC2B,EAAIr0B,aAAa,iBAIrBq0B,EAAItpB,UAAUC,OAjpBM,QAkpBpB3F,KAAKrD,OAAO0wB,WAAY,EACxBrtB,KAAKgQ,OACLhQ,KAAKiQ,OACLjQ,KAAKrD,OAAO0wB,UAAY4E,MAKnBlsB,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KA/tBb,cAguBL6K,EAA4B,iBAAXlO,GAAuBA,EAE9C,IAAKkC,IAAQ,eAAepB,KAAKd,MAI5BkC,IACHA,EAAO,IAAI8vB,EAAQ3uB,KAAM6K,IAGL,iBAAXlO,GAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,YAKJwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EArvBP,uDAgHb,MAjHY,+CAqHZ,OAAOwP,gCAIP,OAAO3E,oCAIP,MA5Ha,2CAgIb,OAAOjJ,qCAIP,MAnIW,kDAuIX,OAAOmO,SAjDL2kB,GAkqBApuB,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQ6pB,GAAQ5oB,gBACrBxF,GAAEmC,GAAGoC,IAAMuB,YAAcsoB,GACzBpuB,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACNuoB,GAAQ5oB,iBC1xBnB,IAAMjB,GAAO,UAKPqoB,GAAqB,IAAI3vB,OAAJ,wBAAyC,KAE9DiM,GAAOpC,EAAAA,EAAA,GACRsnB,GAAQllB,SADA,GAAA,CAEXwP,UAAW,QACXhV,QAAS,QACTitB,QAAS,GACT5D,SAAU,gJAMNtjB,GAAW3C,EAAAA,EAAA,GACZsnB,GAAQ3kB,aADI,GAAA,CAEfknB,QAAS,8BAGLr1B,GAAQ,CACZoyB,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAeNwD,GAAAA,SAAAA,+KAiCJpC,cAAA,WACE,OAAO9vB,KAAKgxB,YAAchxB,KAAKmyB,iBAGjC5B,WAAA,WACE,IAAMvB,EAAMhvB,KAAK4vB,gBAGjB5vB,KAAKixB,kBAAkB5oB,GAAe9I,QAlDnB,kBAkD2CyvB,GAAMhvB,KAAKgxB,YACzE,IAAIE,EAAUlxB,KAAKmyB,cACI,mBAAZjB,IACTA,EAAUA,EAAQ7zB,KAAK2C,KAAK/F,UAG9B+F,KAAKixB,kBAAkB5oB,GAAe9I,QAvDjB,gBAuD2CyvB,GAAMkC,GAEtElC,EAAItpB,UAAUC,OA7DM,OACA,WA+DtB+qB,oBAAA,SAAoBF,GAClBxwB,KAAK4vB,gBAAgBlqB,UAAU0C,IAAOipB,cAAgBb,MAKxD2B,YAAA,WACE,OAAOnyB,KAAK/F,QAAQU,aAAa,iBAC/BqF,KAAKrD,OAAOu0B,WAGhBH,eAAA,WACE,IAAM/B,EAAMhvB,KAAK4vB,gBACXgC,EAAW5C,EAAIr0B,aAAa,SAAS2C,MAAM6vB,IAChC,OAAbyE,GAAqBA,EAASjwB,OAAS,GACzCiwB,EAASvY,KAAI,SAAAwY,GAAK,OAAIA,EAAMh3B,UACzBkC,SAAQ,SAAA+0B,GAAM,OAAI9C,EAAItpB,UAAUC,OAAOmsB,SAMvC/rB,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KA1Hb,cA2HL6K,EAA4B,iBAAXlO,EAAsBA,EAAS,KAEtD,IAAKkC,IAAQ,eAAepB,KAAKd,MAI5BkC,IACHA,EAAO,IAAIqzB,EAAQlyB,KAAM6K,GACzB5L,EAAae,KAnIJ,aAmIoBnB,IAGT,iBAAXlC,GAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,YAKJwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EAjJP,uDAkDb,MAnDY,+CAuDZ,OAAOwP,gCAIP,OAAO3E,oCAIP,MA9Da,2CAkEb,OAAOjJ,qCAIP,MArEW,kDAyEX,OAAOmO,SA5BLkoB,CAAgBvD,IAuGhBpuB,GAAIjC,IAQV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQotB,GAAQnsB,gBACrBxF,GAAEmC,GAAGoC,IAAMuB,YAAc6rB,GACzB3xB,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACN8rB,GAAQnsB,iBC9JnB,IAAMjB,GAAO,YAMP2E,GAAU,CACdjC,OAAQ,GACR4qB,OAAQ,OACRxvB,OAAQ,IAGJoH,GAAc,CAClBxC,OAAQ,SACR4qB,OAAQ,SACRxvB,OAAQ,oBA2BJyvB,GAAAA,WACJ,SAAAA,EAAYp4B,EAAS0C,GAAQ,IAAA8I,EAAAzF,KAC3BA,KAAKgF,SAAW/K,EAChB+F,KAAKsyB,eAAqC,SAApBr4B,EAAQiT,QAAqB/R,OAASlB,EAC5D+F,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAK4P,UAAe5P,KAAK6K,QAAQjI,OAAb5C,cACKA,KAAK6K,QAAQjI,OADrB,qBAEQ5C,KAAK6K,QAAQjI,OAFrB,kBAGjB5C,KAAKuyB,SAAW,GAChBvyB,KAAKwyB,SAAW,GAChBxyB,KAAKyyB,cAAgB,KACrBzyB,KAAK0yB,cAAgB,EAErB7vB,EAAaO,GAAGpD,KAAKsyB,eApCP,uBAoCqC,SAAA1wB,GAAK,OAAI6D,EAAKktB,SAAS/wB,MAE1E5B,KAAK4yB,UACL5yB,KAAK2yB,WAEL1zB,EAAahF,EA1DA,eA0DmB+F,iCAelC4yB,QAAA,WAAU,IAAAtmB,EAAAtM,KACF6yB,EAAa7yB,KAAKsyB,iBAAmBtyB,KAAKsyB,eAAen3B,OA3C7C,SACE,WA8Cd23B,EAAuC,SAAxB9yB,KAAK6K,QAAQunB,OAChCS,EACA7yB,KAAK6K,QAAQunB,OAETW,EAlDc,aAkDDD,EACjB9yB,KAAKgzB,gBACL,EAEFhzB,KAAKuyB,SAAW,GAChBvyB,KAAKwyB,SAAW,GAEhBxyB,KAAK0yB,cAAgB1yB,KAAKizB,mBAEV5qB,GAAelJ,KAAKa,KAAK4P,WAGtCyJ,KAAI,SAAApf,GACH,IAAI2I,EACEswB,EAAiBp4B,EAAuBb,GAM9C,GAJIi5B,IACFtwB,EAASyF,GAAe9I,QAAQ2zB,IAG9BtwB,EAAQ,CACV,IAAMuwB,EAAYvwB,EAAO8E,wBACzB,GAAIyrB,EAAUvd,OAASud,EAAUxd,OAC/B,MAAO,CACL3O,GAAY8rB,GAAclwB,GAAQ+E,IAAMorB,EACxCG,GAKN,OAAO,QAERtqB,QAAO,SAAAwqB,GAAI,OAAIA,KACf7Z,MAAK,SAACC,EAAGC,GAAJ,OAAUD,EAAE,GAAKC,EAAE,MACxB1c,SAAQ,SAAAq2B,GACP9mB,EAAKimB,SAASrpB,KAAKkqB,EAAK,IACxB9mB,EAAKkmB,SAAStpB,KAAKkqB,EAAK,UAI9B7tB,QAAA,WACEtG,EAAgBe,KAAKgF,SA3HR,gBA4HbnC,EAAaC,IAAI9C,KAAKsyB,eA3HX,iBA6HXtyB,KAAKgF,SAAW,KAChBhF,KAAKsyB,eAAiB,KACtBtyB,KAAK6K,QAAU,KACf7K,KAAK4P,UAAY,KACjB5P,KAAKuyB,SAAW,KAChBvyB,KAAKwyB,SAAW,KAChBxyB,KAAKyyB,cAAgB,KACrBzyB,KAAK0yB,cAAgB,QAKvB5nB,WAAA,SAAWnO,GAMT,GAA6B,iBAL7BA,EAAM0K,EAAAA,EAAA,GACDoC,IACkB,iBAAX9M,GAAuBA,EAASA,EAAS,KAGnCiG,QAAuB9G,EAAUa,EAAOiG,QAAS,CAAA,IAC3D7I,EAAO4C,EAAOiG,OAAd7I,GACDA,IACHA,EAAKG,EAAO4K,IACZnI,EAAOiG,OAAO7I,GAAKA,GAGrB4C,EAAOiG,OAAP,IAAoB7I,EAKtB,OAFA0C,EAAgBqI,GAAMnI,EAAQqN,IAEvBrN,KAGTq2B,cAAA,WACE,OAAOhzB,KAAKsyB,iBAAmBn3B,OAC7B6E,KAAKsyB,eAAee,YACpBrzB,KAAKsyB,eAAe1qB,aAGxBqrB,iBAAA,WACE,OAAOjzB,KAAKsyB,eAAenJ,cAAgB/uB,KAAKqb,IAC9Clb,SAASiE,KAAK2qB,aACd5uB,SAASgO,gBAAgB4gB,iBAI7BmK,iBAAA,WACE,OAAOtzB,KAAKsyB,iBAAmBn3B,OAC7BA,OAAO+c,YACPlY,KAAKsyB,eAAe5qB,wBAAwBiO,UAGhDgd,SAAA,WACE,IAAM/qB,EAAY5H,KAAKgzB,gBAAkBhzB,KAAK6K,QAAQrD,OAChD2hB,EAAenpB,KAAKizB,mBACpBM,EAAYvzB,KAAK6K,QAAQrD,OAC7B2hB,EACAnpB,KAAKszB,mBAMP,GAJItzB,KAAK0yB,gBAAkBvJ,GACzBnpB,KAAK4yB,UAGHhrB,GAAa2rB,EAAjB,CACE,IAAM3wB,EAAS5C,KAAKwyB,SAASxyB,KAAKwyB,SAAS7wB,OAAS,GAEhD3B,KAAKyyB,gBAAkB7vB,GACzB5C,KAAKwzB,UAAU5wB,OAJnB,CAUA,GAAI5C,KAAKyyB,eAAiB7qB,EAAY5H,KAAKuyB,SAAS,IAAMvyB,KAAKuyB,SAAS,GAAK,EAG3E,OAFAvyB,KAAKyyB,cAAgB,UACrBzyB,KAAKyzB,SAIP,IAAK,IAAIhyB,EAAIzB,KAAKuyB,SAAS5wB,OAAQF,KAAM,CAChBzB,KAAKyyB,gBAAkBzyB,KAAKwyB,SAAS/wB,IACxDmG,GAAa5H,KAAKuyB,SAAS9wB,UACM,IAAzBzB,KAAKuyB,SAAS9wB,EAAI,IACtBmG,EAAY5H,KAAKuyB,SAAS9wB,EAAI,KAGpCzB,KAAKwzB,UAAUxzB,KAAKwyB,SAAS/wB,SAKnC+xB,UAAA,SAAU5wB,GACR5C,KAAKyyB,cAAgB7vB,EAErB5C,KAAKyzB,SAEL,IAAMC,EAAU1zB,KAAK4P,UAAUlU,MAAM,KAClC2d,KAAI,SAAA3e,GAAQ,OAAOA,EAAP,iBAAgCkI,EAAhC,MAA4ClI,EAA5C,UAA8DkI,EAA9D,QAET+wB,EAAOtrB,GAAe9I,QAAQm0B,EAAQE,KAAK,MAE7CD,EAAKjuB,UAAUE,SA/MU,kBAgN3ByC,GACG9I,QAxMwB,mBAwMUo0B,EAAKnuB,QAzMtB,cA0MjBE,UAAU0C,IAjNO,UAmNpBurB,EAAKjuB,UAAU0C,IAnNK,YAsNpBurB,EAAKjuB,UAAU0C,IAtNK,UAwNpBC,GACGS,QAAQ6qB,EAtNe,qBAuNvB52B,SAAQ,SAAA82B,GAGPxrB,GAAec,KAAK0qB,EAAcC,+BAC/B/2B,SAAQ,SAAAq2B,GAAI,OAAIA,EAAK1tB,UAAU0C,IA9NlB,aAiOhBC,GAAec,KAAK0qB,EA5NH,aA6Nd92B,SAAQ,SAAAg3B,GACP1rB,GAAeK,SAASqrB,EA/NX,aAgOVh3B,SAAQ,SAAAq2B,GAAI,OAAIA,EAAK1tB,UAAU0C,IApOtB,oBAyOtBvF,EAAaoB,QAAQjE,KAAKsyB,eA9OV,wBA8O0C,CACxD5kB,cAAe9K,OAInB6wB,OAAA,WACEprB,GAAelJ,KAAKa,KAAK4P,WACtBhH,QAAO,SAAA2K,GAAI,OAAIA,EAAK7N,UAAUE,SAhPX,aAiPnB7I,SAAQ,SAAAwW,GAAI,OAAIA,EAAK7N,UAAUC,OAjPZ,gBAsPjBI,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KA7Qb,gBAoRX,GAJKnB,IACHA,EAAO,IAAIwzB,EAAUryB,KAHW,iBAAXrD,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,YAKJwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EA/RP,yDAgEb,MAjEY,+CAqEZ,OAAOwP,SA5BL4oB,GAiQNxvB,EAAaO,GAAGjI,OAvRS,8BAuRoB,WAC3CkN,GAAelJ,KAnRS,uBAoRrBpC,SAAQ,SAAAi3B,GAAG,OAAI,IAAI3B,GAAU2B,EAAKhtB,GAAYG,kBAAkB6sB,UAGrE,IAAMzzB,GAAIjC,IAQV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQutB,GAAUtsB,gBACvBxF,GAAEmC,GAAGoC,IAAMuB,YAAcgsB,GACzB9xB,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACNisB,GAAUtsB,iBC9TrB,IAgCMkuB,GAAAA,WACJ,SAAAA,EAAYh6B,GACV+F,KAAKgF,SAAW/K,EAEhBgF,EAAae,KAAKgF,SAlCL,SAkCyBhF,iCAWxCiQ,KAAA,WAAO,IAAAxK,EAAAzF,KACL,KAAKA,KAAKgF,SAASlH,YACjBkC,KAAKgF,SAASlH,WAAW9B,WAAagN,KAAKC,cAC3CjJ,KAAKgF,SAASU,UAAUE,SArCJ,WAsCpB5F,KAAKgF,SAASU,UAAUE,SArCF,aAkCxB,CAOA,IAAIwD,EACExG,EAAS5H,EAAuBgF,KAAKgF,UACrCkvB,EAAcl0B,KAAKgF,SAASQ,QAtCN,qBAwC5B,GAAI0uB,EAAa,CACf,IAAMC,EAAwC,OAAzBD,EAAY/hB,UAA8C,OAAzB+hB,EAAY/hB,SAvC7C,wBADH,UA0ClB/I,GADAA,EAAWf,GAAelJ,KAAKg1B,EAAcD,IACzB9qB,EAASzH,OAAS,GAGxC,IAAIyyB,EAAY,KAYhB,GAVIhrB,IACFgrB,EAAYvxB,EAAaoB,QAAQmF,EA9DvB,cA8D6C,CACrDsE,cAAe1N,KAAKgF,cAINnC,EAAaoB,QAAQjE,KAAKgF,SAjEhC,cAiEsD,CAChE0I,cAAetE,IAGHvJ,kBACG,OAAdu0B,GAAsBA,EAAUv0B,kBADnC,CAKAG,KAAKwzB,UACHxzB,KAAKgF,SACLkvB,GAGF,IAAMtD,EAAW,WACf/tB,EAAaoB,QAAQmF,EAjFT,gBAiFiC,CAC3CsE,cAAejI,EAAKT,WAEtBnC,EAAaoB,QAAQwB,EAAKT,SAlFf,eAkFsC,CAC/C0I,cAAetE,KAIfxG,EACF5C,KAAKwzB,UAAU5wB,EAAQA,EAAO9E,WAAY8yB,GAE1CA,SAIJrrB,QAAA,WACEtG,EAAgBe,KAAKgF,SAtGR,UAuGbhF,KAAKgF,SAAW,QAKlBwuB,UAAA,SAAUv5B,EAASmW,EAAWsN,GAAU,IAAApR,EAAAtM,KAKhCq0B,IAJiBjkB,GAAqC,OAAvBA,EAAU+B,UAA4C,OAAvB/B,EAAU+B,SAE5E9J,GAAeK,SAAS0H,EA7FN,WA4FlB/H,GAAelJ,KA3FM,wBA2FmBiR,IAGZ,GACxBS,EAAkB6M,GACrB2W,GAAUA,EAAO3uB,UAAUE,SAtGV,QAwGdgrB,EAAW,WAAA,OAAMtkB,EAAKgoB,oBAC1Br6B,EACAo6B,EACA3W,IAGF,GAAI2W,GAAUxjB,EAAiB,CAC7B,IAAMxV,EAAqBJ,EAAiCo5B,GAC5DA,EAAO3uB,UAAUC,OA/GC,QAiHlB9C,EAAaQ,IAAIgxB,EjBjJA,gBiBiJwBzD,GACzC30B,EAAqBo4B,EAAQh5B,QAE7Bu1B,OAIJ0D,oBAAA,SAAoBr6B,EAASo6B,EAAQ3W,GACnC,GAAI2W,EAAQ,CACVA,EAAO3uB,UAAUC,OA7HG,UA+HpB,IAAM4uB,EAAgBlsB,GAAe9I,QApHJ,kCAoH4C80B,EAAOv2B,YAEhFy2B,GACFA,EAAc7uB,UAAUC,OAlIN,UAqIgB,QAAhC0uB,EAAO15B,aAAa,SACtB05B,EAAO5tB,aAAa,iBAAiB,IAIzCxM,EAAQyL,UAAU0C,IA1II,UA2Ie,QAAjCnO,EAAQU,aAAa,SACvBV,EAAQwM,aAAa,iBAAiB,GAGxCrI,EAAOnE,GAEHA,EAAQyL,UAAUE,SA/IF,SAgJlB3L,EAAQyL,UAAU0C,IA/IA,QAkJhBnO,EAAQ6D,YAAc7D,EAAQ6D,WAAW4H,UAAUE,SAtJ1B,oBAuJH3L,EAAQuL,QAjJZ,cAoJlB6C,GAAelJ,KA/IU,oBAgJtBpC,SAAQ,SAAAy3B,GAAQ,OAAIA,EAAS9uB,UAAU0C,IA1JxB,aA6JpBnO,EAAQwM,aAAa,iBAAiB,IAGpCiX,GACFA,OAMG3X,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAMnH,EAAOI,EAAae,KApLf,WAoLkC,IAAIi0B,EAAIj0B,MAErD,GAAsB,iBAAXrD,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,YAKJwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EAjMP,mDAwCb,MAzCY,qBA+BVg6B,GA6KNpxB,EAAaO,GAAG7I,SAnMU,wBAYG,mEAuLyC,SAAUqH,GAC9EA,EAAMhC,kBAEOX,EAAae,KA9MX,WA8M8B,IAAIi0B,GAAIj0B,OAChDiQ,UAGP,IAAM1P,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAF,IAC3BnC,GAAEmC,GAAF,IAAauxB,GAAIluB,gBACjBxF,GAAEmC,GAAF,IAAW2D,YAAc4tB,GACzB1zB,GAAEmC,GAAF,IAAW4D,WAAa,WAEtB,OADA/F,GAAEmC,GAAF,IAAa0D,GACN6tB,GAAIluB,iBCnOf,IAgBMiE,GAAc,CAClBqjB,UAAW,UACXoH,SAAU,UACVjH,MAAO,UAGH/jB,GAAU,CACd4jB,WAAW,EACXoH,UAAU,EACVjH,MAAO,KAWHkH,GAAAA,WACJ,SAAAA,EAAYz6B,EAAS0C,GACnBqD,KAAKgF,SAAW/K,EAChB+F,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAK6uB,SAAW,KAChB7uB,KAAKivB,gBACLhwB,EAAahF,EAxCA,WAwCmB+F,iCAmBlCiQ,KAAA,WAAO,IAAAxK,EAAAzF,KAGL,IAFkB6C,EAAaoB,QAAQjE,KAAKgF,SAtDhC,iBAwDEnF,iBAAd,CAIIG,KAAK6K,QAAQwiB,WACfrtB,KAAKgF,SAASU,UAAU0C,IA1DN,QA6DpB,IAAMwoB,EAAW,WACfnrB,EAAKT,SAASU,UAAUC,OA3DH,WA4DrBF,EAAKT,SAASU,UAAU0C,IA7DN,QA+DlBvF,EAAaoB,QAAQwB,EAAKT,SAnEf,kBAqEPS,EAAKoF,QAAQ4pB,WACfhvB,EAAKopB,SAAWryB,YAAW,WACzBiJ,EAAKuK,SACJvK,EAAKoF,QAAQ2iB,SAOpB,GAHAxtB,KAAKgF,SAASU,UAAUC,OAzEJ,QA0EpBvH,EAAO4B,KAAKgF,UACZhF,KAAKgF,SAASU,UAAU0C,IAzED,WA0EnBpI,KAAK6K,QAAQwiB,UAAW,CAC1B,IAAMhyB,EAAqBJ,EAAiC+E,KAAKgF,UAEjEnC,EAAaQ,IAAIrD,KAAKgF,SlB3GL,gBkB2G+B4rB,GAChD30B,EAAqB+D,KAAKgF,SAAU3J,QAEpCu1B,QAIJ5gB,KAAA,WAAO,IAAA1D,EAAAtM,KACL,GAAKA,KAAKgF,SAASU,UAAUE,SAtFT,UA0FF/C,EAAaoB,QAAQjE,KAAKgF,SAjGhC,iBAmGEnF,iBAAd,CAIA,IAAM+wB,EAAW,WACftkB,EAAKtH,SAASU,UAAU0C,IAlGN,QAmGlBvF,EAAaoB,QAAQqI,EAAKtH,SAxGd,oBA4Gd,GADAhF,KAAKgF,SAASU,UAAUC,OArGJ,QAsGhB3F,KAAK6K,QAAQwiB,UAAW,CAC1B,IAAMhyB,EAAqBJ,EAAiC+E,KAAKgF,UAEjEnC,EAAaQ,IAAIrD,KAAKgF,SlBtIL,gBkBsI+B4rB,GAChD30B,EAAqB+D,KAAKgF,SAAU3J,QAEpCu1B,QAIJrrB,QAAA,WACEwH,aAAa/M,KAAK6uB,UAClB7uB,KAAK6uB,SAAW,KAEZ7uB,KAAKgF,SAASU,UAAUE,SApHR,SAqHlB5F,KAAKgF,SAASU,UAAUC,OArHN,QAwHpB9C,EAAaC,IAAI9C,KAAKgF,SAhID,0BAiIrB/F,EAAgBe,KAAKgF,SApIR,YAsIbhF,KAAKgF,SAAW,KAChBhF,KAAK6K,QAAU,QAKjBC,WAAA,SAAWnO,GAaT,OAZAA,EAAM0K,EAAAA,EAAAA,EAAA,GACDoC,IACAzC,GAAYG,kBAAkBnH,KAAKgF,WACjB,iBAAXrI,GAAuBA,EAASA,EAAS,IAGrDF,EArJS,QAuJPE,EACAqD,KAAKimB,YAAYjc,aAGZrN,KAGTsyB,cAAA,WAAgB,IAAAxiB,EAAAzM,KACd6C,EAAaO,GACXpD,KAAKgF,SA3Jc,yBAuBK,0BAuIxB,WAAA,OAAMyH,EAAKuD,aAMRjK,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KAzKb,YAgLX,GAJKnB,IACHA,EAAO,IAAI61B,EAAM10B,KAHe,iBAAXrD,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,GAAQqD,aAKZmG,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EA3LP,qDA8Cb,MA/CY,mDAmDZ,OAAO+P,mCAIP,OAAOP,SApBLirB,GA6JAn0B,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAF,MAC3BnC,GAAEmC,GAAF,MAAagyB,GAAM3uB,gBACnBxF,GAAEmC,GAAF,MAAW2D,YAAcquB,GACzBn0B,GAAEmC,GAAF,MAAW4D,WAAa,WAEtB,OADA/F,GAAEmC,GAAF,MAAa0D,GACNsuB,GAAM3uB,uBCtNF,CACbhB,MAAAA,EACAwB,OAAAA,EACA6D,SAAAA,GACAgF,SAAAA,GACA+V,SAAAA,GACA0B,MAAAA,GACAqL,QAAAA,GACAG,UAAAA,GACA4B,IAAAA,GACAS,MAAAA,GACA/F,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = parseFloat(transitionDuration)\n  const floatTransitionDelay = parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes)\n    .forEach(property => {\n      const expectedTypes = configTypes[property]\n      const value = config[property]\n      const valueType = value && isElement(value) ?\n        'element' :\n        toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new Error(\n          `${componentName.toUpperCase()}: ` +\n          `Option \"${property}\" provided type \"${valueType}\" ` +\n          `but expected type \"${expectedTypes}\".`)\n      }\n    })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nexport {\n  getjQuery,\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.key === 'undefined') {\n        element.key = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.key.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.key === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.key\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.key === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.key\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.key\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/* istanbul ignore file */\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.0-alpha1): dom/polyfill.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getUID } from '../util/index'\n\nlet find = Element.prototype.querySelectorAll\nlet findOne = Element.prototype.querySelector\n\n// MSEdge resets defaultPrevented flag upon dispatchEvent call if at least one listener is attached\nconst defaultPreventedPreservedOnDispatch = (() => {\n  const e = new CustomEvent('Bootstrap', {\n    cancelable: true\n  })\n\n  const element = document.createElement('div')\n  element.addEventListener('Bootstrap', () => null)\n\n  e.preventDefault()\n  element.dispatchEvent(e)\n  return e.defaultPrevented\n})()\n\nconst scopeSelectorRegex = /:scope\\b/\nconst supportScopeQuery = (() => {\n  const element = document.createElement('div')\n\n  try {\n    element.querySelectorAll(':scope *')\n  } catch (_) {\n    return false\n  }\n\n  return true\n})()\n\nif (!supportScopeQuery) {\n  find = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelectorAll(selector)\n    }\n\n    const hasId = Boolean(this.id)\n\n    if (!hasId) {\n      this.id = getUID('scope')\n    }\n\n    let nodeList = null\n    try {\n      selector = selector.replace(scopeSelectorRegex, `#${this.id}`)\n      nodeList = this.querySelectorAll(selector)\n    } finally {\n      if (!hasId) {\n        this.removeAttribute('id')\n      }\n    }\n\n    return nodeList\n  }\n\n  findOne = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelector(selector)\n    }\n\n    const matches = find.call(this, selector)\n\n    if (typeof matches[0] !== 'undefined') {\n      return matches[0]\n    }\n\n    return null\n  }\n}\n\nexport {\n  find,\n  findOne,\n  defaultPreventedPreservedOnDispatch\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\nimport { defaultPreventedPreservedOnDispatch } from './polyfill'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst $ = getjQuery()\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n]\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent)\n    .forEach(handlerKey => {\n      if (handlerKey.indexOf(namespace) > -1) {\n        const event = storeElementEvent[handlerKey]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.charAt(0) === '.'\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events)\n        .forEach(elementEvent => {\n          removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n        })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent)\n      .forEach(keyHandlers => {\n        const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n        if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n          const event = storeElementEvent[keyHandlers]\n\n          removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n        }\n      })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom informations in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args)\n        .forEach(key => {\n          Object.defineProperty(evt, key, {\n            get() {\n              return args[key]\n            }\n          })\n        })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n\n      if (!defaultPreventedPreservedOnDispatch) {\n        Object.defineProperty(evt, 'defaultPrevented', {\n          get: () => true\n        })\n      }\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this)\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    let rootElement = this._element\n    if (element) {\n      rootElement = this._getRootElement(element)\n    }\n\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler\n      .one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .alert to jQuery only if jQuery is present\n */\n\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Alert.jQueryInterface\n  $.fn[NAME].Constructor = Alert\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Alert.jQueryInterface\n  }\n}\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .button to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Button.jQueryInterface\n  $.fn[NAME].Constructor = Button\n\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Button.jQueryInterface\n  }\n}\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {\n      ...element.dataset\n    }\n\n    Object.keys(attributes).forEach(key => {\n      attributes[key] = normalizeData(attributes[key])\n    })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  },\n\n  toggleClass(element, className) {\n    if (!element) {\n      return\n    }\n\n    if (element.classList.contains(className)) {\n      element.classList.remove(className)\n    } else {\n      element.classList.add(className)\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { find as findFn, findOne } from './polyfill'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...findFn.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return findOne.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler\n        .on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler\n        .on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler\n        .on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement &&\n      this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const nextElementInterval = parseInt(nextElement.getAttribute('data-interval'), 10)\n      if (nextElementInterval) {\n        this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n        this._config.interval = nextElementInterval\n      } else {\n        this._config.interval = this._config.defaultInterval || this._config.interval\n      }\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler\n        .one(activeElement, TRANSITION_END, () => {\n          nextElement.classList.remove(directionalClassName, orderClassName)\n          nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n          activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n          this._isSliding = false\n\n          setTimeout(() => {\n            EventHandler.trigger(this._element, EVENT_SLID, {\n              relatedTarget: nextElement,\n              direction: eventDirectionName,\n              from: activeElementIndex,\n              to: nextElementIndex\n            })\n          }, 0)\n        })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .carousel to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Carousel.jQueryInterface\n  $.fn[NAME].Constructor = Carousel\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Carousel.jQueryInterface\n  }\n}\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.filter(elem => container !== elem)\n      activesData = tempActiveData[0] ? Data.getData(tempActiveData[0], DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    const hasWidth = this._element.classList.contains(WIDTH)\n    return hasWidth ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (element) {\n      const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n      if (triggerArray.length) {\n        triggerArray.forEach(elem => {\n          if (isOpen) {\n            elem.classList.remove(CLASS_NAME_COLLAPSED)\n          } else {\n            elem.classList.add(CLASS_NAME_COLLAPSED)\n          }\n\n          elem.setAttribute('aria-expanded', isOpen)\n        })\n      }\n    }\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .collapse to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Collapse.jQueryInterface\n  $.fn[NAME].Constructor = Collapse\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Collapse.jQueryInterface\n  }\n}\n\nexport default Collapse\n", "/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.16.0\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n\nvar timeoutDuration = function () {\n  var longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}();\n\nfunction microtaskDebounce(fn) {\n  var called = false;\n  return function () {\n    if (called) {\n      return;\n    }\n    called = true;\n    window.Promise.resolve().then(function () {\n      called = false;\n      fn();\n    });\n  };\n}\n\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nvar supportsMicroTasks = isBrowser && window.Promise;\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var window = element.ownerDocument.defaultView;\n  var css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n      overflow = _getStyleComputedProp.overflow,\n      overflowX = _getStyleComputedProp.overflowX,\n      overflowY = _getStyleComputedProp.overflowY;\n\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nfunction getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n\nvar isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nvar isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nfunction isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  var noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  var offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  var nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = element.ownerDocument.documentElement;\n    var scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return parseFloat(styles['border' + sideA + 'Width'], 10) + parseFloat(styles['border' + sideB + 'Width'], 10);\n}\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE(10) ? parseInt(html['offset' + axis]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')]) : 0);\n}\n\nfunction getWindowSizes(document) {\n  var body = document.body;\n  var html = document.documentElement;\n  var computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n};\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } else {\n      rect = element.getBoundingClientRect();\n    }\n  } catch (e) {}\n\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  var width = sizes.width || element.clientWidth || result.width;\n  var height = sizes.height || element.clientHeight || result.height;\n\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var fixedPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var isIE10 = isIE(10);\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = parseFloat(styles.borderTopWidth, 10);\n  var borderLeftWidth = parseFloat(styles.borderLeftWidth, 10);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = parseFloat(styles.marginTop, 10);\n    var marginLeft = parseFloat(styles.marginLeft, 10);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (isIE10 && !fixedPosition ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var excludeScroll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var html = element.ownerDocument.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  var scrollTop = !excludeScroll ? getScroll(html) : 0;\n  var scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  var parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  var el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  var fixedPosition = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n\n  // NOTE: 1 DOM access here\n\n  var boundaries = { top: 0, left: 0 };\n  var offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(popper.ownerDocument),\n          height = _getWindowSizes.height,\n          width = _getWindowSizes.width;\n\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  var isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0;\n  boundaries.top += isPaddingNumber ? padding : padding.top || 0;\n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0;\n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0;\n\n  return boundaries;\n}\n\nfunction getArea(_ref) {\n  var width = _ref.width,\n      height = _ref.height;\n\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n        height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n\n  var variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var fixedPosition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n\n  var commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var window = element.ownerDocument.defaultView;\n  var styles = window.getComputedStyle(element);\n  var x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  var y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier['function']) {\n      // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference, this.options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n\n  data.offsets.popper.position = this.options.positionFixed ? 'fixed' : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n        enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (var i = 0; i < prefixes.length; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\n/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nfunction getWindow(element) {\n  var ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n\n/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nfunction getRoundedOffsets(data, shouldRound) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n  var round = Math.round,\n      floor = Math.floor;\n\n  var noRound = function noRound(v) {\n    return v;\n  };\n\n  var referenceWidth = round(reference.width);\n  var popperWidth = round(popper.width);\n\n  var isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  var isVariation = data.placement.indexOf('-') !== -1;\n  var sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  var bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n\n  var horizontalToInteger = !shouldRound ? noRound : isVertical || isVariation || sameWidthParity ? round : floor;\n  var verticalToInteger = !shouldRound ? noRound : round;\n\n  return {\n    left: horizontalToInteger(bothOddWidth && !isVariation && shouldRound ? popper.left - 1 : popper.left),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right)\n  };\n}\n\nvar isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n      y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n\n  var offsets = getRoundedOffsets(data, window.devicePixelRatio < 2 || !isFirefox);\n\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n      top = void 0;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  var _data$offsets$arrow;\n\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var css = getStyleComputedProperty(data.instance.popper);\n  var popperMarginSide = parseFloat(css['margin' + sideCapitalized], 10);\n  var popperBorderSide = parseFloat(css['border' + sideCapitalized + 'Width'], 10);\n  var sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ''), _data$offsets$arrow);\n\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement, data.positionFixed);\n\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n\n  var flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    var flippedVariationByRef = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    // flips variation if popper content overflows boundaries\n    var flippedVariationByContent = !!options.flipVariationsByContent && (isVertical && variation === 'start' && overflowsRight || isVertical && variation === 'end' && overflowsLeft || !isVertical && variation === 'start' && overflowsBottom || !isVertical && variation === 'end' && overflowsTop);\n\n    var flippedVariation = flippedVariationByRef || flippedVariationByContent;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n      _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var basePlacement = placement.split('-')[0];\n\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  var transformProp = getSupportedPropertyName('transform');\n  var popperStyles = data.instance.popper.style; // assignment to help minification\n  var top = popperStyles.top,\n      left = popperStyles.left,\n      transform = popperStyles[transformProp];\n\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement, data.positionFixed);\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  var order = options.priority;\n  var popper = data.offsets.popper;\n\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n        reference = _data$offsets.reference,\n        popper = _data$offsets.popper;\n\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedules an update. It will run on the next UI update available.\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\n\nexport default Popper;\n//# sourceMappingURL=popper.js.map\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_NAVBAR = 'navbar'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        parent.classList.add(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    Manipulator.toggleClass(this._menu, CLASS_NAME_SHOW)\n    Manipulator.toggleClass(this._element, CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    Manipulator.toggleClass(this._menu, CLASS_NAME_SHOW)\n    Manipulator.toggleClass(this._element, CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._element, EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      placement = PLACEMENT_TOP\n      if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n        placement = PLACEMENT_TOPEND\n      }\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return Boolean(this._element.closest(`.${CLASS_NAME_NAVBAR}`))\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this._config.offset(data.offsets, this._element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON ||\n      (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent)\n      .filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.key === ARROW_UP_KEY && index > 0) { // Up\n      index--\n    }\n\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) { // Down\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .dropdown to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Dropdown.jQueryInterface\n  $.fn[NAME].Constructor = Dropdown\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Dropdown.jQueryInterface\n  }\n}\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n      if (hideEvent.defaultPrevented) {\n        return\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n      const modalTransitionDuration = getTransitionDurationFromElement(this._element)\n      EventHandler.one(this._element, TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n      })\n      emulateTransitionEnd(this._element, modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...typeof config === 'object' && config ? config : {}\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .modal to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Modal.jQueryInterface\n  $.fn[NAME].Constructor = Modal\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Modal.jQueryInterface\n  }\n}\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultWhitelist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, whiteList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const whitelistKeys = Object.keys(whiteList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (whitelistKeys.indexOf(elName) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const whitelistedAttributes = [].concat(whiteList['*'] || [], whiteList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, whitelistedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultWhitelist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'whiteList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  whiteList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"tooltip-arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  whiteList: DefaultWhitelist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.target, dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.target,\n          this._getDelegateConfig()\n        )\n        Data.setData(event.target, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    Data.removeData(this.element, this.constructor.DATA_KEY)\n\n    EventHandler.off(this.element, this.constructor.EVENT_KEY)\n    EventHandler.off(this.element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if (this.element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this.element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this.element)\n      const isInTheDom = shadowRoot === null ?\n        this.element.ownerDocument.documentElement.contains(this.element) :\n        shadowRoot.contains(this.element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this.element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this.element, this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        EventHandler.trigger(this.element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this.element, this.constructor.Event.HIDDEN)\n      this._popper.destroy()\n    }\n\n    const hideEvent = EventHandler.trigger(this.element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.whiteList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: `.${this.constructor.NAME}-arrow`\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this.config.offset(data.offsets, this.element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this.element,\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this.element,\n          eventIn,\n          this.config.selector,\n          event => this._enter(event)\n        )\n        EventHandler.on(this.element,\n          eventOut,\n          this.config.selector,\n          event => this._leave(event)\n        )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this.element.closest(`.${CLASS_NAME_MODAL}`),\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.target, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.target,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.target, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) ||\n        context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.target, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.target,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.target, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this.element)\n\n    Object.keys(dataAttributes)\n      .forEach(dataAttr => {\n        if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n          delete dataAttributes[dataAttr]\n        }\n      })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.whiteList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const popperInstance = popperData.instance\n    this.tip = popperInstance.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .tooltip to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Tooltip.jQueryInterface\n  $.fn[NAME].Constructor = Tooltip\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tooltip.jQueryInterface\n  }\n}\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  // Private\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Popover.jQueryInterface\n  $.fn[NAME].Constructor = Popover\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Popover.jQueryInterface\n  }\n}\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS},` +\n                          `${this._config.target} ${SELECTOR_LIST_ITEMS},` +\n                          `${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets\n      .map(element => {\n        let target\n        const targetSelector = getSelectorFromElement(element)\n\n        if (targetSelector) {\n          target = SelectorEngine.findOne(targetSelector)\n        }\n\n        if (target) {\n          const targetBCR = target.getBoundingClientRect()\n          if (targetBCR.width || targetBCR.height) {\n            return [\n              Manipulator[offsetMethod](target).top + offsetBase,\n              targetSelector\n            ]\n          }\n        }\n\n        return null\n      })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine\n        .findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine\n        .parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = ScrollSpy.jQueryInterface\n  $.fn[NAME].Constructor = ScrollSpy\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return ScrollSpy.jQueryInterface\n  }\n}\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n\n    Data.setData(this._element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented ||\n      (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback &&\n      (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .tab to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Tab.jQueryInterface\n  $.fn[NAME].Constructor = Tab\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tab.jQueryInterface\n  }\n}\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 500\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(\n      this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      () => this.hide()\n    )\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n *  add .toast to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Toast.jQueryInterface\n  $.fn[NAME].Constructor = Toast\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Toast.jQueryInterface\n  }\n}\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}