{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/polyfill.js", "../../js/src/dom/event-handler.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "_window$getComputedSt", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "nodeType", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "mapData", "storeData", "id", "set", "key", "data", "get", "keyProperties", "delete", "Data", "setData", "instance", "getData", "removeData", "find", "Element", "prototype", "querySelectorAll", "findOne", "defaultPreventedPreservedOnDispatch", "e", "CustomEvent", "cancelable", "createElement", "preventDefault", "defaultPrevented", "scopeSelectorRegex", "supportScopeQuery", "_", "this", "hasId", "Boolean", "nodeList", "replace", "removeAttribute", "matches", "$", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "fn", "handler", "event", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "target", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "custom", "indexOf", "add<PERSON><PERSON><PERSON>", "_normalizeParams", "handlers", "previousFn", "<PERSON><PERSON><PERSON><PERSON>", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "_normalizeParams2", "inNamespace", "isNamespace", "char<PERSON>t", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "isNative", "bubbles", "nativeDispatch", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "defineProperty", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASSNAME_ALERT", "CLASSNAME_FADE", "CLASSNAME_SHOW", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "dispose", "closest", "_this", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "getInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "Number", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "getDataAttributes", "attributes", "_objectSpread2", "dataset", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "toggleClass", "className", "add", "NODE_TEXT", "SelectorEngine", "_ref", "concat", "findFn", "children", "_ref2", "filter", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "DIRECTION_NEXT", "DIRECTION_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_RIGHT", "CLASS_NAME_LEFT", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_this2", "_keydown", "_addTouchEventListeners", "_this3", "start", "pointerType", "clientX", "touches", "end", "clearTimeout", "itemImg", "move", "tagName", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "directionalClassName", "orderClassName", "_this4", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "nextElementInterval", "parseInt", "defaultInterval", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPRIGHT", "CLASS_NAME_DROPLEFT", "CLASS_NAME_MENURIGHT", "CLASS_NAME_NAVBAR", "CLASS_NAME_POSITION_STATIC", "SELECTOR_FORM_CHILD", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "flip", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "focus", "destroy", "update", "scheduleUpdate", "stopPropagation", "constructor", "_getPlacement", "parentDropdown", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_FADE", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "showEvent", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_this5", "_triggerBackdropTransition", "_this6", "_this7", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "callback", "_this8", "animate", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "_this9", "modalTransitionDuration", "isModalOverflowing", "scrollHeight", "clientHeight", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "_this10", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "_this11", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "*", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "whiteList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "whitelist<PERSON><PERSON>s", "elements", "_loop", "el", "el<PERSON>ame", "attributeList", "whitelistedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacement", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "complete", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "behavior", "arrow", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "tabClass", "map", "token", "tClass", "popperData", "popperInstance", "popper", "initConfigAnimation", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "hideEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast"], "mappings": ";;;;;spCAOA,IAAMA,QAAU,IACVC,wBAA0B,IAC1BC,eAAiB,gBAGjBC,OAAS,SAAAC,GACb,OAAIA,MAAAA,EACF,GAAUA,EAGL,GAAGC,SAASC,KAAKF,GAAKG,MAAM,eAAe,GAAGC,eASjDC,OAAS,SAAAC,GACb,GACEA,GAAUC,KAAKC,MAAMD,KAAKE,SAAWb,eAC9Bc,SAASC,eAAeL,IAEjC,OAAOA,GAGHM,YAAc,SAAAC,GAClB,IAAIC,EAAWD,EAAQE,aAAa,eAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAME,EAAWH,EAAQE,aAAa,QAEtCD,EAAWE,GAAyB,MAAbA,EAAmBA,EAASC,OAAS,KAG9D,OAAOH,GAGHI,uBAAyB,SAAAL,GAC7B,IAAMC,EAAWF,YAAYC,GAE7B,OAAIC,GACKJ,SAASS,cAAcL,GAAYA,EAGrC,MAGHM,uBAAyB,SAAAP,GAC7B,IAAMC,EAAWF,YAAYC,GAE7B,OAAOC,EAAWJ,SAASS,cAAcL,GAAY,MAGjDO,iCAAmC,SAAAR,GACvC,IAAKA,EACH,OAAO,EAFyC,IAAAS,EAS9CC,OAAOC,iBAAiBX,GAF1BY,EAPgDH,EAOhDG,mBACAC,EARgDJ,EAQhDI,gBAGIC,EAA0BC,WAAWH,GACrCI,EAAuBD,WAAWF,GAGxC,OAAKC,GAA4BE,GAKjCJ,EAAqBA,EAAmBK,MAAM,KAAK,GACnDJ,EAAkBA,EAAgBI,MAAM,KAAK,IAErCF,WAAWH,GAAsBG,WAAWF,IAAoB7B,yBAP/D,GAULkC,qBAAuB,SAAAlB,GAC3BA,EAAQmB,cAAc,IAAIC,MAAMnC,kBAG5BoC,UAAY,SAAAlC,GAAG,OAAKA,EAAI,IAAMA,GAAKmC,UAEnCC,qBAAuB,SAACvB,EAASwB,GACrC,IAAIC,GAAS,EAEPC,EAAmBF,EADD,EAOxBxB,EAAQ2B,iBAAiB1C,gBALzB,SAAS2C,IACPH,GAAS,EACTzB,EAAQ6B,oBAAoB5C,eAAgB2C,MAI9CE,YAAW,WACJL,GACHP,qBAAqBlB,KAEtB0B,IAGCK,gBAAkB,SAACC,EAAeC,EAAQC,GAC9CC,OAAOC,KAAKF,GACTG,SAAQ,SAAAC,GACP,IAAMC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASnB,UAAUmB,GACnC,UACAtD,OAAOsD,GAET,IAAK,IAAIE,OAAOH,GAAeI,KAAKF,GAClC,MAAM,IAAIG,MACLZ,EAAca,cAAdb,aACQM,EADX,oBACuCG,EADpCT,wBAEmBO,EAFtB,UAOJO,UAAY,SAAA9C,GAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQ+C,OAAS/C,EAAQgD,YAAchD,EAAQgD,WAAWD,MAAO,CACnE,IAAME,EAAetC,iBAAiBX,GAChCkD,EAAkBvC,iBAAiBX,EAAQgD,YAEjD,MAAgC,SAAzBC,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GAGHC,eAAiB,SAAjBA,EAAiBrD,GACrB,IAAKH,SAASyD,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBvD,EAAQwD,YAA4B,CAC7C,IAAMC,EAAOzD,EAAQwD,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIzD,aAAmB0D,WACd1D,EAIJA,EAAQgD,WAINK,EAAerD,EAAQgD,YAHrB,MAMLW,KAAO,WAAA,OAAM,cAEbC,OAAS,SAAA5D,GAAO,OAAIA,EAAQ6D,cAE5BC,UAAY,WAAM,IACdC,EAAWrD,OAAXqD,OAER,OAAIA,IAAWlE,SAASmE,KAAKC,aAAa,kBACjCF,EAGF,MCvKHG,QAAW,WACf,IAAMC,EAAY,GACdC,EAAK,EACT,MAAO,CACLC,IADK,SACDrE,EAASsE,EAAKC,QACW,IAAhBvE,EAAQsE,MACjBtE,EAAQsE,IAAM,CACZA,IAAAA,EACAF,GAAAA,GAEFA,KAGFD,EAAUnE,EAAQsE,IAAIF,IAAMG,GAE9BC,IAZK,SAYDxE,EAASsE,GACX,IAAKtE,QAAkC,IAAhBA,EAAQsE,IAC7B,OAAO,KAGT,IAAMG,EAAgBzE,EAAQsE,IAC9B,OAAIG,EAAcH,MAAQA,EACjBH,EAAUM,EAAcL,IAG1B,MAETM,OAxBK,SAwBE1E,EAASsE,GACd,QAA2B,IAAhBtE,EAAQsE,IAAnB,CAIA,IAAMG,EAAgBzE,EAAQsE,IAC1BG,EAAcH,MAAQA,WACjBH,EAAUM,EAAcL,WACxBpE,EAAQsE,QAnCN,GAyCXK,KAAO,CACXC,QADW,SACHC,EAAUP,EAAKC,GACrBL,QAAQG,IAAIQ,EAAUP,EAAKC,IAE7BO,QAJW,SAIHD,EAAUP,GAChB,OAAOJ,QAAQM,IAAIK,EAAUP,IAE/BS,WAPW,SAOAF,EAAUP,GACnBJ,QAAQQ,OAAOG,EAAUP,KCnDzBU,KAAOC,QAAQC,UAAUC,iBACzBC,QAAUH,QAAQC,UAAU5E,cAG1B+E,oCAAuC,WAC3C,IAAMC,EAAI,IAAIC,YAAY,YAAa,CACrCC,YAAY,IAGRxF,EAAUH,SAAS4F,cAAc,OAKvC,OAJAzF,EAAQ2B,iBAAiB,aAAa,WAAA,OAAM,QAE5C2D,EAAEI,iBACF1F,EAAQmB,cAAcmE,GACfA,EAAEK,iBAVkC,GAavCC,mBAAqB,WACrBC,kBAAqB,WACzB,IAAM7F,EAAUH,SAAS4F,cAAc,OAEvC,IACEzF,EAAQmF,iBAAiB,YACzB,MAAOW,GACP,OAAO,EAGT,OAAO,EATkB,GAYtBD,oBACHb,KAAO,SAAU/E,GACf,IAAK2F,mBAAmBjD,KAAK1C,GAC3B,OAAO8F,KAAKZ,iBAAiBlF,GAG/B,IAAM+F,EAAQC,QAAQF,KAAK3B,IAEtB4B,IACHD,KAAK3B,GAAK5E,OAAO,UAGnB,IAAI0G,EAAW,KACf,IACEjG,EAAWA,EAASkG,QAAQP,mBAAjB,IAAyCG,KAAK3B,IACzD8B,EAAWH,KAAKZ,iBAAiBlF,GAFnC,QAIO+F,GACHD,KAAKK,gBAAgB,MAIzB,OAAOF,GAGTd,QAAU,SAAUnF,GAClB,IAAK2F,mBAAmBjD,KAAK1C,GAC3B,OAAO8F,KAAKzF,cAAcL,GAG5B,IAAMoG,EAAUrB,KAAK3F,KAAK0G,KAAM9F,GAEhC,YAA0B,IAAfoG,EAAQ,GACVA,EAAQ,GAGV,OC7DX,IAAMC,EAAIxC,YACJyC,eAAiB,qBACjBC,eAAiB,OACjBC,cAAgB,SAChBC,cAAgB,GAClBC,SAAW,EACTC,aAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,aAAe,CACnB,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,UASF,SAASC,YAAYhH,EAASiH,GAC5B,OAAQA,GAAUA,EAAP,KAAeN,YAAiB3G,EAAQ2G,UAAYA,WAGjE,SAASO,SAASlH,GAChB,IAAMiH,EAAMD,YAAYhH,GAKxB,OAHAA,EAAQ2G,SAAWM,EACnBP,cAAcO,GAAOP,cAAcO,IAAQ,GAEpCP,cAAcO,GAGvB,SAASE,iBAAiBnH,EAASoH,GACjC,OAAO,SAASC,EAAQC,GAKtB,OAJID,EAAQE,QACVC,aAAaC,IAAIzH,EAASsH,EAAMI,KAAMN,GAGjCA,EAAGO,MAAM3H,EAAS,CAACsH,KAI9B,SAASM,2BAA2B5H,EAASC,EAAUmH,GACrD,OAAO,SAASC,EAAQC,GAGtB,IAFA,IAAMO,EAAc7H,EAAQmF,iBAAiBlF,GAElC6H,EAAWR,EAAXQ,OAAkBA,GAAUA,IAAW/B,KAAM+B,EAASA,EAAO9E,WACtE,IAAK,IAAI+E,EAAIF,EAAYG,OAAQD,KAC/B,GAAIF,EAAYE,KAAOD,EAKrB,OAJIT,EAAQE,QACVC,aAAaC,IAAIzH,EAASsH,EAAMI,KAAMN,GAGjCA,EAAGO,MAAMG,EAAQ,CAACR,IAM/B,OAAO,MAIX,SAASW,YAAYC,EAAQb,EAASc,QAA2B,IAA3BA,IAAAA,EAAqB,MAGzD,IAFA,IAAMC,EAAejG,OAAOC,KAAK8F,GAExBH,EAAI,EAAGM,EAAMD,EAAaJ,OAAQD,EAAIM,EAAKN,IAAK,CACvD,IAAMT,EAAQY,EAAOE,EAAaL,IAElC,GAAIT,EAAMgB,kBAAoBjB,GAAWC,EAAMa,qBAAuBA,EACpE,OAAOb,EAIX,OAAO,KAGT,SAASiB,gBAAgBC,EAAmBnB,EAASoB,GACnD,IAAMC,EAAgC,iBAAZrB,EACpBiB,EAAkBI,EAAaD,EAAepB,EAGhDsB,EAAYH,EAAkBrC,QAAQK,eAAgB,IACpDoC,EAAShC,aAAa+B,GAY5B,OAVIC,IACFD,EAAYC,GAGG7B,aAAa8B,QAAQF,IAAc,IAGlDA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,WAAW9I,EAASwI,EAAmBnB,EAASoB,EAAclB,GACrE,GAAiC,iBAAtBiB,GAAmCxI,EAA9C,CAIKqH,IACHA,EAAUoB,EACVA,EAAe,MAP4D,IAAAM,EAU5BR,gBAAgBC,EAAmBnB,EAASoB,GAAtFC,EAVsEK,EAAA,GAU1DT,EAV0DS,EAAA,GAUzCJ,EAVyCI,EAAA,GAWvEb,EAAShB,SAASlH,GAClBgJ,EAAWd,EAAOS,KAAeT,EAAOS,GAAa,IACrDM,EAAahB,YAAYe,EAAUV,EAAiBI,EAAarB,EAAU,MAEjF,GAAI4B,EACFA,EAAW1B,OAAS0B,EAAW1B,QAAUA,MAD3C,CAMA,IAAMN,EAAMD,YAAYsB,EAAiBE,EAAkBrC,QAAQI,eAAgB,KAC7Ea,EAAKsB,EACTd,2BAA2B5H,EAASqH,EAASoB,GAC7CtB,iBAAiBnH,EAASqH,GAE5BD,EAAGe,mBAAqBO,EAAarB,EAAU,KAC/CD,EAAGkB,gBAAkBA,EACrBlB,EAAGG,OAASA,EACZH,EAAGT,SAAWM,EACd+B,EAAS/B,GAAOG,EAEhBpH,EAAQ2B,iBAAiBgH,EAAWvB,EAAIsB,KAG1C,SAASQ,cAAclJ,EAASkI,EAAQS,EAAWtB,EAASc,GAC1D,IAAMf,EAAKa,YAAYC,EAAOS,GAAYtB,EAASc,GAE9Cf,IAILpH,EAAQ6B,oBAAoB8G,EAAWvB,EAAInB,QAAQkC,WAC5CD,EAAOS,GAAWvB,EAAGT,WAG9B,SAASwC,yBAAyBnJ,EAASkI,EAAQS,EAAWS,GAC5D,IAAMC,EAAoBnB,EAAOS,IAAc,GAE/CxG,OAAOC,KAAKiH,GACThH,SAAQ,SAAAiH,GACP,GAAIA,EAAWT,QAAQO,IAAc,EAAG,CACtC,IAAM9B,EAAQ+B,EAAkBC,GAEhCJ,cAAclJ,EAASkI,EAAQS,EAAWrB,EAAMgB,gBAAiBhB,EAAMa,wBAK/E,IAAMX,aAAe,CACnB+B,GADmB,SAChBvJ,EAASsH,EAAOD,EAASoB,GAC1BK,WAAW9I,EAASsH,EAAOD,EAASoB,GAAc,IAGpDe,IALmB,SAKfxJ,EAASsH,EAAOD,EAASoB,GAC3BK,WAAW9I,EAASsH,EAAOD,EAASoB,GAAc,IAGpDhB,IATmB,SASfzH,EAASwI,EAAmBnB,EAASoB,GACvC,GAAiC,iBAAtBD,GAAmCxI,EAA9C,CADqD,IAAAyJ,EAKJlB,gBAAgBC,EAAmBnB,EAASoB,GAAtFC,EAL8Ce,EAAA,GAKlCnB,EALkCmB,EAAA,GAKjBd,EALiBc,EAAA,GAM/CC,EAAcf,IAAcH,EAC5BN,EAAShB,SAASlH,GAClB2J,EAA8C,MAAhCnB,EAAkBoB,OAAO,GAE7C,QAA+B,IAApBtB,EAAX,CAUIqB,GACFxH,OAAOC,KAAK8F,GACT7F,SAAQ,SAAAwH,GACPV,yBAAyBnJ,EAASkI,EAAQ2B,EAAcrB,EAAkBsB,MAAM,OAItF,IAAMT,EAAoBnB,EAAOS,IAAc,GAC/CxG,OAAOC,KAAKiH,GACThH,SAAQ,SAAA0H,GACP,IAAMT,EAAaS,EAAY5D,QAAQM,cAAe,IAEtD,IAAKiD,GAAelB,EAAkBK,QAAQS,IAAe,EAAG,CAC9D,IAAMhC,EAAQ+B,EAAkBU,GAEhCb,cAAclJ,EAASkI,EAAQS,EAAWrB,EAAMgB,gBAAiBhB,EAAMa,4BAzB7E,CAEE,IAAKD,IAAWA,EAAOS,GACrB,OAGFO,cAAclJ,EAASkI,EAAQS,EAAWL,EAAiBI,EAAarB,EAAU,SAwBtF2C,QAjDmB,SAiDXhK,EAASsH,EAAO2C,GACtB,GAAqB,iBAAV3C,IAAuBtH,EAChC,OAAO,KAGT,IAIIkK,EAJEvB,EAAYrB,EAAMnB,QAAQK,eAAgB,IAC1CkD,EAAcpC,IAAUqB,EACxBwB,EAAWpD,aAAa8B,QAAQF,IAAc,EAGhDyB,GAAU,EACVC,GAAiB,EACjB1E,GAAmB,EACnB2E,EAAM,KAmDV,OAjDIZ,GAAepD,IACjB4D,EAAc5D,EAAElF,MAAMkG,EAAO2C,GAE7B3D,EAAEtG,GAASgK,QAAQE,GACnBE,GAAWF,EAAYK,uBACvBF,GAAkBH,EAAYM,gCAC9B7E,EAAmBuE,EAAYO,sBAG7BN,GACFG,EAAMzK,SAAS6K,YAAY,eACvBC,UAAUhC,EAAWyB,GAAS,GAElCE,EAAM,IAAI/E,YAAY+B,EAAO,CAC3B8C,QAAAA,EACA5E,YAAY,SAKI,IAATyE,GACT9H,OAAOC,KAAK6H,GACT5H,SAAQ,SAAAiC,GACPnC,OAAOyI,eAAeN,EAAKhG,EAAK,CAC9BE,IAD8B,WAE5B,OAAOyF,EAAK3F,SAMlBqB,IACF2E,EAAI5E,iBAECL,qCACHlD,OAAOyI,eAAeN,EAAK,mBAAoB,CAC7C9F,IAAK,WAAA,OAAM,MAKb6F,GACFrK,EAAQmB,cAAcmJ,GAGpBA,EAAI3E,uBAA2C,IAAhBuE,GACjCA,EAAYxE,iBAGP4E,ICrTLO,KAAO,QACPC,QAAU,eACVC,SAAW,WACXC,UAAS,IAAOD,SAChBE,aAAe,YAEfC,iBAAmB,yBAEnBC,YAAW,QAAWH,UACtBI,aAAY,SAAYJ,UACxBK,qBAAoB,QAAWL,UAAYC,aAE3CK,gBAAkB,QAClBC,eAAiB,OACjBC,eAAiB,OAQjBC,MAAAA,WACJ,SAAAA,EAAYzL,GACV+F,KAAK2F,SAAW1L,EAEZ+F,KAAK2F,UACP/G,KAAKC,QAAQ5E,EAAS+K,SAAUhF,iCAYpC4F,MAAA,SAAM3L,GACJ,IAAI4L,EAAc7F,KAAK2F,SACnB1L,IACF4L,EAAc7F,KAAK8F,gBAAgB7L,IAGrC,IAAM8L,EAAc/F,KAAKgG,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAYnG,kBAIxCI,KAAKiG,eAAeJ,MAGtBK,QAAA,WACEtH,KAAKI,WAAWgB,KAAK2F,SAAUX,UAC/BhF,KAAK2F,SAAW,QAKlBG,gBAAA,SAAgB7L,GACd,OAAOO,uBAAuBP,IAAYA,EAAQkM,QAAR,IAAoBZ,oBAGhES,mBAAA,SAAmB/L,GACjB,OAAOwH,aAAawC,QAAQhK,EAASmL,gBAGvCa,eAAA,SAAehM,GAAS,IAAAmM,EAAApG,KAGtB,GAFA/F,EAAQoM,UAAUC,OAAOb,gBAEpBxL,EAAQoM,UAAUE,SAASf,gBAAhC,CAKA,IAAM3K,EAAqBJ,iCAAiCR,GAE5DwH,aACGgC,IAAIxJ,EAASf,gBAAgB,WAAA,OAAMkN,EAAKI,gBAAgBvM,MAC3DuB,qBAAqBvB,EAASY,QAR5BmF,KAAKwG,gBAAgBvM,MAWzBuM,gBAAA,SAAgBvM,GACVA,EAAQgD,YACVhD,EAAQgD,WAAWwJ,YAAYxM,GAGjCwH,aAAawC,QAAQhK,EAASoL,iBAKzBqB,gBAAP,SAAuBxK,GACrB,OAAO8D,KAAK2G,MAAK,WACf,IAAInI,EAAOI,KAAKG,QAAQiB,KAAMgF,UAEzBxG,IACHA,EAAO,IAAIkH,EAAM1F,OAGJ,UAAX9D,GACFsC,EAAKtC,GAAQ8D,YAKZ4G,cAAP,SAAqBC,GACnB,OAAO,SAAUtF,GACXA,GACFA,EAAM5B,iBAGRkH,EAAcjB,MAAM5F,UAIjB8G,YAAP,SAAmB7M,GACjB,OAAO2E,KAAKG,QAAQ9E,EAAS+K,8DArF7B,OAAOD,cAZLW,GA0GNjE,aACG+B,GAAG1J,SAAUwL,qBAAsBH,iBAAkBO,MAAMkB,cAAc,IAAIlB,QAEhF,IAAMnF,IAAIxC,YAUV,GAAIwC,IAAG,CACL,IAAMwG,mBAAqBxG,IAAEc,GAAGyD,MAChCvE,IAAEc,GAAGyD,MAAQY,MAAMgB,gBACnBnG,IAAEc,GAAGyD,MAAMkC,YAActB,MACzBnF,IAAEc,GAAGyD,MAAMmC,WAAa,WAEtB,OADA1G,IAAEc,GAAGyD,MAAQiC,mBACNrB,MAAMgB,iBCzJjB,IAAM5B,OAAO,SACPC,UAAU,eACVC,WAAW,YACXC,YAAS,IAAOD,WAChBE,eAAe,YAEfgC,kBAAoB,SAEpBC,qBAAuB,yBAEvB7B,uBAAoB,QAAWL,YAAYC,eAQ3CkC,OAAAA,WACJ,SAAAA,EAAYnN,GACV+F,KAAK2F,SAAW1L,EAChB2E,KAAKC,QAAQ5E,EAAS+K,WAAUhF,iCAWlCqH,OAAA,WAEErH,KAAK2F,SAAS2B,aAAa,eAAgBtH,KAAK2F,SAASU,UAAUgB,OAAOH,uBAG5EhB,QAAA,WACEtH,KAAKI,WAAWgB,KAAK2F,SAAUX,YAC/BhF,KAAK2F,SAAW,QAKXe,gBAAP,SAAuBxK,GACrB,OAAO8D,KAAK2G,MAAK,WACf,IAAInI,EAAOI,KAAKG,QAAQiB,KAAMgF,YAEzBxG,IACHA,EAAO,IAAI4I,EAAOpH,OAGL,WAAX9D,GACFsC,EAAKtC,WAKJ4K,YAAP,SAAmB7M,GACjB,OAAO2E,KAAKG,QAAQ9E,EAAS+K,gEAhC7B,OAAOD,gBATLqC,GAmDN3F,aAAa+B,GAAG1J,SAAUwL,uBAAsB6B,sBAAsB,SAAA5F,GACpEA,EAAM5B,iBAEN,IAAM4H,EAAShG,EAAMQ,OAAOoE,QAAQgB,sBAEhC3I,EAAOI,KAAKG,QAAQwI,EAAQvC,YAC3BxG,IACHA,EAAO,IAAI4I,OAAOG,IAGpB/I,EAAK6I,YAGP,IAAM9G,IAAIxC,YASV,GAAIwC,IAAG,CACL,IAAMwG,qBAAqBxG,IAAEc,GAAGyD,QAChCvE,IAAEc,GAAGyD,QAAQsC,OAAOV,gBACpBnG,IAAEc,GAAGyD,QAAMkC,YAAcI,OAEzB7G,IAAEc,GAAGyD,QAAMmC,WAAa,WAEtB,OADA1G,IAAEc,GAAGyD,QAAQiC,qBACNK,OAAOV,iBC5GlB,SAASc,cAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQC,OAAOD,GAAKpO,WACfqO,OAAOD,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASE,iBAAiBpJ,GACxB,OAAOA,EAAI6B,QAAQ,UAAU,SAAAwH,GAAG,MAAA,IAAQA,EAAIpO,iBAG9C,IAAMqO,YAAc,CAClBC,iBADkB,SACD7N,EAASsE,EAAK9B,GAC7BxC,EAAQqN,aAAR,QAA6BK,iBAAiBpJ,GAAQ9B,IAGxDsL,oBALkB,SAKE9N,EAASsE,GAC3BtE,EAAQoG,gBAAR,QAAgCsH,iBAAiBpJ,KAGnDyJ,kBATkB,SASA/N,GAChB,IAAKA,EACH,MAAO,GAGT,IAAMgO,EAAUC,eAAA,GACXjO,EAAQkO,SAOb,OAJA/L,OAAOC,KAAK4L,GAAY3L,SAAQ,SAAAiC,GAC9B0J,EAAW1J,GAAOiJ,cAAcS,EAAW1J,OAGtC0J,GAGTG,iBAzBkB,SAyBDnO,EAASsE,GACxB,OAAOiJ,cAAcvN,EAAQE,aAAR,QAA6BwN,iBAAiBpJ,MAGrE8J,OA7BkB,SA6BXpO,GACL,IAAMqO,EAAOrO,EAAQsO,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAM1O,SAASmE,KAAKwK,UAC9BC,KAAMJ,EAAKI,KAAO5O,SAASmE,KAAK0K,aAIpCC,SAtCkB,SAsCT3O,GACP,MAAO,CACLuO,IAAKvO,EAAQ4O,UACbH,KAAMzO,EAAQ6O,aAIlBC,YA7CkB,SA6CN9O,EAAS+O,GACd/O,IAIDA,EAAQoM,UAAUE,SAASyC,GAC7B/O,EAAQoM,UAAUC,OAAO0C,GAEzB/O,EAAQoM,UAAU4C,IAAID,MCrEtBE,UAAY,EAEZC,eAAiB,CACrB7I,QADqB,SACbrG,EAASC,GACf,OAAOD,EAAQqG,QAAQpG,IAGzB+E,KALqB,SAKhB/E,EAAUD,GAAoC,IAAAmP,EACjD,YADiD,IAApCnP,IAAAA,EAAUH,SAASyD,kBACzB6L,EAAA,IAAGC,OAAHzH,MAAAwH,EAAaE,KAAOhQ,KAAKW,EAASC,KAG3CmF,QATqB,SASbnF,EAAUD,GAChB,YADoD,IAApCA,IAAAA,EAAUH,SAASyD,iBAC5B8B,QAAQ/F,KAAKW,EAASC,IAG/BqP,SAbqB,SAaZtP,EAASC,GAAU,IAAAsP,EACpBD,GAAWC,EAAA,IAAGH,OAAHzH,MAAA4H,EAAavP,EAAQsP,UAEtC,OAAOA,EAASE,QAAO,SAAAC,GAAK,OAAIA,EAAMpJ,QAAQpG,OAGhDyP,QAnBqB,SAmBb1P,EAASC,GAKf,IAJA,IAAMyP,EAAU,GAEZC,EAAW3P,EAAQgD,WAEhB2M,GAAYA,EAASrO,WAAasO,KAAKC,cAAgBF,EAASrO,WAAa2N,WAC9ElJ,KAAKM,QAAQsJ,EAAU1P,IACzByP,EAAQI,KAAKH,GAGfA,EAAWA,EAAS3M,WAGtB,OAAO0M,GAGTK,KAnCqB,SAmChB/P,EAASC,GAGZ,IAFA,IAAI+P,EAAWhQ,EAAQiQ,uBAEhBD,GAAU,CACf,GAAIA,EAAS3J,QAAQpG,GACnB,MAAO,CAAC+P,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAjDqB,SAiDhBlQ,EAASC,GAGZ,IAFA,IAAIiQ,EAAOlQ,EAAQmQ,mBAEZD,GAAM,CACX,GAAInK,KAAKM,QAAQ6J,EAAMjQ,GACrB,MAAO,CAACiQ,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KChDLtF,OAAO,WACPC,UAAU,eACVC,WAAW,cACXC,YAAS,IAAOD,WAChBE,eAAe,YAEfmF,eAAiB,YACjBC,gBAAkB,aAClBC,uBAAyB,IACzBC,gBAAkB,GAElBC,QAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,YAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,eAAiB,OACjBC,eAAiB,OACjBC,eAAiB,OACjBC,gBAAkB,QAElBC,YAAW,QAAWpG,YACtBqG,WAAU,OAAUrG,YACpBsG,cAAa,UAAatG,YAC1BuG,iBAAgB,aAAgBvG,YAChCwG,iBAAgB,aAAgBxG,YAChCyG,iBAAgB,aAAgBzG,YAChC0G,gBAAe,YAAe1G,YAC9B2G,eAAc,WAAc3G,YAC5B4G,kBAAiB,cAAiB5G,YAClC6G,gBAAe,YAAe7G,YAC9B8G,iBAAgB,YAAe9G,YAC/B+G,oBAAmB,OAAU/G,YAAYC,eACzCI,uBAAoB,QAAWL,YAAYC,eAE3C+G,oBAAsB,WACtB/E,oBAAoB,SACpBgF,iBAAmB,QACnBC,iBAAmB,sBACnBC,gBAAkB,qBAClBC,gBAAkB,qBAClBC,gBAAkB,qBAClBC,yBAA2B,gBAE3BC,gBAAkB,UAClBC,qBAAuB,wBACvBC,cAAgB,iBAChBC,kBAAoB,qBACpBC,mBAAqB,2CACrBC,oBAAsB,uBACtBC,oBAAsB,gCACtBC,mBAAqB,yBAErBC,YAAc,CAClBC,MAAO,QACPC,IAAK,OAQDC,SAAAA,WACJ,SAAAA,EAAYlT,EAASiC,GACnB8D,KAAKoN,OAAS,KACdpN,KAAKqN,UAAY,KACjBrN,KAAKsN,eAAiB,KACtBtN,KAAKuN,WAAY,EACjBvN,KAAKwN,YAAa,EAClBxN,KAAKyN,aAAe,KACpBzN,KAAK0N,YAAc,EACnB1N,KAAK2N,YAAc,EAEnB3N,KAAK4N,QAAU5N,KAAK6N,WAAW3R,GAC/B8D,KAAK2F,SAAW1L,EAChB+F,KAAK8N,mBAAqB3E,eAAe9J,QAAQwN,oBAAqB7M,KAAK2F,UAC3E3F,KAAK+N,gBAAkB,iBAAkBjU,SAASyD,iBAAmByQ,UAAUC,eAAiB,EAChGjO,KAAKkO,cAAgBhO,QAAQvF,OAAOwT,cAEpCnO,KAAKoO,qBACLxP,KAAKC,QAAQ5E,EAAS+K,WAAUhF,iCAelCmK,KAAA,WACOnK,KAAKwN,YACRxN,KAAKqO,OAAOpD,mBAIhBqD,gBAAA,YAGOxU,SAASyU,QAAUxR,UAAUiD,KAAK2F,WACrC3F,KAAKmK,UAITH,KAAA,WACOhK,KAAKwN,YACRxN,KAAKqO,OAAOnD,mBAIhBL,MAAA,SAAMtJ,GACCA,IACHvB,KAAKuN,WAAY,GAGfpE,eAAe9J,QAAQuN,mBAAoB5M,KAAK2F,YAClDxK,qBAAqB6E,KAAK2F,UAC1B3F,KAAKwO,OAAM,IAGbC,cAAczO,KAAKqN,WACnBrN,KAAKqN,UAAY,QAGnBmB,MAAA,SAAMjN,GACCA,IACHvB,KAAKuN,WAAY,GAGfvN,KAAKqN,YACPoB,cAAczO,KAAKqN,WACnBrN,KAAKqN,UAAY,MAGfrN,KAAK4N,SAAW5N,KAAK4N,QAAQlD,WAAa1K,KAAKuN,YACjDvN,KAAKqN,UAAYqB,aACd5U,SAAS6U,gBAAkB3O,KAAKsO,gBAAkBtO,KAAKmK,MAAMyE,KAAK5O,MACnEA,KAAK4N,QAAQlD,cAKnBmE,GAAA,SAAGC,GAAO,IAAA1I,EAAApG,KACRA,KAAKsN,eAAiBnE,eAAe9J,QAAQoN,qBAAsBzM,KAAK2F,UACxE,IAAMoJ,EAAc/O,KAAKgP,cAAchP,KAAKsN,gBAE5C,KAAIwB,EAAQ9O,KAAKoN,OAAOnL,OAAS,GAAK6M,EAAQ,GAI9C,GAAI9O,KAAKwN,WACP/L,aAAagC,IAAIzD,KAAK2F,SAAU2F,YAAY,WAAA,OAAMlF,EAAKyI,GAAGC,UAD5D,CAKA,GAAIC,IAAgBD,EAGlB,OAFA9O,KAAK6K,aACL7K,KAAKwO,QAIP,IAAMS,EAAYH,EAAQC,EACxB9D,eACAC,eAEFlL,KAAKqO,OAAOY,EAAWjP,KAAKoN,OAAO0B,QAGrC5I,QAAA,WACEzE,aAAaC,IAAI1B,KAAK2F,SAAUV,aAChCrG,KAAKI,WAAWgB,KAAK2F,SAAUX,YAE/BhF,KAAKoN,OAAS,KACdpN,KAAK4N,QAAU,KACf5N,KAAK2F,SAAW,KAChB3F,KAAKqN,UAAY,KACjBrN,KAAKuN,UAAY,KACjBvN,KAAKwN,WAAa,KAClBxN,KAAKsN,eAAiB,KACtBtN,KAAK8N,mBAAqB,QAK5BD,WAAA,SAAW3R,GAMT,OALAA,EAAMgM,eAAAA,eAAA,GACDuC,SACAvO,GAELF,gBAAgB8I,OAAM5I,EAAQ8O,aACvB9O,KAGTgT,aAAA,WACE,IAAMC,EAAYxV,KAAKyV,IAAIpP,KAAK2N,aAEhC,KAAIwB,GAAa3E,iBAAjB,CAIA,IAAMyE,EAAYE,EAAYnP,KAAK2N,YAEnC3N,KAAK2N,YAAc,EAGfsB,EAAY,GACdjP,KAAKgK,OAIHiF,EAAY,GACdjP,KAAKmK,WAITiE,mBAAA,WAAqB,IAAAiB,EAAArP,KACfA,KAAK4N,QAAQjD,UACflJ,aACG+B,GAAGxD,KAAK2F,SAAU4F,eAAe,SAAAhK,GAAK,OAAI8N,EAAKC,SAAS/N,MAGlC,UAAvBvB,KAAK4N,QAAQ/C,QACfpJ,aACG+B,GAAGxD,KAAK2F,SAAU6F,kBAAkB,SAAAjK,GAAK,OAAI8N,EAAKxE,MAAMtJ,MAC3DE,aACG+B,GAAGxD,KAAK2F,SAAU8F,kBAAkB,SAAAlK,GAAK,OAAI8N,EAAKb,MAAMjN,OAGzDvB,KAAK4N,QAAQ7C,OAAS/K,KAAK+N,iBAC7B/N,KAAKuP,6BAITA,wBAAA,WAA0B,IAAAC,EAAAxP,KAClByP,EAAQ,SAAAlO,GACRiO,EAAKtB,eAAiBlB,YAAYzL,EAAMmO,YAAY5S,eACtD0S,EAAK9B,YAAcnM,EAAMoO,QACfH,EAAKtB,gBACfsB,EAAK9B,YAAcnM,EAAMqO,QAAQ,GAAGD,UAalCE,EAAM,SAAAtO,GACNiO,EAAKtB,eAAiBlB,YAAYzL,EAAMmO,YAAY5S,iBACtD0S,EAAK7B,YAAcpM,EAAMoO,QAAUH,EAAK9B,aAG1C8B,EAAKN,eACsB,UAAvBM,EAAK5B,QAAQ/C,QASf2E,EAAK3E,QACD2E,EAAK/B,cACPqC,aAAaN,EAAK/B,cAGpB+B,EAAK/B,aAAe1R,YAAW,SAAAwF,GAAK,OAAIiO,EAAKhB,MAAMjN,KAAQgJ,uBAAyBiF,EAAK5B,QAAQlD,YAIrGvB,eAAelK,KAAK0N,kBAAmB3M,KAAK2F,UAAUrJ,SAAQ,SAAAyT,GAC5DtO,aAAa+B,GAAGuM,EAAShE,kBAAkB,SAAAxM,GAAC,OAAIA,EAAEI,uBAGhDK,KAAKkO,eACPzM,aAAa+B,GAAGxD,KAAK2F,SAAUkG,mBAAmB,SAAAtK,GAAK,OAAIkO,EAAMlO,MACjEE,aAAa+B,GAAGxD,KAAK2F,SAAUmG,iBAAiB,SAAAvK,GAAK,OAAIsO,EAAItO,MAE7DvB,KAAK2F,SAASU,UAAU4C,IAAIsD,4BAE5B9K,aAAa+B,GAAGxD,KAAK2F,SAAU+F,kBAAkB,SAAAnK,GAAK,OAAIkO,EAAMlO,MAChEE,aAAa+B,GAAGxD,KAAK2F,SAAUgG,iBAAiB,SAAApK,GAAK,OA5C1C,SAAAA,GAEPA,EAAMqO,SAAWrO,EAAMqO,QAAQ3N,OAAS,EAC1CuN,EAAK7B,YAAc,EAEnB6B,EAAK7B,YAAcpM,EAAMqO,QAAQ,GAAGD,QAAUH,EAAK9B,YAuCIsC,CAAKzO,MAC9DE,aAAa+B,GAAGxD,KAAK2F,SAAUiG,gBAAgB,SAAArK,GAAK,OAAIsO,EAAItO,UAIhE+N,SAAA,SAAS/N,GACP,IAAI,kBAAkB3E,KAAK2E,EAAMQ,OAAOkO,SAIxC,OAAQ1O,EAAMhD,KACZ,KAAK8L,eACH9I,EAAM5B,iBACNK,KAAKgK,OACL,MACF,KAAKM,gBACH/I,EAAM5B,iBACNK,KAAKmK,WAMX6E,cAAA,SAAc/U,GAKZ,OAJA+F,KAAKoN,OAASnT,GAAWA,EAAQgD,WAC/BkM,eAAelK,KAAKyN,cAAezS,EAAQgD,YAC3C,GAEK+C,KAAKoN,OAAOtK,QAAQ7I,MAG7BiW,oBAAA,SAAoBjB,EAAWkB,GAC7B,IAAMC,EAAkBnB,IAAchE,eAChCoF,EAAkBpB,IAAc/D,eAChC6D,EAAc/O,KAAKgP,cAAcmB,GACjCG,EAAgBtQ,KAAKoN,OAAOnL,OAAS,EAI3C,IAHuBoO,GAAmC,IAAhBtB,GACjBqB,GAAmBrB,IAAgBuB,KAEtCtQ,KAAK4N,QAAQ9C,KACjC,OAAOqF,EAGT,IACMI,GAAaxB,GADLE,IAAc/D,gBAAkB,EAAI,IACRlL,KAAKoN,OAAOnL,OAEtD,OAAsB,IAAfsO,EACLvQ,KAAKoN,OAAOpN,KAAKoN,OAAOnL,OAAS,GACjCjC,KAAKoN,OAAOmD,MAGhBC,mBAAA,SAAmBC,EAAeC,GAChC,IAAMC,EAAc3Q,KAAKgP,cAAcyB,GACjCG,EAAY5Q,KAAKgP,cAAc7F,eAAe9J,QAAQoN,qBAAsBzM,KAAK2F,WAEvF,OAAOlE,aAAawC,QAAQjE,KAAK2F,SAAU0F,YAAa,CACtDoF,cAAAA,EACAxB,UAAWyB,EACXG,KAAMD,EACN/B,GAAI8B,OAIRG,2BAAA,SAA2B7W,GACzB,GAAI+F,KAAK8N,mBAAoB,CAE3B,IADA,IAAMiD,EAAa5H,eAAelK,KAAKuN,gBAAiBxM,KAAK8N,oBACpD9L,EAAI,EAAGA,EAAI+O,EAAW9O,OAAQD,IACrC+O,EAAW/O,GAAGqE,UAAUC,OAAOY,qBAGjC,IAAM8J,EAAgBhR,KAAK8N,mBAAmBvE,SAC5CvJ,KAAKgP,cAAc/U,IAGjB+W,GACFA,EAAc3K,UAAU4C,IAAI/B,yBAKlCmH,OAAA,SAAOY,EAAWhV,GAAS,IASrBgX,EACAC,EACAR,EAXqBS,EAAAnR,KACnBmQ,EAAgBhH,eAAe9J,QAAQoN,qBAAsBzM,KAAK2F,UAClEyL,EAAqBpR,KAAKgP,cAAcmB,GACxCkB,EAAcpX,GAAYkW,GAC9BnQ,KAAKkQ,oBAAoBjB,EAAWkB,GAEhCmB,EAAmBtR,KAAKgP,cAAcqC,GACtCE,EAAYrR,QAAQF,KAAKqN,WAgB/B,GAVI4B,IAAchE,gBAChBgG,EAAuB7E,gBACvB8E,EAAiB7E,gBACjBqE,EAAqBvF,iBAErB8F,EAAuB9E,iBACvB+E,EAAiB5E,gBACjBoE,EAAqBtF,iBAGnBiG,GAAeA,EAAYhL,UAAUE,SAASW,qBAChDlH,KAAKwN,YAAa,OAKpB,IADmBxN,KAAKwQ,mBAAmBa,EAAaX,GACzC9Q,kBAIVuQ,GAAkBkB,EAAvB,CAaA,GARArR,KAAKwN,YAAa,EAEd+D,GACFvR,KAAK6K,QAGP7K,KAAK8Q,2BAA2BO,GAE5BrR,KAAK2F,SAASU,UAAUE,SAAS2F,kBAAmB,CACtDmF,EAAYhL,UAAU4C,IAAIiI,GAE1BrT,OAAOwT,GAEPlB,EAAc9J,UAAU4C,IAAIgI,GAC5BI,EAAYhL,UAAU4C,IAAIgI,GAE1B,IAAMO,EAAsBC,SAASJ,EAAYlX,aAAa,iBAAkB,IAC5EqX,GACFxR,KAAK4N,QAAQ8D,gBAAkB1R,KAAK4N,QAAQ8D,iBAAmB1R,KAAK4N,QAAQlD,SAC5E1K,KAAK4N,QAAQlD,SAAW8G,GAExBxR,KAAK4N,QAAQlD,SAAW1K,KAAK4N,QAAQ8D,iBAAmB1R,KAAK4N,QAAQlD,SAGvE,IAAM7P,EAAqBJ,iCAAiC0V,GAE5D1O,aACGgC,IAAI0M,EAAejX,gBAAgB,WAClCmY,EAAYhL,UAAUC,OAAO2K,EAAsBC,GACnDG,EAAYhL,UAAU4C,IAAI/B,qBAE1BiJ,EAAc9J,UAAUC,OAAOY,oBAAmBgK,EAAgBD,GAElEE,EAAK3D,YAAa,EAElBzR,YAAW,WACT0F,aAAawC,QAAQkN,EAAKxL,SAAU2F,WAAY,CAC9CmF,cAAeY,EACfpC,UAAWyB,EACXG,KAAMO,EACNvC,GAAIyC,MAEL,MAGP9V,qBAAqB2U,EAAetV,QAEpCsV,EAAc9J,UAAUC,OAAOY,qBAC/BmK,EAAYhL,UAAU4C,IAAI/B,qBAE1BlH,KAAKwN,YAAa,EAClB/L,aAAawC,QAAQjE,KAAK2F,SAAU2F,WAAY,CAC9CmF,cAAeY,EACfpC,UAAWyB,EACXG,KAAMO,EACNvC,GAAIyC,IAIJC,GACFvR,KAAKwO,YAMFmD,kBAAP,SAAyB1X,EAASiC,GAChC,IAAIsC,EAAOI,KAAKG,QAAQ9E,EAAS+K,YAC7B4I,EAAO1F,eAAAA,eAAA,GACNuC,SACA5C,YAAYG,kBAAkB/N,IAGb,iBAAXiC,IACT0R,EAAO1F,eAAAA,eAAA,GACF0F,GACA1R,IAIP,IAAM0V,EAA2B,iBAAX1V,EAAsBA,EAAS0R,EAAQhD,MAM7D,GAJKpM,IACHA,EAAO,IAAI2O,EAASlT,EAAS2T,IAGT,iBAAX1R,EACTsC,EAAKqQ,GAAG3S,QACH,GAAsB,iBAAX0V,EAAqB,CACrC,QAA4B,IAAjBpT,EAAKoT,GACd,MAAM,IAAIC,UAAJ,oBAAkCD,EAAlC,KAGRpT,EAAKoT,UACIhE,EAAQlD,UAAYkD,EAAQkE,OACrCtT,EAAKqM,QACLrM,EAAKgQ,YAIF9H,gBAAP,SAAuBxK,GACrB,OAAO8D,KAAK2G,MAAK,WACfwG,EAASwE,kBAAkB3R,KAAM9D,SAI9B6V,oBAAP,SAA2BxQ,GACzB,IAAMQ,EAASvH,uBAAuBwF,MAEtC,GAAK+B,GAAWA,EAAOsE,UAAUE,SAAS0F,qBAA1C,CAIA,IAAM/P,EAAMgM,eAAAA,eAAA,GACPL,YAAYG,kBAAkBjG,IAC9B8F,YAAYG,kBAAkBhI,OAE7BgS,EAAahS,KAAK7F,aAAa,iBAEjC6X,IACF9V,EAAOwO,UAAW,GAGpByC,EAASwE,kBAAkB5P,EAAQ7F,GAE/B8V,GACFpT,KAAKG,QAAQgD,EAAQiD,YAAU6J,GAAGmD,GAGpCzQ,EAAM5B,qBAGDmH,YAAP,SAAmB7M,GACjB,OAAO2E,KAAKG,QAAQ9E,EAAS+K,gEAxc7B,OAAOD,0CAIP,OAAO0F,cA5BL0C,GA0eN1L,aACG+B,GAAG1J,SAAUwL,uBAAsBwH,oBAAqBK,SAAS4E,qBAEpEtQ,aAAa+B,GAAG7I,OAAQqR,qBAAqB,WAG3C,IAFA,IAAMiG,EAAY9I,eAAelK,KAAK8N,oBAE7B/K,EAAI,EAAGM,EAAM2P,EAAUhQ,OAAQD,EAAIM,EAAKN,IAC/CmL,SAASwE,kBAAkBM,EAAUjQ,GAAIpD,KAAKG,QAAQkT,EAAUjQ,GAAIgD,gBAIxE,IAAMzE,IAAIxC,YASV,GAAIwC,IAAG,CACL,IAAMwG,qBAAqBxG,IAAEc,GAAGyD,QAChCvE,IAAEc,GAAGyD,QAAQqI,SAASzG,gBACtBnG,IAAEc,GAAGyD,QAAMkC,YAAcmG,SACzB5M,IAAEc,GAAGyD,QAAMmC,WAAa,WAEtB,OADA1G,IAAEc,GAAGyD,QAAQiC,qBACNoG,SAASzG,iBChlBpB,IAAM5B,OAAO,WACPC,UAAU,eACVC,WAAW,cACXC,YAAS,IAAOD,WAChBE,eAAe,YAEfuF,UAAU,CACdpD,QAAQ,EACR6K,OAAQ,IAGJlH,cAAc,CAClB3D,OAAQ,UACR6K,OAAQ,oBAGJC,WAAU,OAAUlN,YACpBmN,YAAW,QAAWnN,YACtBoN,WAAU,OAAUpN,YACpBqN,aAAY,SAAYrN,YACxBK,uBAAoB,QAAWL,YAAYC,eAE3CqN,gBAAkB,OAClBC,oBAAsB,WACtBC,sBAAwB,aACxBC,qBAAuB,YAEvBC,MAAQ,QACRC,OAAS,SAETC,iBAAmB,qBACnB1L,uBAAuB,2BAQvB2L,SAAAA,WACJ,SAAAA,EAAY7Y,EAASiC,GACnB8D,KAAK+S,kBAAmB,EACxB/S,KAAK2F,SAAW1L,EAChB+F,KAAK4N,QAAU5N,KAAK6N,WAAW3R,GAC/B8D,KAAKgT,cAAgB7J,eAAelK,KAC/BkI,uBAAH,WAAkClN,EAAQoE,GAA1C,MACG8I,uBADH,kBACyClN,EAAQoE,GADjD,MAMF,IAFA,IAAM4U,EAAa9J,eAAelK,KAAKkI,wBAE9BnF,EAAI,EAAGM,EAAM2Q,EAAWhR,OAAQD,EAAIM,EAAKN,IAAK,CACrD,IAAMkR,EAAOD,EAAWjR,GAClB9H,EAAWI,uBAAuB4Y,GAClCC,EAAgBhK,eAAelK,KAAK/E,GACvCuP,QAAO,SAAA2J,GAAS,OAAIA,IAAcnZ,KAEpB,OAAbC,GAAqBiZ,EAAclR,SACrCjC,KAAKqT,UAAYnZ,EACjB8F,KAAKgT,cAAcjJ,KAAKmJ,IAI5BlT,KAAKsT,QAAUtT,KAAK4N,QAAQsE,OAASlS,KAAKuT,aAAe,KAEpDvT,KAAK4N,QAAQsE,QAChBlS,KAAKwT,0BAA0BxT,KAAK2F,SAAU3F,KAAKgT,eAGjDhT,KAAK4N,QAAQvG,QACfrH,KAAKqH,SAGPzI,KAAKC,QAAQ5E,EAAS+K,WAAUhF,iCAelCqH,OAAA,WACMrH,KAAK2F,SAASU,UAAUE,SAASgM,iBACnCvS,KAAKyT,OAELzT,KAAK0T,UAITA,KAAA,WAAO,IAAAtN,EAAApG,KACL,IAAIA,KAAK+S,mBACP/S,KAAK2F,SAASU,UAAUE,SAASgM,iBADnC,CAKA,IAAIoB,EACAC,EAEA5T,KAAKsT,SAUgB,KATvBK,EAAUxK,eAAelK,KAAK4T,iBAAkB7S,KAAKsT,SAClD7J,QAAO,SAAAyJ,GACN,MAAmC,iBAAxB9M,EAAKwH,QAAQsE,OACfgB,EAAK/Y,aAAa,iBAAmBiM,EAAKwH,QAAQsE,OAGpDgB,EAAK7M,UAAUE,SAASiM,yBAGvBvQ,SACV0R,EAAU,MAId,IAAME,EAAY1K,eAAe9J,QAAQW,KAAKqT,WAC9C,GAAIM,EAAS,CACX,IAAMG,EAAiBH,EAAQlK,QAAO,SAAAyJ,GAAI,OAAIW,IAAcX,KAG5D,IAFAU,EAAcE,EAAe,GAAKlV,KAAKG,QAAQ+U,EAAe,GAAI9O,YAAY,OAE3D4O,EAAYb,iBAC7B,OAKJ,IADmBtR,aAAawC,QAAQjE,KAAK2F,SAAUwM,YACxCvS,iBAAf,CAII+T,GACFA,EAAQrX,SAAQ,SAAAyX,GACVF,IAAcE,GAChBjB,EAASkB,kBAAkBD,EAAY,QAGpCH,GACHhV,KAAKC,QAAQkV,EAAY/O,WAAU,SAKzC,IAAMiP,EAAYjU,KAAKkU,gBAEvBlU,KAAK2F,SAASU,UAAUC,OAAOkM,qBAC/BxS,KAAK2F,SAASU,UAAU4C,IAAIwJ,uBAE5BzS,KAAK2F,SAAS3I,MAAMiX,GAAa,EAE7BjU,KAAKgT,cAAc/Q,QACrBjC,KAAKgT,cAAc1W,SAAQ,SAAArC,GACzBA,EAAQoM,UAAUC,OAAOoM,sBACzBzY,EAAQqN,aAAa,iBAAiB,MAI1CtH,KAAKmU,kBAAiB,GAEtB,IAYMC,EAAU,UADaH,EAAU,GAAGnX,cAAgBmX,EAAUlQ,MAAM,IAEpElJ,EAAqBJ,iCAAiCuF,KAAK2F,UAEjElE,aAAagC,IAAIzD,KAAK2F,SAAUzM,gBAff,WACfkN,EAAKT,SAASU,UAAUC,OAAOmM,uBAC/BrM,EAAKT,SAASU,UAAU4C,IAAIuJ,oBAAqBD,iBAEjDnM,EAAKT,SAAS3I,MAAMiX,GAAa,GAEjC7N,EAAK+N,kBAAiB,GAEtB1S,aAAawC,QAAQmC,EAAKT,SAAUyM,gBAStC5W,qBAAqBwE,KAAK2F,SAAU9K,GACpCmF,KAAK2F,SAAS3I,MAAMiX,GAAgBjU,KAAK2F,SAASyO,GAAlD,UAGFX,KAAA,WAAO,IAAApE,EAAArP,KACL,IAAIA,KAAK+S,kBACN/S,KAAK2F,SAASU,UAAUE,SAASgM,mBAIjB9Q,aAAawC,QAAQjE,KAAK2F,SAAU0M,YACxCzS,iBAAf,CAIA,IAAMqU,EAAYjU,KAAKkU,gBAEvBlU,KAAK2F,SAAS3I,MAAMiX,GAAgBjU,KAAK2F,SAAS4C,wBAAwB0L,GAA1E,KAEApW,OAAOmC,KAAK2F,UAEZ3F,KAAK2F,SAASU,UAAU4C,IAAIwJ,uBAC5BzS,KAAK2F,SAASU,UAAUC,OAAOkM,oBAAqBD,iBAEpD,IAAM8B,EAAqBrU,KAAKgT,cAAc/Q,OAC9C,GAAIoS,EAAqB,EACvB,IAAK,IAAIrS,EAAI,EAAGA,EAAIqS,EAAoBrS,IAAK,CAC3C,IAAMiC,EAAUjE,KAAKgT,cAAchR,GAC7BkR,EAAO1Y,uBAAuByJ,GAEhCiP,IAASA,EAAK7M,UAAUE,SAASgM,mBACnCtO,EAAQoC,UAAU4C,IAAIyJ,sBACtBzO,EAAQqD,aAAa,iBAAiB,IAK5CtH,KAAKmU,kBAAiB,GAStBnU,KAAK2F,SAAS3I,MAAMiX,GAAa,GACjC,IAAMpZ,EAAqBJ,iCAAiCuF,KAAK2F,UAEjElE,aAAagC,IAAIzD,KAAK2F,SAAUzM,gBAVf,WACfmW,EAAK8E,kBAAiB,GACtB9E,EAAK1J,SAASU,UAAUC,OAAOmM,uBAC/BpD,EAAK1J,SAASU,UAAU4C,IAAIuJ,qBAC5B/Q,aAAawC,QAAQoL,EAAK1J,SAAU2M,iBAOtC9W,qBAAqBwE,KAAK2F,SAAU9K,OAGtCsZ,iBAAA,SAAiBG,GACftU,KAAK+S,iBAAmBuB,KAG1BpO,QAAA,WACEtH,KAAKI,WAAWgB,KAAK2F,SAAUX,YAE/BhF,KAAK4N,QAAU,KACf5N,KAAKsT,QAAU,KACftT,KAAK2F,SAAW,KAChB3F,KAAKgT,cAAgB,KACrBhT,KAAK+S,iBAAmB,QAK1BlF,WAAA,SAAW3R,GAOT,OANAA,EAAMgM,eAAAA,eAAA,GACDuC,WACAvO,IAEEmL,OAASnH,QAAQhE,EAAOmL,QAC/BrL,gBAAgB8I,OAAM5I,EAAQ8O,eACvB9O,KAGTgY,cAAA,WAEE,OADiBlU,KAAK2F,SAASU,UAAUE,SAASoM,OAChCA,MAAQC,UAG5BW,WAAA,WAAa,IAAA/D,EAAAxP,KACLkS,EAAWlS,KAAK4N,QAAhBsE,OAEF5W,UAAU4W,QAEiB,IAAlBA,EAAOqC,aAA+C,IAAdrC,EAAO,KACxDA,EAASA,EAAO,IAGlBA,EAAS/I,eAAe9J,QAAQ6S,GAGlC,IAAMhY,EAAciN,uBAAN,iBAA2C+K,EAA3C,KAYd,OAVA/I,eAAelK,KAAK/E,EAAUgY,GAC3B5V,SAAQ,SAAArC,GACP,IAAMua,EAAWha,uBAAuBP,GAExCuV,EAAKgE,0BACHgB,EACA,CAACva,OAIAiY,KAGTsB,0BAAA,SAA0BvZ,EAASwa,GACjC,GAAIxa,EAAS,CACX,IAAMya,EAASza,EAAQoM,UAAUE,SAASgM,iBAEtCkC,EAAaxS,QACfwS,EAAanY,SAAQ,SAAA4W,GACfwB,EACFxB,EAAK7M,UAAUC,OAAOoM,sBAEtBQ,EAAK7M,UAAU4C,IAAIyJ,sBAGrBQ,EAAK5L,aAAa,gBAAiBoN,UAQpCV,kBAAP,SAAyB/Z,EAASiC,GAChC,IAAIsC,EAAOI,KAAKG,QAAQ9E,EAAS+K,YAC3B4I,EAAO1F,eAAAA,eAAAA,eAAA,GACRuC,WACA5C,YAAYG,kBAAkB/N,IACZ,iBAAXiC,GAAuBA,EAASA,EAAS,IAWrD,IARKsC,GAAQoP,EAAQvG,QAA4B,iBAAXnL,GAAuB,YAAYU,KAAKV,KAC5E0R,EAAQvG,QAAS,GAGd7I,IACHA,EAAO,IAAIsU,EAAS7Y,EAAS2T,IAGT,iBAAX1R,EAAqB,CAC9B,QAA4B,IAAjBsC,EAAKtC,GACd,MAAM,IAAI2V,UAAJ,oBAAkC3V,EAAlC,KAGRsC,EAAKtC,SAIFwK,gBAAP,SAAuBxK,GACrB,OAAO8D,KAAK2G,MAAK,WACfmM,EAASkB,kBAAkBhU,KAAM9D,SAI9B4K,YAAP,SAAmB7M,GACjB,OAAO2E,KAAKG,QAAQ9E,EAAS+K,gEAxQ7B,OAAOD,0CAIP,OAAO0F,gBA5CLqI,GA0TNrR,aAAa+B,GAAG1J,SAAUwL,uBAAsB6B,wBAAsB,SAAU5F,GAEjD,MAAzBA,EAAMQ,OAAOkO,SACf1O,EAAM5B,iBAGR,IAAMgV,EAAc9M,YAAYG,kBAAkBhI,MAC5C9F,EAAWI,uBAAuB0F,MACfmJ,eAAelK,KAAK/E,GAE5BoC,SAAQ,SAAArC,GACvB,IACIiC,EADEsC,EAAOI,KAAKG,QAAQ9E,EAAS+K,YAE/BxG,GAEmB,OAAjBA,EAAK8U,SAAkD,iBAAvBqB,EAAYzC,SAC9C1T,EAAKoP,QAAQsE,OAASyC,EAAYzC,OAClC1T,EAAK8U,QAAU9U,EAAK+U,cAGtBrX,EAAS,UAETA,EAASyY,EAGX7B,SAASkB,kBAAkB/Z,EAASiC,SAIxC,IAAMqE,IAAIxC,YASV,GAAIwC,IAAG,CACL,IAAMwG,qBAAqBxG,IAAEc,GAAGyD,QAChCvE,IAAEc,GAAGyD,QAAQgO,SAASpM,gBACtBnG,IAAEc,GAAGyD,QAAMkC,YAAc8L,SACzBvS,IAAEc,GAAGyD,QAAMmC,WAAa,WAEtB,OADA1G,IAAEc,GAAGyD,QAAQiC,qBACN+L,SAASpM,iBC/YpB,IAAM5B,OAAO,WACPC,UAAU,eACVC,WAAW,cACXC,YAAS,IAAOD,WAChBE,eAAe,YAEf0P,WAAa,SACbC,UAAY,QACZC,QAAU,MACVC,aAAe,UACfC,eAAiB,YACjBC,mBAAqB,EAErBC,eAAiB,IAAIvY,OAAUoY,aAAd,IAA8BC,eAA9B,IAAgDJ,YAEjEvC,aAAU,OAAUpN,YACpBqN,eAAY,SAAYrN,YACxBkN,aAAU,OAAUlN,YACpBmN,cAAW,QAAWnN,YACtBkQ,YAAW,QAAWlQ,YACtBK,uBAAoB,QAAWL,YAAYC,eAC3CkQ,uBAAsB,UAAanQ,YAAYC,eAC/CmQ,qBAAoB,QAAWpQ,YAAYC,eAE3CoQ,oBAAsB,WACtB/C,kBAAkB,OAClBgD,kBAAoB,SACpBC,qBAAuB,YACvBC,oBAAsB,WACtBC,qBAAuB,sBACvBC,kBAAoB,SACpBC,2BAA6B,kBAE7BzO,uBAAuB,2BACvB0O,oBAAsB,iBACtBC,cAAgB,iBAChBC,oBAAsB,cACtBC,uBAAyB,8DAEzBC,cAAgB,YAChBC,iBAAmB,UACnBC,iBAAmB,eACnBC,oBAAsB,aACtBC,gBAAkB,cAClBC,eAAiB,aAEjB7L,UAAU,CACdpC,OAAQ,EACRkO,MAAM,EACNC,SAAU,eACVC,UAAW,SACXrZ,QAAS,UACTsZ,aAAc,MAGV1L,cAAc,CAClB3C,OAAQ,2BACRkO,KAAM,UACNC,SAAU,mBACVC,UAAW,mBACXrZ,QAAS,SACTsZ,aAAc,iBASVC,SAAAA,WACJ,SAAAA,EAAY1c,EAASiC,GACnB8D,KAAK2F,SAAW1L,EAChB+F,KAAK4W,QAAU,KACf5W,KAAK4N,QAAU5N,KAAK6N,WAAW3R,GAC/B8D,KAAK6W,MAAQ7W,KAAK8W,kBAClB9W,KAAK+W,UAAY/W,KAAKgX,gBAEtBhX,KAAKoO,qBACLxP,KAAKC,QAAQ5E,EAAS+K,WAAUhF,iCAmBlCqH,OAAA,WACE,IAAIrH,KAAK2F,SAASsR,WAAYjX,KAAK2F,SAASU,UAAUE,SAAS+O,qBAA/D,CAIA,IAAM4B,EAAWlX,KAAK2F,SAASU,UAAUE,SAASgM,mBAElDoE,EAASQ,aAELD,GAIJlX,KAAK0T,WAGPA,KAAA,WACE,KAAI1T,KAAK2F,SAASsR,UAAYjX,KAAK2F,SAASU,UAAUE,SAAS+O,sBAAwBtV,KAAK6W,MAAMxQ,UAAUE,SAASgM,oBAArH,CAIA,IAAML,EAASyE,EAASS,qBAAqBpX,KAAK2F,UAC5C8K,EAAgB,CACpBA,cAAezQ,KAAK2F,UAKtB,IAFkBlE,aAAawC,QAAQjE,KAAK2F,SAAUwM,aAAY1B,GAEpD7Q,iBAAd,CAKA,IAAKI,KAAK+W,UAAW,CACnB,QAAsB,IAAXM,OACT,MAAM,IAAIxF,UAAU,mEAGtB,IAAIyF,EAAmBtX,KAAK2F,SAEG,WAA3B3F,KAAK4N,QAAQ6I,UACfa,EAAmBpF,EACV5W,UAAU0E,KAAK4N,QAAQ6I,aAChCa,EAAmBtX,KAAK4N,QAAQ6I,eAGa,IAAlCzW,KAAK4N,QAAQ6I,UAAUlC,SAChC+C,EAAmBtX,KAAK4N,QAAQ6I,UAAU,KAOhB,iBAA1BzW,KAAK4N,QAAQ4I,UACftE,EAAO7L,UAAU4C,IAAI2M,4BAGvB5V,KAAK4W,QAAU,IAAIS,OAAOC,EAAkBtX,KAAK6W,MAAO7W,KAAKuX,oBAQvB,IAAAnO,EADxC,GAAI,iBAAkBtP,SAASyD,kBAC5B2U,EAAO/L,QAAQ4P,sBAChB3M,EAAA,IAAGC,OAAHzH,MAAAwH,EAAatP,SAASmE,KAAKsL,UACxBjN,SAAQ,SAAA4W,GAAI,OAAIzR,aAAa+B,GAAG0P,EAAM,YAAa,KAAMtV,WAG9DoC,KAAK2F,SAAS6R,QACdxX,KAAK2F,SAAS2B,aAAa,iBAAiB,GAE5CO,YAAYkB,YAAY/I,KAAK6W,MAAOtE,mBACpC1K,YAAYkB,YAAY/I,KAAK2F,SAAU4M,mBACvC9Q,aAAawC,QAAQiO,EAAQE,cAAa3B,QAG5CgD,KAAA,WACE,IAAIzT,KAAK2F,SAASsR,WAAYjX,KAAK2F,SAASU,UAAUE,SAAS+O,sBAAyBtV,KAAK6W,MAAMxQ,UAAUE,SAASgM,mBAAtH,CAIA,IAAML,EAASyE,EAASS,qBAAqBpX,KAAK2F,UAC5C8K,EAAgB,CACpBA,cAAezQ,KAAK2F,UAGJlE,aAAawC,QAAQiO,EAAQG,aAAY5B,GAE7C7Q,mBAIVI,KAAK4W,SACP5W,KAAK4W,QAAQa,UAGf5P,YAAYkB,YAAY/I,KAAK6W,MAAOtE,mBACpC1K,YAAYkB,YAAY/I,KAAK2F,SAAU4M,mBACvC9Q,aAAawC,QAAQiO,EAAQI,eAAc7B,QAG7CvK,QAAA,WACEtH,KAAKI,WAAWgB,KAAK2F,SAAUX,YAC/BvD,aAAaC,IAAI1B,KAAK2F,SAAUV,aAChCjF,KAAK2F,SAAW,KAChB3F,KAAK6W,MAAQ,KACT7W,KAAK4W,UACP5W,KAAK4W,QAAQa,UACbzX,KAAK4W,QAAU,SAInBc,OAAA,WACE1X,KAAK+W,UAAY/W,KAAKgX,gBAClBhX,KAAK4W,SACP5W,KAAK4W,QAAQe,oBAMjBvJ,mBAAA,WAAqB,IAAAhI,EAAApG,KACnByB,aAAa+B,GAAGxD,KAAK2F,SAAUwP,aAAa,SAAA5T,GAC1CA,EAAM5B,iBACN4B,EAAMqW,kBACNxR,EAAKiB,eAITwG,WAAA,SAAW3R,GAaT,OAZAA,EAAMgM,eAAAA,eAAAA,eAAA,GACDlI,KAAK6X,YAAYpN,SACjB5C,YAAYG,kBAAkBhI,KAAK2F,WACnCzJ,GAGLF,gBACE8I,OACA5I,EACA8D,KAAK6X,YAAY7M,aAGZ9O,KAGT4a,gBAAA,WACE,OAAO3N,eAAegB,KAAKnK,KAAK2F,SAAUmQ,eAAe,MAG3DgC,cAAA,WACE,IAAMC,EAAiB/X,KAAK2F,SAAS1I,WACjC+a,EAAY7B,iBAgBhB,OAbI4B,EAAe1R,UAAUE,SAASgP,oBACpCyC,EAAY/B,cACRjW,KAAK6W,MAAMxQ,UAAUE,SAASmP,wBAChCsC,EAAY9B,mBAEL6B,EAAe1R,UAAUE,SAASiP,sBAC3CwC,EAAY3B,gBACH0B,EAAe1R,UAAUE,SAASkP,qBAC3CuC,EAAY1B,eACHtW,KAAK6W,MAAMxQ,UAAUE,SAASmP,wBACvCsC,EAAY5B,qBAGP4B,KAGThB,cAAA,WACE,OAAO9W,QAAQF,KAAK2F,SAASQ,QAAd,IAA0BwP,uBAG3CsC,WAAA,WAAa,IAAA5I,EAAArP,KACLqI,EAAS,GAef,MAbmC,mBAAxBrI,KAAK4N,QAAQvF,OACtBA,EAAOhH,GAAK,SAAA7C,GAMV,OALAA,EAAK0Z,QAALhQ,eAAAA,eAAA,GACK1J,EAAK0Z,SACL7I,EAAKzB,QAAQvF,OAAO7J,EAAK0Z,QAAS7I,EAAK1J,WAAa,IAGlDnH,GAGT6J,EAAOA,OAASrI,KAAK4N,QAAQvF,OAGxBA,KAGTkP,iBAAA,WACE,IAAMb,EAAe,CACnBsB,UAAWhY,KAAK8X,gBAChBK,UAAW,CACT9P,OAAQrI,KAAKiY,aACb1B,KAAM,CACJ6B,QAASpY,KAAK4N,QAAQ2I,MAExB8B,gBAAiB,CACfC,kBAAmBtY,KAAK4N,QAAQ4I,YAYtC,MAN6B,WAAzBxW,KAAK4N,QAAQxQ,UACfsZ,EAAayB,UAAUI,WAAa,CAClCH,SAAS,IAIblQ,eAAAA,eAAA,GACKwO,GACA1W,KAAK4N,QAAQ8I,iBAMb8B,kBAAP,SAAyBve,EAASiC,GAChC,IAAIsC,EAAOI,KAAKG,QAAQ9E,EAAS+K,YAOjC,GAJKxG,IACHA,EAAO,IAAImY,EAAS1c,EAHY,iBAAXiC,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBsC,EAAKtC,GACd,MAAM,IAAI2V,UAAJ,oBAAkC3V,EAAlC,KAGRsC,EAAKtC,SAIFwK,gBAAP,SAAuBxK,GACrB,OAAO8D,KAAK2G,MAAK,WACfgQ,EAAS6B,kBAAkBxY,KAAM9D,SAI9Bib,WAAP,SAAkB5V,GAChB,IAAIA,GAAUA,EAAMgG,SAAW0N,qBACb,UAAf1T,EAAMI,MAAoBJ,EAAMhD,MAAQuW,SAM3C,IAFA,IAAM2D,EAAUtP,eAAelK,KAAKkI,wBAE3BnF,EAAI,EAAGM,EAAMmW,EAAQxW,OAAQD,EAAIM,EAAKN,IAAK,CAClD,IAAMkQ,EAASyE,EAASS,qBAAqBqB,EAAQzW,IAC/C0W,EAAU9Z,KAAKG,QAAQ0Z,EAAQzW,GAAIgD,YACnCyL,EAAgB,CACpBA,cAAegI,EAAQzW,IAOzB,GAJIT,GAAwB,UAAfA,EAAMI,OACjB8O,EAAckI,WAAapX,GAGxBmX,EAAL,CAIA,IAAME,EAAeF,EAAQ7B,MAC7B,GAAK4B,EAAQzW,GAAGqE,UAAUE,SAASgM,mBAInC,KAAIhR,IAA0B,UAAfA,EAAMI,MACjB,kBAAkB/E,KAAK2E,EAAMQ,OAAOkO,UACpB,UAAf1O,EAAMI,MAAoBJ,EAAMhD,MAAQuW,UACzC8D,EAAarS,SAAShF,EAAMQ,SAKhC,IADkBN,aAAawC,QAAQiO,EAAQG,aAAY5B,GAC7C7Q,iBAAd,CAMgD,IAAA4J,EAAhD,GAAI,iBAAkB1P,SAASyD,iBAC7BiM,EAAA,IAAGH,OAAHzH,MAAA4H,EAAa1P,SAASmE,KAAKsL,UACxBjN,SAAQ,SAAA4W,GAAI,OAAIzR,aAAaC,IAAIwR,EAAM,YAAa,KAAMtV,WAG/D6a,EAAQzW,GAAGsF,aAAa,gBAAiB,SAErCoR,EAAQ9B,SACV8B,EAAQ9B,QAAQa,UAGlBmB,EAAavS,UAAUC,OAAOiM,mBAC9BkG,EAAQzW,GAAGqE,UAAUC,OAAOiM,mBAC5B9Q,aAAawC,QAAQiO,EAAQI,eAAc7B,SAIxC2G,qBAAP,SAA4Bnd,GAC1B,OAAOO,uBAAuBP,IAAYA,EAAQgD,cAG7C4b,sBAAP,SAA6BtX,GAQ3B,KAAI,kBAAkB3E,KAAK2E,EAAMQ,OAAOkO,SACtC1O,EAAMhD,MAAQsW,WAActT,EAAMhD,MAAQqW,aACxCrT,EAAMhD,MAAQyW,gBAAkBzT,EAAMhD,MAAQwW,cAC9CxT,EAAMQ,OAAOoE,QAAQ2P,iBACtBZ,eAAetY,KAAK2E,EAAMhD,QAI7BgD,EAAM5B,iBACN4B,EAAMqW,mBAEF5X,KAAKiX,WAAYjX,KAAKqG,UAAUE,SAAS+O,sBAA7C,CAIA,IAAMpD,EAASyE,EAASS,qBAAqBpX,MACvCkX,EAAWlX,KAAKqG,UAAUE,SAASgM,mBAEzC,GAAIhR,EAAMhD,MAAQqW,WAIhB,OAHe5U,KAAKM,QAAQ6G,wBAAwBnH,KAAOmJ,eAAea,KAAKhK,KAAMmH,wBAAsB,IACpGqQ,aACPb,EAASQ,aAIX,GAAKD,GAAY3V,EAAMhD,MAAQsW,UAA/B,CAKA,IAAMiE,EAAQ3P,eAAelK,KAAK+W,uBAAwB9D,GACvDzI,OAAO1M,WAEV,GAAK+b,EAAM7W,OAAX,CAIA,IAAI6M,EAAQgK,EAAMhW,QAAQvB,EAAMQ,QAE5BR,EAAMhD,MAAQwW,cAAgBjG,EAAQ,GACxCA,IAGEvN,EAAMhD,MAAQyW,gBAAkBlG,EAAQgK,EAAM7W,OAAS,GACzD6M,IAMFgK,EAFAhK,GAAmB,IAAXA,EAAe,EAAIA,GAEd0I,cAxBXb,EAASQ,iBA2BNrQ,YAAP,SAAmB7M,GACjB,OAAO2E,KAAKG,QAAQ9E,EAAS+K,gEAlY7B,OAAOD,0CAIP,OAAO0F,8CAIP,OAAOO,oBAvBL2L,GA2ZNlV,aAAa+B,GAAG1J,SAAUsb,uBAAwBjO,uBAAsBwP,SAASkC,uBACjFpX,aAAa+B,GAAG1J,SAAUsb,uBAAwBU,cAAea,SAASkC,uBAC1EpX,aAAa+B,GAAG1J,SAAUwL,uBAAsBqR,SAASQ,YACzD1V,aAAa+B,GAAG1J,SAAUub,qBAAsBsB,SAASQ,YACzD1V,aAAa+B,GAAG1J,SAAUwL,uBAAsB6B,wBAAsB,SAAU5F,GAC9EA,EAAM5B,iBACN4B,EAAMqW,kBACNjB,SAAS6B,kBAAkBxY,KAAM,aAEnCyB,aACG+B,GAAG1J,SAAUwL,uBAAsBuQ,qBAAqB,SAAAtW,GAAC,OAAIA,EAAEqY,qBAElE,IAAMrX,IAAIxC,YASV,GAAIwC,IAAG,CACL,IAAMwG,qBAAqBxG,IAAEc,GAAGyD,QAChCvE,IAAEc,GAAGyD,QAAQ6R,SAASjQ,gBACtBnG,IAAEc,GAAGyD,QAAMkC,YAAc2P,SACzBpW,IAAEc,GAAGyD,QAAMmC,WAAa,WAEtB,OADA1G,IAAEc,GAAGyD,QAAQiC,qBACN4P,SAASjQ,iBC3fpB,IAAM5B,OAAO,QACPC,UAAU,eACVC,WAAW,WACXC,YAAS,IAAOD,WAChBE,eAAe,YACf0P,aAAa,SAEbnK,UAAU,CACdsO,UAAU,EACVpO,UAAU,EACV6M,OAAO,EACP9D,MAAM,GAGF1I,cAAc,CAClB+N,SAAU,mBACVpO,SAAU,UACV6M,MAAO,UACP9D,KAAM,WAGFrB,aAAU,OAAUpN,YACpB+T,qBAAoB,gBAAmB/T,YACvCqN,eAAY,SAAYrN,YACxBkN,aAAU,OAAUlN,YACpBmN,cAAW,QAAWnN,YACtBgU,cAAa,UAAahU,YAC1BiU,aAAY,SAAYjU,YACxBkU,oBAAmB,gBAAmBlU,YACtCmU,sBAAqB,kBAAqBnU,YAC1CoU,sBAAqB,kBAAqBpU,YAC1CqU,wBAAuB,oBAAuBrU,YAC9CK,uBAAoB,QAAWL,YAAYC,eAE3CqU,8BAAgC,0BAChCC,oBAAsB,iBACtBC,gBAAkB,aAClBC,gBAAkB,OAClBnH,kBAAkB,OAClBoH,kBAAoB,eAEpBC,gBAAkB,gBAClBC,oBAAsB,cACtB1S,uBAAuB,wBACvB2S,sBAAwB,yBACxBC,uBAAyB,oDACzBC,wBAA0B,cAQ1BC,MAAAA,WACJ,SAAAA,EAAYhgB,EAASiC,GACnB8D,KAAK4N,QAAU5N,KAAK6N,WAAW3R,GAC/B8D,KAAK2F,SAAW1L,EAChB+F,KAAKka,QAAU/Q,eAAe9J,QAAQua,gBAAiB3f,GACvD+F,KAAKma,UAAY,KACjBna,KAAKoa,UAAW,EAChBpa,KAAKqa,oBAAqB,EAC1Bra,KAAKsa,sBAAuB,EAC5Bta,KAAK+S,kBAAmB,EACxB/S,KAAKua,gBAAkB,EACvB3b,KAAKC,QAAQ5E,EAAS+K,WAAUhF,iCAelCqH,OAAA,SAAOoJ,GACL,OAAOzQ,KAAKoa,SAAWpa,KAAKyT,OAASzT,KAAK0T,KAAKjD,MAGjDiD,KAAA,SAAKjD,GAAe,IAAArK,EAAApG,KAClB,IAAIA,KAAKoa,WAAYpa,KAAK+S,iBAA1B,CAII/S,KAAK2F,SAASU,UAAUE,SAASmT,mBACnC1Z,KAAK+S,kBAAmB,GAG1B,IAAMyH,EAAY/Y,aAAawC,QAAQjE,KAAK2F,SAAUwM,aAAY,CAChE1B,cAAAA,IAGEzQ,KAAKoa,UAAYI,EAAU5a,mBAI/BI,KAAKoa,UAAW,EAEhBpa,KAAKya,kBACLza,KAAK0a,gBAEL1a,KAAK2a,gBAEL3a,KAAK4a,kBACL5a,KAAK6a,kBAELpZ,aAAa+B,GAAGxD,KAAK2F,SACnBwT,oBACAW,uBACA,SAAAvY,GAAK,OAAI6E,EAAKqN,KAAKlS,MAGrBE,aAAa+B,GAAGxD,KAAKka,QAASZ,yBAAyB,WACrD7X,aAAagC,IAAI2C,EAAKT,SAAU0T,uBAAuB,SAAA9X,GACjDA,EAAMQ,SAAWqE,EAAKT,WACxBS,EAAKkU,sBAAuB,SAKlCta,KAAK8a,eAAc,WAAA,OAAM1U,EAAK2U,aAAatK,WAG7CgD,KAAA,SAAKlS,GAAO,IAAA8N,EAAArP,KAKV,IAJIuB,GACFA,EAAM5B,iBAGHK,KAAKoa,WAAYpa,KAAK+S,oBAITtR,aAAawC,QAAQjE,KAAK2F,SAAU0M,cAExCzS,iBAAd,CAIAI,KAAKoa,UAAW,EAChB,IAAMY,EAAahb,KAAK2F,SAASU,UAAUE,SAASmT,iBAgBpD,GAdIsB,IACFhb,KAAK+S,kBAAmB,GAG1B/S,KAAK4a,kBACL5a,KAAK6a,kBAELpZ,aAAaC,IAAI5H,SAAUmf,eAE3BjZ,KAAK2F,SAASU,UAAUC,OAAOiM,mBAE/B9Q,aAAaC,IAAI1B,KAAK2F,SAAUwT,qBAChC1X,aAAaC,IAAI1B,KAAKka,QAASZ,yBAE3B0B,EAAY,CACd,IAAMngB,EAAqBJ,iCAAiCuF,KAAK2F,UAEjElE,aAAagC,IAAIzD,KAAK2F,SAAUzM,gBAAgB,SAAAqI,GAAK,OAAI8N,EAAK4L,WAAW1Z,MACzE/F,qBAAqBwE,KAAK2F,SAAU9K,QAEpCmF,KAAKib,iBAIT/U,QAAA,WACE,CAACvL,OAAQqF,KAAK2F,SAAU3F,KAAKka,SAC1B5d,SAAQ,SAAA4e,GAAW,OAAIzZ,aAAaC,IAAIwZ,EAAajW,gBAOxDxD,aAAaC,IAAI5H,SAAUmf,eAE3Bra,KAAKI,WAAWgB,KAAK2F,SAAUX,YAE/BhF,KAAK4N,QAAU,KACf5N,KAAK2F,SAAW,KAChB3F,KAAKka,QAAU,KACfla,KAAKma,UAAY,KACjBna,KAAKoa,SAAW,KAChBpa,KAAKqa,mBAAqB,KAC1Bra,KAAKsa,qBAAuB,KAC5Bta,KAAK+S,iBAAmB,KACxB/S,KAAKua,gBAAkB,QAGzBY,aAAA,WACEnb,KAAK2a,mBAKP9M,WAAA,SAAW3R,GAMT,OALAA,EAAMgM,eAAAA,eAAA,GACDuC,WACAvO,GAELF,gBAAgB8I,OAAM5I,EAAQ8O,eACvB9O,KAGT6e,aAAA,SAAatK,GAAe,IAAAjB,EAAAxP,KACpBgb,EAAahb,KAAK2F,SAASU,UAAUE,SAASmT,iBAC9C0B,EAAYjS,eAAe9J,QAAQwa,oBAAqB7Z,KAAKka,SAE9Dla,KAAK2F,SAAS1I,YACf+C,KAAK2F,SAAS1I,WAAW1B,WAAasO,KAAKC,cAE7ChQ,SAASmE,KAAKod,YAAYrb,KAAK2F,UAGjC3F,KAAK2F,SAAS3I,MAAMI,QAAU,QAC9B4C,KAAK2F,SAAStF,gBAAgB,eAC9BL,KAAK2F,SAAS2B,aAAa,cAAc,GACzCtH,KAAK2F,SAAS2B,aAAa,OAAQ,UACnCtH,KAAK2F,SAAS8C,UAAY,EAEtB2S,IACFA,EAAU3S,UAAY,GAGpBuS,GACFnd,OAAOmC,KAAK2F,UAGd3F,KAAK2F,SAASU,UAAU4C,IAAIsJ,mBAExBvS,KAAK4N,QAAQ4J,OACfxX,KAAKsb,gBAGP,IAAMC,EAAqB,WACrB/L,EAAK5B,QAAQ4J,OACfhI,EAAK7J,SAAS6R,QAGhBhI,EAAKuD,kBAAmB,EACxBtR,aAAawC,QAAQuL,EAAK7J,SAAUyM,cAAa,CAC/C3B,cAAAA,KAIJ,GAAIuK,EAAY,CACd,IAAMngB,EAAqBJ,iCAAiCuF,KAAKka,SAEjEzY,aAAagC,IAAIzD,KAAKka,QAAShhB,eAAgBqiB,GAC/C/f,qBAAqBwE,KAAKka,QAASrf,QAEnC0gB,OAIJD,cAAA,WAAgB,IAAAnK,EAAAnR,KACdyB,aAAaC,IAAI5H,SAAUmf,eAC3BxX,aAAa+B,GAAG1J,SAAUmf,eAAe,SAAA1X,GACnCzH,WAAayH,EAAMQ,QACnBoP,EAAKxL,WAAapE,EAAMQ,QACvBoP,EAAKxL,SAASY,SAAShF,EAAMQ,SAChCoP,EAAKxL,SAAS6R,cAKpBoD,gBAAA,WAAkB,IAAAY,EAAAxb,KACZA,KAAKoa,SACP3Y,aAAa+B,GAAGxD,KAAK2F,SAAUyT,uBAAuB,SAAA7X,GAChDia,EAAK5N,QAAQjD,UAAYpJ,EAAMhD,MAAQqW,cACzCrT,EAAM5B,iBACN6b,EAAK/H,QACK+H,EAAK5N,QAAQjD,UAAYpJ,EAAMhD,MAAQqW,cACjD4G,EAAKC,gCAITha,aAAaC,IAAI1B,KAAK2F,SAAUyT,0BAIpCyB,gBAAA,WAAkB,IAAAa,EAAA1b,KACZA,KAAKoa,SACP3Y,aAAa+B,GAAG7I,OAAQue,cAAc,WAAA,OAAMwC,EAAKf,mBAEjDlZ,aAAaC,IAAI/G,OAAQue,iBAI7B+B,WAAA,WAAa,IAAAU,EAAA3b,KACXA,KAAK2F,SAAS3I,MAAMI,QAAU,OAC9B4C,KAAK2F,SAAS2B,aAAa,eAAe,GAC1CtH,KAAK2F,SAAStF,gBAAgB,cAC9BL,KAAK2F,SAAStF,gBAAgB,QAC9BL,KAAK+S,kBAAmB,EACxB/S,KAAK8a,eAAc,WACjBhhB,SAASmE,KAAKoI,UAAUC,OAAOmT,iBAC/BkC,EAAKC,oBACLD,EAAKE,kBACLpa,aAAawC,QAAQ0X,EAAKhW,SAAU2M,sBAIxCwJ,gBAAA,WACE9b,KAAKma,UAAUld,WAAWwJ,YAAYzG,KAAKma,WAC3Cna,KAAKma,UAAY,QAGnBW,cAAA,SAAciB,GAAU,IAAAC,EAAAhc,KAChBic,EAAUjc,KAAK2F,SAASU,UAAUE,SAASmT,iBAC/CA,gBACA,GAEF,GAAI1Z,KAAKoa,UAAYpa,KAAK4N,QAAQmL,SAAU,CA6B1C,GA5BA/Y,KAAKma,UAAYrgB,SAAS4F,cAAc,OACxCM,KAAKma,UAAUnR,UAAYwQ,oBAEvByC,GACFjc,KAAKma,UAAU9T,UAAU4C,IAAIgT,GAG/BniB,SAASmE,KAAKod,YAAYrb,KAAKma,WAE/B1Y,aAAa+B,GAAGxD,KAAK2F,SAAUwT,qBAAqB,SAAA5X,GAC9Cya,EAAK1B,qBACP0B,EAAK1B,sBAAuB,EAI1B/Y,EAAMQ,SAAWR,EAAM2a,eAI3BF,EAAKP,gCAGHQ,GACFpe,OAAOmC,KAAKma,WAGdna,KAAKma,UAAU9T,UAAU4C,IAAIsJ,oBAExB0J,EAEH,YADAF,IAIF,IAAMI,EAA6B1hB,iCAAiCuF,KAAKma,WAEzE1Y,aAAagC,IAAIzD,KAAKma,UAAWjhB,eAAgB6iB,GACjDvgB,qBAAqBwE,KAAKma,UAAWgC,QAChC,IAAKnc,KAAKoa,UAAYpa,KAAKma,UAAW,CAC3Cna,KAAKma,UAAU9T,UAAUC,OAAOiM,mBAEhC,IAAM6J,EAAiB,WACrBJ,EAAKF,kBACLC,KAGF,GAAI/b,KAAK2F,SAASU,UAAUE,SAASmT,iBAAkB,CACrD,IAAMyC,EAA6B1hB,iCAAiCuF,KAAKma,WACzE1Y,aAAagC,IAAIzD,KAAKma,UAAWjhB,eAAgBkjB,GACjD5gB,qBAAqBwE,KAAKma,UAAWgC,QAErCC,SAGFL,OAIJN,2BAAA,WAA6B,IAAAY,EAAArc,KAC3B,GAA8B,WAA1BA,KAAK4N,QAAQmL,SAAuB,CAEtC,GADkBtX,aAAawC,QAAQjE,KAAK2F,SAAUqT,sBACxCpZ,iBACZ,OAGFI,KAAK2F,SAASU,UAAU4C,IAAI0Q,mBAC5B,IAAM2C,EAA0B7hB,iCAAiCuF,KAAK2F,UACtElE,aAAagC,IAAIzD,KAAK2F,SAAUzM,gBAAgB,WAC9CmjB,EAAK1W,SAASU,UAAUC,OAAOqT,sBAEjCne,qBAAqBwE,KAAK2F,SAAU2W,GACpCtc,KAAK2F,SAAS6R,aAEdxX,KAAKyT,UAQTkH,cAAA,WACE,IAAM4B,EACJvc,KAAK2F,SAAS6W,aAAe1iB,SAASyD,gBAAgBkf,cAEnDzc,KAAKqa,oBAAsBkC,IAC9Bvc,KAAK2F,SAAS3I,MAAM0f,YAAiB1c,KAAKua,gBAA1C,MAGEva,KAAKqa,qBAAuBkC,IAC9Bvc,KAAK2F,SAAS3I,MAAM2f,aAAkB3c,KAAKua,gBAA3C,SAIJqB,kBAAA,WACE5b,KAAK2F,SAAS3I,MAAM0f,YAAc,GAClC1c,KAAK2F,SAAS3I,MAAM2f,aAAe,MAGrClC,gBAAA,WACE,IAAMnS,EAAOxO,SAASmE,KAAKsK,wBAC3BvI,KAAKqa,mBAAqB1gB,KAAKijB,MAAMtU,EAAKI,KAAOJ,EAAKuU,OAASliB,OAAOmiB,WACtE9c,KAAKua,gBAAkBva,KAAK+c,wBAG9BrC,cAAA,WAAgB,IAAAsC,EAAAhd,KACd,GAAIA,KAAKqa,mBAAoB,CAK3BlR,eAAelK,KAAK8a,wBACjBzd,SAAQ,SAAArC,GACP,IAAMgjB,EAAgBhjB,EAAQ+C,MAAM2f,aAC9BO,EAAoBviB,OAAOC,iBAAiBX,GAAS,iBAC3D4N,YAAYC,iBAAiB7N,EAAS,gBAAiBgjB,GACvDhjB,EAAQ+C,MAAM2f,aAAkB3hB,WAAWkiB,GAAqBF,EAAKzC,gBAArE,QAIJpR,eAAelK,KAAK+a,yBACjB1d,SAAQ,SAAArC,GACP,IAAMkjB,EAAeljB,EAAQ+C,MAAMogB,YAC7BC,EAAmB1iB,OAAOC,iBAAiBX,GAAS,gBAC1D4N,YAAYC,iBAAiB7N,EAAS,eAAgBkjB,GACtDljB,EAAQ+C,MAAMogB,YAAiBpiB,WAAWqiB,GAAoBL,EAAKzC,gBAAnE,QAIJ,IAAM0C,EAAgBnjB,SAASmE,KAAKjB,MAAM2f,aACpCO,EAAoBviB,OAAOC,iBAAiBd,SAASmE,MAAM,iBAEjE4J,YAAYC,iBAAiBhO,SAASmE,KAAM,gBAAiBgf,GAC7DnjB,SAASmE,KAAKjB,MAAM2f,aAAkB3hB,WAAWkiB,GAAqBld,KAAKua,gBAA3E,KAGFzgB,SAASmE,KAAKoI,UAAU4C,IAAIwQ,oBAG9BoC,gBAAA,WAEE1S,eAAelK,KAAK8a,wBACjBzd,SAAQ,SAAArC,GACP,IAAMqjB,EAAUzV,YAAYO,iBAAiBnO,EAAS,sBAC/B,IAAZqjB,IACTzV,YAAYE,oBAAoB9N,EAAS,iBACzCA,EAAQ+C,MAAM2f,aAAeW,MAKnCnU,eAAelK,KAAf,GAAuB+a,yBACpB1d,SAAQ,SAAArC,GACP,IAAMsjB,EAAS1V,YAAYO,iBAAiBnO,EAAS,qBAC/B,IAAXsjB,IACT1V,YAAYE,oBAAoB9N,EAAS,gBACzCA,EAAQ+C,MAAMogB,YAAcG,MAKlC,IAAMD,EAAUzV,YAAYO,iBAAiBtO,SAASmE,KAAM,sBACrC,IAAZqf,EACTxjB,SAASmE,KAAKjB,MAAM2f,aAAe,IAEnC9U,YAAYE,oBAAoBjO,SAASmE,KAAM,iBAC/CnE,SAASmE,KAAKjB,MAAM2f,aAAeW,MAIvCP,mBAAA,WACE,IAAMS,EAAY1jB,SAAS4F,cAAc,OACzC8d,EAAUxU,UAAYuQ,8BACtBzf,SAASmE,KAAKod,YAAYmC,GAC1B,IAAMC,EAAiBD,EAAUjV,wBAAwBmV,MAAQF,EAAUG,YAE3E,OADA7jB,SAASmE,KAAKwI,YAAY+W,GACnBC,KAKF/W,gBAAP,SAAuBxK,EAAQuU,GAC7B,OAAOzQ,KAAK2G,MAAK,WACf,IAAInI,EAAOI,KAAKG,QAAQiB,KAAMgF,YACxB4I,EAAO1F,eAAAA,eAAAA,eAAA,GACRuC,WACA5C,YAAYG,kBAAkBhI,OACZ,iBAAX9D,GAAuBA,EAASA,EAAS,IAOrD,GAJKsC,IACHA,EAAO,IAAIyb,EAAMja,KAAM4N,IAGH,iBAAX1R,EAAqB,CAC9B,QAA4B,IAAjBsC,EAAKtC,GACd,MAAM,IAAI2V,UAAJ,oBAAkC3V,EAAlC,KAGRsC,EAAKtC,GAAQuU,QACJ7C,EAAQ8F,MACjBlV,EAAKkV,KAAKjD,SAKT3J,YAAP,SAAmB7M,GACjB,OAAO2E,KAAKG,QAAQ9E,EAAS+K,gEAtc7B,OAAOD,0CAIP,OAAO0F,gBArBLwP,GAieNxY,aAAa+B,GAAG1J,SAAUwL,uBAAsB6B,wBAAsB,SAAU5F,GAAO,IAAAqc,EAAA5d,KAC/E+B,EAASvH,uBAAuBwF,MAEjB,MAAjBA,KAAKiQ,SAAoC,SAAjBjQ,KAAKiQ,SAC/B1O,EAAM5B,iBAGR8B,aAAagC,IAAI1B,EAAQoQ,cAAY,SAAAqI,GAC/BA,EAAU5a,kBAKd6B,aAAagC,IAAI1B,EAAQuQ,gBAAc,WACjCvV,UAAU6gB,IACZA,EAAKpG,cAKX,IAAIhZ,EAAOI,KAAKG,QAAQgD,EAAQiD,YAChC,IAAKxG,EAAM,CACT,IAAMtC,EAAMgM,eAAAA,eAAA,GACPL,YAAYG,kBAAkBjG,IAC9B8F,YAAYG,kBAAkBhI,OAGnCxB,EAAO,IAAIyb,MAAMlY,EAAQ7F,GAG3BsC,EAAKkV,KAAK1T,SAGZ,IAAMO,IAAIxC,YASV,GAAIwC,IAAG,CACL,IAAMwG,qBAAqBxG,IAAEc,GAAGyD,QAChCvE,IAAEc,GAAGyD,QAAQmV,MAAMvT,gBACnBnG,IAAEc,GAAGyD,QAAMkC,YAAciT,MACzB1Z,IAAEc,GAAGyD,QAAMmC,WAAa,WAEtB,OADA1G,IAAEc,GAAGyD,QAAQiC,qBACNkT,MAAMvT,iBC5lBjB,IAAMmX,SAAW,CACf,aACA,OACA,OACA,WACA,WACA,SACA,MACA,cAGIC,uBAAyB,iBAOzBC,iBAAmB,8DAOnBC,iBAAmB,qIAEnBC,iBAAmB,SAACC,EAAMC,GAC9B,IAAMC,EAAWF,EAAKG,SAAS7kB,cAE/B,IAAgD,IAA5C2kB,EAAqBrb,QAAQsb,GAC/B,OAAoC,IAAhCP,SAAS/a,QAAQsb,IACZle,QAAQge,EAAKI,UAAU/kB,MAAMwkB,mBAAqBG,EAAKI,UAAU/kB,MAAMykB,mBASlF,IAHA,IAAMO,EAASJ,EAAqB1U,QAAO,SAAA+U,GAAS,OAAIA,aAAqB7hB,UAGpEqF,EAAI,EAAGM,EAAMic,EAAOtc,OAAQD,EAAIM,EAAKN,IAC5C,GAAIoc,EAAS7kB,MAAMglB,EAAOvc,IACxB,OAAO,EAIX,OAAO,GAGIyc,iBAAmB,CAE9BC,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAAQZ,wBAC5Ca,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJzd,EAAG,GACH0d,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAGC,SAASC,aAAaC,EAAYC,EAAWC,GAAY,IAAAtX,EAC9D,IAAKoX,EAAWve,OACd,OAAOue,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAQpB,IALA,IACMG,GADY,IAAIhmB,OAAOimB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgB1kB,OAAOC,KAAKokB,GAC5BM,GAAW3X,EAAA,IAAGC,OAAHzH,MAAAwH,EAAauX,EAAgB1iB,KAAKmB,iBAAiB,MAZN4hB,EAAA,SAcrDhf,EAAOM,GAd8C,IAAAkH,EAetDyX,EAAKF,EAAS/e,GACdkf,EAASD,EAAG5C,SAAS7kB,cAE3B,IAAuC,IAAnCsnB,EAAche,QAAQoe,GAGxB,OAFAD,EAAGhkB,WAAWwJ,YAAYwa,GAE1B,WAGF,IAAME,GAAgB3X,EAAA,IAAGH,OAAHzH,MAAA4H,EAAayX,EAAGhZ,YAChCmZ,EAAwB,GAAG/X,OAAOoX,EAAU,MAAQ,GAAIA,EAAUS,IAAW,IAEnFC,EAAc7kB,SAAQ,SAAA4hB,GACfD,iBAAiBC,EAAMkD,IAC1BH,EAAG5gB,gBAAgB6d,EAAKG,cAfrBrc,EAAI,EAAGM,EAAMye,EAAS9e,OAAQD,EAAIM,EAAKN,IAAKgf,EAA5Chf,GAoBT,OAAO2e,EAAgB1iB,KAAKojB,UC3F9B,IAAMvc,OAAO,UACPC,UAAU,eACVC,WAAW,aACXC,YAAS,IAAOD,WAChBsc,aAAe,aACfC,mBAAqB,IAAI5kB,OAAJ,UAAqB2kB,aAArB,OAAyC,KAC9DE,sBAAwB,CAAC,WAAY,YAAa,cAElDxW,cAAc,CAClByW,UAAW,UACXC,SAAU,SACVC,MAAO,4BACP1d,QAAS,SACT2d,MAAO,kBACPC,KAAM,UACN3nB,SAAU,mBACV8d,UAAW,oBACX3P,OAAQ,2BACRwL,UAAW,2BACXiO,kBAAmB,iBACnBtL,SAAU,mBACVuL,SAAU,UACVrB,WAAY,kBACZD,UAAW,SACX/J,aAAc,iBAGVsL,cAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAO,QACPC,OAAQ,SACRC,KAAM,QAGF5X,UAAU,CACdgX,WAAW,EACXC,SAAU,+GAGVzd,QAAS,cACT0d,MAAO,GACPC,MAAO,EACPC,MAAM,EACN3nB,UAAU,EACV8d,UAAW,MACX3P,OAAQ,EACRwL,WAAW,EACXiO,kBAAmB,OACnBtL,SAAU,eACVuL,UAAU,EACVrB,WAAY,KACZD,UAAWhC,iBACX/H,aAAc,MAGVrb,QAAQ,CACZinB,KAAI,OAASrd,YACbsd,OAAM,SAAWtd,YACjBud,KAAI,OAASvd,YACbwd,MAAK,QAAUxd,YACfyd,SAAQ,WAAazd,YACrB0d,MAAK,QAAU1d,YACf2d,QAAO,UAAY3d,YACnB4d,SAAQ,WAAa5d,YACrB6d,WAAU,aAAe7d,YACzB8d,WAAU,aAAe9d,aAGrByU,kBAAkB,OAClBsJ,iBAAmB,QACnBzQ,kBAAkB,OAElB0Q,iBAAmB,OACnBC,gBAAkB,MAElBC,uBAAyB,iBAEzBC,cAAgB,QAChBC,cAAgB,QAChBC,cAAgB,QAChBC,eAAiB,SAQjBC,QAAAA,WACJ,SAAAA,EAAYvpB,EAASiC,GACnB,QAAsB,IAAXmb,OACT,MAAM,IAAIxF,UAAU,kEAItB7R,KAAKyjB,YAAa,EAClBzjB,KAAK0jB,SAAW,EAChB1jB,KAAK2jB,YAAc,GACnB3jB,KAAK4jB,eAAiB,GACtB5jB,KAAK4W,QAAU,KAGf5W,KAAK/F,QAAUA,EACf+F,KAAK9D,OAAS8D,KAAK6N,WAAW3R,GAC9B8D,KAAK6jB,IAAM,KAEX7jB,KAAK8jB,gBACLllB,KAAKC,QAAQ5E,EAAS+F,KAAK6X,YAAY7S,SAAUhF,iCAmCnD+jB,OAAA,WACE/jB,KAAKyjB,YAAa,KAGpBO,QAAA,WACEhkB,KAAKyjB,YAAa,KAGpBQ,cAAA,WACEjkB,KAAKyjB,YAAczjB,KAAKyjB,cAG1Bpc,OAAA,SAAO9F,GACL,GAAKvB,KAAKyjB,WAIV,GAAIliB,EAAO,CACT,IAAM2iB,EAAUlkB,KAAK6X,YAAY7S,SAC7B0T,EAAU9Z,KAAKG,QAAQwC,EAAMQ,OAAQmiB,GAEpCxL,IACHA,EAAU,IAAI1Y,KAAK6X,YACjBtW,EAAMQ,OACN/B,KAAKmkB,sBAEPvlB,KAAKC,QAAQ0C,EAAMQ,OAAQmiB,EAASxL,IAGtCA,EAAQkL,eAAeQ,OAAS1L,EAAQkL,eAAeQ,MAEnD1L,EAAQ2L,uBACV3L,EAAQ4L,OAAO,KAAM5L,GAErBA,EAAQ6L,OAAO,KAAM7L,OAElB,CACL,GAAI1Y,KAAKwkB,gBAAgBne,UAAUE,SAASgM,mBAE1C,YADAvS,KAAKukB,OAAO,KAAMvkB,MAIpBA,KAAKskB,OAAO,KAAMtkB,UAItBkG,QAAA,WACE4J,aAAa9P,KAAK0jB,UAElB9kB,KAAKI,WAAWgB,KAAK/F,QAAS+F,KAAK6X,YAAY7S,UAE/CvD,aAAaC,IAAI1B,KAAK/F,QAAS+F,KAAK6X,YAAY5S,WAChDxD,aAAaC,IAAI1B,KAAK/F,QAAQkM,QAAb,IAAyB6c,kBAAqB,gBAAiBhjB,KAAKykB,mBAEjFzkB,KAAK6jB,KACP7jB,KAAK6jB,IAAI5mB,WAAWwJ,YAAYzG,KAAK6jB,KAGvC7jB,KAAKyjB,WAAa,KAClBzjB,KAAK0jB,SAAW,KAChB1jB,KAAK2jB,YAAc,KACnB3jB,KAAK4jB,eAAiB,KAClB5jB,KAAK4W,SACP5W,KAAK4W,QAAQa,UAGfzX,KAAK4W,QAAU,KACf5W,KAAK/F,QAAU,KACf+F,KAAK9D,OAAS,KACd8D,KAAK6jB,IAAM,QAGbnQ,KAAA,WAAO,IAAAtN,EAAApG,KACL,GAAmC,SAA/BA,KAAK/F,QAAQ+C,MAAMI,QACrB,MAAM,IAAIP,MAAM,uCAGlB,GAAImD,KAAK0kB,iBAAmB1kB,KAAKyjB,WAAY,CAC3C,IAAMjJ,EAAY/Y,aAAawC,QAAQjE,KAAK/F,QAAS+F,KAAK6X,YAAYxc,MAAMmnB,MACtEmC,EAAarnB,eAAe0C,KAAK/F,SACjC2qB,EAA4B,OAAfD,EACjB3kB,KAAK/F,QAAQ4qB,cAActnB,gBAAgBgJ,SAASvG,KAAK/F,SACzD0qB,EAAWpe,SAASvG,KAAK/F,SAE3B,GAAIugB,EAAU5a,mBAAqBglB,EACjC,OAGF,IAAMf,EAAM7jB,KAAKwkB,gBACXM,EAAQrrB,OAAOuG,KAAK6X,YAAY/S,MAEtC+e,EAAIvc,aAAa,KAAMwd,GACvB9kB,KAAK/F,QAAQqN,aAAa,mBAAoBwd,GAE9C9kB,KAAK+kB,aAED/kB,KAAK9D,OAAOulB,WACdoC,EAAIxd,UAAU4C,IAAIyQ,mBAGpB,IAAM1B,EAA6C,mBAA1BhY,KAAK9D,OAAO8b,UACnChY,KAAK9D,OAAO8b,UAAU1e,KAAK0G,KAAM6jB,EAAK7jB,KAAK/F,SAC3C+F,KAAK9D,OAAO8b,UAERgN,EAAahlB,KAAKilB,eAAejN,GACvChY,KAAKklB,oBAAoBF,GAEzB,IAiBgD5b,EAjB1CyK,EAAY7T,KAAKmlB,gBAiBvB,GAhBAvmB,KAAKC,QAAQglB,EAAK7jB,KAAK6X,YAAY7S,SAAUhF,MAExCA,KAAK/F,QAAQ4qB,cAActnB,gBAAgBgJ,SAASvG,KAAK6jB,MAC5DhQ,EAAUwH,YAAYwI,GAGxBpiB,aAAawC,QAAQjE,KAAK/F,QAAS+F,KAAK6X,YAAYxc,MAAMqnB,UAE1D1iB,KAAK4W,QAAU,IAAIS,OAAOrX,KAAK/F,QAAS4pB,EAAK7jB,KAAKuX,iBAAiByN,IAEnEnB,EAAIxd,UAAU4C,IAAIsJ,mBAMd,iBAAkBzY,SAASyD,iBAC7B6L,EAAA,IAAGC,OAAHzH,MAAAwH,EAAatP,SAASmE,KAAKsL,UAAUjN,SAAQ,SAAArC,GAC3CwH,aAAa+B,GAAGvJ,EAAS,YAAa2D,WAI1C,IAAMwnB,EAAW,WACXhf,EAAKlK,OAAOulB,WACdrb,EAAKif,iBAGP,IAAMC,EAAiBlf,EAAKud,YAC5Bvd,EAAKud,YAAc,KAEnBliB,aAAawC,QAAQmC,EAAKnM,QAASmM,EAAKyR,YAAYxc,MAAMonB,OAEtD6C,IAAmBpC,iBACrB9c,EAAKme,OAAO,KAAMne,IAItB,GAAIpG,KAAK6jB,IAAIxd,UAAUE,SAASmT,mBAAkB,CAChD,IAAM7e,EAAqBJ,iCAAiCuF,KAAK6jB,KACjEpiB,aAAagC,IAAIzD,KAAK6jB,IAAK3qB,eAAgBksB,GAC3C5pB,qBAAqBwE,KAAK6jB,IAAKhpB,QAE/BuqB,QAKN3R,KAAA,WAAO,IAAApE,EAAArP,KACC6jB,EAAM7jB,KAAKwkB,gBACXY,EAAW,WACX/V,EAAKsU,cAAgBV,kBAAoBY,EAAI5mB,YAC/C4mB,EAAI5mB,WAAWwJ,YAAYod,GAG7BxU,EAAKkW,iBACLlW,EAAKpV,QAAQoG,gBAAgB,oBAC7BoB,aAAawC,QAAQoL,EAAKpV,QAASoV,EAAKwI,YAAYxc,MAAMknB,QAC1DlT,EAAKuH,QAAQa,WAIf,IADkBhW,aAAawC,QAAQjE,KAAK/F,QAAS+F,KAAK6X,YAAYxc,MAAMinB,MAC9D1iB,iBAAd,CAQgD,IAAA4J,EAAhD,GAJAqa,EAAIxd,UAAUC,OAAOiM,mBAIjB,iBAAkBzY,SAASyD,iBAC7BiM,EAAA,IAAGH,OAAHzH,MAAA4H,EAAa1P,SAASmE,KAAKsL,UACxBjN,SAAQ,SAAArC,GAAO,OAAIwH,aAAaC,IAAIzH,EAAS,YAAa2D,SAO/D,GAJAoC,KAAK4jB,eAAeN,gBAAiB,EACrCtjB,KAAK4jB,eAAeP,gBAAiB,EACrCrjB,KAAK4jB,eAAeR,gBAAiB,EAEjCpjB,KAAK6jB,IAAIxd,UAAUE,SAASmT,mBAAkB,CAChD,IAAM7e,EAAqBJ,iCAAiCopB,GAE5DpiB,aAAagC,IAAIogB,EAAK3qB,eAAgBksB,GACtC5pB,qBAAqBqoB,EAAKhpB,QAE1BuqB,IAGFplB,KAAK2jB,YAAc,OAGrBjM,OAAA,WACuB,OAAjB1X,KAAK4W,SACP5W,KAAK4W,QAAQe,oBAMjB+M,cAAA,WACE,OAAOxkB,QAAQF,KAAKwlB,eAGtBhB,cAAA,WACE,GAAIxkB,KAAK6jB,IACP,OAAO7jB,KAAK6jB,IAGd,IAAM5pB,EAAUH,SAAS4F,cAAc,OAIvC,OAHAzF,EAAQonB,UAAYrhB,KAAK9D,OAAOwlB,SAEhC1hB,KAAK6jB,IAAM5pB,EAAQsP,SAAS,GACrBvJ,KAAK6jB,OAGdkB,WAAA,WACE,IAAMlB,EAAM7jB,KAAKwkB,gBACjBxkB,KAAKylB,kBAAkBtc,eAAe9J,QAAQ8jB,uBAAwBU,GAAM7jB,KAAKwlB,YACjF3B,EAAIxd,UAAUC,OAAOoT,kBAAiBnH,sBAGxCkT,kBAAA,SAAkBxrB,EAASyrB,GACzB,GAAgB,OAAZzrB,EAIJ,MAAuB,iBAAZyrB,GAAwBpqB,UAAUoqB,IACvCA,EAAQnR,SACVmR,EAAUA,EAAQ,SAIhB1lB,KAAK9D,OAAO2lB,KACV6D,EAAQzoB,aAAehD,IACzBA,EAAQonB,UAAY,GACpBpnB,EAAQohB,YAAYqK,IAGtBzrB,EAAQ0rB,YAAcD,EAAQC,mBAM9B3lB,KAAK9D,OAAO2lB,MACV7hB,KAAK9D,OAAO6lB,WACd2D,EAAUnF,aAAamF,EAAS1lB,KAAK9D,OAAOukB,UAAWzgB,KAAK9D,OAAOwkB,aAGrEzmB,EAAQonB,UAAYqE,GAEpBzrB,EAAQ0rB,YAAcD,MAI1BF,SAAA,WACE,IAAI7D,EAAQ3hB,KAAK/F,QAAQE,aAAa,uBAQtC,OANKwnB,IACHA,EAAqC,mBAAtB3hB,KAAK9D,OAAOylB,MACzB3hB,KAAK9D,OAAOylB,MAAMroB,KAAK0G,KAAK/F,SAC5B+F,KAAK9D,OAAOylB,OAGTA,KAKTpK,iBAAA,SAAiByN,GAAY,IAAAxV,EAAAxP,KAuB3B,OAAAkI,eAAAA,eAAA,GAtBwB,CACtB8P,UAAWgN,EACX7M,UAAW,CACT9P,OAAQrI,KAAKiY,aACb1B,KAAM,CACJqP,SAAU5lB,KAAK9D,OAAO4lB,mBAExB+D,MAAO,CACL5rB,QAAO,IAAM+F,KAAK6X,YAAY/S,KAAvB,UAETuT,gBAAiB,CACfC,kBAAmBtY,KAAK9D,OAAOsa,WAGnCsP,SAAU,SAAAtnB,GACJA,EAAKunB,oBAAsBvnB,EAAKwZ,WAClCxI,EAAKwW,6BAA6BxnB,IAGtCynB,SAAU,SAAAznB,GAAI,OAAIgR,EAAKwW,6BAA6BxnB,MAKjDwB,KAAK9D,OAAOwa,iBAInBwO,oBAAA,SAAoBF,GAClBhlB,KAAKwkB,gBAAgBne,UAAU4C,IAAOqY,aAAtC,IAAsD0D,MAGxD/M,WAAA,WAAa,IAAA9G,EAAAnR,KACLqI,EAAS,GAef,MAbkC,mBAAvBrI,KAAK9D,OAAOmM,OACrBA,EAAOhH,GAAK,SAAA7C,GAMV,OALAA,EAAK0Z,QAALhQ,eAAAA,eAAA,GACK1J,EAAK0Z,SACL/G,EAAKjV,OAAOmM,OAAO7J,EAAK0Z,QAAS/G,EAAKlX,UAAY,IAGhDuE,GAGT6J,EAAOA,OAASrI,KAAK9D,OAAOmM,OAGvBA,KAGT8c,cAAA,WACE,OAA8B,IAA1BnlB,KAAK9D,OAAO2X,UACP/Z,SAASmE,KAGd3C,UAAU0E,KAAK9D,OAAO2X,WACjB7T,KAAK9D,OAAO2X,UAGd1K,eAAe9J,QAAQW,KAAK9D,OAAO2X,cAG5CoR,eAAA,SAAejN,GACb,OAAOgK,cAAchK,EAAUlb,kBAGjCgnB,cAAA,WAAgB,IAAAtI,EAAAxb,KACGA,KAAK9D,OAAO+H,QAAQ/I,MAAM,KAElCoB,SAAQ,SAAA2H,GACf,GAAgB,UAAZA,EACFxC,aAAa+B,GAAGgY,EAAKvhB,QACnBuhB,EAAK3D,YAAYxc,MAAMsnB,MACvBnH,EAAKtf,OAAOhC,UACZ,SAAAqH,GAAK,OAAIia,EAAKnU,OAAO9F,WAElB,GAAI0C,IAAYsf,eAAgB,CACrC,IAAM2C,EAAUjiB,IAAYmf,cAC1B5H,EAAK3D,YAAYxc,MAAMynB,WACvBtH,EAAK3D,YAAYxc,MAAMunB,QACnBuD,EAAWliB,IAAYmf,cAC3B5H,EAAK3D,YAAYxc,MAAM0nB,WACvBvH,EAAK3D,YAAYxc,MAAMwnB,SAEzBphB,aAAa+B,GAAGgY,EAAKvhB,QACnBisB,EACA1K,EAAKtf,OAAOhC,UACZ,SAAAqH,GAAK,OAAIia,EAAK8I,OAAO/iB,MAEvBE,aAAa+B,GAAGgY,EAAKvhB,QACnBksB,EACA3K,EAAKtf,OAAOhC,UACZ,SAAAqH,GAAK,OAAIia,EAAK+I,OAAOhjB,UAK3BvB,KAAKykB,kBAAoB,WACnBjJ,EAAKvhB,SACPuhB,EAAK/H,QAIThS,aAAa+B,GAAGxD,KAAK/F,QAAQkM,QAAb,IAAyB6c,kBACvC,gBACAhjB,KAAKykB,mBAGHzkB,KAAK9D,OAAOhC,SACd8F,KAAK9D,OAALgM,eAAAA,eAAA,GACKlI,KAAK9D,QADV,GAAA,CAEE+H,QAAS,SACT/J,SAAU,KAGZ8F,KAAKomB,eAITA,UAAA,WACE,IAAMC,SAAmBrmB,KAAK/F,QAAQE,aAAa,wBAE/C6F,KAAK/F,QAAQE,aAAa,UAA0B,WAAdksB,KACxCrmB,KAAK/F,QAAQqN,aACX,sBACAtH,KAAK/F,QAAQE,aAAa,UAAY,IAGxC6F,KAAK/F,QAAQqN,aAAa,QAAS,QAIvCgd,OAAA,SAAO/iB,EAAOmX,GACZ,IAAMwL,EAAUlkB,KAAK6X,YAAY7S,UACjC0T,EAAUA,GAAW9Z,KAAKG,QAAQwC,EAAMQ,OAAQmiB,MAG9CxL,EAAU,IAAI1Y,KAAK6X,YACjBtW,EAAMQ,OACN/B,KAAKmkB,sBAEPvlB,KAAKC,QAAQ0C,EAAMQ,OAAQmiB,EAASxL,IAGlCnX,IACFmX,EAAQkL,eACS,YAAfriB,EAAMI,KAAqB0hB,cAAgBD,gBACzC,GAGF1K,EAAQ8L,gBAAgBne,UAAUE,SAASgM,oBAC3CmG,EAAQiL,cAAgBV,iBAC1BvK,EAAQiL,YAAcV,kBAIxBnT,aAAa4I,EAAQgL,UAErBhL,EAAQiL,YAAcV,iBAEjBvK,EAAQxc,OAAO0lB,OAAUlJ,EAAQxc,OAAO0lB,MAAMlO,KAKnDgF,EAAQgL,SAAW3nB,YAAW,WACxB2c,EAAQiL,cAAgBV,kBAC1BvK,EAAQhF,SAETgF,EAAQxc,OAAO0lB,MAAMlO,MARtBgF,EAAQhF,WAWZ6Q,OAAA,SAAOhjB,EAAOmX,GACZ,IAAMwL,EAAUlkB,KAAK6X,YAAY7S,UACjC0T,EAAUA,GAAW9Z,KAAKG,QAAQwC,EAAMQ,OAAQmiB,MAG9CxL,EAAU,IAAI1Y,KAAK6X,YACjBtW,EAAMQ,OACN/B,KAAKmkB,sBAEPvlB,KAAKC,QAAQ0C,EAAMQ,OAAQmiB,EAASxL,IAGlCnX,IACFmX,EAAQkL,eACS,aAAfriB,EAAMI,KAAsB0hB,cAAgBD,gBAC1C,GAGF1K,EAAQ2L,yBAIZvU,aAAa4I,EAAQgL,UAErBhL,EAAQiL,YAAcT,gBAEjBxK,EAAQxc,OAAO0lB,OAAUlJ,EAAQxc,OAAO0lB,MAAMnO,KAKnDiF,EAAQgL,SAAW3nB,YAAW,WACxB2c,EAAQiL,cAAgBT,iBAC1BxK,EAAQjF,SAETiF,EAAQxc,OAAO0lB,MAAMnO,MARtBiF,EAAQjF,WAWZ4Q,qBAAA,WACE,IAAK,IAAMpgB,KAAWjE,KAAK4jB,eACzB,GAAI5jB,KAAK4jB,eAAe3f,GACtB,OAAO,EAIX,OAAO,KAGT4J,WAAA,SAAW3R,GACT,IAAMoqB,EAAiBze,YAAYG,kBAAkBhI,KAAK/F,SA4C1D,OA1CAmC,OAAOC,KAAKiqB,GACThqB,SAAQ,SAAAiqB,IAC0C,IAA7C/E,sBAAsB1e,QAAQyjB,WACzBD,EAAeC,MAIxBrqB,GAAsC,iBAArBA,EAAO2X,WAA0B3X,EAAO2X,UAAUU,SACrErY,EAAO2X,UAAY3X,EAAO2X,UAAU,IASV,iBAN5B3X,EAAMgM,eAAAA,eAAAA,eAAA,GACDlI,KAAK6X,YAAYpN,SACjB6b,GACkB,iBAAXpqB,GAAuBA,EAASA,EAAS,KAGnC0lB,QAChB1lB,EAAO0lB,MAAQ,CACblO,KAAMxX,EAAO0lB,MACbnO,KAAMvX,EAAO0lB,QAIW,iBAAjB1lB,EAAOylB,QAChBzlB,EAAOylB,MAAQzlB,EAAOylB,MAAMtoB,YAGA,iBAAnB6C,EAAOwpB,UAChBxpB,EAAOwpB,QAAUxpB,EAAOwpB,QAAQrsB,YAGlC2C,gBACE8I,OACA5I,EACA8D,KAAK6X,YAAY7M,aAGf9O,EAAO6lB,WACT7lB,EAAOwlB,SAAWnB,aAAarkB,EAAOwlB,SAAUxlB,EAAOukB,UAAWvkB,EAAOwkB,aAGpExkB,KAGTioB,mBAAA,WACE,IAAMjoB,EAAS,GAEf,GAAI8D,KAAK9D,OACP,IAAK,IAAMqC,KAAOyB,KAAK9D,OACjB8D,KAAK6X,YAAYpN,QAAQlM,KAASyB,KAAK9D,OAAOqC,KAChDrC,EAAOqC,GAAOyB,KAAK9D,OAAOqC,IAKhC,OAAOrC,KAGTqpB,eAAA,WACE,IAAM1B,EAAM7jB,KAAKwkB,gBACXgC,EAAW3C,EAAI1pB,aAAa,SAASZ,MAAMgoB,oBAChC,OAAbiF,GAAqBA,EAASvkB,OAAS,GACzCukB,EAASC,KAAI,SAAAC,GAAK,OAAIA,EAAMrsB,UACzBiC,SAAQ,SAAAqqB,GAAM,OAAI9C,EAAIxd,UAAUC,OAAOqgB,SAI9CX,6BAAA,SAA6BY,GAC3B,IAAMC,EAAiBD,EAAW9nB,SAClCkB,KAAK6jB,IAAMgD,EAAeC,OAC1B9mB,KAAKulB,iBACLvlB,KAAKklB,oBAAoBllB,KAAKilB,eAAe2B,EAAW5O,eAG1DqN,eAAA,WACE,IAAMxB,EAAM7jB,KAAKwkB,gBACXuC,EAAsB/mB,KAAK9D,OAAOulB,UACA,OAApCoC,EAAI1pB,aAAa,iBAIrB0pB,EAAIxd,UAAUC,OAAOoT,mBACrB1Z,KAAK9D,OAAOulB,WAAY,EACxBzhB,KAAKyT,OACLzT,KAAK0T,OACL1T,KAAK9D,OAAOulB,UAAYsF,MAKnBrgB,gBAAP,SAAuBxK,GACrB,OAAO8D,KAAK2G,MAAK,WACf,IAAInI,EAAOI,KAAKG,QAAQiB,KAAMgF,YACxB4I,EAA4B,iBAAX1R,GAAuBA,EAE9C,IAAKsC,IAAQ,eAAe5B,KAAKV,MAI5BsC,IACHA,EAAO,IAAIglB,EAAQxjB,KAAM4N,IAGL,iBAAX1R,GAAqB,CAC9B,QAA4B,IAAjBsC,EAAKtC,GACd,MAAM,IAAI2V,UAAJ,oBAAkC3V,EAAlC,KAGRsC,EAAKtC,YAKJ4K,YAAP,SAAmB7M,GACjB,OAAO2E,KAAKG,QAAQ9E,EAAS+K,gEAroB7B,OAAOD,0CAIP,OAAO0F,uCAIP,OAAO3F,wCAIP,OAAOE,yCAIP,OAAO3J,0CAIP,OAAO4J,gDAIP,OAAO+F,oBAjDLwY,GAkqBAjjB,IAAIxC,YASV,GAAIwC,IAAG,CACL,IAAMwG,qBAAqBxG,IAAEc,GAAGyD,QAChCvE,IAAEc,GAAGyD,QAAQ0e,QAAQ9c,gBACrBnG,IAAEc,GAAGyD,QAAMkC,YAAcwc,QACzBjjB,IAAEc,GAAGyD,QAAMmC,WAAa,WAEtB,OADA1G,IAAEc,GAAGyD,QAAQiC,qBACNyc,QAAQ9c,iBC1xBnB,IAAM5B,OAAO,UACPC,UAAU,eACVC,WAAW,aACXC,YAAS,IAAOD,WAChBsc,eAAe,aACfC,qBAAqB,IAAI5kB,OAAJ,UAAqB2kB,eAArB,OAAyC,KAE9D7W,UAAOvC,eAAAA,eAAA,GACRsb,QAAQ/Y,SADA,GAAA,CAEXuN,UAAW,QACX/T,QAAS,QACTyhB,QAAS,GACThE,SAAU,gJAMN1W,cAAW9C,eAAAA,eAAA,GACZsb,QAAQxY,aADI,GAAA,CAEf0a,QAAS,8BAGLrqB,QAAQ,CACZinB,KAAI,OAASrd,YACbsd,OAAM,SAAWtd,YACjBud,KAAI,OAASvd,YACbwd,MAAK,QAAUxd,YACfyd,SAAQ,WAAazd,YACrB0d,MAAK,QAAU1d,YACf2d,QAAO,UAAY3d,YACnB4d,SAAQ,WAAa5d,YACrB6d,WAAU,aAAe7d,YACzB8d,WAAU,aAAe9d,aAGrByU,kBAAkB,OAClBnH,kBAAkB,OAElByU,eAAiB,kBACjBC,iBAAmB,gBAQnBC,QAAAA,SAAAA,oGAiCJxC,cAAA,WACE,OAAO1kB,KAAKwlB,YAAcxlB,KAAKmnB,iBAGjCpC,WAAA,WACE,IAAMlB,EAAM7jB,KAAKwkB,gBAGjBxkB,KAAKylB,kBAAkBtc,eAAe9J,QAAQ2nB,eAAgBnD,GAAM7jB,KAAKwlB,YACzE,IAAIE,EAAU1lB,KAAKmnB,cACI,mBAAZzB,IACTA,EAAUA,EAAQpsB,KAAK0G,KAAK/F,UAG9B+F,KAAKylB,kBAAkBtc,eAAe9J,QAAQ4nB,iBAAkBpD,GAAM6B,GAEtE7B,EAAIxd,UAAUC,OAAOoT,kBAAiBnH,sBAGxC2S,oBAAA,SAAoBF,GAClBhlB,KAAKwkB,gBAAgBne,UAAU4C,IAAOqY,eAAtC,IAAsD0D,MAKxDmC,YAAA,WACE,OAAOnnB,KAAK/F,QAAQE,aAAa,iBAC/B6F,KAAK9D,OAAOwpB,WAGhBH,eAAA,WACE,IAAM1B,EAAM7jB,KAAKwkB,gBACXgC,EAAW3C,EAAI1pB,aAAa,SAASZ,MAAMgoB,sBAChC,OAAbiF,GAAqBA,EAASvkB,OAAS,GACzCukB,EAASC,KAAI,SAAAC,GAAK,OAAIA,EAAMrsB,UACzBiC,SAAQ,SAAAqqB,GAAM,OAAI9C,EAAIxd,UAAUC,OAAOqgB,SAMvCjgB,gBAAP,SAAuBxK,GACrB,OAAO8D,KAAK2G,MAAK,WACf,IAAInI,EAAOI,KAAKG,QAAQiB,KAAMgF,YACxB4I,EAA4B,iBAAX1R,EAAsBA,EAAS,KAEtD,IAAKsC,IAAQ,eAAe5B,KAAKV,MAI5BsC,IACHA,EAAO,IAAI0oB,EAAQlnB,KAAM4N,GACzBhP,KAAKC,QAAQmB,KAAMgF,WAAUxG,IAGT,iBAAXtC,GAAqB,CAC9B,QAA4B,IAAjBsC,EAAKtC,GACd,MAAM,IAAI2V,UAAJ,oBAAkC3V,EAAlC,KAGRsC,EAAKtC,YAKJ4K,YAAP,SAAmB7M,GACjB,OAAO2E,KAAKG,QAAQ9E,EAAS+K,gEA/F7B,OAAOD,0CAIP,OAAO0F,uCAIP,OAAO3F,wCAIP,OAAOE,yCAIP,OAAO3J,0CAIP,OAAO4J,gDAIP,OAAO+F,oBA5BLkc,CAAgB1D,SAuGhBjjB,IAAIxC,YAQV,GAAIwC,IAAG,CACL,IAAMwG,qBAAqBxG,IAAEc,GAAGyD,QAChCvE,IAAEc,GAAGyD,QAAQoiB,QAAQxgB,gBACrBnG,IAAEc,GAAGyD,QAAMkC,YAAckgB,QACzB3mB,IAAEc,GAAGyD,QAAMmC,WAAa,WAEtB,OADA1G,IAAEc,GAAGyD,QAAQiC,qBACNmgB,QAAQxgB,iBC9JnB,IAAM5B,OAAO,YACPC,UAAU,eACVC,WAAW,eACXC,YAAS,IAAOD,WAChBE,eAAe,YAEfuF,UAAU,CACdpC,OAAQ,GACR+e,OAAQ,OACRrlB,OAAQ,IAGJiJ,cAAc,CAClB3C,OAAQ,SACR+e,OAAQ,SACRrlB,OAAQ,oBAGJslB,eAAc,WAAcpiB,YAC5BqiB,aAAY,SAAYriB,YACxB+G,sBAAmB,OAAU/G,YAAYC,eAEzCqiB,yBAA2B,gBAC3BrgB,oBAAoB,SAEpBsgB,kBAAoB,sBACpBC,wBAA0B,oBAC1BC,mBAAqB,YACrBC,mBAAqB,YACrBC,oBAAsB,mBACtBC,kBAAoB,YACpBC,yBAA2B,mBAE3BC,cAAgB,SAChBC,gBAAkB,WAQlBC,UAAAA,WACJ,SAAAA,EAAYhuB,EAASiC,GAAQ,IAAAkK,EAAApG,KAC3BA,KAAK2F,SAAW1L,EAChB+F,KAAKkoB,eAAqC,SAApBjuB,EAAQgW,QAAqBtV,OAASV,EAC5D+F,KAAK4N,QAAU5N,KAAK6N,WAAW3R,GAC/B8D,KAAKqT,UAAerT,KAAK4N,QAAQ7L,OAAhB,IAA0B2lB,mBAA1B,IACQ1nB,KAAK4N,QAAQ7L,OADrB,IAC+B6lB,oBAD/B,IAEQ5nB,KAAK4N,QAAQ7L,OAFrB,KAEgCwlB,yBACjDvnB,KAAKmoB,SAAW,GAChBnoB,KAAKooB,SAAW,GAChBpoB,KAAKqoB,cAAgB,KACrBroB,KAAKsoB,cAAgB,EAErB7mB,aAAa+B,GAAGxD,KAAKkoB,eAAgBZ,cAAc,SAAA/lB,GAAK,OAAI6E,EAAKmiB,SAAShnB,MAE1EvB,KAAKwoB,UACLxoB,KAAKuoB,WAEL3pB,KAAKC,QAAQ5E,EAAS+K,WAAUhF,iCAelCwoB,QAAA,WAAU,IAAAnZ,EAAArP,KACFyoB,EAAazoB,KAAKkoB,iBAAmBloB,KAAKkoB,eAAevtB,OAC7DotB,cACAC,gBAEIU,EAAuC,SAAxB1oB,KAAK4N,QAAQwZ,OAChCqB,EACAzoB,KAAK4N,QAAQwZ,OAETuB,EAAaD,IAAiBV,gBAClChoB,KAAK4oB,gBACL,EAEF5oB,KAAKmoB,SAAW,GAChBnoB,KAAKooB,SAAW,GAEhBpoB,KAAKsoB,cAAgBtoB,KAAK6oB,mBAEV1f,eAAelK,KAAKe,KAAKqT,WAGtCoT,KAAI,SAAAxsB,GACH,IAAI8H,EACE+mB,EAAiBxuB,uBAAuBL,GAM9C,GAJI6uB,IACF/mB,EAASoH,eAAe9J,QAAQypB,IAG9B/mB,EAAQ,CACV,IAAMgnB,EAAYhnB,EAAOwG,wBACzB,GAAIwgB,EAAUrL,OAASqL,EAAUC,OAC/B,MAAO,CACLnhB,YAAY6gB,GAAc3mB,GAAQyG,IAAMmgB,EACxCG,GAKN,OAAO,QAERrf,QAAO,SAAAwf,GAAI,OAAIA,KACfC,MAAK,SAACvK,EAAGE,GAAJ,OAAUF,EAAE,GAAKE,EAAE,MACxBviB,SAAQ,SAAA2sB,GACP5Z,EAAK8Y,SAASpe,KAAKkf,EAAK,IACxB5Z,EAAK+Y,SAASre,KAAKkf,EAAK,UAI9B/iB,QAAA,WACEtH,KAAKI,WAAWgB,KAAK2F,SAAUX,YAC/BvD,aAAaC,IAAI1B,KAAKkoB,eAAgBjjB,aAEtCjF,KAAK2F,SAAW,KAChB3F,KAAKkoB,eAAiB,KACtBloB,KAAK4N,QAAU,KACf5N,KAAKqT,UAAY,KACjBrT,KAAKmoB,SAAW,KAChBnoB,KAAKooB,SAAW,KAChBpoB,KAAKqoB,cAAgB,KACrBroB,KAAKsoB,cAAgB,QAKvBza,WAAA,SAAW3R,GAMT,GAA6B,iBAL7BA,EAAMgM,eAAAA,eAAA,GACDuC,WACkB,iBAAXvO,GAAuBA,EAASA,EAAS,KAGnC6F,QAAuBzG,UAAUY,EAAO6F,QAAS,CAAA,IAC3D1D,EAAOnC,EAAO6F,OAAd1D,GACDA,IACHA,EAAK5E,OAAOqL,QACZ5I,EAAO6F,OAAO1D,GAAKA,GAGrBnC,EAAO6F,OAAP,IAAoB1D,EAKtB,OAFArC,gBAAgB8I,OAAM5I,EAAQ8O,eAEvB9O,KAGT0sB,cAAA,WACE,OAAO5oB,KAAKkoB,iBAAmBvtB,OAC7BqF,KAAKkoB,eAAeiB,YACpBnpB,KAAKkoB,eAAezf,aAGxBogB,iBAAA,WACE,OAAO7oB,KAAKkoB,eAAe1L,cAAgB7iB,KAAKyvB,IAC9CtvB,SAASmE,KAAKue,aACd1iB,SAASyD,gBAAgBif,iBAI7B6M,iBAAA,WACE,OAAOrpB,KAAKkoB,iBAAmBvtB,OAC7BA,OAAO2uB,YACPtpB,KAAKkoB,eAAe3f,wBAAwBygB,UAGhDT,SAAA,WACE,IAAM9f,EAAYzI,KAAK4oB,gBAAkB5oB,KAAK4N,QAAQvF,OAChDmU,EAAexc,KAAK6oB,mBACpBU,EAAYvpB,KAAK4N,QAAQvF,OAC7BmU,EACAxc,KAAKqpB,mBAMP,GAJIrpB,KAAKsoB,gBAAkB9L,GACzBxc,KAAKwoB,UAGH/f,GAAa8gB,EAAjB,CACE,IAAMxnB,EAAS/B,KAAKooB,SAASpoB,KAAKooB,SAASnmB,OAAS,GAEhDjC,KAAKqoB,gBAAkBtmB,GACzB/B,KAAKwpB,UAAUznB,OAJnB,CAUA,GAAI/B,KAAKqoB,eAAiB5f,EAAYzI,KAAKmoB,SAAS,IAAMnoB,KAAKmoB,SAAS,GAAK,EAG3E,OAFAnoB,KAAKqoB,cAAgB,UACrBroB,KAAKypB,SAIP,IAAK,IAAIznB,EAAIhC,KAAKmoB,SAASlmB,OAAQD,KAAM,CAChBhC,KAAKqoB,gBAAkBroB,KAAKooB,SAASpmB,IACxDyG,GAAazI,KAAKmoB,SAASnmB,UACM,IAAzBhC,KAAKmoB,SAASnmB,EAAI,IACtByG,EAAYzI,KAAKmoB,SAASnmB,EAAI,KAGpChC,KAAKwpB,UAAUxpB,KAAKooB,SAASpmB,SAKnCwnB,UAAA,SAAUznB,GACR/B,KAAKqoB,cAAgBtmB,EAErB/B,KAAKypB,SAEL,IAAMC,EAAU1pB,KAAKqT,UAAUnY,MAAM,KAClCurB,KAAI,SAAAvsB,GAAQ,OAAOA,EAAP,iBAAgC6H,EAAhC,MAA4C7H,EAA5C,UAA8D6H,EAA9D,QAET4nB,EAAOxgB,eAAe9J,QAAQqqB,EAAQE,KAAK,MAE7CD,EAAKtjB,UAAUE,SAASghB,2BAC1Bpe,eACG9J,QAAQyoB,yBAA0B6B,EAAKxjB,QAAQ0hB,oBAC/CxhB,UAAU4C,IAAI/B,qBAEjByiB,EAAKtjB,UAAU4C,IAAI/B,uBAGnByiB,EAAKtjB,UAAU4C,IAAI/B,qBAEnBiC,eACGQ,QAAQggB,EAAMlC,yBACdnrB,SAAQ,SAAAutB,GAGP1gB,eAAea,KAAK6f,EAAcnC,mBAAlC,KAAyDE,qBACtDtrB,SAAQ,SAAA2sB,GAAI,OAAIA,EAAK5iB,UAAU4C,IAAI/B,wBAGtCiC,eAAea,KAAK6f,EAAWlC,oBAC5BrrB,SAAQ,SAAAwtB,GACP3gB,eAAeI,SAASugB,EAASpC,oBAC9BprB,SAAQ,SAAA2sB,GAAI,OAAIA,EAAK5iB,UAAU4C,IAAI/B,+BAKhDzF,aAAawC,QAAQjE,KAAKkoB,eAAgBb,eAAgB,CACxD5W,cAAe1O,OAInB0nB,OAAA,WACEtgB,eAAelK,KAAKe,KAAKqT,WACtB5J,QAAO,SAAAsgB,GAAI,OAAIA,EAAK1jB,UAAUE,SAASW,wBACvC5K,SAAQ,SAAAytB,GAAI,OAAIA,EAAK1jB,UAAUC,OAAOY,2BAKpCR,gBAAP,SAAuBxK,GACrB,OAAO8D,KAAK2G,MAAK,WACf,IAAInI,EAAOI,KAAKG,QAAQiB,KAAMgF,YAO9B,GAJKxG,IACHA,EAAO,IAAIypB,EAAUjoB,KAHW,iBAAX9D,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBsC,EAAKtC,GACd,MAAM,IAAI2V,UAAJ,oBAAkC3V,EAAlC,KAGRsC,EAAKtC,YAKJ4K,YAAP,SAAmB7M,GACjB,OAAO2E,KAAKG,QAAQ9E,EAAS+K,gEA/N7B,OAAOD,0CAIP,OAAO0F,gBA5BLwd,GAiQNxmB,aAAa+B,GAAG7I,OAAQqR,uBAAqB,WAC3C7C,eAAelK,KAAKuoB,mBACjBlrB,SAAQ,SAAA0tB,GAAG,OAAI,IAAI/B,UAAU+B,EAAKniB,YAAYG,kBAAkBgiB,UAGrE,IAAMzpB,IAAIxC,YAQV,GAAIwC,IAAG,CACL,IAAMwG,qBAAqBxG,IAAEc,GAAGyD,QAChCvE,IAAEc,GAAGyD,QAAQmjB,UAAUvhB,gBACvBnG,IAAEc,GAAGyD,QAAMkC,YAAcihB,UACzB1nB,IAAEc,GAAGyD,QAAMmC,WAAa,WAEtB,OADA1G,IAAEc,GAAGyD,QAAQiC,qBACNkhB,UAAUvhB,iBC9TrB,IAAM5B,OAAO,MACPC,UAAU,eACVC,WAAW,SACXC,YAAS,IAAOD,WAChBE,eAAe,YAEfmN,aAAU,OAAUpN,YACpBqN,eAAY,SAAYrN,YACxBkN,aAAU,OAAUlN,YACpBmN,cAAW,QAAWnN,YACtBK,uBAAoB,QAAWL,YAAYC,eAE3C+kB,yBAA2B,gBAC3B/iB,oBAAoB,SACpBoO,sBAAsB,WACtBoE,kBAAkB,OAClBnH,kBAAkB,OAElBsV,oBAAoB,YACpBJ,0BAA0B,oBAC1Bjb,kBAAkB,UAClB0d,mBAAqB,wBACrB/iB,uBAAuB,kEACvB2gB,2BAA2B,mBAC3BqC,+BAAiC,kCAQjCC,IAAAA,WACJ,SAAAA,EAAYnwB,GACV+F,KAAK2F,SAAW1L,EAEhB2E,KAAKC,QAAQmB,KAAK2F,SAAUX,WAAUhF,iCAWxC0T,KAAA,WAAO,IAAAtN,EAAApG,KACL,KAAKA,KAAK2F,SAAS1I,YACjB+C,KAAK2F,SAAS1I,WAAW1B,WAAasO,KAAKC,cAC3C9J,KAAK2F,SAASU,UAAUE,SAASW,sBACjClH,KAAK2F,SAASU,UAAUE,SAAS+O,wBAHnC,CAOA,IAAIrL,EACElI,EAASvH,uBAAuBwF,KAAK2F,UACrC0kB,EAAcrqB,KAAK2F,SAASQ,QAAQshB,2BAE1C,GAAI4C,EAAa,CACf,IAAMC,EAAwC,OAAzBD,EAAYhM,UAA8C,OAAzBgM,EAAYhM,SAAoB6L,mBAAqB1d,kBAE3GvC,GADAA,EAAWd,eAAelK,KAAKqrB,EAAcD,IACzBpgB,EAAShI,OAAS,GAGxC,IAAIsoB,EAAY,KAYhB,GAVItgB,IACFsgB,EAAY9oB,aAAawC,QAAQgG,EAAUoI,aAAY,CACrD5B,cAAezQ,KAAK2F,cAINlE,aAAawC,QAAQjE,KAAK2F,SAAUwM,aAAY,CAChE1B,cAAexG,IAGHrK,kBACG,OAAd2qB,GAAsBA,EAAU3qB,kBADnC,CAKAI,KAAKwpB,UACHxpB,KAAK2F,SACL0kB,GAGF,IAAMjF,EAAW,WACf3jB,aAAawC,QAAQgG,EAAUqI,eAAc,CAC3C7B,cAAerK,EAAKT,WAEtBlE,aAAawC,QAAQmC,EAAKT,SAAUyM,cAAa,CAC/C3B,cAAexG,KAIflI,EACF/B,KAAKwpB,UAAUznB,EAAQA,EAAO9E,WAAYmoB,GAE1CA,SAIJlf,QAAA,WACEtH,KAAKI,WAAWgB,KAAK2F,SAAUX,YAC/BhF,KAAK2F,SAAW,QAKlB6jB,UAAA,SAAUvvB,EAAS4Z,EAAWkI,GAAU,IAAA1M,EAAArP,KAKhCwqB,IAJiB3W,GAAqC,OAAvBA,EAAUwK,UAA4C,OAAvBxK,EAAUwK,SAE5ElV,eAAeI,SAASsK,EAAWrH,mBADnCrD,eAAelK,KAAKirB,mBAAoBrW,IAGZ,GACxBS,EAAkByH,GACrByO,GAAUA,EAAOnkB,UAAUE,SAASmT,mBAEjC0L,EAAW,WAAA,OAAM/V,EAAKob,oBAC1BxwB,EACAuwB,EACAzO,IAGF,GAAIyO,GAAUlW,EAAiB,CAC7B,IAAMzZ,EAAqBJ,iCAAiC+vB,GAC5DA,EAAOnkB,UAAUC,OAAOiM,mBAExB9Q,aAAagC,IAAI+mB,EAAQtxB,eAAgBksB,GACzC5pB,qBAAqBgvB,EAAQ3vB,QAE7BuqB,OAIJqF,oBAAA,SAAoBxwB,EAASuwB,EAAQzO,GACnC,GAAIyO,EAAQ,CACVA,EAAOnkB,UAAUC,OAAOY,qBAExB,IAAMwjB,EAAgBvhB,eAAe9J,QAAQ8qB,+BAAgCK,EAAOvtB,YAEhFytB,GACFA,EAAcrkB,UAAUC,OAAOY,qBAGG,QAAhCsjB,EAAOrwB,aAAa,SACtBqwB,EAAOljB,aAAa,iBAAiB,IAIzCrN,EAAQoM,UAAU4C,IAAI/B,qBACe,QAAjCjN,EAAQE,aAAa,SACvBF,EAAQqN,aAAa,iBAAiB,GAGxCzJ,OAAO5D,GAEHA,EAAQoM,UAAUE,SAASmT,oBAC7Bzf,EAAQoM,UAAU4C,IAAIsJ,mBAGpBtY,EAAQgD,YAAchD,EAAQgD,WAAWoJ,UAAUE,SAAS0jB,6BACtChwB,EAAQkM,QAAQ0hB,sBAGtC1e,eAAelK,KAAK6oB,4BACjBxrB,SAAQ,SAAAquB,GAAQ,OAAIA,EAAStkB,UAAU4C,IAAI/B,wBAGhDjN,EAAQqN,aAAa,iBAAiB,IAGpCyU,GACFA,OAMGrV,gBAAP,SAAuBxK,GACrB,OAAO8D,KAAK2G,MAAK,WACf,IAAMnI,EAAOI,KAAKG,QAAQiB,KAAMgF,aAAa,IAAIolB,EAAIpqB,MAErD,GAAsB,iBAAX9D,EAAqB,CAC9B,QAA4B,IAAjBsC,EAAKtC,GACd,MAAM,IAAI2V,UAAJ,oBAAkC3V,EAAlC,KAGRsC,EAAKtC,YAKJ4K,YAAP,SAAmB7M,GACjB,OAAO2E,KAAKG,QAAQ9E,EAAS+K,gEAzJ7B,OAAOD,gBAVLqlB,GA6KN3oB,aAAa+B,GAAG1J,SAAUwL,uBAAsB6B,wBAAsB,SAAU5F,GAC9EA,EAAM5B,kBAEOf,KAAKG,QAAQiB,KAAMgF,aAAa,IAAIolB,IAAIpqB,OAChD0T,UAGP,IAAMnT,IAAIxC,YASV,GAAIwC,IAAG,CACL,IAAMwG,qBAAqBxG,IAAEc,GAAGyD,QAChCvE,IAAEc,GAAGyD,QAAQslB,IAAI1jB,gBACjBnG,IAAEc,GAAGyD,QAAMkC,YAAcojB,IACzB7pB,IAAEc,GAAGyD,QAAMmC,WAAa,WAEtB,OADA1G,IAAEc,GAAGyD,QAAQiC,qBACNqjB,IAAI1jB,iBCnOf,IAAM5B,OAAO,QACPC,UAAU,eACVC,WAAW,WACXC,YAAS,IAAOD,WAEhBmU,sBAAmB,gBAAmBlU,YACtCoN,aAAU,OAAUpN,YACpBqN,eAAY,SAAYrN,YACxBkN,aAAU,OAAUlN,YACpBmN,cAAW,QAAWnN,YAEtByU,kBAAkB,OAClBkR,gBAAkB,OAClBrY,kBAAkB,OAClBsY,mBAAqB,UAErB7f,cAAc,CAClByW,UAAW,UACXqJ,SAAU,UACVlJ,MAAO,UAGHnX,UAAU,CACdgX,WAAW,EACXqJ,UAAU,EACVlJ,MAAO,KAGH9H,wBAAwB,yBAQxBiR,MAAAA,WACJ,SAAAA,EAAY9wB,EAASiC,GACnB8D,KAAK2F,SAAW1L,EAChB+F,KAAK4N,QAAU5N,KAAK6N,WAAW3R,GAC/B8D,KAAK0jB,SAAW,KAChB1jB,KAAK8jB,gBACLllB,KAAKC,QAAQ5E,EAAS+K,WAAUhF,iCAmBlC0T,KAAA,WAAO,IAAAtN,EAAApG,KAGL,IAFkByB,aAAawC,QAAQjE,KAAK2F,SAAUwM,cAExCvS,iBAAd,CAIII,KAAK4N,QAAQ6T,WACfzhB,KAAK2F,SAASU,UAAU4C,IAAIyQ,mBAG9B,IAAM0L,EAAW,WACfhf,EAAKT,SAASU,UAAUC,OAAOukB,oBAC/BzkB,EAAKT,SAASU,UAAU4C,IAAIsJ,mBAE5B9Q,aAAawC,QAAQmC,EAAKT,SAAUyM,eAEhChM,EAAKwH,QAAQkd,WACf1kB,EAAKsd,SAAW3nB,YAAW,WACzBqK,EAAKqN,SACJrN,EAAKwH,QAAQgU,SAOpB,GAHA5hB,KAAK2F,SAASU,UAAUC,OAAOskB,iBAC/B/sB,OAAOmC,KAAK2F,UACZ3F,KAAK2F,SAASU,UAAU4C,IAAI4hB,oBACxB7qB,KAAK4N,QAAQ6T,UAAW,CAC1B,IAAM5mB,EAAqBJ,iCAAiCuF,KAAK2F,UAEjElE,aAAagC,IAAIzD,KAAK2F,SAAUzM,eAAgBksB,GAChD5pB,qBAAqBwE,KAAK2F,SAAU9K,QAEpCuqB,QAIJ3R,KAAA,WAAO,IAAApE,EAAArP,KACL,GAAKA,KAAK2F,SAASU,UAAUE,SAASgM,qBAIpB9Q,aAAawC,QAAQjE,KAAK2F,SAAU0M,cAExCzS,iBAAd,CAIA,IAAMwlB,EAAW,WACf/V,EAAK1J,SAASU,UAAU4C,IAAI2hB,iBAC5BnpB,aAAawC,QAAQoL,EAAK1J,SAAU2M,iBAItC,GADAtS,KAAK2F,SAASU,UAAUC,OAAOiM,mBAC3BvS,KAAK4N,QAAQ6T,UAAW,CAC1B,IAAM5mB,EAAqBJ,iCAAiCuF,KAAK2F,UAEjElE,aAAagC,IAAIzD,KAAK2F,SAAUzM,eAAgBksB,GAChD5pB,qBAAqBwE,KAAK2F,SAAU9K,QAEpCuqB,QAIJlf,QAAA,WACE4J,aAAa9P,KAAK0jB,UAClB1jB,KAAK0jB,SAAW,KAEZ1jB,KAAK2F,SAASU,UAAUE,SAASgM,oBACnCvS,KAAK2F,SAASU,UAAUC,OAAOiM,mBAGjC9Q,aAAaC,IAAI1B,KAAK2F,SAAUwT,uBAChCva,KAAKI,WAAWgB,KAAK2F,SAAUX,YAE/BhF,KAAK2F,SAAW,KAChB3F,KAAK4N,QAAU,QAKjBC,WAAA,SAAW3R,GAaT,OAZAA,EAAMgM,eAAAA,eAAAA,eAAA,GACDuC,WACA5C,YAAYG,kBAAkBhI,KAAK2F,WACjB,iBAAXzJ,GAAuBA,EAASA,EAAS,IAGrDF,gBACE8I,OACA5I,EACA8D,KAAK6X,YAAY7M,aAGZ9O,KAGT4nB,cAAA,WAAgB,IAAAtU,EAAAxP,KACdyB,aAAa+B,GACXxD,KAAK2F,SACLwT,sBACAW,yBACA,WAAA,OAAMtK,EAAKiE,aAMR/M,gBAAP,SAAuBxK,GACrB,OAAO8D,KAAK2G,MAAK,WACf,IAAInI,EAAOI,KAAKG,QAAQiB,KAAMgF,YAO9B,GAJKxG,IACHA,EAAO,IAAIusB,EAAM/qB,KAHe,iBAAX9D,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBsC,EAAKtC,GACd,MAAM,IAAI2V,UAAJ,oBAAkC3V,EAAlC,KAGRsC,EAAKtC,GAAQ8D,aAKZ8G,YAAP,SAAmB7M,GACjB,OAAO2E,KAAKG,QAAQ9E,EAAS+K,gEA7I7B,OAAOD,8CAIP,OAAOiG,8CAIP,OAAOP,gBApBLsgB,GA6JAxqB,IAAIxC,YASV,GAAIwC,IAAG,CACL,IAAMwG,qBAAqBxG,IAAEc,GAAGyD,QAChCvE,IAAEc,GAAGyD,QAAQimB,MAAMrkB,gBACnBnG,IAAEc,GAAGyD,QAAMkC,YAAc+jB,MACzBxqB,IAAEc,GAAGyD,QAAMmC,WAAa,WAEtB,OADA1G,IAAEc,GAAGyD,QAAQiC,qBACNgkB,MAAMrkB", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = parseFloat(transitionDuration)\n  const floatTransitionDelay = parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes)\n    .forEach(property => {\n      const expectedTypes = configTypes[property]\n      const value = config[property]\n      const valueType = value && isElement(value) ?\n        'element' :\n        toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new Error(\n          `${componentName.toUpperCase()}: ` +\n          `Option \"${property}\" provided type \"${valueType}\" ` +\n          `but expected type \"${expectedTypes}\".`)\n      }\n    })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nexport {\n  getjQuery,\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.key === 'undefined') {\n        element.key = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.key.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.key === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.key\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.key === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.key\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.key\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/* istanbul ignore file */\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.0-alpha1): dom/polyfill.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getUID } from '../util/index'\n\nlet find = Element.prototype.querySelectorAll\nlet findOne = Element.prototype.querySelector\n\n// MSEdge resets defaultPrevented flag upon dispatchEvent call if at least one listener is attached\nconst defaultPreventedPreservedOnDispatch = (() => {\n  const e = new CustomEvent('Bootstrap', {\n    cancelable: true\n  })\n\n  const element = document.createElement('div')\n  element.addEventListener('Bootstrap', () => null)\n\n  e.preventDefault()\n  element.dispatchEvent(e)\n  return e.defaultPrevented\n})()\n\nconst scopeSelectorRegex = /:scope\\b/\nconst supportScopeQuery = (() => {\n  const element = document.createElement('div')\n\n  try {\n    element.querySelectorAll(':scope *')\n  } catch (_) {\n    return false\n  }\n\n  return true\n})()\n\nif (!supportScopeQuery) {\n  find = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelectorAll(selector)\n    }\n\n    const hasId = Boolean(this.id)\n\n    if (!hasId) {\n      this.id = getUID('scope')\n    }\n\n    let nodeList = null\n    try {\n      selector = selector.replace(scopeSelectorRegex, `#${this.id}`)\n      nodeList = this.querySelectorAll(selector)\n    } finally {\n      if (!hasId) {\n        this.removeAttribute('id')\n      }\n    }\n\n    return nodeList\n  }\n\n  findOne = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelector(selector)\n    }\n\n    const matches = find.call(this, selector)\n\n    if (typeof matches[0] !== 'undefined') {\n      return matches[0]\n    }\n\n    return null\n  }\n}\n\nexport {\n  find,\n  findOne,\n  defaultPreventedPreservedOnDispatch\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\nimport { defaultPreventedPreservedOnDispatch } from './polyfill'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst $ = getjQuery()\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n]\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent)\n    .forEach(handlerKey => {\n      if (handlerKey.indexOf(namespace) > -1) {\n        const event = storeElementEvent[handlerKey]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.charAt(0) === '.'\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events)\n        .forEach(elementEvent => {\n          removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n        })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent)\n      .forEach(keyHandlers => {\n        const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n        if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n          const event = storeElementEvent[keyHandlers]\n\n          removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n        }\n      })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom informations in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args)\n        .forEach(key => {\n          Object.defineProperty(evt, key, {\n            get() {\n              return args[key]\n            }\n          })\n        })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n\n      if (!defaultPreventedPreservedOnDispatch) {\n        Object.defineProperty(evt, 'defaultPrevented', {\n          get: () => true\n        })\n      }\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this)\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    let rootElement = this._element\n    if (element) {\n      rootElement = this._getRootElement(element)\n    }\n\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler\n      .one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .alert to jQuery only if jQuery is present\n */\n\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Alert.jQueryInterface\n  $.fn[NAME].Constructor = Alert\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Alert.jQueryInterface\n  }\n}\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .button to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Button.jQueryInterface\n  $.fn[NAME].Constructor = Button\n\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Button.jQueryInterface\n  }\n}\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {\n      ...element.dataset\n    }\n\n    Object.keys(attributes).forEach(key => {\n      attributes[key] = normalizeData(attributes[key])\n    })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  },\n\n  toggleClass(element, className) {\n    if (!element) {\n      return\n    }\n\n    if (element.classList.contains(className)) {\n      element.classList.remove(className)\n    } else {\n      element.classList.add(className)\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { find as findFn, findOne } from './polyfill'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...findFn.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return findOne.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler\n        .on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler\n        .on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler\n        .on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement &&\n      this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const nextElementInterval = parseInt(nextElement.getAttribute('data-interval'), 10)\n      if (nextElementInterval) {\n        this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n        this._config.interval = nextElementInterval\n      } else {\n        this._config.interval = this._config.defaultInterval || this._config.interval\n      }\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler\n        .one(activeElement, TRANSITION_END, () => {\n          nextElement.classList.remove(directionalClassName, orderClassName)\n          nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n          activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n          this._isSliding = false\n\n          setTimeout(() => {\n            EventHandler.trigger(this._element, EVENT_SLID, {\n              relatedTarget: nextElement,\n              direction: eventDirectionName,\n              from: activeElementIndex,\n              to: nextElementIndex\n            })\n          }, 0)\n        })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .carousel to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Carousel.jQueryInterface\n  $.fn[NAME].Constructor = Carousel\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Carousel.jQueryInterface\n  }\n}\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.filter(elem => container !== elem)\n      activesData = tempActiveData[0] ? Data.getData(tempActiveData[0], DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    const hasWidth = this._element.classList.contains(WIDTH)\n    return hasWidth ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (element) {\n      const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n      if (triggerArray.length) {\n        triggerArray.forEach(elem => {\n          if (isOpen) {\n            elem.classList.remove(CLASS_NAME_COLLAPSED)\n          } else {\n            elem.classList.add(CLASS_NAME_COLLAPSED)\n          }\n\n          elem.setAttribute('aria-expanded', isOpen)\n        })\n      }\n    }\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .collapse to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Collapse.jQueryInterface\n  $.fn[NAME].Constructor = Collapse\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Collapse.jQueryInterface\n  }\n}\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_NAVBAR = 'navbar'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        parent.classList.add(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    Manipulator.toggleClass(this._menu, CLASS_NAME_SHOW)\n    Manipulator.toggleClass(this._element, CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    Manipulator.toggleClass(this._menu, CLASS_NAME_SHOW)\n    Manipulator.toggleClass(this._element, CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._element, EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      placement = PLACEMENT_TOP\n      if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n        placement = PLACEMENT_TOPEND\n      }\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return Boolean(this._element.closest(`.${CLASS_NAME_NAVBAR}`))\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this._config.offset(data.offsets, this._element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON ||\n      (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent)\n      .filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.key === ARROW_UP_KEY && index > 0) { // Up\n      index--\n    }\n\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) { // Down\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .dropdown to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Dropdown.jQueryInterface\n  $.fn[NAME].Constructor = Dropdown\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Dropdown.jQueryInterface\n  }\n}\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n      if (hideEvent.defaultPrevented) {\n        return\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n      const modalTransitionDuration = getTransitionDurationFromElement(this._element)\n      EventHandler.one(this._element, TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n      })\n      emulateTransitionEnd(this._element, modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...typeof config === 'object' && config ? config : {}\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .modal to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Modal.jQueryInterface\n  $.fn[NAME].Constructor = Modal\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Modal.jQueryInterface\n  }\n}\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultWhitelist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, whiteList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const whitelistKeys = Object.keys(whiteList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (whitelistKeys.indexOf(elName) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const whitelistedAttributes = [].concat(whiteList['*'] || [], whiteList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, whitelistedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultWhitelist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'whiteList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  whiteList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"tooltip-arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  whiteList: DefaultWhitelist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.target, dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.target,\n          this._getDelegateConfig()\n        )\n        Data.setData(event.target, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    Data.removeData(this.element, this.constructor.DATA_KEY)\n\n    EventHandler.off(this.element, this.constructor.EVENT_KEY)\n    EventHandler.off(this.element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if (this.element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this.element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this.element)\n      const isInTheDom = shadowRoot === null ?\n        this.element.ownerDocument.documentElement.contains(this.element) :\n        shadowRoot.contains(this.element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this.element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this.element, this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        EventHandler.trigger(this.element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this.element, this.constructor.Event.HIDDEN)\n      this._popper.destroy()\n    }\n\n    const hideEvent = EventHandler.trigger(this.element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.whiteList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: `.${this.constructor.NAME}-arrow`\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this.config.offset(data.offsets, this.element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this.element,\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this.element,\n          eventIn,\n          this.config.selector,\n          event => this._enter(event)\n        )\n        EventHandler.on(this.element,\n          eventOut,\n          this.config.selector,\n          event => this._leave(event)\n        )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this.element.closest(`.${CLASS_NAME_MODAL}`),\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.target, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.target,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.target, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) ||\n        context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.target, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.target,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.target, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this.element)\n\n    Object.keys(dataAttributes)\n      .forEach(dataAttr => {\n        if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n          delete dataAttributes[dataAttr]\n        }\n      })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.whiteList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const popperInstance = popperData.instance\n    this.tip = popperInstance.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .tooltip to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Tooltip.jQueryInterface\n  $.fn[NAME].Constructor = Tooltip\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tooltip.jQueryInterface\n  }\n}\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  // Private\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Popover.jQueryInterface\n  $.fn[NAME].Constructor = Popover\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Popover.jQueryInterface\n  }\n}\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS},` +\n                          `${this._config.target} ${SELECTOR_LIST_ITEMS},` +\n                          `${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets\n      .map(element => {\n        let target\n        const targetSelector = getSelectorFromElement(element)\n\n        if (targetSelector) {\n          target = SelectorEngine.findOne(targetSelector)\n        }\n\n        if (target) {\n          const targetBCR = target.getBoundingClientRect()\n          if (targetBCR.width || targetBCR.height) {\n            return [\n              Manipulator[offsetMethod](target).top + offsetBase,\n              targetSelector\n            ]\n          }\n        }\n\n        return null\n      })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine\n        .findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine\n        .parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = ScrollSpy.jQueryInterface\n  $.fn[NAME].Constructor = ScrollSpy\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return ScrollSpy.jQueryInterface\n  }\n}\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n\n    Data.setData(this._element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented ||\n      (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback &&\n      (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .tab to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Tab.jQueryInterface\n  $.fn[NAME].Constructor = Tab\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tab.jQueryInterface\n  }\n}\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 500\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(\n      this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      () => this.hide()\n    )\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n *  add .toast to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Toast.jQueryInterface\n  $.fn[NAME].Constructor = Toast\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Toast.jQueryInterface\n  }\n}\n\nexport default Toast\n"]}