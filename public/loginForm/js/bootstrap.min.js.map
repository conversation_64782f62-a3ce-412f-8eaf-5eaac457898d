{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/polyfill.js", "../../js/src/dom/event-handler.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["storeData", "id", "e", "element", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "_window$getComputedSt", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "nodeType", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "call", "match", "toLowerCase", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "mapData", "set", "key", "data", "get", "keyProperties", "delete", "Data", "instance", "find", "Element", "prototype", "querySelectorAll", "findOne", "defaultPreventedPreservedOnDispatch", "CustomEvent", "cancelable", "createElement", "preventDefault", "defaultPrevented", "scopeSelectorRegex", "_", "this", "hasId", "Boolean", "nodeList", "replace", "removeAttribute", "matches", "$", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "handler", "delegationSelector", "uidEventList", "i", "len", "length", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "custom", "indexOf", "add<PERSON><PERSON><PERSON>", "oneOff", "_normalizeParams", "handlers", "previousFn", "fn", "dom<PERSON><PERSON>s", "target", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "on", "one", "_normalizeParams2", "inNamespace", "isNamespace", "char<PERSON>t", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "isNative", "bubbles", "nativeDispatch", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "defineProperty", "NAME", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "dispose", "closest", "_this", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "getInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "Number", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "getDataAttributes", "attributes", "_objectSpread2", "dataset", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "toggleClass", "className", "add", "SelectorEngine", "_ref", "documentElement", "concat", "findFn", "children", "_ref2", "filter", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "EVENT_KEY", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_this2", "_keydown", "_addTouchEventListeners", "_this3", "start", "pointerType", "clientX", "touches", "end", "clearTimeout", "itemImg", "move", "tagName", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "directionalClassName", "orderClassName", "_this4", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "nextElementInterval", "parseInt", "defaultInterval", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "SELECTOR_DATA_TOGGLE", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "REGEXP_KEYDOWN", "ARROW_UP_KEY", "flip", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "focus", "destroy", "update", "scheduleUpdate", "stopPropagation", "constructor", "_getPlacement", "parentDropdown", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "showEvent", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_this5", "_triggerBackdropTransition", "_this6", "_this7", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "callback", "_this8", "animate", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "_this9", "modalTransitionDuration", "isModalOverflowing", "scrollHeight", "clientHeight", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "_this10", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "_this11", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "*", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "whiteList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "whitelist<PERSON><PERSON>s", "elements", "_loop", "el", "el<PERSON>ame", "nodeName", "attributeList", "whitelistedAttributes", "attr", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "allowedAttribute", "innerHTML", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacement", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "DATA_KEY", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "findShadowRoot", "attachShadow", "getRootNode", "root", "ShadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "complete", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "behavior", "arrow", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "CLASS_PREFIX", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "tabClass", "map", "token", "tClass", "popperData", "popperInstance", "popper", "initConfigAnimation", "Popover", "_getContent", "method", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "SELECTOR_NAV_LINKS", "navItem", "node", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdown", "autohide", "Toast"], "mappings": ";;;;;osCAOA,ICOQA,EACFC,ECCEC,EAIAC,EFMFC,EAAS,SAAAC,GACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnBC,SAASC,eAAeL,IAEjC,OAAOA,GAGHM,EAAc,SAAAR,GAClB,IAAIS,EAAWT,EAAQU,aAAa,eAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAME,EAAWX,EAAQU,aAAa,QAEtCD,EAAWE,GAAyB,MAAbA,EAAmBA,EAASC,OAAS,KAG9D,OAAOH,GAGHI,EAAyB,SAAAb,GAC7B,IAAMS,EAAWD,EAAYR,GAE7B,OAAIS,GACKH,SAASQ,cAAcL,GAAYA,EAGrC,MAGHM,EAAyB,SAAAf,GAC7B,IAAMS,EAAWD,EAAYR,GAE7B,OAAOS,EAAWH,SAASQ,cAAcL,GAAY,MAGjDO,EAAmC,SAAAhB,GACvC,IAAKA,EACH,OAAO,EAFyC,IAAAiB,EAS9CC,OAAOC,iBAAiBnB,GAF1BoB,EAPgDH,EAOhDG,mBACAC,EARgDJ,EAQhDI,gBAGIC,EAA0BC,WAAWH,GACrCI,EAAuBD,WAAWF,GAGxC,OAAKC,GAA4BE,GAKjCJ,EAAqBA,EAAmBK,MAAM,KAAK,GACnDJ,EAAkBA,EAAgBI,MAAM,KAAK,GA3Ef,KA6EtBF,WAAWH,GAAsBG,WAAWF,KAP3C,GAULK,EAAuB,SAAA1B,GAC3BA,EAAQ2B,cAAc,IAAIC,MAhFL,mBAmFjBC,EAAY,SAAAC,GAAG,OAAKA,EAAI,IAAMA,GAAKC,UAEnCC,EAAuB,SAAChC,EAASiC,GACrC,IAAIC,GAAS,EAEPC,EAAmBF,EADD,EAOxBjC,EAAQoC,iBA9Fa,iBAyFrB,SAASC,IACPH,GAAS,EACTlC,EAAQsC,oBA3FW,gBA2FyBD,MAI9CE,YAAW,WACJL,GACHR,EAAqB1B,KAEtBmC,IAGCK,EAAkB,SAACC,EAAeC,EAAQC,GAC9CC,OAAOC,KAAKF,GACTG,SAAQ,SAAAC,GACP,IAtGSjB,EAsGHkB,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASpB,EAAUoB,GACnC,UAxGFnB,OADSA,EA0GAmB,GAxGX,GAAUnB,EAGL,GAAGqB,SAASC,KAAKtB,GAAKuB,MAAM,eAAe,GAAGC,cAuGjD,IAAK,IAAIC,OAAOP,GAAeQ,KAAKN,GAClC,MAAM,IAAIO,MACLhB,EAAciB,cAAdjB,aACQM,EADX,oBACuCG,EADpCT,wBAEmBO,EAFtB,UAOJW,EAAY,SAAA3D,GAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQ4D,OAAS5D,EAAQ6D,YAAc7D,EAAQ6D,WAAWD,MAAO,CACnE,IAAME,EAAe3C,iBAAiBnB,GAChC+D,EAAkB5C,iBAAiBnB,EAAQ6D,YAEjD,MAAgC,SAAzBC,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GA0BHC,EAAO,WAAA,OAAM,cAEbC,EAAS,SAAAnE,GAAO,OAAIA,EAAQoE,cAE5BC,EAAY,WAAM,IACdC,EAAWpD,OAAXoD,OAER,OAAIA,IAAWhE,SAASiE,KAAKC,aAAa,kBACjCF,EAGF,MCvKHG,GACE5E,EAAY,GACdC,EAAK,EACF,CACL4E,IADK,SACD1E,EAAS2E,EAAKC,QACW,IAAhB5E,EAAQ2E,MACjB3E,EAAQ2E,IAAM,CACZA,IAAAA,EACA7E,GAAAA,GAEFA,KAGFD,EAAUG,EAAQ2E,IAAI7E,IAAM8E,GAE9BC,IAZK,SAYD7E,EAAS2E,GACX,IAAK3E,QAAkC,IAAhBA,EAAQ2E,IAC7B,OAAO,KAGT,IAAMG,EAAgB9E,EAAQ2E,IAC9B,OAAIG,EAAcH,MAAQA,EACjB9E,EAAUiF,EAAchF,IAG1B,MAETiF,OAxBK,SAwBE/E,EAAS2E,GACd,QAA2B,IAAhB3E,EAAQ2E,IAAnB,CAIA,IAAMG,EAAgB9E,EAAQ2E,IAC1BG,EAAcH,MAAQA,WACjB9E,EAAUiF,EAAchF,WACxBE,EAAQ2E,SAMjBK,EAAO,SACHC,EAAUN,EAAKC,GACrBH,EAAQC,IAAIO,EAAUN,EAAKC,IAFzBI,EAAO,SAIHC,EAAUN,GAChB,OAAOF,EAAQI,IAAII,EAAUN,IAL3BK,EAAO,SAOAC,EAAUN,GACnBF,EAAQM,OAAOE,EAAUN,ICnDzBO,EAAOC,QAAQC,UAAUC,iBACzBC,EAAUH,QAAQC,UAAUtE,cAG1ByE,GACExF,EAAI,IAAIyF,YAAY,YAAa,CACrCC,YAAY,KAGRzF,EAAUM,SAASoF,cAAc,QAC/BtD,iBAAiB,aAAa,WAAA,OAAM,QAE5CrC,EAAE4F,iBACF3F,EAAQ2B,cAAc5B,GACfA,EAAE6F,kBAGLC,EAAqB,YACA,WACzB,IAAM7F,EAAUM,SAASoF,cAAc,OAEvC,IACE1F,EAAQqF,iBAAiB,YACzB,MAAOS,GACP,OAAO,EAGT,OAAO,GATkB,KAazBZ,EAAO,SAAUzE,GACf,IAAKoF,EAAmBrC,KAAK/C,GAC3B,OAAOsF,KAAKV,iBAAiB5E,GAG/B,IAAMuF,EAAQC,QAAQF,KAAKjG,IAEtBkG,IACHD,KAAKjG,GAAKG,EAAO,UAGnB,IAAIiG,EAAW,KACf,IACEzF,EAAWA,EAAS0F,QAAQN,EAAjB,IAAyCE,KAAKjG,IACzDoG,EAAWH,KAAKV,iBAAiB5E,GAFnC,QAIOuF,GACHD,KAAKK,gBAAgB,MAIzB,OAAOF,GAGTZ,EAAU,SAAU7E,GAClB,IAAKoF,EAAmBrC,KAAK/C,GAC3B,OAAOsF,KAAKjF,cAAcL,GAG5B,IAAM4F,EAAUnB,EAAK9B,KAAK2C,KAAMtF,GAEhC,YAA0B,IAAf4F,EAAQ,GACVA,EAAQ,GAGV,OC7DX,IAAMC,EAAIjC,IACJkC,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GAClBC,EAAW,EACTC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAe,CACnB,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,UASF,SAASC,EAAYhH,EAASiH,GAC5B,OAAQA,GAAUA,EAAP,KAAeN,KAAiB3G,EAAQ2G,UAAYA,IAGjE,SAASO,EAASlH,GAChB,IAAMiH,EAAMD,EAAYhH,GAKxB,OAHAA,EAAQ2G,SAAWM,EACnBP,EAAcO,GAAOP,EAAcO,IAAQ,GAEpCP,EAAcO,GAkCvB,SAASE,EAAYC,EAAQC,EAASC,QAA2B,IAA3BA,IAAAA,EAAqB,MAGzD,IAFA,IAAMC,EAAe3E,OAAOC,KAAKuE,GAExBI,EAAI,EAAGC,EAAMF,EAAaG,OAAQF,EAAIC,EAAKD,IAAK,CACvD,IAAMG,EAAQP,EAAOG,EAAaC,IAElC,GAAIG,EAAMC,kBAAoBP,GAAWM,EAAML,qBAAuBA,EACpE,OAAOK,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBT,EAASU,GACnD,IAAMC,EAAgC,iBAAZX,EACpBO,EAAkBI,EAAaD,EAAeV,EAGhDY,EAAYH,EAAkB3B,QAAQK,EAAgB,IACpD0B,EAAStB,EAAaqB,GAY5B,OAVIC,IACFD,EAAYC,GAGGnB,EAAaoB,QAAQF,IAAc,IAGlDA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,EAAWpI,EAAS8H,EAAmBT,EAASU,EAAcM,GACrE,GAAiC,iBAAtBP,GAAmC9H,EAA9C,CAIKqH,IACHA,EAAUU,EACVA,EAAe,MAP4D,IAAAO,EAU5BT,EAAgBC,EAAmBT,EAASU,GAAtFC,EAVsEM,EAAA,GAU1DV,EAV0DU,EAAA,GAUzCL,EAVyCK,EAAA,GAWvElB,EAASF,EAASlH,GAClBuI,EAAWnB,EAAOa,KAAeb,EAAOa,GAAa,IACrDO,EAAarB,EAAYoB,EAAUX,EAAiBI,EAAaX,EAAU,MAEjF,GAAImB,EACFA,EAAWH,OAASG,EAAWH,QAAUA,MAD3C,CAMA,IAAMpB,EAAMD,EAAYY,EAAiBE,EAAkB3B,QAAQI,EAAgB,KAC7EkC,EAAKT,EA9Eb,SAAoChI,EAASS,EAAUgI,GACrD,OAAO,SAASpB,EAAQM,GAGtB,IAFA,IAAMe,EAAc1I,EAAQqF,iBAAiB5E,GAElCkI,EAAWhB,EAAXgB,OAAkBA,GAAUA,IAAW5C,KAAM4C,EAASA,EAAO9E,WACtE,IAAK,IAAI2D,EAAIkB,EAAYhB,OAAQF,KAC/B,GAAIkB,EAAYlB,KAAOmB,EAKrB,OAJItB,EAAQgB,QACVO,EAAaC,IAAI7I,EAAS2H,EAAMmB,KAAML,GAGjCA,EAAGM,MAAMJ,EAAQ,CAAChB,IAM/B,OAAO,MA8DPqB,CAA2BhJ,EAASqH,EAASU,GAzFjD,SAA0B/H,EAASyI,GACjC,OAAO,SAASpB,EAAQM,GAKtB,OAJIN,EAAQgB,QACVO,EAAaC,IAAI7I,EAAS2H,EAAMmB,KAAML,GAGjCA,EAAGM,MAAM/I,EAAS,CAAC2H,KAoF1BsB,CAAiBjJ,EAASqH,GAE5BoB,EAAGnB,mBAAqBU,EAAaX,EAAU,KAC/CoB,EAAGb,gBAAkBA,EACrBa,EAAGJ,OAASA,EACZI,EAAG9B,SAAWM,EACdsB,EAAStB,GAAOwB,EAEhBzI,EAAQoC,iBAAiB6F,EAAWQ,EAAIT,KAG1C,SAASkB,EAAclJ,EAASoH,EAAQa,EAAWZ,EAASC,GAC1D,IAAMmB,EAAKtB,EAAYC,EAAOa,GAAYZ,EAASC,GAE9CmB,IAILzI,EAAQsC,oBAAoB2F,EAAWQ,EAAIxC,QAAQqB,WAC5CF,EAAOa,GAAWQ,EAAG9B,WAgB9B,IAAMiC,EAAe,CACnBO,GADmB,SAChBnJ,EAAS2H,EAAON,EAASU,GAC1BK,EAAWpI,EAAS2H,EAAON,EAASU,GAAc,IAGpDqB,IALmB,SAKfpJ,EAAS2H,EAAON,EAASU,GAC3BK,EAAWpI,EAAS2H,EAAON,EAASU,GAAc,IAGpDc,IATmB,SASf7I,EAAS8H,EAAmBT,EAASU,GACvC,GAAiC,iBAAtBD,GAAmC9H,EAA9C,CADqD,IAAAqJ,EAKJxB,EAAgBC,EAAmBT,EAASU,GAAtFC,EAL8CqB,EAAA,GAKlCzB,EALkCyB,EAAA,GAKjBpB,EALiBoB,EAAA,GAM/CC,EAAcrB,IAAcH,EAC5BV,EAASF,EAASlH,GAClBuJ,EAA8C,MAAhCzB,EAAkB0B,OAAO,GAE7C,QAA+B,IAApB5B,EAAX,CAUI2B,GACF3G,OAAOC,KAAKuE,GACTtE,SAAQ,SAAA2G,IA5CjB,SAAkCzJ,EAASoH,EAAQa,EAAWyB,GAC5D,IAAMC,EAAoBvC,EAAOa,IAAc,GAE/CrF,OAAOC,KAAK8G,GACT7G,SAAQ,SAAA8G,GACP,GAAIA,EAAWzB,QAAQuB,IAAc,EAAG,CACtC,IAAM/B,EAAQgC,EAAkBC,GAEhCV,EAAclJ,EAASoH,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,wBAqCrEuC,CAAyB7J,EAASoH,EAAQqC,EAAc3B,EAAkBgC,MAAM,OAItF,IAAMH,EAAoBvC,EAAOa,IAAc,GAC/CrF,OAAOC,KAAK8G,GACT7G,SAAQ,SAAAiH,GACP,IAAMH,EAAaG,EAAY5D,QAAQM,EAAe,IAEtD,IAAK6C,GAAexB,EAAkBK,QAAQyB,IAAe,EAAG,CAC9D,IAAMjC,EAAQgC,EAAkBI,GAEhCb,EAAclJ,EAASoH,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,4BAzB7E,CAEE,IAAKF,IAAWA,EAAOa,GACrB,OAGFiB,EAAclJ,EAASoH,EAAQa,EAAWL,EAAiBI,EAAaX,EAAU,SAwBtF2C,QAjDmB,SAiDXhK,EAAS2H,EAAOsC,GACtB,GAAqB,iBAAVtC,IAAuB3H,EAChC,OAAO,KAGT,IAIIkK,EAJEjC,EAAYN,EAAMxB,QAAQK,EAAgB,IAC1C8C,EAAc3B,IAAUM,EACxBkC,EAAWpD,EAAaoB,QAAQF,IAAc,EAGhDmC,GAAU,EACVC,GAAiB,EACjBzE,GAAmB,EACnB0E,EAAM,KAmDV,OAjDIhB,GAAehD,IACjB4D,EAAc5D,EAAE1E,MAAM+F,EAAOsC,GAE7B3D,EAAEtG,GAASgK,QAAQE,GACnBE,GAAWF,EAAYK,uBACvBF,GAAkBH,EAAYM,gCAC9B5E,EAAmBsE,EAAYO,sBAG7BN,GACFG,EAAMhK,SAASoK,YAAY,eACvBC,UAAU1C,EAAWmC,GAAS,GAElCE,EAAM,IAAI9E,YAAYmC,EAAO,CAC3ByC,QAAAA,EACA3E,YAAY,SAKI,IAATwE,GACTrH,OAAOC,KAAKoH,GACTnH,SAAQ,SAAA6B,GACP/B,OAAOgI,eAAeN,EAAK3F,EAAK,CAC9BE,IAD8B,WAE5B,OAAOoF,EAAKtF,SAMlBiB,IACF0E,EAAI3E,iBAECJ,GACH3C,OAAOgI,eAAeN,EAAK,mBAAoB,CAC7CzF,IAAK,WAAA,OAAM,MAKbwF,GACFrK,EAAQ2B,cAAc2I,GAGpBA,EAAI1E,uBAA2C,IAAhBsE,GACjCA,EAAYvE,iBAGP2E,ICrTLO,EAAO,QAsBPC,EAAAA,WACJ,SAAAA,EAAY9K,GACV+F,KAAKgF,SAAW/K,EAEZ+F,KAAKgF,UACP/F,EAAahF,EAzBF,WAyBqB+F,iCAYpCiF,MAAA,SAAMhL,GACJ,IAAIiL,EAAclF,KAAKgF,SACnB/K,IACFiL,EAAclF,KAAKmF,gBAAgBlL,IAGrC,IAAMmL,EAAcpF,KAAKqF,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAYvF,kBAIxCG,KAAKsF,eAAeJ,MAGtBK,QAAA,WACEtG,EAAgBe,KAAKgF,SArDR,YAsDbhF,KAAKgF,SAAW,QAKlBG,gBAAA,SAAgBlL,GACd,OAAOe,EAAuBf,IAAYA,EAAQuL,QAAR,aAG5CH,mBAAA,SAAmBpL,GACjB,OAAO4I,EAAaoB,QAAQhK,EA1Df,qBA6DfqL,eAAA,SAAerL,GAAS,IAAAwL,EAAAzF,KAGtB,GAFA/F,EAAQyL,UAAUC,OAxDC,QA0Dd1L,EAAQyL,UAAUE,SA3DJ,QA2DnB,CAKA,IAAMvK,EAAqBJ,EAAiChB,GAE5D4I,EACGQ,IAAIpJ,EJ9FY,iBI8Fa,WAAA,OAAMwL,EAAKI,gBAAgB5L,MAC3DgC,EAAqBhC,EAASoB,QAR5B2E,KAAK6F,gBAAgB5L,MAWzB4L,gBAAA,SAAgB5L,GACVA,EAAQ6D,YACV7D,EAAQ6D,WAAWgI,YAAY7L,GAGjC4I,EAAaoB,QAAQhK,EAhFP,sBAqFT8L,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KA9Fb,YAgGNnB,IACHA,EAAO,IAAIkG,EAAM/E,OAGJ,UAAXrD,GACFkC,EAAKlC,GAAQqD,YAKZiG,cAAP,SAAqBC,GACnB,OAAO,SAAUtE,GACXA,GACFA,EAAMhC,iBAGRsG,EAAcjB,MAAMjF,UAIjBmG,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EArHP,qDAgCb,MAjCY,qBAqBV8K,GA0GNlC,EACGO,GAAG7I,SAvHoB,0BAJD,yBA2H+BwK,EAAMkB,cAAc,IAAIlB,IAEhF,IAAMxE,EAAIjC,IAUV,GAAIiC,EAAG,CACL,IAAM6F,EAAqB7F,EAAEmC,GAAGoC,GAChCvE,EAAEmC,GAAGoC,GAAQC,EAAMgB,gBACnBxF,EAAEmC,GAAGoC,GAAMuB,YAActB,EACzBxE,EAAEmC,GAAGoC,GAAMwB,WAAa,WAEtB,OADA/F,EAAEmC,GAAGoC,GAAQsB,EACNrB,EAAMgB,iBCzJjB,IAkBMQ,EAAAA,WACJ,SAAAA,EAAYtM,GACV+F,KAAKgF,SAAW/K,EAChBgF,EAAahF,EAnBA,YAmBmB+F,iCAWlCwG,OAAA,WAEExG,KAAKgF,SAASyB,aAAa,eAAgBzG,KAAKgF,SAASU,UAAUc,OA5B7C,cA+BxBjB,QAAA,WACEtG,EAAgBe,KAAKgF,SApCR,aAqCbhF,KAAKgF,SAAW,QAKXe,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KA5Cb,aA8CNnB,IACHA,EAAO,IAAI0H,EAAOvG,OAGL,WAAXrD,GACFkC,EAAKlC,WAKJwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EAzDP,sDAyBb,MA1BY,qBAiBVsM,GAmDN1D,EAAaO,GAAG7I,SA3DU,2BAFG,0BA6DyC,SAAAqH,GACpEA,EAAMhC,iBAEN,IAAM8G,EAAS9E,EAAMgB,OAAO4C,QAhED,0BAkEvB3G,EAAOI,EAAayH,EAxET,aAyEV7H,IACHA,EAAO,IAAI0H,EAAOG,IAGpB7H,EAAK2H,YAGP,IAAMjG,EAAIjC,IASV,GAAIiC,EAAG,CACL,IAAM6F,EAAqB7F,EAAEmC,GAAF,OAC3BnC,EAAEmC,GAAF,OAAa6D,EAAOR,gBACpBxF,EAAEmC,GAAF,OAAW2D,YAAcE,EAEzBhG,EAAEmC,GAAF,OAAW4D,WAAa,WAEtB,OADA/F,EAAEmC,GAAF,OAAa0D,EACNG,EAAOR,iBC5GlB,SAASY,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQC,OAAOD,GAAKxJ,WACfyJ,OAAOD,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASE,GAAiBlI,GACxB,OAAOA,EAAIwB,QAAQ,UAAU,SAAA2G,GAAG,MAAA,IAAQA,EAAIxJ,iBAG9C,IAAMyJ,GAAc,CAClBC,iBADkB,SACDhN,EAAS2E,EAAK1B,GAC7BjD,EAAQwM,aAAR,QAA6BK,GAAiBlI,GAAQ1B,IAGxDgK,oBALkB,SAKEjN,EAAS2E,GAC3B3E,EAAQoG,gBAAR,QAAgCyG,GAAiBlI,KAGnDuI,kBATkB,SASAlN,GAChB,IAAKA,EACH,MAAO,GAGT,IAAMmN,EAAUC,EAAA,GACXpN,EAAQqN,SAOb,OAJAzK,OAAOC,KAAKsK,GAAYrK,SAAQ,SAAA6B,GAC9BwI,EAAWxI,GAAO+H,EAAcS,EAAWxI,OAGtCwI,GAGTG,iBAzBkB,SAyBDtN,EAAS2E,GACxB,OAAO+H,EAAc1M,EAAQU,aAAR,QAA6BmM,GAAiBlI,MAGrE4I,OA7BkB,SA6BXvN,GACL,IAAMwN,EAAOxN,EAAQyN,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAMpN,SAASiE,KAAKoJ,UAC9BC,KAAMJ,EAAKI,KAAOtN,SAASiE,KAAKsJ,aAIpCC,SAtCkB,SAsCT9N,GACP,MAAO,CACL0N,IAAK1N,EAAQ+N,UACbH,KAAM5N,EAAQgO,aAIlBC,YA7CkB,SA6CNjO,EAASkO,GACdlO,IAIDA,EAAQyL,UAAUE,SAASuC,GAC7BlO,EAAQyL,UAAUC,OAAOwC,GAEzBlO,EAAQyL,UAAU0C,IAAID,MCnEtBE,GAAiB,CACrB/H,QADqB,SACbrG,EAASS,GACf,OAAOT,EAAQqG,QAAQ5F,IAGzByE,KALqB,SAKhBzE,EAAUT,GAAoC,IAAAqO,EACjD,YADiD,IAApCrO,IAAAA,EAAUM,SAASgO,kBACzBD,EAAA,IAAGE,OAAHxF,MAAAsF,EAAaG,EAAOpL,KAAKpD,EAASS,KAG3C6E,QATqB,SASb7E,EAAUT,GAChB,YADoD,IAApCA,IAAAA,EAAUM,SAASgO,iBAC5BhJ,EAAQlC,KAAKpD,EAASS,IAG/BgO,SAbqB,SAaZzO,EAASS,GAAU,IAAAiO,EACpBD,GAAWC,EAAA,IAAGH,OAAHxF,MAAA2F,EAAa1O,EAAQyO,UAEtC,OAAOA,EAASE,QAAO,SAAAC,GAAK,OAAIA,EAAMvI,QAAQ5F,OAGhDoO,QAnBqB,SAmBb7O,EAASS,GAKf,IAJA,IAAMoO,EAAU,GAEZC,EAAW9O,EAAQ6D,WAEhBiL,GAAYA,EAAS/M,WAAagN,KAAKC,cA1BhC,IA0BgDF,EAAS/M,UACjEgE,KAAKM,QAAQyI,EAAUrO,IACzBoO,EAAQI,KAAKH,GAGfA,EAAWA,EAASjL,WAGtB,OAAOgL,GAGTK,KAnCqB,SAmChBlP,EAASS,GAGZ,IAFA,IAAI0O,EAAWnP,EAAQoP,uBAEhBD,GAAU,CACf,GAAIA,EAAS9I,QAAQ5F,GACnB,MAAO,CAAC0O,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAjDqB,SAiDhBrP,EAASS,GAGZ,IAFA,IAAI4O,EAAOrP,EAAQsP,mBAEZD,GAAM,CACX,GAAItJ,KAAKM,QAAQgJ,EAAM5O,GACrB,MAAO,CAAC4O,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KChDLzE,GAAO,WAGP0E,GAAS,eAQTC,GAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,GAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAwCHE,GAAc,CAClBC,MAAO,QACPC,IAAK,OAQDC,GAAAA,WACJ,SAAAA,EAAYnQ,EAAS0C,GACnBqD,KAAKqK,OAAS,KACdrK,KAAKsK,UAAY,KACjBtK,KAAKuK,eAAiB,KACtBvK,KAAKwK,WAAY,EACjBxK,KAAKyK,YAAa,EAClBzK,KAAK0K,aAAe,KACpB1K,KAAK2K,YAAc,EACnB3K,KAAK4K,YAAc,EAEnB5K,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAKgF,SAAW/K,EAChB+F,KAAK+K,mBAAqB1C,GAAe9I,QA3BjB,uBA2B8CS,KAAKgF,UAC3EhF,KAAKgL,gBAAkB,iBAAkBzQ,SAASgO,iBAAmB0C,UAAUC,eAAiB,EAChGlL,KAAKmL,cAAgBjL,QAAQ/E,OAAOiQ,cAEpCpL,KAAKqL,qBACLpM,EAAahF,EA5FA,cA4FmB+F,iCAelCsJ,KAAA,WACOtJ,KAAKyK,YACRzK,KAAKsL,OAlFY,WAsFrBC,gBAAA,YAGOhR,SAASiR,QAAU5N,EAAUoC,KAAKgF,WACrChF,KAAKsJ,UAITH,KAAA,WACOnJ,KAAKyK,YACRzK,KAAKsL,OA/FY,WAmGrBzB,MAAA,SAAMjI,GACCA,IACH5B,KAAKwK,WAAY,GAGfnC,GAAe9I,QAzEI,2CAyEwBS,KAAKgF,YAClDrJ,EAAqBqE,KAAKgF,UAC1BhF,KAAKyL,OAAM,IAGbC,cAAc1L,KAAKsK,WACnBtK,KAAKsK,UAAY,QAGnBmB,MAAA,SAAM7J,GACCA,IACH5B,KAAKwK,WAAY,GAGfxK,KAAKsK,YACPoB,cAAc1L,KAAKsK,WACnBtK,KAAKsK,UAAY,MAGftK,KAAK6K,SAAW7K,KAAK6K,QAAQnB,WAAa1J,KAAKwK,YACjDxK,KAAKsK,UAAYqB,aACdpR,SAASqR,gBAAkB5L,KAAKuL,gBAAkBvL,KAAKsJ,MAAMuC,KAAK7L,MACnEA,KAAK6K,QAAQnB,cAKnBoC,GAAA,SAAGC,GAAO,IAAAtG,EAAAzF,KACRA,KAAKuK,eAAiBlC,GAAe9I,QAxGZ,wBAwG0CS,KAAKgF,UACxE,IAAMgH,EAAchM,KAAKiM,cAAcjM,KAAKuK,gBAE5C,KAAIwB,EAAQ/L,KAAKqK,OAAO1I,OAAS,GAAKoK,EAAQ,GAI9C,GAAI/L,KAAKyK,WACP5H,EAAaQ,IAAIrD,KAAKgF,SAvIZ,oBAuIkC,WAAA,OAAMS,EAAKqG,GAAGC,UAD5D,CAKA,GAAIC,IAAgBD,EAGlB,OAFA/L,KAAK6J,aACL7J,KAAKyL,QAIP,IAAMS,EAAYH,EAAQC,EAvJP,OACA,OA0JnBhM,KAAKsL,OAAOY,EAAWlM,KAAKqK,OAAO0B,QAGrCxG,QAAA,WACE1C,EAAaC,IAAI9C,KAAKgF,SAAUwE,IAChCvK,EAAgBe,KAAKgF,SA3LR,eA6LbhF,KAAKqK,OAAS,KACdrK,KAAK6K,QAAU,KACf7K,KAAKgF,SAAW,KAChBhF,KAAKsK,UAAY,KACjBtK,KAAKwK,UAAY,KACjBxK,KAAKyK,WAAa,KAClBzK,KAAKuK,eAAiB,KACtBvK,KAAK+K,mBAAqB,QAK5BD,WAAA,SAAWnO,GAMT,OALAA,EAAM0K,EAAAA,EAAA,GACDoC,IACA9M,GAELF,EAAgBqI,GAAMnI,EAAQqN,IACvBrN,KAGTwP,aAAA,WACE,IAAMC,EAAYhS,KAAKiS,IAAIrM,KAAK4K,aAEhC,KAAIwB,GA9MgB,IA8MpB,CAIA,IAAMF,EAAYE,EAAYpM,KAAK4K,YAEnC5K,KAAK4K,YAAc,EAGfsB,EAAY,GACdlM,KAAKmJ,OAIH+C,EAAY,GACdlM,KAAKsJ,WAIT+B,mBAAA,WAAqB,IAAAiB,EAAAtM,KACfA,KAAK6K,QAAQlB,UACf9G,EACGO,GAAGpD,KAAKgF,SAzME,uBAyMuB,SAAApD,GAAK,OAAI0K,EAAKC,SAAS3K,MAGlC,UAAvB5B,KAAK6K,QAAQhB,QACfhH,EACGO,GAAGpD,KAAKgF,SA7MK,0BA6MuB,SAAApD,GAAK,OAAI0K,EAAKzC,MAAMjI,MAC3DiB,EACGO,GAAGpD,KAAKgF,SA9MK,0BA8MuB,SAAApD,GAAK,OAAI0K,EAAKb,MAAM7J,OAGzD5B,KAAK6K,QAAQd,OAAS/J,KAAKgL,iBAC7BhL,KAAKwM,6BAITA,wBAAA,WAA0B,IAAAC,EAAAzM,KAClB0M,EAAQ,SAAA9K,GACR6K,EAAKtB,eAAiBlB,GAAYrI,EAAM+K,YAAYhP,eACtD8O,EAAK9B,YAAc/I,EAAMgL,QACfH,EAAKtB,gBACfsB,EAAK9B,YAAc/I,EAAMiL,QAAQ,GAAGD,UAalCE,EAAM,SAAAlL,GACN6K,EAAKtB,eAAiBlB,GAAYrI,EAAM+K,YAAYhP,iBACtD8O,EAAK7B,YAAchJ,EAAMgL,QAAUH,EAAK9B,aAG1C8B,EAAKN,eACsB,UAAvBM,EAAK5B,QAAQhB,QASf4C,EAAK5C,QACD4C,EAAK/B,cACPqC,aAAaN,EAAK/B,cAGpB+B,EAAK/B,aAAelO,YAAW,SAAAoF,GAAK,OAAI6K,EAAKhB,MAAM7J,KA1R5B,IA0R6D6K,EAAK5B,QAAQnB,YAIrGrB,GAAelJ,KA1OO,qBA0OiBa,KAAKgF,UAAUjI,SAAQ,SAAAiQ,GAC5DnK,EAAaO,GAAG4J,EA3PA,yBA2P2B,SAAAhT,GAAC,OAAIA,EAAE4F,uBAGhDI,KAAKmL,eACPtI,EAAaO,GAAGpD,KAAKgF,SAjQJ,2BAiQiC,SAAApD,GAAK,OAAI8K,EAAM9K,MACjEiB,EAAaO,GAAGpD,KAAKgF,SAjQN,yBAiQiC,SAAApD,GAAK,OAAIkL,EAAIlL,MAE7D5B,KAAKgF,SAASU,UAAU0C,IAvPG,mBAyP3BvF,EAAaO,GAAGpD,KAAKgF,SAzQL,0BAyQiC,SAAApD,GAAK,OAAI8K,EAAM9K,MAChEiB,EAAaO,GAAGpD,KAAKgF,SAzQN,yBAyQiC,SAAApD,GAAK,OA5C1C,SAAAA,GAEPA,EAAMiL,SAAWjL,EAAMiL,QAAQlL,OAAS,EAC1C8K,EAAK7B,YAAc,EAEnB6B,EAAK7B,YAAchJ,EAAMiL,QAAQ,GAAGD,QAAUH,EAAK9B,YAuCIsC,CAAKrL,MAC9DiB,EAAaO,GAAGpD,KAAKgF,SAzQP,wBAyQiC,SAAApD,GAAK,OAAIkL,EAAIlL,UAIhE2K,SAAA,SAAS3K,GACP,IAAI,kBAAkBnE,KAAKmE,EAAMgB,OAAOsK,SAIxC,OAAQtL,EAAMhD,KACZ,IAtTiB,YAuTfgD,EAAMhC,iBACNI,KAAKmJ,OACL,MACF,IAzTkB,aA0ThBvH,EAAMhC,iBACNI,KAAKsJ,WAMX2C,cAAA,SAAchS,GAKZ,OAJA+F,KAAKqK,OAASpQ,GAAWA,EAAQ6D,WAC/BuK,GAAelJ,KA/QC,iBA+QmBlF,EAAQ6D,YAC3C,GAEKkC,KAAKqK,OAAOjI,QAAQnI,MAG7BkT,oBAAA,SAAoBjB,EAAWkB,GAC7B,IAAMC,EApTa,SAoTKnB,EAClBoB,EApTa,SAoTKpB,EAClBF,EAAchM,KAAKiM,cAAcmB,GACjCG,EAAgBvN,KAAKqK,OAAO1I,OAAS,EAI3C,IAHuB2L,GAAmC,IAAhBtB,GACjBqB,GAAmBrB,IAAgBuB,KAEtCvN,KAAK6K,QAAQf,KACjC,OAAOsD,EAGT,IACMI,GAAaxB,GA/TA,SA8TLE,GAAgC,EAAI,IACRlM,KAAKqK,OAAO1I,OAEtD,OAAsB,IAAf6L,EACLxN,KAAKqK,OAAOrK,KAAKqK,OAAO1I,OAAS,GACjC3B,KAAKqK,OAAOmD,MAGhBC,mBAAA,SAAmBC,EAAeC,GAChC,IAAMC,EAAc5N,KAAKiM,cAAcyB,GACjCG,EAAY7N,KAAKiM,cAAc5D,GAAe9I,QA5S3B,wBA4SyDS,KAAKgF,WAEvF,OAAOnC,EAAaoB,QAAQjE,KAAKgF,SAtUpB,oBAsU2C,CACtD0I,cAAAA,EACAxB,UAAWyB,EACXG,KAAMD,EACN/B,GAAI8B,OAIRG,2BAAA,SAA2B9T,GACzB,GAAI+F,KAAK+K,mBAAoB,CAE3B,IADA,IAAMiD,EAAa3F,GAAelJ,KAzThB,UAyTsCa,KAAK+K,oBACpDtJ,EAAI,EAAGA,EAAIuM,EAAWrM,OAAQF,IACrCuM,EAAWvM,GAAGiE,UAAUC,OAnUN,UAsUpB,IAAMsI,EAAgBjO,KAAK+K,mBAAmBrC,SAC5C1I,KAAKiM,cAAchS,IAGjBgU,GACFA,EAAcvI,UAAU0C,IA3UN,cAgVxBkD,OAAA,SAAOY,EAAWjS,GAAS,IASrBiU,EACAC,EACAR,EAXqBS,EAAApO,KACnBoN,EAAgB/E,GAAe9I,QAxUZ,wBAwU0CS,KAAKgF,UAClEqJ,EAAqBrO,KAAKiM,cAAcmB,GACxCkB,EAAcrU,GAAYmT,GAC9BpN,KAAKmN,oBAAoBjB,EAAWkB,GAEhCmB,EAAmBvO,KAAKiM,cAAcqC,GACtCE,EAAYtO,QAAQF,KAAKsK,WAgB/B,GA3XmB,SAiXf4B,GACFgC,EA3VkB,qBA4VlBC,EA3VkB,qBA4VlBR,EAlXiB,SAoXjBO,EAhWmB,sBAiWnBC,EA9VkB,qBA+VlBR,EArXkB,SAwXhBW,GAAeA,EAAY5I,UAAUE,SAvWnB,UAwWpB5F,KAAKyK,YAAa,OAKpB,IADmBzK,KAAKyN,mBAAmBa,EAAaX,GACzC9N,kBAIVuN,GAAkBkB,EAAvB,CAaA,GARAtO,KAAKyK,YAAa,EAEd+D,GACFxO,KAAK6J,QAGP7J,KAAK+N,2BAA2BO,GAE5BtO,KAAKgF,SAASU,UAAUE,SA7XP,SA6XmC,CACtD0I,EAAY5I,UAAU0C,IAAI+F,GAE1B/P,EAAOkQ,GAEPlB,EAAc1H,UAAU0C,IAAI8F,GAC5BI,EAAY5I,UAAU0C,IAAI8F,GAE1B,IAAMO,EAAsBC,SAASJ,EAAY3T,aAAa,iBAAkB,IAC5E8T,GACFzO,KAAK6K,QAAQ8D,gBAAkB3O,KAAK6K,QAAQ8D,iBAAmB3O,KAAK6K,QAAQnB,SAC5E1J,KAAK6K,QAAQnB,SAAW+E,GAExBzO,KAAK6K,QAAQnB,SAAW1J,KAAK6K,QAAQ8D,iBAAmB3O,KAAK6K,QAAQnB,SAGvE,IAAMrO,EAAqBJ,EAAiCmS,GAE5DvK,EACGQ,IAAI+J,ERtdU,iBQsdqB,WAClCkB,EAAY5I,UAAUC,OAAOuI,EAAsBC,GACnDG,EAAY5I,UAAU0C,IAnZN,UAqZhBgF,EAAc1H,UAAUC,OArZR,SAqZkCwI,EAAgBD,GAElEE,EAAK3D,YAAa,EAElBjO,YAAW,WACTqG,EAAaoB,QAAQmK,EAAKpJ,SAxatB,mBAwa4C,CAC9C0I,cAAeY,EACfpC,UAAWyB,EACXG,KAAMO,EACNvC,GAAIyC,MAEL,MAGPtS,EAAqBmR,EAAe/R,QAEpC+R,EAAc1H,UAAUC,OAraJ,UAsapB2I,EAAY5I,UAAU0C,IAtaF,UAwapBpI,KAAKyK,YAAa,EAClB5H,EAAaoB,QAAQjE,KAAKgF,SAvbhB,mBAubsC,CAC9C0I,cAAeY,EACfpC,UAAWyB,EACXG,KAAMO,EACNvC,GAAIyC,IAIJC,GACFxO,KAAKyL,YAMFmD,kBAAP,SAAyB3U,EAAS0C,GAChC,IAAIkC,EAAOI,EAAahF,EAxeX,eAyeT4Q,EAAOxD,EAAAA,EAAA,GACNoC,IACAzC,GAAYG,kBAAkBlN,IAGb,iBAAX0C,IACTkO,EAAOxD,EAAAA,EAAA,GACFwD,GACAlO,IAIP,IAAMkS,EAA2B,iBAAXlS,EAAsBA,EAASkO,EAAQjB,MAM7D,GAJK/K,IACHA,EAAO,IAAIuL,EAASnQ,EAAS4Q,IAGT,iBAAXlO,EACTkC,EAAKiN,GAAGnP,QACH,GAAsB,iBAAXkS,EAAqB,CACrC,QAA4B,IAAjBhQ,EAAKgQ,GACd,MAAM,IAAIC,UAAJ,oBAAkCD,EAAlC,KAGRhQ,EAAKgQ,UACIhE,EAAQnB,UAAYmB,EAAQkE,OACrClQ,EAAKgL,QACLhL,EAAK4M,YAIF1F,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACfoE,EAASwE,kBAAkB5O,KAAMrD,SAI9BqS,oBAAP,SAA2BpN,GACzB,IAAMgB,EAAS5H,EAAuBgF,MAEtC,GAAK4C,GAAWA,EAAO8C,UAAUE,SApeT,YAoexB,CAIA,IAAMjJ,EAAM0K,EAAAA,EAAA,GACPL,GAAYG,kBAAkBvE,IAC9BoE,GAAYG,kBAAkBnH,OAE7BiP,EAAajP,KAAKrF,aAAa,iBAEjCsU,IACFtS,EAAO+M,UAAW,GAGpBU,EAASwE,kBAAkBhM,EAAQjG,GAE/BsS,GACFhQ,EAAa2D,EAniBF,eAmiBoBkJ,GAAGmD,GAGpCrN,EAAMhC,qBAGDuG,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EA1iBP,wDAkGb,MAnGY,+CAuGZ,OAAOwP,SA5BLW,GA0eNvH,EACGO,GAAG7I,SAzgBoB,6BAiBE,gCAwf+B6P,GAAS4E,qBAEpEnM,EAAaO,GAAGjI,OA5gBS,6BA4gBoB,WAG3C,IAFA,IAAM+T,EAAY7G,GAAelJ,KA1fR,0BA4fhBsC,EAAI,EAAGC,EAAMwN,EAAUvN,OAAQF,EAAIC,EAAKD,IAC/C2I,GAASwE,kBAAkBM,EAAUzN,GAAIxC,EAAaiQ,EAAUzN,GA3jBnD,mBA+jBjB,IAAMlB,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQsF,GAASrE,gBACtBxF,GAAEmC,GAAGoC,IAAMuB,YAAc+D,GACzB7J,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACNgE,GAASrE,iBChlBpB,IAAMjB,GAAO,WAMP2E,GAAU,CACdjD,QAAQ,EACR2I,OAAQ,IAGJnF,GAAc,CAClBxD,OAAQ,UACR2I,OAAQ,oBA0BJC,GAAAA,WACJ,SAAAA,EAAYnV,EAAS0C,GACnBqD,KAAKqP,kBAAmB,EACxBrP,KAAKgF,SAAW/K,EAChB+F,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAKsP,cAAgBjH,GAAelJ,KAC/BoQ,mCAA+BtV,EAAQF,GAAvCwV,6CACsCtV,EAAQF,GADjD,MAMF,IAFA,IAAMyV,EAAanH,GAAelJ,KAlBT,4BAoBhBsC,EAAI,EAAGC,EAAM8N,EAAW7N,OAAQF,EAAIC,EAAKD,IAAK,CACrD,IAAMgO,EAAOD,EAAW/N,GAClB/G,EAAWI,EAAuB2U,GAClCC,EAAgBrH,GAAelJ,KAAKzE,GACvCkO,QAAO,SAAA+G,GAAS,OAAIA,IAAc1V,KAEpB,OAAbS,GAAqBgV,EAAc/N,SACrC3B,KAAK4P,UAAYlV,EACjBsF,KAAKsP,cAAcpG,KAAKuG,IAI5BzP,KAAK6P,QAAU7P,KAAK6K,QAAQsE,OAASnP,KAAK8P,aAAe,KAEpD9P,KAAK6K,QAAQsE,QAChBnP,KAAK+P,0BAA0B/P,KAAKgF,SAAUhF,KAAKsP,eAGjDtP,KAAK6K,QAAQrE,QACfxG,KAAKwG,SAGPvH,EAAahF,EAvEA,cAuEmB+F,iCAelCwG,OAAA,WACMxG,KAAKgF,SAASU,UAAUE,SAnER,QAoElB5F,KAAKgQ,OAELhQ,KAAKiQ,UAITA,KAAA,WAAO,IAAAxK,EAAAzF,KACL,IAAIA,KAAKqP,mBACPrP,KAAKgF,SAASU,UAAUE,SA5EN,QA2EpB,CAKA,IAAIsK,EACAC,EAEAnQ,KAAK6P,SAUgB,KATvBK,EAAU7H,GAAelJ,KA5EN,qBA4E6Ba,KAAK6P,SAClDjH,QAAO,SAAA6G,GACN,MAAmC,iBAAxBhK,EAAKoF,QAAQsE,OACfM,EAAK9U,aAAa,iBAAmB8K,EAAKoF,QAAQsE,OAGpDM,EAAK/J,UAAUE,SAzFJ,gBA4FVjE,SACVuO,EAAU,MAId,IAAME,EAAY/H,GAAe9I,QAAQS,KAAK4P,WAC9C,GAAIM,EAAS,CACX,IAAMG,EAAiBH,EAAQtH,QAAO,SAAA6G,GAAI,OAAIW,IAAcX,KAG5D,IAFAU,EAAcE,EAAe,GAAKpR,EAAaoR,EAAe,GAzHnD,eAyHmE,OAE3DF,EAAYd,iBAC7B,OAKJ,IADmBxM,EAAaoB,QAAQjE,KAAKgF,SAlHjC,oBAmHGnF,iBAAf,CAIIqQ,GACFA,EAAQnT,SAAQ,SAAAuT,GACVF,IAAcE,GAChBlB,EAASmB,kBAAkBD,EAAY,QAGpCH,GACHlR,EAAaqR,EA5IN,cA4I4B,SAKzC,IAAME,EAAYxQ,KAAKyQ,gBAEvBzQ,KAAKgF,SAASU,UAAUC,OA9HA,YA+HxB3F,KAAKgF,SAASU,UAAU0C,IA9HE,cAgI1BpI,KAAKgF,SAASnH,MAAM2S,GAAa,EAE7BxQ,KAAKsP,cAAc3N,QACrB3B,KAAKsP,cAAcvS,SAAQ,SAAA9C,GACzBA,EAAQyL,UAAUC,OAnIG,aAoIrB1L,EAAQwM,aAAa,iBAAiB,MAI1CzG,KAAK0Q,kBAAiB,GAEtB,IAYMC,EAAU,UADaH,EAAU,GAAG7S,cAAgB6S,EAAUzM,MAAM,IAEpE1I,EAAqBJ,EAAiC+E,KAAKgF,UAEjEnC,EAAaQ,IAAIrD,KAAKgF,STtMH,iBSuLF,WACfS,EAAKT,SAASU,UAAUC,OA5IA,cA6IxBF,EAAKT,SAASU,UAAU0C,IA9IF,WADJ,QAiJlB3C,EAAKT,SAASnH,MAAM2S,GAAa,GAEjC/K,EAAKiL,kBAAiB,GAEtB7N,EAAaoB,QAAQwB,EAAKT,SA1Jf,wBAmKb/I,EAAqB+D,KAAKgF,SAAU3J,GACpC2E,KAAKgF,SAASnH,MAAM2S,GAAgBxQ,KAAKgF,SAAS2L,GAAlD,UAGFX,KAAA,WAAO,IAAA1D,EAAAtM,KACL,IAAIA,KAAKqP,kBACNrP,KAAKgF,SAASU,UAAUE,SApKP,UAwKD/C,EAAaoB,QAAQjE,KAAKgF,SA5KjC,oBA6KGnF,iBAAf,CAIA,IAAM2Q,EAAYxQ,KAAKyQ,gBAEvBzQ,KAAKgF,SAASnH,MAAM2S,GAAgBxQ,KAAKgF,SAAS0C,wBAAwB8I,GAA1E,KAEApS,EAAO4B,KAAKgF,UAEZhF,KAAKgF,SAASU,UAAU0C,IAjLE,cAkL1BpI,KAAKgF,SAASU,UAAUC,OAnLA,WADJ,QAsLpB,IAAMiL,EAAqB5Q,KAAKsP,cAAc3N,OAC9C,GAAIiP,EAAqB,EACvB,IAAK,IAAInP,EAAI,EAAGA,EAAImP,EAAoBnP,IAAK,CAC3C,IAAMwC,EAAUjE,KAAKsP,cAAc7N,GAC7BgO,EAAOzU,EAAuBiJ,GAEhCwL,IAASA,EAAK/J,UAAUE,SA5LZ,UA6Ld3B,EAAQyB,UAAU0C,IA1LC,aA2LnBnE,EAAQwC,aAAa,iBAAiB,IAK5CzG,KAAK0Q,kBAAiB,GAStB1Q,KAAKgF,SAASnH,MAAM2S,GAAa,GACjC,IAAMnV,EAAqBJ,EAAiC+E,KAAKgF,UAEjEnC,EAAaQ,IAAIrD,KAAKgF,STzPH,iBS+OF,WACfsH,EAAKoE,kBAAiB,GACtBpE,EAAKtH,SAASU,UAAUC,OArMA,cAsMxB2G,EAAKtH,SAASU,UAAU0C,IAvMF,YAwMtBvF,EAAaoB,QAAQqI,EAAKtH,SA5Md,yBAmNd/I,EAAqB+D,KAAKgF,SAAU3J,OAGtCqV,iBAAA,SAAiBG,GACf7Q,KAAKqP,iBAAmBwB,KAG1BtL,QAAA,WACEtG,EAAgBe,KAAKgF,SA5OR,eA8ObhF,KAAK6K,QAAU,KACf7K,KAAK6P,QAAU,KACf7P,KAAKgF,SAAW,KAChBhF,KAAKsP,cAAgB,KACrBtP,KAAKqP,iBAAmB,QAK1BvE,WAAA,SAAWnO,GAOT,OANAA,EAAM0K,EAAAA,EAAA,GACDoC,IACA9M,IAEE6J,OAAStG,QAAQvD,EAAO6J,QAC/B/J,EAAgBqI,GAAMnI,EAAQqN,IACvBrN,KAGT8T,cAAA,WAEE,OADiBzQ,KAAKgF,SAASU,UAAUE,SAzO/B,SAAA,QACC,YA4ObkK,WAAA,WAAa,IAAArD,EAAAzM,KACLmP,EAAWnP,KAAK6K,QAAhBsE,OAEFrT,EAAUqT,QAEiB,IAAlBA,EAAO2B,aAA+C,IAAd3B,EAAO,KACxDA,EAASA,EAAO,IAGlBA,EAAS9G,GAAe9I,QAAQ4P,GAGlC,IAAMzU,EAAc6U,yCAAqCJ,EAA3C,KAYd,OAVA9G,GAAelJ,KAAKzE,EAAUyU,GAC3BpS,SAAQ,SAAA9C,GACP,IAAM8W,EAAW/V,EAAuBf,GAExCwS,EAAKsD,0BACHgB,EACA,CAAC9W,OAIAkV,KAGTY,0BAAA,SAA0B9V,EAAS+W,GACjC,GAAI/W,EAAS,CACX,IAAMgX,EAAShX,EAAQyL,UAAUE,SA/Qf,QAiRdoL,EAAarP,QACfqP,EAAajU,SAAQ,SAAA0S,GACfwB,EACFxB,EAAK/J,UAAUC,OAjRE,aAmRjB8J,EAAK/J,UAAU0C,IAnRE,aAsRnBqH,EAAKhJ,aAAa,gBAAiBwK,UAQpCV,kBAAP,SAAyBtW,EAAS0C,GAChC,IAAIkC,EAAOI,EAAahF,EAtTX,eAuTP4Q,EAAOxD,EAAAA,EAAAA,EAAA,GACRoC,IACAzC,GAAYG,kBAAkBlN,IACZ,iBAAX0C,GAAuBA,EAASA,EAAS,IAWrD,IARKkC,GAAQgM,EAAQrE,QAA4B,iBAAX7J,GAAuB,YAAYc,KAAKd,KAC5EkO,EAAQrE,QAAS,GAGd3H,IACHA,EAAO,IAAIuQ,EAASnV,EAAS4Q,IAGT,iBAAXlO,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,SAIFoJ,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACfoJ,EAASmB,kBAAkBvQ,KAAMrD,SAI9BwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EArVP,wDA6Eb,MA9EY,+CAkFZ,OAAOwP,SA5CL2F,GA0TNvM,EAAaO,GAAG7I,SA7UU,6BAWG,4BAkUyC,SAAUqH,GAEjD,MAAzBA,EAAMgB,OAAOsK,SACftL,EAAMhC,iBAGR,IAAMsR,EAAclK,GAAYG,kBAAkBnH,MAC5CtF,EAAWI,EAAuBkF,MACfqI,GAAelJ,KAAKzE,GAE5BqC,SAAQ,SAAA9C,GACvB,IACI0C,EADEkC,EAAOI,EAAahF,EA1Wb,eA4WT4E,GAEmB,OAAjBA,EAAKgR,SAAkD,iBAAvBqB,EAAY/B,SAC9CtQ,EAAKgM,QAAQsE,OAAS+B,EAAY/B,OAClCtQ,EAAKgR,QAAUhR,EAAKiR,cAGtBnT,EAAS,UAETA,EAASuU,EAGX9B,GAASmB,kBAAkBtW,EAAS0C,SAIxC,IAAM4D,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQsK,GAASrJ,gBACtBxF,GAAEmC,GAAGoC,IAAMuB,YAAc+I,GACzB7O,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACNgJ,GAASrJ,iBC/YpB,IAAMjB,GAAO,WAaPqM,GAAiB,IAAI3T,OAAU4T,4BAiC/B3H,GAAU,CACdjC,OAAQ,EACR6J,MAAM,EACNC,SAAU,eACVC,UAAW,SACXtT,QAAS,UACTuT,aAAc,MAGVxH,GAAc,CAClBxC,OAAQ,2BACR6J,KAAM,UACNC,SAAU,mBACVC,UAAW,mBACXtT,QAAS,SACTuT,aAAc,iBASVC,GAAAA,WACJ,SAAAA,EAAYxX,EAAS0C,GACnBqD,KAAKgF,SAAW/K,EAChB+F,KAAK0R,QAAU,KACf1R,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAK2R,MAAQ3R,KAAK4R,kBAClB5R,KAAK6R,UAAY7R,KAAK8R,gBAEtB9R,KAAKqL,qBACLpM,EAAahF,EA7EA,cA6EmB+F,iCAmBlCwG,OAAA,WACE,IAAIxG,KAAKgF,SAAS+M,WAAY/R,KAAKgF,SAASU,UAAUE,SA3E9B,YA2ExB,CAIA,IAAMoM,EAAWhS,KAAKgF,SAASU,UAAUE,SA9ErB,QAgFpB6L,EAASQ,aAELD,GAIJhS,KAAKiQ,WAGPA,KAAA,WACE,KAAIjQ,KAAKgF,SAAS+M,UAAY/R,KAAKgF,SAASU,UAAUE,SA3F9B,aA2F+D5F,KAAK2R,MAAMjM,UAAUE,SA1FxF,SA0FpB,CAIA,IAAMuJ,EAASsC,EAASS,qBAAqBlS,KAAKgF,UAC5C0I,EAAgB,CACpBA,cAAe1N,KAAKgF,UAKtB,IAFkBnC,EAAaoB,QAAQjE,KAAKgF,SA3GhC,mBA2GsD0I,GAEpD7N,iBAAd,CAKA,IAAKG,KAAK6R,UAAW,CACnB,QAAsB,IAAXM,EACT,MAAM,IAAIrD,UAAU,mEAGtB,IAAIsD,EAAmBpS,KAAKgF,SAEG,WAA3BhF,KAAK6K,QAAQ0G,UACfa,EAAmBjD,EACVrT,EAAUkE,KAAK6K,QAAQ0G,aAChCa,EAAmBpS,KAAK6K,QAAQ0G,eAGa,IAAlCvR,KAAK6K,QAAQ0G,UAAUT,SAChCsB,EAAmBpS,KAAK6K,QAAQ0G,UAAU,KAOhB,iBAA1BvR,KAAK6K,QAAQyG,UACfnC,EAAOzJ,UAAU0C,IA1HU,mBA6H7BpI,KAAK0R,QAAU,IAAIS,EAAOC,EAAkBpS,KAAK2R,MAAO3R,KAAKqS,oBAQvB,IAAA/J,EADxC,GAAI,iBAAkB/N,SAASgO,kBAC5B4G,EAAO3J,QAhIc,gBAiItB8C,EAAA,IAAGE,OAAHxF,MAAAsF,EAAa/N,SAASiE,KAAKkK,UACxB3L,SAAQ,SAAA0S,GAAI,OAAI5M,EAAaO,GAAGqM,EAAM,YAAa,MVxBzC,kBU2BfzP,KAAKgF,SAASsN,QACdtS,KAAKgF,SAASyB,aAAa,iBAAiB,GAE5CO,GAAYkB,YAAYlI,KAAK2R,MAnJT,QAoJpB3K,GAAYkB,YAAYlI,KAAKgF,SApJT,QAqJpBnC,EAAaoB,QAAQkL,EA5JR,oBA4J6BzB,QAG5CsC,KAAA,WACE,IAAIhQ,KAAKgF,SAAS+M,WAAY/R,KAAKgF,SAASU,UAAUE,SA1J9B,aA0JgE5F,KAAK2R,MAAMjM,UAAUE,SAzJzF,QAyJpB,CAIA,IAAMuJ,EAASsC,EAASS,qBAAqBlS,KAAKgF,UAC5C0I,EAAgB,CACpBA,cAAe1N,KAAKgF,UAGJnC,EAAaoB,QAAQkL,EA5K3B,mBA4K+CzB,GAE7C7N,mBAIVG,KAAK0R,SACP1R,KAAK0R,QAAQa,UAGfvL,GAAYkB,YAAYlI,KAAK2R,MA5KT,QA6KpB3K,GAAYkB,YAAYlI,KAAKgF,SA7KT,QA8KpBnC,EAAaoB,QAAQkL,EAvLP,qBAuL6BzB,QAG7CnI,QAAA,WACEtG,EAAgBe,KAAKgF,SAzMR,eA0MbnC,EAAaC,IAAI9C,KAAKgF,SAzMX,gBA0MXhF,KAAKgF,SAAW,KAChBhF,KAAK2R,MAAQ,KACT3R,KAAK0R,UACP1R,KAAK0R,QAAQa,UACbvS,KAAK0R,QAAU,SAInBc,OAAA,WACExS,KAAK6R,UAAY7R,KAAK8R,gBAClB9R,KAAK0R,SACP1R,KAAK0R,QAAQe,oBAMjBpH,mBAAA,WAAqB,IAAA5F,EAAAzF,KACnB6C,EAAaO,GAAGpD,KAAKgF,SA5MR,qBA4M+B,SAAApD,GAC1CA,EAAMhC,iBACNgC,EAAM8Q,kBACNjN,EAAKe,eAITsE,WAAA,SAAWnO,GAaT,OAZAA,EAAM0K,EAAAA,EAAAA,EAAA,GACDrH,KAAK2S,YAAYlJ,SACjBzC,GAAYG,kBAAkBnH,KAAKgF,WACnCrI,GAGLF,EACEqI,GACAnI,EACAqD,KAAK2S,YAAY3I,aAGZrN,KAGTiV,gBAAA,WACE,OAAOvJ,GAAeiB,KAAKtJ,KAAKgF,SApNd,kBAoNuC,MAG3D4N,cAAA,WACE,IAAMC,EAAiB7S,KAAKgF,SAASlH,WACjCgV,EAnNiB,eAmOrB,OAbID,EAAenN,UAAUE,SArOP,WAsOpBkN,EAzNgB,YA0NZ9S,KAAK2R,MAAMjM,UAAUE,SApOF,yBAqOrBkN,EA1NiB,YA4NVD,EAAenN,UAAUE,SAzOX,aA0OvBkN,EA1NkB,cA2NTD,EAAenN,UAAUE,SA1OZ,YA2OtBkN,EA3NiB,aA4NR9S,KAAK2R,MAAMjM,UAAUE,SA3OP,yBA4OvBkN,EA/NsB,cAkOjBA,KAGThB,cAAA,WACE,OAAO5R,QAAQF,KAAKgF,SAASQ,QAAd,eAGjBuN,WAAA,WAAa,IAAAzG,EAAAtM,KACLwH,EAAS,GAef,MAbmC,mBAAxBxH,KAAK6K,QAAQrD,OACtBA,EAAO9E,GAAK,SAAA7D,GAMV,OALAA,EAAKmU,QAAL3L,EAAAA,EAAA,GACKxI,EAAKmU,SACL1G,EAAKzB,QAAQrD,OAAO3I,EAAKmU,QAAS1G,EAAKtH,WAAa,IAGlDnG,GAGT2I,EAAOA,OAASxH,KAAK6K,QAAQrD,OAGxBA,KAGT6K,iBAAA,WACE,IAAMb,EAAe,CACnBsB,UAAW9S,KAAK4S,gBAChBK,UAAW,CACTzL,OAAQxH,KAAK+S,aACb1B,KAAM,CACJ6B,QAASlT,KAAK6K,QAAQwG,MAExB8B,gBAAiB,CACfC,kBAAmBpT,KAAK6K,QAAQyG,YAYtC,MAN6B,WAAzBtR,KAAK6K,QAAQ5M,UACfuT,EAAayB,UAAUI,WAAa,CAClCH,SAAS,IAIb7L,EAAAA,EAAA,GACKmK,GACAxR,KAAK6K,QAAQ2G,iBAMb8B,kBAAP,SAAyBrZ,EAAS0C,GAChC,IAAIkC,EAAOI,EAAahF,EAlUX,eAyUb,GAJK4E,IACHA,EAAO,IAAI4S,EAASxX,EAHY,iBAAX0C,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,SAIFoJ,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACfyL,EAAS6B,kBAAkBtT,KAAMrD,SAI9BsV,WAAP,SAAkBrQ,GAChB,IAAIA,GAhVmB,IAgVTA,EAAM8E,SACF,UAAf9E,EAAMmB,MApVG,QAoViBnB,EAAMhD,KAMnC,IAFA,IAAM2U,EAAUlL,GAAelJ,KA/TN,4BAiUhBsC,EAAI,EAAGC,EAAM6R,EAAQ5R,OAAQF,EAAIC,EAAKD,IAAK,CAClD,IAAM0N,EAASsC,EAASS,qBAAqBqB,EAAQ9R,IAC/C+R,EAAUvU,EAAasU,EAAQ9R,GAlW1B,eAmWLiM,EAAgB,CACpBA,cAAe6F,EAAQ9R,IAOzB,GAJIG,GAAwB,UAAfA,EAAMmB,OACjB2K,EAAc+F,WAAa7R,GAGxB4R,EAAL,CAIA,IAAME,EAAeF,EAAQ7B,MAC7B,GAAK4B,EAAQ9R,GAAGiE,UAAUE,SAzVR,QA6VlB,KAAIhE,IAA0B,UAAfA,EAAMmB,MACjB,kBAAkBtF,KAAKmE,EAAMgB,OAAOsK,UACpB,UAAftL,EAAMmB,MAhXD,QAgXqBnB,EAAMhD,MACjC8U,EAAa9N,SAAShE,EAAMgB,SAKhC,IADkBC,EAAaoB,QAAQkL,EA9W7B,mBA8WiDzB,GAC7C7N,iBAAd,CAMgD,IAAA8I,EAAhD,GAAI,iBAAkBpO,SAASgO,iBAC7BI,EAAA,IAAGH,OAAHxF,MAAA2F,EAAapO,SAASiE,KAAKkK,UACxB3L,SAAQ,SAAA0S,GAAI,OAAI5M,EAAaC,IAAI2M,EAAM,YAAa,MVxP5C,kBU2Pb8D,EAAQ9R,GAAGgF,aAAa,gBAAiB,SAErC+M,EAAQ9B,SACV8B,EAAQ9B,QAAQa,UAGlBmB,EAAahO,UAAUC,OAtXL,QAuXlB4N,EAAQ9R,GAAGiE,UAAUC,OAvXH,QAwXlB9C,EAAaoB,QAAQkL,EAjYT,qBAiY+BzB,SAIxCwE,qBAAP,SAA4BjY,GAC1B,OAAOe,EAAuBf,IAAYA,EAAQ6D,cAG7C6V,sBAAP,SAA6B/R,GAQ3B,KAAI,kBAAkBnE,KAAKmE,EAAMgB,OAAOsK,SA1Z1B,UA2ZZtL,EAAMhD,KA5ZO,WA4ZegD,EAAMhD,MAxZjB,cAyZfgD,EAAMhD,KA1ZO,YA0ZmBgD,EAAMhD,KACtCgD,EAAMgB,OAAO4C,QAjYC,oBAkYf2L,GAAe1T,KAAKmE,EAAMhD,QAI7BgD,EAAMhC,iBACNgC,EAAM8Q,mBAEF1S,KAAK+R,WAAY/R,KAAK0F,UAAUE,SApZZ,aAoZxB,CAIA,IAAMuJ,EAASsC,EAASS,qBAAqBlS,MACvCgS,EAAWhS,KAAK0F,UAAUE,SAxZZ,QA0ZpB,GA7ae,WA6aXhE,EAAMhD,IAIR,OAHeoB,KAAKM,QAnZG,4BAmZ6BN,KAAOqI,GAAec,KAAKnJ,KAnZxD,4BAmZoF,IACpGsS,aACPb,EAASQ,aAIX,GAAKD,GAnbS,UAmbGpQ,EAAMhD,IAAvB,CAKA,IAAMgV,EAAQvL,GAAelJ,KA1ZF,8DA0Z+BgQ,GACvDvG,OAAOhL,GAEV,GAAKgW,EAAMjS,OAAX,CAIA,IAAIoK,EAAQ6H,EAAMxR,QAAQR,EAAMgB,QA7bf,YA+bbhB,EAAMhD,KAAwBmN,EAAQ,GACxCA,IA/biB,cAkcfnK,EAAMhD,KAA0BmN,EAAQ6H,EAAMjS,OAAS,GACzDoK,IAMF6H,EAFA7H,GAAmB,IAAXA,EAAe,EAAIA,GAEduG,cAxBXb,EAASQ,iBA2BN9L,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EArdP,wDAmFb,MApFY,+CAwFZ,OAAOwP,uCAIP,OAAOO,SAvBLyH,GA2ZN5O,EAAaO,GAAG7I,SA5cY,+BAYC,2BAgc2CkX,GAASkC,uBACjF9Q,EAAaO,GAAG7I,SA7cY,+BAcN,iBA+b2CkX,GAASkC,uBAC1E9Q,EAAaO,GAAG7I,SA/cU,6BA+csBkX,GAASQ,YACzDpP,EAAaO,GAAG7I,SA9cU,6BA8csBkX,GAASQ,YACzDpP,EAAaO,GAAG7I,SAjdU,6BAaG,4BAocyC,SAAUqH,GAC9EA,EAAMhC,iBACNgC,EAAM8Q,kBACNjB,GAAS6B,kBAAkBtT,KAAM,aAEnC6C,EACGO,GAAG7I,SAvdoB,6BAcE,kBAyc+B,SAAAP,GAAC,OAAIA,EAAE0Y,qBAElE,IAAMnS,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQ2M,GAAS1L,gBACtBxF,GAAEmC,GAAGoC,IAAMuB,YAAcoL,GACzBlR,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACNqL,GAAS1L,iBC3fpB,IAOM0D,GAAU,CACdoK,UAAU,EACVlK,UAAU,EACV2I,OAAO,EACPrC,MAAM,GAGFjG,GAAc,CAClB6J,SAAU,mBACVlK,SAAU,UACV2I,MAAO,UACPrC,KAAM,WAoCF6D,GAAAA,WACJ,SAAAA,EAAY7Z,EAAS0C,GACnBqD,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAKgF,SAAW/K,EAChB+F,KAAK+T,QAAU1L,GAAe9I,QAjBV,gBAiBmCtF,GACvD+F,KAAKgU,UAAY,KACjBhU,KAAKiU,UAAW,EAChBjU,KAAKkU,oBAAqB,EAC1BlU,KAAKmU,sBAAuB,EAC5BnU,KAAKqP,kBAAmB,EACxBrP,KAAKoU,gBAAkB,EACvBnV,EAAahF,EA/DA,WA+DmB+F,iCAelCwG,OAAA,SAAOkH,GACL,OAAO1N,KAAKiU,SAAWjU,KAAKgQ,OAAShQ,KAAKiQ,KAAKvC,MAGjDuC,KAAA,SAAKvC,GAAe,IAAAjI,EAAAzF,KAClB,IAAIA,KAAKiU,WAAYjU,KAAKqP,iBAA1B,CAIIrP,KAAKgF,SAASU,UAAUE,SApDR,UAqDlB5F,KAAKqP,kBAAmB,GAG1B,IAAMgF,EAAYxR,EAAaoB,QAAQjE,KAAKgF,SArEhC,gBAqEsD,CAChE0I,cAAAA,IAGE1N,KAAKiU,UAAYI,EAAUxU,mBAI/BG,KAAKiU,UAAW,EAEhBjU,KAAKsU,kBACLtU,KAAKuU,gBAELvU,KAAKwU,gBAELxU,KAAKyU,kBACLzU,KAAK0U,kBAEL7R,EAAaO,GAAGpD,KAAKgF,SAnFA,yBAgBK,0BAsExB,SAAApD,GAAK,OAAI6D,EAAKuK,KAAKpO,MAGrBiB,EAAaO,GAAGpD,KAAK+T,QAtFI,8BAsF8B,WACrDlR,EAAaQ,IAAIoC,EAAKT,SAxFD,4BAwFkC,SAAApD,GACjDA,EAAMgB,SAAW6C,EAAKT,WACxBS,EAAK0O,sBAAuB,SAKlCnU,KAAK2U,eAAc,WAAA,OAAMlP,EAAKmP,aAAalH,WAG7CsC,KAAA,SAAKpO,GAAO,IAAA0K,EAAAtM,KAKV,IAJI4B,GACFA,EAAMhC,iBAGHI,KAAKiU,WAAYjU,KAAKqP,oBAITxM,EAAaoB,QAAQjE,KAAKgF,SApHhC,iBAsHEnF,iBAAd,CAIAG,KAAKiU,UAAW,EAChB,IAAMY,EAAa7U,KAAKgF,SAASU,UAAUE,SA3GvB,QA2HpB,GAdIiP,IACF7U,KAAKqP,kBAAmB,GAG1BrP,KAAKyU,kBACLzU,KAAK0U,kBAEL7R,EAAaC,IAAIvI,SA/HF,oBAiIfyF,KAAKgF,SAASU,UAAUC,OArHJ,QAuHpB9C,EAAaC,IAAI9C,KAAKgF,SAjID,0BAkIrBnC,EAAaC,IAAI9C,KAAK+T,QA/HG,8BAiIrBc,EAAY,CACd,IAAMxZ,EAAqBJ,EAAiC+E,KAAKgF,UAEjEnC,EAAaQ,IAAIrD,KAAKgF,SXtLL,iBWsL+B,SAAApD,GAAK,OAAI0K,EAAKwI,WAAWlT,MACzE3F,EAAqB+D,KAAKgF,SAAU3J,QAEpC2E,KAAK8U,iBAITvP,QAAA,WACE,CAACpK,OAAQ6E,KAAKgF,SAAUhF,KAAK+T,SAC1BhX,SAAQ,SAAAgY,GAAW,OAAIlS,EAAaC,IAAIiS,EAzKhC,gBAgLXlS,EAAaC,IAAIvI,SAzJF,oBA2Jf0E,EAAgBe,KAAKgF,SAnLR,YAqLbhF,KAAK6K,QAAU,KACf7K,KAAKgF,SAAW,KAChBhF,KAAK+T,QAAU,KACf/T,KAAKgU,UAAY,KACjBhU,KAAKiU,SAAW,KAChBjU,KAAKkU,mBAAqB,KAC1BlU,KAAKmU,qBAAuB,KAC5BnU,KAAKqP,iBAAmB,KACxBrP,KAAKoU,gBAAkB,QAGzBY,aAAA,WACEhV,KAAKwU,mBAKP1J,WAAA,SAAWnO,GAMT,OALAA,EAAM0K,EAAAA,EAAA,GACDoC,IACA9M,GAELF,EA7MS,QA6MaE,EAAQqN,IACvBrN,KAGTiY,aAAA,SAAalH,GAAe,IAAAjB,EAAAzM,KACpB6U,EAAa7U,KAAKgF,SAASU,UAAUE,SA7KvB,QA8KdqP,EAAY5M,GAAe9I,QAzKT,cAyKsCS,KAAK+T,SAE9D/T,KAAKgF,SAASlH,YACfkC,KAAKgF,SAASlH,WAAW9B,WAAagN,KAAKC,cAE7C1O,SAASiE,KAAK0W,YAAYlV,KAAKgF,UAGjChF,KAAKgF,SAASnH,MAAMI,QAAU,QAC9B+B,KAAKgF,SAAS3E,gBAAgB,eAC9BL,KAAKgF,SAASyB,aAAa,cAAc,GACzCzG,KAAKgF,SAASyB,aAAa,OAAQ,UACnCzG,KAAKgF,SAAS4C,UAAY,EAEtBqN,IACFA,EAAUrN,UAAY,GAGpBiN,GACFzW,EAAO4B,KAAKgF,UAGdhF,KAAKgF,SAASU,UAAU0C,IAnMJ,QAqMhBpI,KAAK6K,QAAQyH,OACftS,KAAKmV,gBAGP,IAAMC,EAAqB,WACrB3I,EAAK5B,QAAQyH,OACf7F,EAAKzH,SAASsN,QAGhB7F,EAAK4C,kBAAmB,EACxBxM,EAAaoB,QAAQwI,EAAKzH,SA5Nf,iBA4NsC,CAC/C0I,cAAAA,KAIJ,GAAImH,EAAY,CACd,IAAMxZ,EAAqBJ,EAAiC+E,KAAK+T,SAEjElR,EAAaQ,IAAIrD,KAAK+T,QXhRL,gBWgR8BqB,GAC/CnZ,EAAqB+D,KAAK+T,QAAS1Y,QAEnC+Z,OAIJD,cAAA,WAAgB,IAAA/G,EAAApO,KACd6C,EAAaC,IAAIvI,SA3OF,oBA4OfsI,EAAaO,GAAG7I,SA5OD,oBA4O0B,SAAAqH,GACnCrH,WAAaqH,EAAMgB,QACnBwL,EAAKpJ,WAAapD,EAAMgB,QACvBwL,EAAKpJ,SAASY,SAAShE,EAAMgB,SAChCwL,EAAKpJ,SAASsN,cAKpBmC,gBAAA,WAAkB,IAAAY,EAAArV,KACZA,KAAKiU,SACPpR,EAAaO,GAAGpD,KAAKgF,SApPA,4BAoPiC,SAAApD,GAChDyT,EAAKxK,QAAQlB,UA7QN,WA6QkB/H,EAAMhD,KACjCgD,EAAMhC,iBACNyV,EAAKrF,QACKqF,EAAKxK,QAAQlB,UAhRd,WAgR0B/H,EAAMhD,KACzCyW,EAAKC,gCAITzS,EAAaC,IAAI9C,KAAKgF,SA7PD,+BAiQzB0P,gBAAA,WAAkB,IAAAa,EAAAvV,KACZA,KAAKiU,SACPpR,EAAaO,GAAGjI,OArQJ,mBAqQ0B,WAAA,OAAMoa,EAAKf,mBAEjD3R,EAAaC,IAAI3H,OAvQL,sBA2QhB2Z,WAAA,WAAa,IAAAU,EAAAxV,KACXA,KAAKgF,SAASnH,MAAMI,QAAU,OAC9B+B,KAAKgF,SAASyB,aAAa,eAAe,GAC1CzG,KAAKgF,SAAS3E,gBAAgB,cAC9BL,KAAKgF,SAAS3E,gBAAgB,QAC9BL,KAAKqP,kBAAmB,EACxBrP,KAAK2U,eAAc,WACjBpa,SAASiE,KAAKkH,UAAUC,OAzQN,cA0QlB6P,EAAKC,oBACLD,EAAKE,kBACL7S,EAAaoB,QAAQuR,EAAKxQ,SAzRd,yBA6RhB2Q,gBAAA,WACE3V,KAAKgU,UAAUlW,WAAWgI,YAAY9F,KAAKgU,WAC3ChU,KAAKgU,UAAY,QAGnBW,cAAA,SAAciB,GAAU,IAAAC,EAAA7V,KAChB8V,EAAU9V,KAAKgF,SAASU,UAAUE,SArRpB,QAAA,OAuRlB,GAEF,GAAI5F,KAAKiU,UAAYjU,KAAK6K,QAAQgJ,SAAU,CA6B1C,GA5BA7T,KAAKgU,UAAYzZ,SAASoF,cAAc,OACxCK,KAAKgU,UAAU7L,UA7RO,iBA+RlB2N,GACF9V,KAAKgU,UAAUtO,UAAU0C,IAAI0N,GAG/Bvb,SAASiE,KAAK0W,YAAYlV,KAAKgU,WAE/BnR,EAAaO,GAAGpD,KAAKgF,SA5SF,0BA4SiC,SAAApD,GAC9CiU,EAAK1B,qBACP0B,EAAK1B,sBAAuB,EAI1BvS,EAAMgB,SAAWhB,EAAMmU,eAI3BF,EAAKP,gCAGHQ,GACF1X,EAAO4B,KAAKgU,WAGdhU,KAAKgU,UAAUtO,UAAU0C,IAnTP,SAqTb0N,EAEH,YADAF,IAIF,IAAMI,EAA6B/a,EAAiC+E,KAAKgU,WAEzEnR,EAAaQ,IAAIrD,KAAKgU,UXrXL,gBWqXgC4B,GACjD3Z,EAAqB+D,KAAKgU,UAAWgC,QAChC,IAAKhW,KAAKiU,UAAYjU,KAAKgU,UAAW,CAC3ChU,KAAKgU,UAAUtO,UAAUC,OA/TP,QAiUlB,IAAMsQ,EAAiB,WACrBJ,EAAKF,kBACLC,KAGF,GAAI5V,KAAKgF,SAASU,UAAUE,SAvUV,QAuUqC,CACrD,IAAMoQ,EAA6B/a,EAAiC+E,KAAKgU,WACzEnR,EAAaQ,IAAIrD,KAAKgU,UXjYP,gBWiYkCiC,GACjDha,EAAqB+D,KAAKgU,UAAWgC,QAErCC,SAGFL,OAIJN,2BAAA,WAA6B,IAAAY,EAAAlW,KAC3B,GAA8B,WAA1BA,KAAK6K,QAAQgJ,SAAuB,CAEtC,GADkBhR,EAAaoB,QAAQjE,KAAKgF,SApWxB,0BAqWNnF,iBACZ,OAGFG,KAAKgF,SAASU,UAAU0C,IAxVJ,gBAyVpB,IAAM+N,EAA0Blb,EAAiC+E,KAAKgF,UACtEnC,EAAaQ,IAAIrD,KAAKgF,SXpZL,iBWoZ+B,WAC9CkR,EAAKlR,SAASU,UAAUC,OA3VN,mBA6VpB1J,EAAqB+D,KAAKgF,SAAUmR,GACpCnW,KAAKgF,SAASsN,aAEdtS,KAAKgQ,UAQTwE,cAAA,WACE,IAAM4B,EACJpW,KAAKgF,SAASqR,aAAe9b,SAASgO,gBAAgB+N,cAEnDtW,KAAKkU,oBAAsBkC,IAC9BpW,KAAKgF,SAASnH,MAAM0Y,YAAiBvW,KAAKoU,gBAA1C,MAGEpU,KAAKkU,qBAAuBkC,IAC9BpW,KAAKgF,SAASnH,MAAM2Y,aAAkBxW,KAAKoU,gBAA3C,SAIJqB,kBAAA,WACEzV,KAAKgF,SAASnH,MAAM0Y,YAAc,GAClCvW,KAAKgF,SAASnH,MAAM2Y,aAAe,MAGrClC,gBAAA,WACE,IAAM7M,EAAOlN,SAASiE,KAAKkJ,wBAC3B1H,KAAKkU,mBAAqB9Z,KAAKqc,MAAMhP,EAAKI,KAAOJ,EAAKiP,OAASvb,OAAOwb,WACtE3W,KAAKoU,gBAAkBpU,KAAK4W,wBAG9BrC,cAAA,WAAgB,IAAAsC,EAAA7W,KACd,GAAIA,KAAKkU,mBAAoB,CAK3B7L,GAAelJ,KAhYU,qDAiYtBpC,SAAQ,SAAA9C,GACP,IAAM6c,EAAgB7c,EAAQ4D,MAAM2Y,aAC9BO,EAAoB5b,OAAOC,iBAAiBnB,GAAS,iBAC3D+M,GAAYC,iBAAiBhN,EAAS,gBAAiB6c,GACvD7c,EAAQ4D,MAAM2Y,aAAkBhb,WAAWub,GAAqBF,EAAKzC,gBAArE,QAIJ/L,GAAelJ,KAxYW,eAyYvBpC,SAAQ,SAAA9C,GACP,IAAM+c,EAAe/c,EAAQ4D,MAAMoZ,YAC7BC,EAAmB/b,OAAOC,iBAAiBnB,GAAS,gBAC1D+M,GAAYC,iBAAiBhN,EAAS,eAAgB+c,GACtD/c,EAAQ4D,MAAMoZ,YAAiBzb,WAAW0b,GAAoBL,EAAKzC,gBAAnE,QAIJ,IAAM0C,EAAgBvc,SAASiE,KAAKX,MAAM2Y,aACpCO,EAAoB5b,OAAOC,iBAAiBb,SAASiE,MAAM,iBAEjEwI,GAAYC,iBAAiB1M,SAASiE,KAAM,gBAAiBsY,GAC7Dvc,SAASiE,KAAKX,MAAM2Y,aAAkBhb,WAAWub,GAAqB/W,KAAKoU,gBAA3E,KAGF7Z,SAASiE,KAAKkH,UAAU0C,IAlaJ,iBAqatBsN,gBAAA,WAEErN,GAAelJ,KA9ZY,qDA+ZxBpC,SAAQ,SAAA9C,GACP,IAAMkd,EAAUnQ,GAAYO,iBAAiBtN,EAAS,sBAC/B,IAAZkd,IACTnQ,GAAYE,oBAAoBjN,EAAS,iBACzCA,EAAQ4D,MAAM2Y,aAAeW,MAKnC9O,GAAelJ,KAvaa,eAwazBpC,SAAQ,SAAA9C,GACP,IAAMmd,EAASpQ,GAAYO,iBAAiBtN,EAAS,qBAC/B,IAAXmd,IACTpQ,GAAYE,oBAAoBjN,EAAS,gBACzCA,EAAQ4D,MAAMoZ,YAAcG,MAKlC,IAAMD,EAAUnQ,GAAYO,iBAAiBhN,SAASiE,KAAM,sBACrC,IAAZ2Y,EACT5c,SAASiE,KAAKX,MAAM2Y,aAAe,IAEnCxP,GAAYE,oBAAoB3M,SAASiE,KAAM,iBAC/CjE,SAASiE,KAAKX,MAAM2Y,aAAeW,MAIvCP,mBAAA,WACE,IAAMS,EAAY9c,SAASoF,cAAc,OACzC0X,EAAUlP,UAxcwB,0BAyclC5N,SAASiE,KAAK0W,YAAYmC,GAC1B,IAAMC,EAAiBD,EAAU3P,wBAAwB6P,MAAQF,EAAUG,YAE3E,OADAjd,SAASiE,KAAKsH,YAAYuR,GACnBC,KAKFvR,gBAAP,SAAuBpJ,EAAQ+Q,GAC7B,OAAO1N,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KAnfb,YAofL6K,EAAOxD,EAAAA,EAAAA,EAAA,GACRoC,IACAzC,GAAYG,kBAAkBnH,OACZ,iBAAXrD,GAAuBA,EAASA,EAAS,IAOrD,GAJKkC,IACHA,EAAO,IAAIiV,EAAM9T,KAAM6K,IAGH,iBAAXlO,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,GAAQ+Q,QACJ7C,EAAQoF,MACjBpR,EAAKoR,KAAKvC,SAKTvH,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EA3gBP,qDAqEb,MAtEY,+CA0EZ,OAAOwP,SArBLqK,GAieNjR,EAAaO,GAAG7I,SAvfU,0BAWG,yBA4eyC,SAAUqH,GAAO,IAAA6V,EAAAzX,KAC/E4C,EAAS5H,EAAuBgF,MAEjB,MAAjBA,KAAKkN,SAAoC,SAAjBlN,KAAKkN,SAC/BtL,EAAMhC,iBAGRiD,EAAaQ,IAAIT,EAtgBH,iBAsgBuB,SAAAyR,GAC/BA,EAAUxU,kBAKdgD,EAAaQ,IAAIT,EA7gBH,mBA6gByB,WACjChF,EAAU6Z,IACZA,EAAKnF,cAKX,IAAIzT,EAAOI,EAAa2D,EAziBT,YA0iBf,IAAK/D,EAAM,CACT,IAAMlC,EAAM0K,EAAAA,EAAA,GACPL,GAAYG,kBAAkBvE,IAC9BoE,GAAYG,kBAAkBnH,OAGnCnB,EAAO,IAAIiV,GAAMlR,EAAQjG,GAG3BkC,EAAKoR,KAAKjQ,SAGZ,IAAMO,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAF,MAC3BnC,GAAEmC,GAAF,MAAaoR,GAAM/N,gBACnBxF,GAAEmC,GAAF,MAAW2D,YAAcyN,GACzBvT,GAAEmC,GAAF,MAAW4D,WAAa,WAEtB,OADA/F,GAAEmC,GAAF,MAAa0D,GACN0N,GAAM/N,iBC5lBjB,IAAM2R,GAAW,CACf,aACA,OACA,OACA,WACA,WACA,SACA,MACA,cAUIC,GAAmB,8DAOnBC,GAAmB,qIAyBZC,GAAmB,CAE9BC,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7BC,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJpX,EAAG,GACHqX,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAGC,SAASC,GAAaC,EAAYC,EAAWC,GAAY,IAAAxR,EAC9D,IAAKsR,EAAWjY,OACd,OAAOiY,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAQpB,IALA,IACMG,GADY,IAAI5e,OAAO6e,WACKC,gBAAgBL,EAAY,aACxDM,EAAgBrd,OAAOC,KAAK+c,GAC5BM,GAAW7R,EAAA,IAAGE,OAAHxF,MAAAsF,EAAayR,EAAgBvb,KAAKc,iBAAiB,MAZN8a,EAAA,SAcrD3Y,EAAOC,GAd8C,IAAAiH,EAetD0R,EAAKF,EAAS1Y,GACd6Y,EAASD,EAAGE,SAAShd,cAE3B,IAAuC,IAAnC2c,EAAc9X,QAAQkY,GAGxB,OAFAD,EAAGvc,WAAWgI,YAAYuU,GAE1B,WAGF,IAAMG,GAAgB7R,EAAA,IAAGH,OAAHxF,MAAA2F,EAAa0R,EAAGjT,YAChCqT,EAAwB,GAAGjS,OAAOqR,EAAU,MAAQ,GAAIA,EAAUS,IAAW,IAEnFE,EAAczd,SAAQ,SAAA2d,IApFD,SAACA,EAAMC,GAC9B,IAAMC,EAAWF,EAAKH,SAAShd,cAE/B,IAAgD,IAA5Cod,EAAqBvY,QAAQwY,GAC/B,OAAoC,IAAhClD,GAAStV,QAAQwY,IACZ1a,QAAQwa,EAAKG,UAAUvd,MAAMqa,KAAqB+C,EAAKG,UAAUvd,MAAMsa,KASlF,IAHA,IAAMkD,EAASH,EAAqB/R,QAAO,SAAAmS,GAAS,OAAIA,aAAqBvd,UAGpEiE,EAAI,EAAGC,EAAMoZ,EAAOnZ,OAAQF,EAAIC,EAAKD,IAC5C,GAAImZ,EAAStd,MAAMwd,EAAOrZ,IACxB,OAAO,EAIX,OAAO,GAiEEuZ,CAAiBN,EAAMD,IAC1BJ,EAAGha,gBAAgBqa,EAAKH,cAfrB9Y,EAAI,EAAGC,EAAMyY,EAASxY,OAAQF,EAAIC,EAAKD,IAAK2Y,EAA5C3Y,GAoBT,OAAOsY,EAAgBvb,KAAKyc,UC3F9B,IAAMnW,GAAO,UAKPoW,GAAqB,IAAI1d,OAAJ,wBAAyC,KAC9D2d,GAAwB,CAAC,WAAY,YAAa,cAElDnR,GAAc,CAClBoR,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPrX,QAAS,SACTsX,MAAO,kBACPC,KAAM,UACN9gB,SAAU,mBACVoY,UAAW,oBACXtL,OAAQ,2BACR4I,UAAW,2BACXqL,kBAAmB,iBACnBnK,SAAU,mBACVoK,SAAU,UACV5B,WAAY,kBACZD,UAAW,SACXrI,aAAc,iBAGVmK,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAO,QACPC,OAAQ,SACRC,KAAM,QAGFvS,GAAU,CACd2R,WAAW,EACXC,SAAU,+GAGVpX,QAAS,cACTqX,MAAO,GACPC,MAAO,EACPC,MAAM,EACN9gB,UAAU,EACVoY,UAAW,MACXtL,OAAQ,EACR4I,WAAW,EACXqL,kBAAmB,OACnBnK,SAAU,eACVoK,UAAU,EACV5B,WAAY,KACZD,UAAWhC,GACXrG,aAAc,MAGV3V,GAAQ,CACZogB,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAuBNC,GAAAA,WACJ,SAAAA,EAAY1iB,EAAS0C,GACnB,QAAsB,IAAXwV,EACT,MAAM,IAAIrD,UAAU,kEAItB9O,KAAK4c,YAAa,EAClB5c,KAAK6c,SAAW,EAChB7c,KAAK8c,YAAc,GACnB9c,KAAK+c,eAAiB,GACtB/c,KAAK0R,QAAU,KAGf1R,KAAK/F,QAAUA,EACf+F,KAAKrD,OAASqD,KAAK8K,WAAWnO,GAC9BqD,KAAKgd,IAAM,KAEXhd,KAAKid,gBACLhe,EAAahF,EAAS+F,KAAK2S,YAAYuK,SAAUld,iCAmCnDmd,OAAA,WACEnd,KAAK4c,YAAa,KAGpBQ,QAAA,WACEpd,KAAK4c,YAAa,KAGpBS,cAAA,WACErd,KAAK4c,YAAc5c,KAAK4c,cAG1BpW,OAAA,SAAO5E,GACL,GAAK5B,KAAK4c,WAIV,GAAIhb,EAAO,CACT,IAAM0b,EAAUtd,KAAK2S,YAAYuK,SAC7B1J,EAAUvU,EAAa2C,EAAMgB,OAAQ0a,GAEpC9J,IACHA,EAAU,IAAIxT,KAAK2S,YACjB/Q,EAAMgB,OACN5C,KAAKud,sBAEPte,EAAa2C,EAAMgB,OAAQ0a,EAAS9J,IAGtCA,EAAQuJ,eAAeS,OAAShK,EAAQuJ,eAAeS,MAEnDhK,EAAQiK,uBACVjK,EAAQkK,OAAO,KAAMlK,GAErBA,EAAQmK,OAAO,KAAMnK,OAElB,CACL,GAAIxT,KAAK4d,gBAAgBlY,UAAUE,SA7GjB,QA+GhB,YADA5F,KAAK2d,OAAO,KAAM3d,MAIpBA,KAAK0d,OAAO,KAAM1d,UAItBuF,QAAA,WACEwH,aAAa/M,KAAK6c,UAElB5d,EAAgBe,KAAK/F,QAAS+F,KAAK2S,YAAYuK,UAE/Cra,EAAaC,IAAI9C,KAAK/F,QAAS+F,KAAK2S,YAAYnJ,WAChD3G,EAAaC,IAAI9C,KAAK/F,QAAQuL,QAAb,UAA8C,gBAAiBxF,KAAK6d,mBAEjF7d,KAAKgd,KACPhd,KAAKgd,IAAIlf,WAAWgI,YAAY9F,KAAKgd,KAGvChd,KAAK4c,WAAa,KAClB5c,KAAK6c,SAAW,KAChB7c,KAAK8c,YAAc,KACnB9c,KAAK+c,eAAiB,KAClB/c,KAAK0R,SACP1R,KAAK0R,QAAQa,UAGfvS,KAAK0R,QAAU,KACf1R,KAAK/F,QAAU,KACf+F,KAAKrD,OAAS,KACdqD,KAAKgd,IAAM,QAGb/M,KAAA,WAAO,IAAAxK,EAAAzF,KACL,GAAmC,SAA/BA,KAAK/F,QAAQ4D,MAAMI,QACrB,MAAM,IAAIP,MAAM,uCAGlB,GAAIsC,KAAK8d,iBAAmB9d,KAAK4c,WAAY,CAC3C,IAAMvI,EAAYxR,EAAaoB,QAAQjE,KAAK/F,QAAS+F,KAAK2S,YAAY9W,MAAMsgB,MACtE4B,Eb9GW,SAAjBC,EAAiB/jB,GACrB,IAAKM,SAASgO,gBAAgB0V,aAC5B,OAAO,KAIT,GAAmC,mBAAxBhkB,EAAQikB,YAA4B,CAC7C,IAAMC,EAAOlkB,EAAQikB,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIlkB,aAAmBmkB,WACdnkB,EAIJA,EAAQ6D,WAINkgB,EAAe/jB,EAAQ6D,YAHrB,Ka6FckgB,CAAehe,KAAK/F,SACjCokB,EAA4B,OAAfN,EACjB/d,KAAK/F,QAAQqkB,cAAc/V,gBAAgB3C,SAAS5F,KAAK/F,SACzD8jB,EAAWnY,SAAS5F,KAAK/F,SAE3B,GAAIoa,EAAUxU,mBAAqBwe,EACjC,OAGF,IAAMrB,EAAMhd,KAAK4d,gBACXW,EAAQrkB,EAAO8F,KAAK2S,YAAY7N,MAEtCkY,EAAIvW,aAAa,KAAM8X,GACvBve,KAAK/F,QAAQwM,aAAa,mBAAoB8X,GAE9Cve,KAAKwe,aAEDxe,KAAKrD,OAAOye,WACd4B,EAAItX,UAAU0C,IA3KE,QA8KlB,IAAM0K,EAA6C,mBAA1B9S,KAAKrD,OAAOmW,UACnC9S,KAAKrD,OAAOmW,UAAUzV,KAAK2C,KAAMgd,EAAKhd,KAAK/F,SAC3C+F,KAAKrD,OAAOmW,UAER2L,EAAaze,KAAK0e,eAAe5L,GACvC9S,KAAK2e,oBAAoBF,GAEzB,IAiBgDnW,EAjB1C8H,EAAYpQ,KAAK4e,gBAiBvB,GAhBA3f,EAAa+d,EAAKhd,KAAK2S,YAAYuK,SAAUld,MAExCA,KAAK/F,QAAQqkB,cAAc/V,gBAAgB3C,SAAS5F,KAAKgd,MAC5D5M,EAAU8E,YAAY8H,GAGxBna,EAAaoB,QAAQjE,KAAK/F,QAAS+F,KAAK2S,YAAY9W,MAAMwgB,UAE1Drc,KAAK0R,QAAU,IAAIS,EAAOnS,KAAK/F,QAAS+iB,EAAKhd,KAAKqS,iBAAiBoM,IAEnEzB,EAAItX,UAAU0C,IA9LI,QAoMd,iBAAkB7N,SAASgO,iBAC7BD,EAAA,IAAGE,OAAHxF,MAAAsF,EAAa/N,SAASiE,KAAKkK,UAAU3L,SAAQ,SAAA9C,GAC3C4I,EAAaO,GAAGnJ,EAAS,abtIhB,kBa0Ib,IAAM4kB,EAAW,WACXpZ,EAAK9I,OAAOye,WACd3V,EAAKqZ,iBAGP,IAAMC,EAAiBtZ,EAAKqX,YAC5BrX,EAAKqX,YAAc,KAEnBja,EAAaoB,QAAQwB,EAAKxL,QAASwL,EAAKkN,YAAY9W,MAAMugB,OA/M1C,QAiNZ2C,GACFtZ,EAAKkY,OAAO,KAAMlY,IAItB,GAAIzF,KAAKgd,IAAItX,UAAUE,SA3NL,QA2NgC,CAChD,IAAMvK,EAAqBJ,EAAiC+E,KAAKgd,KACjEna,EAAaQ,IAAIrD,KAAKgd,Ib3TP,gBa2T4B6B,GAC3C5iB,EAAqB+D,KAAKgd,IAAK3hB,QAE/BwjB,QAKN7O,KAAA,WAAO,IAAA1D,EAAAtM,KACCgd,EAAMhd,KAAK4d,gBACXiB,EAAW,WAnOI,SAoOfvS,EAAKwQ,aAAoCE,EAAIlf,YAC/Ckf,EAAIlf,WAAWgI,YAAYkX,GAG7B1Q,EAAK0S,iBACL1S,EAAKrS,QAAQoG,gBAAgB,oBAC7BwC,EAAaoB,QAAQqI,EAAKrS,QAASqS,EAAKqG,YAAY9W,MAAMqgB,QAC1D5P,EAAKoF,QAAQa,WAIf,IADkB1P,EAAaoB,QAAQjE,KAAK/F,QAAS+F,KAAK2S,YAAY9W,MAAMogB,MAC9Dpc,iBAAd,CAQgD,IAAA8I,EAAhD,GAJAqU,EAAItX,UAAUC,OArPM,QAyPhB,iBAAkBpL,SAASgO,iBAC7BI,EAAA,IAAGH,OAAHxF,MAAA2F,EAAapO,SAASiE,KAAKkK,UACxB3L,SAAQ,SAAA9C,GAAO,OAAI4I,EAAaC,IAAI7I,EAAS,YAAakE,MAO/D,GAJA6B,KAAK+c,eAAL,OAAqC,EACrC/c,KAAK+c,eAAL,OAAqC,EACrC/c,KAAK+c,eAAL,OAAqC,EAEjC/c,KAAKgd,IAAItX,UAAUE,SApQH,QAoQ8B,CAChD,IAAMvK,EAAqBJ,EAAiC+hB,GAE5Dna,EAAaQ,IAAI2Z,EbrWA,gBaqWqB6B,GACtC5iB,EAAqB+gB,EAAK3hB,QAE1BwjB,IAGF7e,KAAK8c,YAAc,OAGrBtK,OAAA,WACuB,OAAjBxS,KAAK0R,SACP1R,KAAK0R,QAAQe,oBAMjBqL,cAAA,WACE,OAAO5d,QAAQF,KAAKif,eAGtBrB,cAAA,WACE,GAAI5d,KAAKgd,IACP,OAAOhd,KAAKgd,IAGd,IAAM/iB,EAAUM,SAASoF,cAAc,OAIvC,OAHA1F,EAAQghB,UAAYjb,KAAKrD,OAAO0e,SAEhCrb,KAAKgd,IAAM/iB,EAAQyO,SAAS,GACrB1I,KAAKgd,OAGdwB,WAAA,WACE,IAAMxB,EAAMhd,KAAK4d,gBACjB5d,KAAKkf,kBAAkB7W,GAAe9I,QAnSX,iBAmS2Cyd,GAAMhd,KAAKif,YACjFjC,EAAItX,UAAUC,OA3SM,OAEA,WA4StBuZ,kBAAA,SAAkBjlB,EAASklB,GACzB,GAAgB,OAAZllB,EAIJ,MAAuB,iBAAZklB,GAAwBrjB,EAAUqjB,IACvCA,EAAQrO,SACVqO,EAAUA,EAAQ,SAIhBnf,KAAKrD,OAAO6e,KACV2D,EAAQrhB,aAAe7D,IACzBA,EAAQghB,UAAY,GACpBhhB,EAAQib,YAAYiK,IAGtBllB,EAAQmlB,YAAcD,EAAQC,mBAM9Bpf,KAAKrD,OAAO6e,MACVxb,KAAKrD,OAAO+e,WACdyD,EAAUxF,GAAawF,EAASnf,KAAKrD,OAAOkd,UAAW7Z,KAAKrD,OAAOmd,aAGrE7f,EAAQghB,UAAYkE,GAEpBllB,EAAQmlB,YAAcD,MAI1BF,SAAA,WACE,IAAI3D,EAAQtb,KAAK/F,QAAQU,aAAa,uBAQtC,OANK2gB,IACHA,EAAqC,mBAAtBtb,KAAKrD,OAAO2e,MACzBtb,KAAKrD,OAAO2e,MAAMje,KAAK2C,KAAK/F,SAC5B+F,KAAKrD,OAAO2e,OAGTA,KAKTjJ,iBAAA,SAAiBoM,GAAY,IAAAhS,EAAAzM,KAuB3B,OAAAqH,EAAAA,EAAA,GAtBwB,CACtByL,UAAW2L,EACXxL,UAAW,CACTzL,OAAQxH,KAAK+S,aACb1B,KAAM,CACJgO,SAAUrf,KAAKrD,OAAO8e,mBAExB6D,MAAO,CACLrlB,QAAO,IAAM+F,KAAK2S,YAAY7N,KAAvB,UAETqO,gBAAiB,CACfC,kBAAmBpT,KAAKrD,OAAO2U,WAGnCiO,SAAU,SAAA1gB,GACJA,EAAK2gB,oBAAsB3gB,EAAKiU,WAClCrG,EAAKgT,6BAA6B5gB,IAGtC6gB,SAAU,SAAA7gB,GAAI,OAAI4N,EAAKgT,6BAA6B5gB,MAKjDmB,KAAKrD,OAAO6U,iBAInBmN,oBAAA,SAAoBF,GAClBze,KAAK4d,gBAAgBlY,UAAU0C,IAAOuX,cAAgBlB,MAGxD1L,WAAA,WAAa,IAAA3E,EAAApO,KACLwH,EAAS,GAef,MAbkC,mBAAvBxH,KAAKrD,OAAO6K,OACrBA,EAAO9E,GAAK,SAAA7D,GAMV,OALAA,EAAKmU,QAAL3L,EAAAA,EAAA,GACKxI,EAAKmU,SACL5E,EAAKzR,OAAO6K,OAAO3I,EAAKmU,QAAS5E,EAAKnU,UAAY,IAGhD4E,GAGT2I,EAAOA,OAASxH,KAAKrD,OAAO6K,OAGvBA,KAGToX,cAAA,WACE,OAA8B,IAA1B5e,KAAKrD,OAAOyT,UACP7V,SAASiE,KAGd1C,EAAUkE,KAAKrD,OAAOyT,WACjBpQ,KAAKrD,OAAOyT,UAGd/H,GAAe9I,QAAQS,KAAKrD,OAAOyT,cAG5CsO,eAAA,SAAe5L,GACb,OAAO6I,GAAc7I,EAAUnV,kBAGjCsf,cAAA,WAAgB,IAAA5H,EAAArV,KACGA,KAAKrD,OAAOsH,QAAQvI,MAAM,KAElCqB,SAAQ,SAAAkH,GACf,GAAgB,UAAZA,EACFpB,EAAaO,GAAGiS,EAAKpb,QACnBob,EAAK1C,YAAY9W,MAAMygB,MACvBjH,EAAK1Y,OAAOjC,UACZ,SAAAkH,GAAK,OAAIyT,EAAK7O,OAAO5E,WAElB,GAhaU,WAgaNqC,EAA4B,CACrC,IAAM2b,EApaQ,UAoaE3b,EACdoR,EAAK1C,YAAY9W,MAAM4gB,WACvBpH,EAAK1C,YAAY9W,MAAM0gB,QACnBsD,EAvaQ,UAuaG5b,EACfoR,EAAK1C,YAAY9W,MAAM6gB,WACvBrH,EAAK1C,YAAY9W,MAAM2gB,SAEzB3Z,EAAaO,GAAGiS,EAAKpb,QACnB2lB,EACAvK,EAAK1Y,OAAOjC,UACZ,SAAAkH,GAAK,OAAIyT,EAAKqI,OAAO9b,MAEvBiB,EAAaO,GAAGiS,EAAKpb,QACnB4lB,EACAxK,EAAK1Y,OAAOjC,UACZ,SAAAkH,GAAK,OAAIyT,EAAKsI,OAAO/b,UAK3B5B,KAAK6d,kBAAoB,WACnBxI,EAAKpb,SACPob,EAAKrF,QAITnN,EAAaO,GAAGpD,KAAK/F,QAAQuL,QAAb,UACd,gBACAxF,KAAK6d,mBAGH7d,KAAKrD,OAAOjC,SACdsF,KAAKrD,OAAL0K,EAAAA,EAAA,GACKrH,KAAKrD,QADV,GAAA,CAEEsH,QAAS,SACTvJ,SAAU,KAGZsF,KAAK8f,eAITA,UAAA,WACE,IAAMC,SAAmB/f,KAAK/F,QAAQU,aAAa,wBAE/CqF,KAAK/F,QAAQU,aAAa,UAA0B,WAAdolB,KACxC/f,KAAK/F,QAAQwM,aACX,sBACAzG,KAAK/F,QAAQU,aAAa,UAAY,IAGxCqF,KAAK/F,QAAQwM,aAAa,QAAS,QAIvCiX,OAAA,SAAO9b,EAAO4R,GACZ,IAAM8J,EAAUtd,KAAK2S,YAAYuK,UACjC1J,EAAUA,GAAWvU,EAAa2C,EAAMgB,OAAQ0a,MAG9C9J,EAAU,IAAIxT,KAAK2S,YACjB/Q,EAAMgB,OACN5C,KAAKud,sBAEPte,EAAa2C,EAAMgB,OAAQ0a,EAAS9J,IAGlC5R,IACF4R,EAAQuJ,eACS,YAAfnb,EAAMmB,KAxeQ,QADA,UA0eZ,GAGFyQ,EAAQoK,gBAAgBlY,UAAUE,SApflB,SAEC,SAmfjB4N,EAAQsJ,YACVtJ,EAAQsJ,YApfW,QAwfrB/P,aAAayG,EAAQqJ,UAErBrJ,EAAQsJ,YA1fa,OA4fhBtJ,EAAQ7W,OAAO4e,OAAU/H,EAAQ7W,OAAO4e,MAAMtL,KAKnDuD,EAAQqJ,SAAWrgB,YAAW,WAjgBT,SAkgBfgX,EAAQsJ,aACVtJ,EAAQvD,SAETuD,EAAQ7W,OAAO4e,MAAMtL,MARtBuD,EAAQvD,WAWZ0N,OAAA,SAAO/b,EAAO4R,GACZ,IAAM8J,EAAUtd,KAAK2S,YAAYuK,UACjC1J,EAAUA,GAAWvU,EAAa2C,EAAMgB,OAAQ0a,MAG9C9J,EAAU,IAAIxT,KAAK2S,YACjB/Q,EAAMgB,OACN5C,KAAKud,sBAEPte,EAAa2C,EAAMgB,OAAQ0a,EAAS9J,IAGlC5R,IACF4R,EAAQuJ,eACS,aAAfnb,EAAMmB,KAhhBQ,QADA,UAkhBZ,GAGFyQ,EAAQiK,yBAIZ1Q,aAAayG,EAAQqJ,UAErBrJ,EAAQsJ,YA/hBY,MAiiBftJ,EAAQ7W,OAAO4e,OAAU/H,EAAQ7W,OAAO4e,MAAMvL,KAKnDwD,EAAQqJ,SAAWrgB,YAAW,WAtiBV,QAuiBdgX,EAAQsJ,aACVtJ,EAAQxD,SAETwD,EAAQ7W,OAAO4e,MAAMvL,MARtBwD,EAAQxD,WAWZyN,qBAAA,WACE,IAAK,IAAMxZ,KAAWjE,KAAK+c,eACzB,GAAI/c,KAAK+c,eAAe9Y,GACtB,OAAO,EAIX,OAAO,KAGT6G,WAAA,SAAWnO,GACT,IAAMqjB,EAAiBhZ,GAAYG,kBAAkBnH,KAAK/F,SA4C1D,OA1CA4C,OAAOC,KAAKkjB,GACTjjB,SAAQ,SAAAkjB,IAC0C,IAA7C9E,GAAsB/Y,QAAQ6d,WACzBD,EAAeC,MAIxBtjB,GAAsC,iBAArBA,EAAOyT,WAA0BzT,EAAOyT,UAAUU,SACrEnU,EAAOyT,UAAYzT,EAAOyT,UAAU,IASV,iBAN5BzT,EAAM0K,EAAAA,EAAAA,EAAA,GACDrH,KAAK2S,YAAYlJ,SACjBuW,GACkB,iBAAXrjB,GAAuBA,EAASA,EAAS,KAGnC4e,QAChB5e,EAAO4e,MAAQ,CACbtL,KAAMtT,EAAO4e,MACbvL,KAAMrT,EAAO4e,QAIW,iBAAjB5e,EAAO2e,QAChB3e,EAAO2e,MAAQ3e,EAAO2e,MAAMle,YAGA,iBAAnBT,EAAOwiB,UAChBxiB,EAAOwiB,QAAUxiB,EAAOwiB,QAAQ/hB,YAGlCX,EACEqI,GACAnI,EACAqD,KAAK2S,YAAY3I,aAGfrN,EAAO+e,WACT/e,EAAO0e,SAAW1B,GAAahd,EAAO0e,SAAU1e,EAAOkd,UAAWld,EAAOmd,aAGpEnd,KAGT4gB,mBAAA,WACE,IAAM5gB,EAAS,GAEf,GAAIqD,KAAKrD,OACP,IAAK,IAAMiC,KAAOoB,KAAKrD,OACjBqD,KAAK2S,YAAYlJ,QAAQ7K,KAASoB,KAAKrD,OAAOiC,KAChDjC,EAAOiC,GAAOoB,KAAKrD,OAAOiC,IAKhC,OAAOjC,KAGTqiB,eAAA,WACE,IAAMhC,EAAMhd,KAAK4d,gBACXsC,EAAWlD,EAAIriB,aAAa,SAAS2C,MAAM4d,IAChC,OAAbgF,GAAqBA,EAASve,OAAS,GACzCue,EAASC,KAAI,SAAAC,GAAK,OAAIA,EAAMvlB,UACzBkC,SAAQ,SAAAsjB,GAAM,OAAIrD,EAAItX,UAAUC,OAAO0a,SAI9CZ,6BAAA,SAA6Ba,GAC3B,IAAMC,EAAiBD,EAAWphB,SAClCc,KAAKgd,IAAMuD,EAAeC,OAC1BxgB,KAAKgf,iBACLhf,KAAK2e,oBAAoB3e,KAAK0e,eAAe4B,EAAWxN,eAG1DgM,eAAA,WACE,IAAM9B,EAAMhd,KAAK4d,gBACX6C,EAAsBzgB,KAAKrD,OAAOye,UACA,OAApC4B,EAAIriB,aAAa,iBAIrBqiB,EAAItX,UAAUC,OAjpBM,QAkpBpB3F,KAAKrD,OAAOye,WAAY,EACxBpb,KAAKgQ,OACLhQ,KAAKiQ,OACLjQ,KAAKrD,OAAOye,UAAYqF,MAKnB1a,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KA/tBb,cAguBL6K,EAA4B,iBAAXlO,GAAuBA,EAE9C,IAAKkC,IAAQ,eAAepB,KAAKd,MAI5BkC,IACHA,EAAO,IAAI8d,EAAQ3c,KAAM6K,IAGL,iBAAXlO,GAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,YAKJwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EArvBP,uDAgHb,MAjHY,+CAqHZ,OAAOwP,gCAIP,OAAO3E,oCAIP,MA5Ha,2CAgIb,OAAOjJ,qCAIP,MAnIW,kDAuIX,OAAOmO,SAjDL2S,GAkqBApc,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQ6X,GAAQ5W,gBACrBxF,GAAEmC,GAAGoC,IAAMuB,YAAcsW,GACzBpc,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACNuW,GAAQ5W,iBC1xBnB,IAAMjB,GAAO,UAKPoW,GAAqB,IAAI1d,OAAJ,wBAAyC,KAE9DiM,GAAOpC,EAAAA,EAAA,GACRsV,GAAQlT,SADA,GAAA,CAEXqJ,UAAW,QACX7O,QAAS,QACTkb,QAAS,GACT9D,SAAU,gJAMNrR,GAAW3C,EAAAA,EAAA,GACZsV,GAAQ3S,aADI,GAAA,CAEfmV,QAAS,8BAGLtjB,GAAQ,CACZogB,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAeNgE,GAAAA,SAAAA,+KAiCJ5C,cAAA,WACE,OAAO9d,KAAKif,YAAcjf,KAAK2gB,iBAGjCnC,WAAA,WACE,IAAMxB,EAAMhd,KAAK4d,gBAGjB5d,KAAKkf,kBAAkB7W,GAAe9I,QAlDnB,kBAkD2Cyd,GAAMhd,KAAKif,YACzE,IAAIE,EAAUnf,KAAK2gB,cACI,mBAAZxB,IACTA,EAAUA,EAAQ9hB,KAAK2C,KAAK/F,UAG9B+F,KAAKkf,kBAAkB7W,GAAe9I,QAvDjB,gBAuD2Cyd,GAAMmC,GAEtEnC,EAAItX,UAAUC,OA7DM,OACA,WA+DtBgZ,oBAAA,SAAoBF,GAClBze,KAAK4d,gBAAgBlY,UAAU0C,IAAOuX,cAAgBlB,MAKxDkC,YAAA,WACE,OAAO3gB,KAAK/F,QAAQU,aAAa,iBAC/BqF,KAAKrD,OAAOwiB,WAGhBH,eAAA,WACE,IAAMhC,EAAMhd,KAAK4d,gBACXsC,EAAWlD,EAAIriB,aAAa,SAAS2C,MAAM4d,IAChC,OAAbgF,GAAqBA,EAASve,OAAS,GACzCue,EAASC,KAAI,SAAAC,GAAK,OAAIA,EAAMvlB,UACzBkC,SAAQ,SAAAsjB,GAAM,OAAIrD,EAAItX,UAAUC,OAAO0a,SAMvCta,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KA1Hb,cA2HL6K,EAA4B,iBAAXlO,EAAsBA,EAAS,KAEtD,IAAKkC,IAAQ,eAAepB,KAAKd,MAI5BkC,IACHA,EAAO,IAAI6hB,EAAQ1gB,KAAM6K,GACzB5L,EAAae,KAnIJ,aAmIoBnB,IAGT,iBAAXlC,GAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,YAKJwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EAjJP,uDAkDb,MAnDY,+CAuDZ,OAAOwP,gCAIP,OAAO3E,oCAIP,MA9Da,2CAkEb,OAAOjJ,qCAIP,MArEW,kDAyEX,OAAOmO,SA5BL0W,CAAgB/D,IAuGhBpc,GAAIjC,IAQV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQ4b,GAAQ3a,gBACrBxF,GAAEmC,GAAGoC,IAAMuB,YAAcqa,GACzBngB,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACNsa,GAAQ3a,iBC9JnB,IAAMjB,GAAO,YAMP2E,GAAU,CACdjC,OAAQ,GACRoZ,OAAQ,OACRhe,OAAQ,IAGJoH,GAAc,CAClBxC,OAAQ,SACRoZ,OAAQ,SACRhe,OAAQ,oBA2BJie,GAAAA,WACJ,SAAAA,EAAY5mB,EAAS0C,GAAQ,IAAA8I,EAAAzF,KAC3BA,KAAKgF,SAAW/K,EAChB+F,KAAK8gB,eAAqC,SAApB7mB,EAAQiT,QAAqB/R,OAASlB,EAC5D+F,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAK4P,UAAe5P,KAAK6K,QAAQjI,OAAb5C,cACKA,KAAK6K,QAAQjI,OADrB,qBAEQ5C,KAAK6K,QAAQjI,OAFrB,kBAGjB5C,KAAK+gB,SAAW,GAChB/gB,KAAKghB,SAAW,GAChBhhB,KAAKihB,cAAgB,KACrBjhB,KAAKkhB,cAAgB,EAErBre,EAAaO,GAAGpD,KAAK8gB,eApCP,uBAoCqC,SAAAlf,GAAK,OAAI6D,EAAK0b,SAASvf,MAE1E5B,KAAKohB,UACLphB,KAAKmhB,WAELliB,EAAahF,EA1DA,eA0DmB+F,iCAelCohB,QAAA,WAAU,IAAA9U,EAAAtM,KACFqhB,EAAarhB,KAAK8gB,iBAAmB9gB,KAAK8gB,eAAe3lB,OA3C7C,SACE,WA8CdmmB,EAAuC,SAAxBthB,KAAK6K,QAAQ+V,OAChCS,EACArhB,KAAK6K,QAAQ+V,OAETW,EAlDc,aAkDDD,EACjBthB,KAAKwhB,gBACL,EAEFxhB,KAAK+gB,SAAW,GAChB/gB,KAAKghB,SAAW,GAEhBhhB,KAAKkhB,cAAgBlhB,KAAKyhB,mBAEVpZ,GAAelJ,KAAKa,KAAK4P,WAGtCuQ,KAAI,SAAAlmB,GACH,IAAI2I,EACE8e,EAAiB5mB,EAAuBb,GAM9C,GAJIynB,IACF9e,EAASyF,GAAe9I,QAAQmiB,IAG9B9e,EAAQ,CACV,IAAM+e,EAAY/e,EAAO8E,wBACzB,GAAIia,EAAUpK,OAASoK,EAAUC,OAC/B,MAAO,CACL5a,GAAYsa,GAAc1e,GAAQ+E,IAAM4Z,EACxCG,GAKN,OAAO,QAER9Y,QAAO,SAAAiZ,GAAI,OAAIA,KACfC,MAAK,SAAC/J,EAAGE,GAAJ,OAAUF,EAAE,GAAKE,EAAE,MACxBlb,SAAQ,SAAA8kB,GACPvV,EAAKyU,SAAS7X,KAAK2Y,EAAK,IACxBvV,EAAK0U,SAAS9X,KAAK2Y,EAAK,UAI9Btc,QAAA,WACEtG,EAAgBe,KAAKgF,SA3HR,gBA4HbnC,EAAaC,IAAI9C,KAAK8gB,eA3HX,iBA6HX9gB,KAAKgF,SAAW,KAChBhF,KAAK8gB,eAAiB,KACtB9gB,KAAK6K,QAAU,KACf7K,KAAK4P,UAAY,KACjB5P,KAAK+gB,SAAW,KAChB/gB,KAAKghB,SAAW,KAChBhhB,KAAKihB,cAAgB,KACrBjhB,KAAKkhB,cAAgB,QAKvBpW,WAAA,SAAWnO,GAMT,GAA6B,iBAL7BA,EAAM0K,EAAAA,EAAA,GACDoC,IACkB,iBAAX9M,GAAuBA,EAASA,EAAS,KAGnCiG,QAAuB9G,EAAUa,EAAOiG,QAAS,CAAA,IAC3D7I,EAAO4C,EAAOiG,OAAd7I,GACDA,IACHA,EAAKG,EAAO4K,IACZnI,EAAOiG,OAAO7I,GAAKA,GAGrB4C,EAAOiG,OAAP,IAAoB7I,EAKtB,OAFA0C,EAAgBqI,GAAMnI,EAAQqN,IAEvBrN,KAGT6kB,cAAA,WACE,OAAOxhB,KAAK8gB,iBAAmB3lB,OAC7B6E,KAAK8gB,eAAeiB,YACpB/hB,KAAK8gB,eAAelZ,aAGxB6Z,iBAAA,WACE,OAAOzhB,KAAK8gB,eAAezK,cAAgBjc,KAAK4nB,IAC9CznB,SAASiE,KAAK6X,aACd9b,SAASgO,gBAAgB8N,iBAI7B4L,iBAAA,WACE,OAAOjiB,KAAK8gB,iBAAmB3lB,OAC7BA,OAAO+mB,YACPliB,KAAK8gB,eAAepZ,wBAAwBka,UAGhDT,SAAA,WACE,IAAMvZ,EAAY5H,KAAKwhB,gBAAkBxhB,KAAK6K,QAAQrD,OAChD6O,EAAerW,KAAKyhB,mBACpBU,EAAYniB,KAAK6K,QAAQrD,OAC7B6O,EACArW,KAAKiiB,mBAMP,GAJIjiB,KAAKkhB,gBAAkB7K,GACzBrW,KAAKohB,UAGHxZ,GAAaua,EAAjB,CACE,IAAMvf,EAAS5C,KAAKghB,SAAShhB,KAAKghB,SAASrf,OAAS,GAEhD3B,KAAKihB,gBAAkBre,GACzB5C,KAAKoiB,UAAUxf,OAJnB,CAUA,GAAI5C,KAAKihB,eAAiBrZ,EAAY5H,KAAK+gB,SAAS,IAAM/gB,KAAK+gB,SAAS,GAAK,EAG3E,OAFA/gB,KAAKihB,cAAgB,UACrBjhB,KAAKqiB,SAIP,IAAK,IAAI5gB,EAAIzB,KAAK+gB,SAASpf,OAAQF,KAAM,CAChBzB,KAAKihB,gBAAkBjhB,KAAKghB,SAASvf,IACxDmG,GAAa5H,KAAK+gB,SAAStf,UACM,IAAzBzB,KAAK+gB,SAAStf,EAAI,IACtBmG,EAAY5H,KAAK+gB,SAAStf,EAAI,KAGpCzB,KAAKoiB,UAAUpiB,KAAKghB,SAASvf,SAKnC2gB,UAAA,SAAUxf,GACR5C,KAAKihB,cAAgBre,EAErB5C,KAAKqiB,SAEL,IAAMC,EAAUtiB,KAAK4P,UAAUlU,MAAM,KAClCykB,KAAI,SAAAzlB,GAAQ,OAAOA,EAAP,iBAAgCkI,EAAhC,MAA4ClI,EAA5C,UAA8DkI,EAA9D,QAET2f,EAAOla,GAAe9I,QAAQ+iB,EAAQE,KAAK,MAE7CD,EAAK7c,UAAUE,SA/MU,kBAgN3ByC,GACG9I,QAxMwB,mBAwMUgjB,EAAK/c,QAzMtB,cA0MjBE,UAAU0C,IAjNO,UAmNpBma,EAAK7c,UAAU0C,IAnNK,YAsNpBma,EAAK7c,UAAU0C,IAtNK,UAwNpBC,GACGS,QAAQyZ,EAtNe,qBAuNvBxlB,SAAQ,SAAA0lB,GAGPpa,GAAec,KAAKsZ,EAAcC,+BAC/B3lB,SAAQ,SAAA8kB,GAAI,OAAIA,EAAKnc,UAAU0C,IA9NlB,aAiOhBC,GAAec,KAAKsZ,EA5NH,aA6Nd1lB,SAAQ,SAAA4lB,GACPta,GAAeK,SAASia,EA/NX,aAgOV5lB,SAAQ,SAAA8kB,GAAI,OAAIA,EAAKnc,UAAU0C,IApOtB,oBAyOtBvF,EAAaoB,QAAQjE,KAAK8gB,eA9OV,wBA8O0C,CACxDpT,cAAe9K,OAInByf,OAAA,WACEha,GAAelJ,KAAKa,KAAK4P,WACtBhH,QAAO,SAAAga,GAAI,OAAIA,EAAKld,UAAUE,SAhPX,aAiPnB7I,SAAQ,SAAA6lB,GAAI,OAAIA,EAAKld,UAAUC,OAjPZ,gBAsPjBI,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KA7Qb,gBAoRX,GAJKnB,IACHA,EAAO,IAAIgiB,EAAU7gB,KAHW,iBAAXrD,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,YAKJwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EA/RP,yDAgEb,MAjEY,+CAqEZ,OAAOwP,SA5BLoX,GAiQNhe,EAAaO,GAAGjI,OAvRS,8BAuRoB,WAC3CkN,GAAelJ,KAnRS,uBAoRrBpC,SAAQ,SAAA8lB,GAAG,OAAI,IAAIhC,GAAUgC,EAAK7b,GAAYG,kBAAkB0b,UAGrE,IAAMtiB,GAAIjC,IAQV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAGoC,IAChCvE,GAAEmC,GAAGoC,IAAQ+b,GAAU9a,gBACvBxF,GAAEmC,GAAGoC,IAAMuB,YAAcwa,GACzBtgB,GAAEmC,GAAGoC,IAAMwB,WAAa,WAEtB,OADA/F,GAAEmC,GAAGoC,IAAQsB,GACNya,GAAU9a,iBC9TrB,IAgCM+c,GAAAA,WACJ,SAAAA,EAAY7oB,GACV+F,KAAKgF,SAAW/K,EAEhBgF,EAAae,KAAKgF,SAlCL,SAkCyBhF,iCAWxCiQ,KAAA,WAAO,IAAAxK,EAAAzF,KACL,KAAKA,KAAKgF,SAASlH,YACjBkC,KAAKgF,SAASlH,WAAW9B,WAAagN,KAAKC,cAC3CjJ,KAAKgF,SAASU,UAAUE,SArCJ,WAsCpB5F,KAAKgF,SAASU,UAAUE,SArCF,aAkCxB,CAOA,IAAIwD,EACExG,EAAS5H,EAAuBgF,KAAKgF,UACrC+d,EAAc/iB,KAAKgF,SAASQ,QAtCN,qBAwC5B,GAAIud,EAAa,CACf,IAAMC,EAAwC,OAAzBD,EAAYxI,UAA8C,OAAzBwI,EAAYxI,SAvC7C,wBADH,UA0ClBnR,GADAA,EAAWf,GAAelJ,KAAK6jB,EAAcD,IACzB3Z,EAASzH,OAAS,GAGxC,IAAIshB,EAAY,KAYhB,GAVI7Z,IACF6Z,EAAYpgB,EAAaoB,QAAQmF,EA9DvB,cA8D6C,CACrDsE,cAAe1N,KAAKgF,cAINnC,EAAaoB,QAAQjE,KAAKgF,SAjEhC,cAiEsD,CAChE0I,cAAetE,IAGHvJ,kBACG,OAAdojB,GAAsBA,EAAUpjB,kBADnC,CAKAG,KAAKoiB,UACHpiB,KAAKgF,SACL+d,GAGF,IAAMlE,EAAW,WACfhc,EAAaoB,QAAQmF,EAjFT,gBAiFiC,CAC3CsE,cAAejI,EAAKT,WAEtBnC,EAAaoB,QAAQwB,EAAKT,SAlFf,eAkFsC,CAC/C0I,cAAetE,KAIfxG,EACF5C,KAAKoiB,UAAUxf,EAAQA,EAAO9E,WAAY+gB,GAE1CA,SAIJtZ,QAAA,WACEtG,EAAgBe,KAAKgF,SAtGR,UAuGbhF,KAAKgF,SAAW,QAKlBod,UAAA,SAAUnoB,EAASmW,EAAWwF,GAAU,IAAAtJ,EAAAtM,KAKhCkjB,IAJiB9S,GAAqC,OAAvBA,EAAUmK,UAA4C,OAAvBnK,EAAUmK,SAE5ElS,GAAeK,SAAS0H,EA7FN,WA4FlB/H,GAAelJ,KA3FM,wBA2FmBiR,IAGZ,GACxBS,EAAkB+E,GACrBsN,GAAUA,EAAOxd,UAAUE,SAtGV,QAwGdiZ,EAAW,WAAA,OAAMvS,EAAK6W,oBAC1BlpB,EACAipB,EACAtN,IAGF,GAAIsN,GAAUrS,EAAiB,CAC7B,IAAMxV,EAAqBJ,EAAiCioB,GAC5DA,EAAOxd,UAAUC,OA/GC,QAiHlB9C,EAAaQ,IAAI6f,EhBjJA,gBgBiJwBrE,GACzC5iB,EAAqBinB,EAAQ7nB,QAE7BwjB,OAIJsE,oBAAA,SAAoBlpB,EAASipB,EAAQtN,GACnC,GAAIsN,EAAQ,CACVA,EAAOxd,UAAUC,OA7HG,UA+HpB,IAAMyd,EAAgB/a,GAAe9I,QApHJ,kCAoH4C2jB,EAAOplB,YAEhFslB,GACFA,EAAc1d,UAAUC,OAlIN,UAqIgB,QAAhCud,EAAOvoB,aAAa,SACtBuoB,EAAOzc,aAAa,iBAAiB,IAIzCxM,EAAQyL,UAAU0C,IA1II,UA2Ie,QAAjCnO,EAAQU,aAAa,SACvBV,EAAQwM,aAAa,iBAAiB,GAGxCrI,EAAOnE,GAEHA,EAAQyL,UAAUE,SA/IF,SAgJlB3L,EAAQyL,UAAU0C,IA/IA,QAkJhBnO,EAAQ6D,YAAc7D,EAAQ6D,WAAW4H,UAAUE,SAtJ1B,oBAuJH3L,EAAQuL,QAjJZ,cAoJlB6C,GAAelJ,KA/IU,oBAgJtBpC,SAAQ,SAAAsmB,GAAQ,OAAIA,EAAS3d,UAAU0C,IA1JxB,aA6JpBnO,EAAQwM,aAAa,iBAAiB,IAGpCmP,GACFA,OAMG7P,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAMnH,EAAOI,EAAae,KApLf,WAoLkC,IAAI8iB,EAAI9iB,MAErD,GAAsB,iBAAXrD,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,YAKJwJ,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EAjMP,mDAwCb,MAzCY,qBA+BV6oB,GA6KNjgB,EAAaO,GAAG7I,SAnMU,wBAYG,mEAuLyC,SAAUqH,GAC9EA,EAAMhC,kBAEOX,EAAae,KA9MX,WA8M8B,IAAI8iB,GAAI9iB,OAChDiQ,UAGP,IAAM1P,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAF,IAC3BnC,GAAEmC,GAAF,IAAaogB,GAAI/c,gBACjBxF,GAAEmC,GAAF,IAAW2D,YAAcyc,GACzBviB,GAAEmC,GAAF,IAAW4D,WAAa,WAEtB,OADA/F,GAAEmC,GAAF,IAAa0D,GACN0c,GAAI/c,iBCnOf,IAgBMiE,GAAc,CAClBoR,UAAW,UACXkI,SAAU,UACV/H,MAAO,UAGH9R,GAAU,CACd2R,WAAW,EACXkI,UAAU,EACV/H,MAAO,KAWHgI,GAAAA,WACJ,SAAAA,EAAYtpB,EAAS0C,GACnBqD,KAAKgF,SAAW/K,EAChB+F,KAAK6K,QAAU7K,KAAK8K,WAAWnO,GAC/BqD,KAAK6c,SAAW,KAChB7c,KAAKid,gBACLhe,EAAahF,EAxCA,WAwCmB+F,iCAmBlCiQ,KAAA,WAAO,IAAAxK,EAAAzF,KAGL,IAFkB6C,EAAaoB,QAAQjE,KAAKgF,SAtDhC,iBAwDEnF,iBAAd,CAIIG,KAAK6K,QAAQuQ,WACfpb,KAAKgF,SAASU,UAAU0C,IA1DN,QA6DpB,IAAMyW,EAAW,WACfpZ,EAAKT,SAASU,UAAUC,OA3DH,WA4DrBF,EAAKT,SAASU,UAAU0C,IA7DN,QA+DlBvF,EAAaoB,QAAQwB,EAAKT,SAnEf,kBAqEPS,EAAKoF,QAAQyY,WACf7d,EAAKoX,SAAWrgB,YAAW,WACzBiJ,EAAKuK,SACJvK,EAAKoF,QAAQ0Q,SAOpB,GAHAvb,KAAKgF,SAASU,UAAUC,OAzEJ,QA0EpBvH,EAAO4B,KAAKgF,UACZhF,KAAKgF,SAASU,UAAU0C,IAzED,WA0EnBpI,KAAK6K,QAAQuQ,UAAW,CAC1B,IAAM/f,EAAqBJ,EAAiC+E,KAAKgF,UAEjEnC,EAAaQ,IAAIrD,KAAKgF,SjB3GL,gBiB2G+B6Z,GAChD5iB,EAAqB+D,KAAKgF,SAAU3J,QAEpCwjB,QAIJ7O,KAAA,WAAO,IAAA1D,EAAAtM,KACL,GAAKA,KAAKgF,SAASU,UAAUE,SAtFT,UA0FF/C,EAAaoB,QAAQjE,KAAKgF,SAjGhC,iBAmGEnF,iBAAd,CAIA,IAAMgf,EAAW,WACfvS,EAAKtH,SAASU,UAAU0C,IAlGN,QAmGlBvF,EAAaoB,QAAQqI,EAAKtH,SAxGd,oBA4Gd,GADAhF,KAAKgF,SAASU,UAAUC,OArGJ,QAsGhB3F,KAAK6K,QAAQuQ,UAAW,CAC1B,IAAM/f,EAAqBJ,EAAiC+E,KAAKgF,UAEjEnC,EAAaQ,IAAIrD,KAAKgF,SjBtIL,gBiBsI+B6Z,GAChD5iB,EAAqB+D,KAAKgF,SAAU3J,QAEpCwjB,QAIJtZ,QAAA,WACEwH,aAAa/M,KAAK6c,UAClB7c,KAAK6c,SAAW,KAEZ7c,KAAKgF,SAASU,UAAUE,SApHR,SAqHlB5F,KAAKgF,SAASU,UAAUC,OArHN,QAwHpB9C,EAAaC,IAAI9C,KAAKgF,SAhID,0BAiIrB/F,EAAgBe,KAAKgF,SApIR,YAsIbhF,KAAKgF,SAAW,KAChBhF,KAAK6K,QAAU,QAKjBC,WAAA,SAAWnO,GAaT,OAZAA,EAAM0K,EAAAA,EAAAA,EAAA,GACDoC,IACAzC,GAAYG,kBAAkBnH,KAAKgF,WACjB,iBAAXrI,GAAuBA,EAASA,EAAS,IAGrDF,EArJS,QAuJPE,EACAqD,KAAK2S,YAAY3I,aAGZrN,KAGTsgB,cAAA,WAAgB,IAAAxQ,EAAAzM,KACd6C,EAAaO,GACXpD,KAAKgF,SA3Jc,yBAuBK,0BAuIxB,WAAA,OAAMyH,EAAKuD,aAMRjK,gBAAP,SAAuBpJ,GACrB,OAAOqD,KAAKgG,MAAK,WACf,IAAInH,EAAOI,EAAae,KAzKb,YAgLX,GAJKnB,IACHA,EAAO,IAAI0kB,EAAMvjB,KAHe,iBAAXrD,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBkC,EAAKlC,GACd,MAAM,IAAImS,UAAJ,oBAAkCnS,EAAlC,KAGRkC,EAAKlC,GAAQqD,aAKZmG,YAAP,SAAmBlM,GACjB,OAAOgF,EAAahF,EA3LP,qDA8Cb,MA/CY,mDAmDZ,OAAO+P,mCAIP,OAAOP,SApBL8Z,GA6JAhjB,GAAIjC,IASV,GAAIiC,GAAG,CACL,IAAM6F,GAAqB7F,GAAEmC,GAAF,MAC3BnC,GAAEmC,GAAF,MAAa6gB,GAAMxd,gBACnBxF,GAAEmC,GAAF,MAAW2D,YAAckd,GACzBhjB,GAAEmC,GAAF,MAAW4D,WAAa,WAEtB,OADA/F,GAAEmC,GAAF,MAAa0D,GACNmd,GAAMxd,uBCtNF,CACbhB,MAAAA,EACAwB,OAAAA,EACA6D,SAAAA,GACAgF,SAAAA,GACAqC,SAAAA,GACAqC,MAAAA,GACA4M,QAAAA,GACAG,UAAAA,GACAiC,IAAAA,GACAS,MAAAA,GACA5G,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = parseFloat(transitionDuration)\n  const floatTransitionDelay = parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes)\n    .forEach(property => {\n      const expectedTypes = configTypes[property]\n      const value = config[property]\n      const valueType = value && isElement(value) ?\n        'element' :\n        toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new Error(\n          `${componentName.toUpperCase()}: ` +\n          `Option \"${property}\" provided type \"${valueType}\" ` +\n          `but expected type \"${expectedTypes}\".`)\n      }\n    })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nexport {\n  getjQuery,\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.key === 'undefined') {\n        element.key = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.key.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.key === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.key\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.key === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.key\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.key\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/* istanbul ignore file */\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.0-alpha1): dom/polyfill.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getUID } from '../util/index'\n\nlet find = Element.prototype.querySelectorAll\nlet findOne = Element.prototype.querySelector\n\n// MSEdge resets defaultPrevented flag upon dispatchEvent call if at least one listener is attached\nconst defaultPreventedPreservedOnDispatch = (() => {\n  const e = new CustomEvent('Bootstrap', {\n    cancelable: true\n  })\n\n  const element = document.createElement('div')\n  element.addEventListener('Bootstrap', () => null)\n\n  e.preventDefault()\n  element.dispatchEvent(e)\n  return e.defaultPrevented\n})()\n\nconst scopeSelectorRegex = /:scope\\b/\nconst supportScopeQuery = (() => {\n  const element = document.createElement('div')\n\n  try {\n    element.querySelectorAll(':scope *')\n  } catch (_) {\n    return false\n  }\n\n  return true\n})()\n\nif (!supportScopeQuery) {\n  find = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelectorAll(selector)\n    }\n\n    const hasId = Boolean(this.id)\n\n    if (!hasId) {\n      this.id = getUID('scope')\n    }\n\n    let nodeList = null\n    try {\n      selector = selector.replace(scopeSelectorRegex, `#${this.id}`)\n      nodeList = this.querySelectorAll(selector)\n    } finally {\n      if (!hasId) {\n        this.removeAttribute('id')\n      }\n    }\n\n    return nodeList\n  }\n\n  findOne = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelector(selector)\n    }\n\n    const matches = find.call(this, selector)\n\n    if (typeof matches[0] !== 'undefined') {\n      return matches[0]\n    }\n\n    return null\n  }\n}\n\nexport {\n  find,\n  findOne,\n  defaultPreventedPreservedOnDispatch\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\nimport { defaultPreventedPreservedOnDispatch } from './polyfill'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst $ = getjQuery()\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n]\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent)\n    .forEach(handlerKey => {\n      if (handlerKey.indexOf(namespace) > -1) {\n        const event = storeElementEvent[handlerKey]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.charAt(0) === '.'\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events)\n        .forEach(elementEvent => {\n          removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n        })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent)\n      .forEach(keyHandlers => {\n        const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n        if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n          const event = storeElementEvent[keyHandlers]\n\n          removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n        }\n      })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom informations in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args)\n        .forEach(key => {\n          Object.defineProperty(evt, key, {\n            get() {\n              return args[key]\n            }\n          })\n        })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n\n      if (!defaultPreventedPreservedOnDispatch) {\n        Object.defineProperty(evt, 'defaultPrevented', {\n          get: () => true\n        })\n      }\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this)\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    let rootElement = this._element\n    if (element) {\n      rootElement = this._getRootElement(element)\n    }\n\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler\n      .one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .alert to jQuery only if jQuery is present\n */\n\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Alert.jQueryInterface\n  $.fn[NAME].Constructor = Alert\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Alert.jQueryInterface\n  }\n}\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .button to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Button.jQueryInterface\n  $.fn[NAME].Constructor = Button\n\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Button.jQueryInterface\n  }\n}\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {\n      ...element.dataset\n    }\n\n    Object.keys(attributes).forEach(key => {\n      attributes[key] = normalizeData(attributes[key])\n    })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  },\n\n  toggleClass(element, className) {\n    if (!element) {\n      return\n    }\n\n    if (element.classList.contains(className)) {\n      element.classList.remove(className)\n    } else {\n      element.classList.add(className)\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { find as findFn, findOne } from './polyfill'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...findFn.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return findOne.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler\n        .on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler\n        .on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler\n        .on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement &&\n      this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const nextElementInterval = parseInt(nextElement.getAttribute('data-interval'), 10)\n      if (nextElementInterval) {\n        this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n        this._config.interval = nextElementInterval\n      } else {\n        this._config.interval = this._config.defaultInterval || this._config.interval\n      }\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler\n        .one(activeElement, TRANSITION_END, () => {\n          nextElement.classList.remove(directionalClassName, orderClassName)\n          nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n          activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n          this._isSliding = false\n\n          setTimeout(() => {\n            EventHandler.trigger(this._element, EVENT_SLID, {\n              relatedTarget: nextElement,\n              direction: eventDirectionName,\n              from: activeElementIndex,\n              to: nextElementIndex\n            })\n          }, 0)\n        })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .carousel to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Carousel.jQueryInterface\n  $.fn[NAME].Constructor = Carousel\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Carousel.jQueryInterface\n  }\n}\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.filter(elem => container !== elem)\n      activesData = tempActiveData[0] ? Data.getData(tempActiveData[0], DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    const hasWidth = this._element.classList.contains(WIDTH)\n    return hasWidth ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (element) {\n      const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n      if (triggerArray.length) {\n        triggerArray.forEach(elem => {\n          if (isOpen) {\n            elem.classList.remove(CLASS_NAME_COLLAPSED)\n          } else {\n            elem.classList.add(CLASS_NAME_COLLAPSED)\n          }\n\n          elem.setAttribute('aria-expanded', isOpen)\n        })\n      }\n    }\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .collapse to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Collapse.jQueryInterface\n  $.fn[NAME].Constructor = Collapse\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Collapse.jQueryInterface\n  }\n}\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_NAVBAR = 'navbar'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        parent.classList.add(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    Manipulator.toggleClass(this._menu, CLASS_NAME_SHOW)\n    Manipulator.toggleClass(this._element, CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    Manipulator.toggleClass(this._menu, CLASS_NAME_SHOW)\n    Manipulator.toggleClass(this._element, CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._element, EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      placement = PLACEMENT_TOP\n      if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n        placement = PLACEMENT_TOPEND\n      }\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return Boolean(this._element.closest(`.${CLASS_NAME_NAVBAR}`))\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this._config.offset(data.offsets, this._element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON ||\n      (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent)\n      .filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.key === ARROW_UP_KEY && index > 0) { // Up\n      index--\n    }\n\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) { // Down\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler\n  .on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .dropdown to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Dropdown.jQueryInterface\n  $.fn[NAME].Constructor = Dropdown\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Dropdown.jQueryInterface\n  }\n}\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n      if (hideEvent.defaultPrevented) {\n        return\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n      const modalTransitionDuration = getTransitionDurationFromElement(this._element)\n      EventHandler.one(this._element, TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n      })\n      emulateTransitionEnd(this._element, modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...typeof config === 'object' && config ? config : {}\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .modal to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Modal.jQueryInterface\n  $.fn[NAME].Constructor = Modal\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Modal.jQueryInterface\n  }\n}\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultWhitelist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, whiteList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const whitelistKeys = Object.keys(whiteList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (whitelistKeys.indexOf(elName) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const whitelistedAttributes = [].concat(whiteList['*'] || [], whiteList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, whitelistedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultWhitelist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'whiteList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  whiteList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"tooltip-arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  whiteList: DefaultWhitelist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.target, dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.target,\n          this._getDelegateConfig()\n        )\n        Data.setData(event.target, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    Data.removeData(this.element, this.constructor.DATA_KEY)\n\n    EventHandler.off(this.element, this.constructor.EVENT_KEY)\n    EventHandler.off(this.element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if (this.element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this.element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this.element)\n      const isInTheDom = shadowRoot === null ?\n        this.element.ownerDocument.documentElement.contains(this.element) :\n        shadowRoot.contains(this.element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this.element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this.element, this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        EventHandler.trigger(this.element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this.element, this.constructor.Event.HIDDEN)\n      this._popper.destroy()\n    }\n\n    const hideEvent = EventHandler.trigger(this.element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.whiteList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: `.${this.constructor.NAME}-arrow`\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this.config.offset(data.offsets, this.element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this.element,\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this.element,\n          eventIn,\n          this.config.selector,\n          event => this._enter(event)\n        )\n        EventHandler.on(this.element,\n          eventOut,\n          this.config.selector,\n          event => this._leave(event)\n        )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this.element.closest(`.${CLASS_NAME_MODAL}`),\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.target, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.target,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.target, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) ||\n        context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.target, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.target,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.target, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this.element)\n\n    Object.keys(dataAttributes)\n      .forEach(dataAttr => {\n        if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n          delete dataAttributes[dataAttr]\n        }\n      })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.whiteList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const popperInstance = popperData.instance\n    this.tip = popperInstance.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .tooltip to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Tooltip.jQueryInterface\n  $.fn[NAME].Constructor = Tooltip\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tooltip.jQueryInterface\n  }\n}\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  // Private\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Popover.jQueryInterface\n  $.fn[NAME].Constructor = Popover\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Popover.jQueryInterface\n  }\n}\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS},` +\n                          `${this._config.target} ${SELECTOR_LIST_ITEMS},` +\n                          `${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets\n      .map(element => {\n        let target\n        const targetSelector = getSelectorFromElement(element)\n\n        if (targetSelector) {\n          target = SelectorEngine.findOne(targetSelector)\n        }\n\n        if (target) {\n          const targetBCR = target.getBoundingClientRect()\n          if (targetBCR.width || targetBCR.height) {\n            return [\n              Manipulator[offsetMethod](target).top + offsetBase,\n              targetSelector\n            ]\n          }\n        }\n\n        return null\n      })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine\n        .findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine\n        .parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = ScrollSpy.jQueryInterface\n  $.fn[NAME].Constructor = ScrollSpy\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return ScrollSpy.jQueryInterface\n  }\n}\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n\n    Data.setData(this._element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented ||\n      (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback &&\n      (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .tab to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Tab.jQueryInterface\n  $.fn[NAME].Constructor = Tab\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tab.jQueryInterface\n  }\n}\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '5.0.0-alpha1'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 500\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(\n      this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      () => this.hide()\n    )\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\nconst $ = getjQuery()\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n *  add .toast to jQuery only if jQuery is present\n */\n/* istanbul ignore if */\nif ($) {\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n  $.fn[NAME] = Toast.jQueryInterface\n  $.fn[NAME].Constructor = Toast\n  $.fn[NAME].noConflict = () => {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Toast.jQueryInterface\n  }\n}\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}