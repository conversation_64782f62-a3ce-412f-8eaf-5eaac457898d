include .env
export $(shell sed 's/=.*//' .env)

copyEnvLocal:
	cp ".env.local" ".env"

copyEnvDocker:
	cp ".env.docker" ".env"

copyEnvDev:
	cp ".env.development" ".env"

copyEnvProd:
	cp ".env.production" ".env"

run:
	./run.sh


# Docker targets - menggunakan folder .devdocker/
runDockerSetup:
	cd .devdocker && docker-compose up --build -d
	cd .devdocker && docker-compose exec php php artisan key:generate
	cd .devdocker && docker-compose exec php php artisan migrate
	cd .devdocker && docker-compose exec php php artisan db:seed

runDockerOnly:
	cd .devdocker && docker-compose up -d

stopDocker:
	cd .devdocker && docker-compose down

freshInstallDocker:
	cd .devdocker && docker-compose exec php php artisan migrate:reset
	cd .devdocker && docker-compose exec php php artisan migrate
	cd .devdocker && docker-compose exec php php artisan db:seed

# Target baru menggunakan script helper
dockerSetupScript:
	./.devdocker/run-docker.sh setup

dockerStartScript:
	./.devdocker/run-docker.sh start

dockerStopScript:
	./.devdocker/run-docker.sh stop

dockerLogsScript:
	./.devdocker/run-docker.sh logs

dockerFreshScript:
	./.devdocker/run-docker.sh fresh

dockerShellScript:
	./.devdocker/run-docker.sh shell

# Local targets - menggunakan folder.devlocal/

runLocal:
	make copyEnvLocal
	make run

runDocker:
	make copyEnvDocker
	make running

freshInstall:
	php artisan migrate:reset
	php artisan migrate
	php artisan db:seed

deployProduction:
	make copyEnvProd
	make running

deployDevelopment:
	make copyEnvDev
	make running

running:
	# chmod +x deploy_queue.sh
	chmod +x deploy.sh
	> docker-compose.log
	> run_output.log
	nohup ./deploy.sh > deploy.log 2>&1 &

# deployQueueBasic:
# 	nohup ./deploy_queue.sh > deploy_queue.log 2>&1 &
